<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.dt</groupId>
        <artifactId>dt-domain-bill</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.dt</groupId>
    <artifactId>dt-bill-client</artifactId>
    <name>dt-bill-client</name>
    <version>1.0.0-SNAPSHOT</version>
    <description>提供暴露出去的服务接口,数据传输对象</description>

    <dependencies>
        <!--common基础类-->
        <dependency>
            <groupId>com.dt</groupId>
            <artifactId>dt-component-common</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.esotericsoftware</groupId>
            <artifactId>kryo</artifactId>
        </dependency>
        <dependency>
            <groupId>de.javakaffee</groupId>
            <artifactId>kryo-serializers</artifactId>
        </dependency>
        <!--加密解密-->
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>danding-encryption-standalone</artifactId>
            <version>1.2.0-RELEASE</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>${maven.source.version}</version>
                <configuration>
                    <attach>true</attach>
                </configuration>
                <executions>
                    <execution>
                        <phase>compile</phase>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <distributionManagement>
        <repository>
            <id>danding</id>
            <name>danding release resp</name>
            <url>http://mvn.yang800.cn/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>danding</id>
            <name>danding snapshot resp</name>
            <url>http://mvn.yang800.cn/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

</project>
