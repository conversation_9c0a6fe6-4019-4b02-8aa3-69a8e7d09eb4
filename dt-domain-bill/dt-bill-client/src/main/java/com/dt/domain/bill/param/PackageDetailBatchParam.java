package com.dt.domain.bill.param;

import com.dt.component.common.param.BaseSearchParam;
import com.dt.domain.bill.dto.PackageDetailDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/10/16 18:28
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "包裹明细", description = "包裹明细")
public class PackageDetailBatchParam extends BaseSearchParam  implements java.io.Serializable  {

    @ApiModelProperty(value = "仓库编码")
    private List<PackageDetailDTO> detailList;

}