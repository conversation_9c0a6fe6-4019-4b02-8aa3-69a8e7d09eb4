package com.dt.domain.bill.bo;

import com.dt.domain.bill.dto.*;
import com.dt.domain.bill.dto.message.MessageMqDTO;
import com.dt.domain.bill.dto.performance.SystemEventDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/10/13 13:44
 */
@Data
public class ReceiptCompleteContainerCommitBillBO implements Serializable {

    @ApiModelProperty(value = "收货作业批次")
    private ReceiptDTO receiptDTO;

    @ApiModelProperty(value = "收货作业批次明细")
    private List<ReceiptDetailDTO> receiptDetailDTOList;

    @ApiModelProperty(value = "入库单")
    private List<AsnDTO> asnDTOList;

    @ApiModelProperty(value = "入库单日志")
    private List<AsnLogDTO> asnLogDTOList;

    @ApiModelProperty(value = "入库单明细")
    private List<AsnDetailDTO> asnDetailDTOList;

    @ApiModelProperty(value = "上架单")
    private ShelfDTO shelfDTO;

    @ApiModelProperty(value = "上架单明细")
    private List<ShelfDetailDTO> shelfDetailDTOList;

    SystemEventDTO systemEventDTO;

    MessageMqDTO messageMqDTO;

}
