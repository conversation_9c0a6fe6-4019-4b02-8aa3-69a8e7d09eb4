package com.dt.domain.bill.param.cw;

import com.dt.component.common.param.BaseSearchParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 入库申请详情
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "StorageApplicationCabinet对象", description = "入库申请详情")
public class StorageApplicationCabinetParam extends BaseSearchParam  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    /**
     * 仓库编码
     */
    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    /**
     * 入库申请编码
     */
    @ApiModelProperty(value = "入库申请编码")
    private String storageApplicationCode;
    private List<String> storageApplicationCodeList;
    
    /**
     * 入库参考号
     */
    @ApiModelProperty(value = "入库参考号")
    private String referenceNumber;
    private List<String> referenceNumberList;

    /**
     * 柜号
     */
    @ApiModelProperty(value = "柜号")
    private String cabinetCode;
    private List<String> cabinetCodeList;

    /**
     * 货主编码
     */
    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private Integer status;
    private List<Integer> statusList;

    /**
     * 库位编码
     */
    @ApiModelProperty(value = "库位编码")
    private String locationCode;

    /**
     * 创建时间 (时间戳)
     */
    @ApiModelProperty(value = "创建时间 (时间戳)")
    private Long unloadTime;

    /**
     * 创建时间 (时间戳)
     */
    @ApiModelProperty(value = "创建时间 (时间戳)")
    private Long collectEndTime;
    private Long collectEndTimeStart;
    private Long collectEndTimeEnd;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "修改人")
    private String updatedBy;
}