package com.dt.domain.bill.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class CollectWaveAnalysisPackStructDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "仓编码")
    private String warehouseCode;

    @ApiModelProperty(value = "包裹结构")
    private String packageStruct;

    @ApiModelProperty(value = "是否预包")
    private String isPre;

    @ApiModelProperty(value = "包裹数量")
    private Long num;



}
