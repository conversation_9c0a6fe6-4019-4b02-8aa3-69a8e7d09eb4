package com.dt.domain.bill.client;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.domain.bill.bo.ShipmentOrderCancelBO;
import com.dt.domain.bill.bo.ShipmentOrderImportBO;
import com.dt.domain.bill.bo.pretreatment.PretreatmentOperationBO;
import com.dt.domain.bill.dto.ShipmentOrderDTO;
import com.dt.domain.bill.dto.ShipmentOrderDetailDTO;
import com.dt.domain.bill.dto.ShipmentOrderLogDTO;
import com.dt.domain.bill.dto.ShipmentOrderMaterialDTO;
import com.dt.domain.bill.param.AllBoxParam;
import com.dt.domain.bill.param.NotifyParams;
import com.dt.domain.bill.param.ShipmentOrderDetailParam;
import com.dt.domain.bill.param.ShipmentOrderParam;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/10/14 19:41
 */
public interface IShipmentOrderClient {

    /**
     * 功能描述:  初始化出库单日汇总数据
     * 创建时间:  2021/3/2 4:50 下午
     *
     * @return java.util.List<com.dt.domain.bill.shipment.entity.ShipmentOrder>
     * <AUTHOR>
     */
    Result<List<ShipmentOrderDTO>> initCount();

    /**
     * 保存 or 更新
     *
     * @param shipmentOrderParam
     * @return
     */
    Result<Boolean> saveOrUpdate(ShipmentOrderParam shipmentOrderParam);

    /**
     * 新增出库单明细信息
     *
     * @param param
     * @return
     */
    Result<Boolean> save(ShipmentOrderParam param);

    /**
     * 修改出库单明细信息
     * id | idList 四选一
     *
     * @param param
     * @return
     */
    Result<Boolean> modify(ShipmentOrderParam param);

    /**
     * 根据条件查询是否存在
     *
     * @param param
     * @return
     */
    Result<Boolean> checkExits(ShipmentOrderParam param);

    /**
     * 功能描述:  统计出库单数据
     * 创建时间:  2021/6/16 3:03 下午
     *
     * @param param:
     * @return com.dt.component.common.result.Result<java.lang.Integer>
     * <AUTHOR>
     */
    Result<Integer> count(ShipmentOrderParam param);

    /**
     * 获取出库单明细信息
     *
     * @param param
     * @return
     */
    Result<ShipmentOrderDTO> get(ShipmentOrderParam param);

    /**
     * 获取出库单明细信息
     *
     * @param param
     * @return
     */
    Result<ShipmentOrderDTO> getDetail(ShipmentOrderParam param);

    /**
     * 获取出库单明细列表
     *
     * @param param
     * @return
     */
    Result<List<ShipmentOrderDTO>> getList(ShipmentOrderParam param);

    /**
     * 分页获取出库单明细
     *
     * @param param
     * @return
     */
    Result<Page<ShipmentOrderDTO>> getPage(ShipmentOrderParam param);


    Result<Page<ShipmentOrderDTO>> getAgainPage(AllBoxParam param);

    /**
     * 明细分页
     *
     * @param param
     * @return
     */
    Result<Page<ShipmentOrderDetailDTO>> getPage2(ShipmentOrderParam param);

    /**
     * 获取ID列表
     *
     * @param param
     * @return
     */
    Result<List<Long>> getIdList(ShipmentOrderParam param);

    /**
     * 修改预处理状态
     *
     * @param param
     * @return
     */
    Result<Boolean> modifyPretreatmentStatus(ShipmentOrderParam param);

    //    /**
//     * 查询明细
//     * @param param
//     * @return
//     */
    Result<ShipmentOrderDTO> findShipmentOrder(ShipmentOrderParam param);

    /**
     * @param param
     * @param fieldList
     * @return com.dt.component.common.result.Result<java.util.List < com.dt.domain.bill.dto.ShipmentOrderDTO>>
     * @author: WuXian
     * description:  查询指定字段的出库单返回
     * create time: 2021/8/10 9:54
     */
    Result<List<ShipmentOrderDTO>> getCollectWaveShipmentOrderList(ShipmentOrderParam param, List<String> fieldList);

    /**
     * 波次查询明细
     *
     * @param param
     * @return
     */
    Result<List<ShipmentOrderDetailDTO>> queryShipmentOrderList(ShipmentOrderParam param);

    /**
     * 波次修改出库单
     *
     * @param shipmentOrderDTOS
     */
    Result<Boolean> submitShipment(List<ShipmentOrderDTO> shipmentOrderDTOS);

    /**
     * 修改通知状态
     *
     * @param shipmentOrderCode
     * @param notifyTime
     * @param notifyStatus
     * @return
     */
    Result<Boolean> modifyNotifyStatus(String shipmentOrderCode, List<String> packageCodeList, Long notifyTime, Integer notifyStatus);


    public Result<Boolean> modifyStatus(String shipmentOrderCode, Long updateTime, String status);

    /**
     * 根据货主查询记录是否存在
     */
    Result<Boolean> checkSoNoExistsByCargoCode(ShipmentOrderParam param);

    Result<List<String>> checkPoNoExistsByCargoCode(ShipmentOrderParam param);

    /**
     * 快递公司以及快递单号
     *
     * @param param
     * @return
     */
    Result<Boolean> checkExpressExistsByCarrierCode(ShipmentOrderParam param);

    /**
     * @param shipmentOrderDTOS
     * @return
     */
    Result<Boolean> submitUpdateCollect(List<ShipmentOrderDTO> shipmentOrderDTOS);

    Result<Boolean> modifyBatch(List<ShipmentOrderDTO> shipmentOrderDTOS);

    /**
     * 获取明细
     *
     * @param param
     * @return
     */
    Result<List<ShipmentOrderDetailDTO>> getDetailList(ShipmentOrderDetailParam param);

    /**
     * 获取包材
     *
     * @param param
     * @return
     */
    Result<List<ShipmentOrderMaterialDTO>> queryOrderMaterialByOrderCode(ShipmentOrderDetailParam param);

    /**
     * 批量修改明细
     *
     * @param shipmentOrderDetailDTOS
     * @return
     */
    Result<Boolean> modifyDetailList(List<ShipmentOrderDetailDTO> shipmentOrderDetailDTOS);

    /**
     * 插入出库单日志
     *
     * @param shipmentOrderLogDTO
     * @return
     */
    Result<Boolean> saveShipmentLog(ShipmentOrderLogDTO shipmentOrderLogDTO);

    /**
     * 批量插入日志
     *
     * @param shipmentOrderLogDTOList
     * @return
     */
    Result<Boolean> saveShipmentLogList(List<ShipmentOrderLogDTO> shipmentOrderLogDTOList);

    Result<List<ShipmentOrderDTO>> getNotifyShipmentOrderList(NotifyParams notifyParams);

    /**
     * 批量查询包材
     *
     * @param param
     * @return
     */
    Result<List<ShipmentOrderMaterialDTO>> getPackMaterialList(ShipmentOrderParam param);

    /**
     * 查询出库单编号列表
     *
     * @param shipmentOrderParam
     * @return
     */
    Result<List<String>> getShipmentCodeList(ShipmentOrderParam shipmentOrderParam);

    Result<Boolean> modifyReceiverInfo(ShipmentOrderParam shipmentOrderParam);

    /**
     * @param shipmentOrderCancelBO
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * @author: WuXian
     * description: 批量取消出库单
     * create time: 2021/7/16 14:34
     */
    Result<Boolean> cancelShipment(ShipmentOrderCancelBO shipmentOrderCancelBO);

    /**
     * @param shipmentOrderDTO
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * @author: WuXian
     * description:
     * create time: 2021/7/16 16:45
     */
    Result<Boolean> saveShipment(ShipmentOrderDTO shipmentOrderDTO);

    /**
     * @param shipmentOrderDTO
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * @author: WuXian
     * description:
     * create time: 2021/7/16 16:45
     */
    Result<Boolean> modifyToSpecialShipment(ShipmentOrderDTO shipmentOrderDTO);

    /**
     * @param param
     * @return com.dt.component.common.result.Result<java.util.List < com.dt.domain.bill.dto.ShipmentOrderDTO>>
     * @author: WuXian
     * description: 获取指定字段的
     * create time: 2021/7/20 17:33
     */
    Result<List<ShipmentOrderDTO>> getAppointMultipleParam(ShipmentOrderParam param, List<String> tableFields);

    /**
     * @param shipmentOrderImportBO
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * @author: WuXian
     * description:  导入新增出库单
     * create time: 2021/7/21 16:25
     */
    Result<Boolean> saveExcelImportShipment(ShipmentOrderImportBO shipmentOrderImportBO);

    /**
     * 预处理数据修改
     * @param param {@link PretreatmentOperationBO}
     * @return {@link Result<Boolean>}
     */
    Result<Boolean> pretreatmentSubmit(PretreatmentOperationBO param);

    /**
     * @param shipmentOrderParam
     * @param tableFields
     * @return java.util.List<com.dt.domain.bill.dto.ShipmentOrderMaterialDTO>
     * @author: WuXian
     * description: 查询包材
     * create time: 2021/11/2 14:02
     */
    Result<List<ShipmentOrderMaterialDTO>> getPackMaterialCollectWaveShipmentOrderList(ShipmentOrderParam shipmentOrderParam, List<String> tableFields);

    /**
     * @param shipmentOrderDTO
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * @author: WuXian
     * description:
     * create time: 2022/5/10 13:47
     */
    Result<Boolean> modify(ShipmentOrderDTO shipmentOrderDTO);
    /**
     * @param shipmentOrderDTO
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     * @describe:
     * @date 2022/9/21 9:26
     */
    Result<Boolean> commitBoxDataShipmentOrder(ShipmentOrderDTO shipmentOrderDTO);

    Result<List<ShipmentOrderLogDTO>> getShipmentLog(ShipmentOrderParam shipmentOrderLogParam);

    @ApiOperation("取消预处理")
    Result<Boolean> cancelPretreatment(PretreatmentOperationBO pretreatmentOperationBO);
}
