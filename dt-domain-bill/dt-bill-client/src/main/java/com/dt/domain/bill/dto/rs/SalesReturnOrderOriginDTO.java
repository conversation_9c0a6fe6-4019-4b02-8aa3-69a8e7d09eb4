package com.dt.domain.bill.dto.rs;

import com.dt.component.common.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import java.math.BigDecimal;

/**
* <p>
    * 退货原单管理
    * </p>
*
* <AUTHOR>
* @since 2025-04-02
*/
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="SalesReturnOrderOrigin对象", description="退货原单管理")
public class SalesReturnOrderOriginDTO extends BaseDTO {

private static final long serialVersionUID = 1L;

    /**
    * 正向运单号
    */
    @ApiModelProperty(value = "正向运单号")
    private String expressNo;

    /**
    * 正向订单号
    */
    @ApiModelProperty(value = "正向订单号")
    private String billNo;

    /**
    * 逆向运单号
    */
    @ApiModelProperty(value = "逆向运单号")
    private String reverseExpressNo;

    /**
    * 10 生效中，20已撤回
    */
    @ApiModelProperty(value = "10 生效中，20已撤回")
    private Integer status;

    /**
    * 拓展字段
    */
    @ApiModelProperty(value = "拓展字段")
    private String extraJson;

@ApiModelProperty(value = "创建人")
private String createdBy;

@ApiModelProperty(value = "修改人")
private String updatedBy;

@ApiModelProperty(value = "仓库")
private String warehouseCode;
}
