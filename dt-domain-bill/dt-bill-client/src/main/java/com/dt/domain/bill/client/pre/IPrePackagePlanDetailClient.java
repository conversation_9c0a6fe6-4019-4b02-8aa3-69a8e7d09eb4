package com.dt.domain.bill.client.pre;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.domain.bill.dto.pre.PrePackagePlanDetailDTO;
import com.dt.domain.bill.param.pre.PrePackagePlanDetailParam;

import java.util.List;

/**
 * <p>
 * 预包计划商品批次明细 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-25
 */
public interface IPrePackagePlanDetailClient {

    /**
     * 新增预包计划商品批次明细
     *
     * @param prePackagePlanDetailDTO
     * @return
     */
    Result<Boolean> save(PrePackagePlanDetailDTO prePackagePlanDetailDTO);

    /**
     * 批量新增预包计划商品批次明细
     *
     * @param prePackagePlanDetailDTOList
     * @return
     */
    Result<Boolean> saveBatch(List<PrePackagePlanDetailDTO> prePackagePlanDetailDTOList);

    /**
     * 修改预包计划商品批次明细
     *
     * ID | Code 二选一
     * @param prePackagePlanDetailDTO
     * @return
     */
    Result<Boolean> modify(PrePackagePlanDetailDTO prePackagePlanDetailDTO);

    /**
     * 批量修改预包计划商品批次明细
     *
     * @param prePackagePlanDetailDTOList
     * @return
     */
    Result<Boolean> modifyBatch(List<PrePackagePlanDetailDTO> prePackagePlanDetailDTOList);

    /**
     * 根据条件查询是否存在
     *
     * @param param
     * @return
     */
    Result<Boolean> checkExits(PrePackagePlanDetailParam param);

    /**
     * 获取预包计划商品批次明细
     *
     * @param param
     * @return
     */
    Result<PrePackagePlanDetailDTO> get(PrePackagePlanDetailParam param);

    /**
     * 获取预包计划商品批次明细列表
     * @param param
     * @return
     */
    Result<List<PrePackagePlanDetailDTO>> getList(PrePackagePlanDetailParam param);

    /**
     * 分页获取预包计划商品批次明细
     *
     * @param param
     * @return
     */
    Result<Page<PrePackagePlanDetailDTO>> getPage(PrePackagePlanDetailParam param);

    /**
     * 功能描述:  删除
     * 创建时间:  2021/1/8 11:25 上午
     *
     * @param param:
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     */
    Result<Boolean> remove(PrePackagePlanDetailParam param);

}

