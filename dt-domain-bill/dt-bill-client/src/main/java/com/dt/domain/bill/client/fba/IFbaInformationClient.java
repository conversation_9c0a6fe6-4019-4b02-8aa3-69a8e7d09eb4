package com.dt.domain.bill.client.fba;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.domain.bill.dto.fba.FbaInformationDTO;
import com.dt.domain.bill.param.fba.FbaInformationParam;

import java.util.List;

/**
 * <p>
 * fba信息采集 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-04
 */
public interface IFbaInformationClient {

    /**
     * 新增fba信息采集
     *
     * @param fbaInformationDTO
     * @return
     */
    Result<Boolean> save(FbaInformationDTO fbaInformationDTO);

    /**
     * 批量新增fba信息采集
     *
     * @param fbaInformationDTOList
     * @return
     */
    Result<Boolean> saveBatch(List<FbaInformationDTO> fbaInformationDTOList);

    /**
     * 修改fba信息采集
     * <p>
     * ID | Code 二选一
     *
     * @param fbaInformationDTO
     * @return
     */
    Result<Boolean> modify(FbaInformationDTO fbaInformationDTO);

    /**
     * 批量修改fba信息采集
     *
     * @param fbaInformationDTOList
     * @return
     */
    Result<Boolean> modifyBatch(List<FbaInformationDTO> fbaInformationDTOList);

    /**
     * 根据条件查询是否存在
     *
     * @param param
     * @return
     */
    Result<Boolean> checkExits(FbaInformationParam param);

    /**
     * 获取fba信息采集
     *
     * @param param
     * @return
     */
    Result<FbaInformationDTO> get(FbaInformationParam param);

    /**
     * 获取fba信息采集列表
     *
     * @param param
     * @return
     */
    Result<List<FbaInformationDTO>> getList(FbaInformationParam param);

    Result<List<FbaInformationDTO>> getListAppointColumn(FbaInformationParam fbaInformationParam, List<String> filedList);

    /**
     * 分页获取fba信息采集
     *
     * @param param
     * @return
     */
    Result<Page<FbaInformationDTO>> getPage(FbaInformationParam param);

    /**
     * 功能描述:  删除
     * 创建时间:  2021/1/8 11:25 上午
     *
     * @param param:
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     */
    Result<Boolean> remove(FbaInformationParam param);

}

