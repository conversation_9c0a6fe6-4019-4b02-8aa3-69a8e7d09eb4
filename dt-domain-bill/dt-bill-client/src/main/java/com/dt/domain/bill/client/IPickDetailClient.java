package com.dt.domain.bill.client;

import com.dt.component.common.result.Result;
import com.dt.domain.bill.dto.PickDetailDTO;
import com.dt.domain.bill.param.PickDetailBatchParam;
import com.dt.domain.bill.param.PickDetailParam;

import javax.sound.midi.SoundbankResource;
import java.util.List;

/**
 * Created by nobody on 2020/12/25 13:21
 */
public interface IPickDetailClient {

    /**
     * 批量修改拣选单明细
     * @param pickDetailBatchParam
     * @return
     */
    Result<Boolean> modifyBatch(PickDetailBatchParam pickDetailBatchParam);

    Result<List<PickDetailDTO>> get(PickDetailParam pickDetailParam);

    /**
     * 拣选单明细列表
     * @param pickDetailParam
     * @return
     */
    Result<List<PickDetailDTO>> list(PickDetailParam pickDetailParam);

    /**
     * 批量修改
     * @param pickDetailDTOList
     * @return
     */
    Result<Boolean> modifyBatch(List<PickDetailDTO> pickDetailDTOList);
    /**
     * @param pickDetailParam
     * @param filedList
     * @return java.util.List<com.dt.domain.bill.dto.PickDetailDTO>
     * @author: WuXian
     * description:  拣选单明细指定字段查询
     * create time: 2021/10/22 15:42
     */
    Result<List<PickDetailDTO>> getPickDetailListAppointColumn(PickDetailParam pickDetailParam, List<String> filedList);
}
