package com.dt.domain.bill.client;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.domain.bill.dto.PackageReturnDTO;
import com.dt.domain.bill.param.PackageReturnParam;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/11/12 15:52
 */
public interface IPackageReturnClient {

    /**
     * 新增出库单明细信息
     * @param param
     * @return
     */
    Result<Boolean> save(PackageReturnParam param);

    /**
     * 修改出库单明细信息
     * id | idList 四选一
     * @param param
     * @return
     */
    Result<Boolean> modify(PackageReturnParam param);

    /**
     * 根据条件查询是否存在
     * @param param
     * @return
     */
    Result<Boolean> checkExits(PackageReturnParam param);

    /**
     * 获取出库单明细信息
     * @param param
     * @return
     */
    Result<PackageReturnDTO> get(PackageReturnParam param);

    /**
     * 获取出库单明细信息
     * @param param
     * @return
     */
    Result<PackageReturnDTO> getDetail(PackageReturnParam param);

    /**
     * 获取出库单明细列表
     * @param param
     * @return
     */
    Result<List<PackageReturnDTO>> getList(PackageReturnParam param);

    /**
     * 分页获取出库单明细
     * @param param
     * @return
     */
    Result<Page<PackageReturnDTO>> getPage(PackageReturnParam param);

    /**
     *
     * @param packageReturnDTO
     * @return
     */
    Result<Boolean> insert(PackageReturnDTO packageReturnDTO);


}
