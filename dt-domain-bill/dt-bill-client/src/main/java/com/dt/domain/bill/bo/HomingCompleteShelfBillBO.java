package com.dt.domain.bill.bo;

import com.dt.domain.bill.dto.ReturnOrderDTO;
import com.dt.domain.bill.dto.ShelfDTO;
import com.dt.domain.bill.dto.message.MessageMqDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/11/19 10:27
 */
@Data
public class HomingCompleteShelfBillBO implements Serializable {

    ShelfDTO shelfDTO;

    ReturnOrderDTO returnOrderDTO;

    MessageMqDTO messageMqDTO;
}
