package com.dt.domain.bill.client;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.domain.bill.dto.ReturnOrderDTO;
import com.dt.domain.bill.dto.ReturnOrderDetailDTO;
import com.dt.domain.bill.param.ReturnOrderDetailParam;
import com.dt.domain.bill.param.ReturnOrderParam;

import java.util.List;

public interface IReturnOrderClient {
    /**
     * 新增归位单信息
     * @param param
     * @return
     */
    Result<Boolean> save(ReturnOrderParam param);
    /**
     * 修改归位单信息
     * @param param
     * @return
     */
    Result<Boolean> modify(ReturnOrderParam param);

    /**
     *
     * @param param
     * @return
     */
    Result<Boolean> modifyByParam(ReturnOrderParam param);

    /**
     * 获取异常单信息
     * @param param
     * @return
     */
    Result<ReturnOrderDTO> get(ReturnOrderParam param);

    /**
     * 归位列表
     * @param param
     * @return
     */
    Result<List<ReturnOrderDTO>> list(ReturnOrderParam param);



    Result<Page<ReturnOrderDTO>> queryPage(ReturnOrderParam param);


    Result<Boolean> modifyDetail(ReturnOrderDetailParam param);
    /**
     * 新增
     * @param returnOrderDTO
     * @return
     */
    Result<Boolean> insert(ReturnOrderDTO returnOrderDTO);

    /**
     * 修改
     * @param orderParam
     * @return
     */
    Result<List<ReturnOrderDetailDTO>> getDetail(ReturnOrderParam orderParam);

    /**
     * 修改归位单
     * @param returnOrderDTO
     * @return
     */
    Result<Boolean> modify(ReturnOrderDTO returnOrderDTO);

    /**
     * 存储归位明细
     * @param returnOrderDetailDTO
     * @return
     */
    Result<Boolean> saveOrUpdateDetail(ReturnOrderDetailDTO returnOrderDetailDTO);

    /**
     * 更新归位单明细
     * @param detailDTOS
     * @return
     */
    Result<Boolean> updateDetails(List<ReturnOrderDetailDTO> detailDTOS);

    /**
     * 更新归位单
     * @param returnOrderDTO
     * @return
     */
    Result<Boolean> update(ReturnOrderDTO returnOrderDTO);

    /**
     * 更新归位单
     * @param returnOrderDTO
     * @return
     */
    Result<Boolean> updateAndDetail(ReturnOrderDTO returnOrderDTO);

    /**
     *
     * @param returnOrderDTO
     * @return
     */
    Result<Boolean> remove(ReturnOrderDTO returnOrderDTO);
}
