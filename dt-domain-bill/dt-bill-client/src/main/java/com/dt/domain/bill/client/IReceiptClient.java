package com.dt.domain.bill.client;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.domain.bill.bo.CompleteReceiptContBillBO;
import com.dt.domain.bill.bo.ReceiptAndContCancelReceiptBillBO;
import com.dt.domain.bill.bo.ReceiptCompleteContainerCommitBillBO;
import com.dt.domain.bill.bo.ReceiptImportCommitBillBO;
import com.dt.domain.bill.bo.receipt.ReceiptCommitBillBO;
import com.dt.domain.bill.dto.ReceiptDTO;
import com.dt.domain.bill.dto.ReceiptDetailDTO;
import com.dt.domain.bill.param.ReceiptBatchParam;
import com.dt.domain.bill.param.ReceiptDetailParam;
import com.dt.domain.bill.param.ReceiptParam;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/9/21 15:03
 */
public interface IReceiptClient {

    /**
     * 新增Receipt信息
     *
     * @param param
     * @return
     */
    Result<Boolean> save(ReceiptParam param);

    /**
     * 批量新增Receipt信息
     *
     * @param param
     * @return
     */
    Result<Boolean> saveBatch(ReceiptBatchParam param);

    /**
     * 根据条件查询是否存在
     *
     * @param param
     * @return
     */
    Result<Boolean> checkExits(ReceiptParam param);

    /**
     * 获取Receipt信息
     *
     * @param param
     * @return
     */
    Result<ReceiptDTO> get(ReceiptParam param);

    /**
     * 获取Receipt列表
     *
     * @param param
     * @return
     */
    Result<List<ReceiptDTO>> getList(ReceiptParam param);

    /**
     * 分页获取Receipt
     *
     * @param param
     * @return
     */
    Result<Page<ReceiptDTO>> getPage(ReceiptParam param);

    /**
     * 提交收货作业批次明细
     *
     * @param receiptDTO
     * @return
     */
    Result<Boolean> commitReceipt(ReceiptDTO receiptDTO);

    /**
     * 查询收货作业批次
     *
     * @param receiptParam
     * @return
     */
    Result<ReceiptDTO> queryReceiptAndDetailByRecId(ReceiptParam receiptParam);

    /**
     * 修改收货作业批次主表
     *
     * @param receiptDTO
     * @return
     */
    Result<Boolean> modifyReceiptOnShelfStatus(ReceiptDTO receiptDTO);


    /**
     * 修改收货作业批次通知主表
     *
     * @param receiptDTO
     * @return
     */
    Result<Boolean> modifyNotifyReceipt(ReceiptDTO receiptDTO);

    /**
     * 查询收货作业批次明细
     *
     * @param receiptParam
     * @return
     */
    Result<List<ReceiptDetailDTO>> queryDetailsByRecId(ReceiptParam receiptParam);

    /**
     * 查询收货作业批次明细
     *
     * @param receiptParam
     * @return
     */
    Result<ReceiptDetailDTO> queryDetailByRecIdAndLineSeq(ReceiptDetailParam receiptParam);


    Result<List<ReceiptDetailDTO>> queryReceiptDetail(ReceiptParam receiptParam);

    /**
     * 删除收货作业批次数据
     *
     * @param param
     * @return
     */

    Result<Boolean> deleteReceipt(ReceiptParam param);

    /**
     * 查询
     *
     * @param param
     * @return
     */
    Result<List<ReceiptDTO>> queryReceipt(ReceiptParam param);

    /**
     * 查询收货作业批次明细
     *
     * @param receiptParam
     * @return
     */
    Result<List<ReceiptDetailDTO>> queryDetails(ReceiptDetailParam receiptParam);

    Result<List<ReceiptDetailDTO>> queryDetails2(ReceiptDetailParam receiptParam);

    /**
     * @param receiptDTO
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * @author: WuXian
     * description:  修改收货作业批次
     * create time: 2021/9/16 13:46
     */
    Result<Boolean> modify(ReceiptDTO receiptDTO);

    /**
     * @param receiptDTO
     * @return java.lang.Boolean
     * @author: WuXian
     * description:
     * create time: 2022/3/16 11:33
     */
    Result<Boolean> returnShipmentClear(ReceiptDTO receiptDTO);

    /**
     * @param receiptDTONewList
     * @return java.lang.Boolean
     * @author: WuXian
     * description:
     * create time: 2022/4/24 9:28
     */
    Result<Boolean> modifyReceiptList(List<ReceiptDTO> receiptDTONewList);

    /**
     * @param receiptCommitBillBO
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     * @describe:
     * @date 2023/6/13 15:52
     */
    Result<Boolean> commitReceiptBill(ReceiptCommitBillBO receiptCommitBillBO);

    /**
     * @param receiptImportCommitBillBO
     * @return java.lang.Boolean
     * <AUTHOR>
     * @describe: 导入生成收货作业批次数据
     * @date 2023/6/14 14:11
     */
    Result<Boolean> modifyCommitContContext(ReceiptImportCommitBillBO receiptImportCommitBillBO);

    /**
     * @param completeReceiptContBillBO
     * @return java.lang.Boolean
     * <AUTHOR>
     * @describe: 导入收货完成容器生成上架单
     * @date 2023/6/14 14:11
     */
    Result<Boolean> commitImportReceiptAndShelf(CompleteReceiptContBillBO completeReceiptContBillBO);

    /**
     * @param receiptAndContCancelReceiptBillBO
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     * @describe: 收货作业批次维度取消收货
     * @date 2023/6/14 14:10
     */
    Result<Boolean> receiptAndContAndCancel(ReceiptAndContCancelReceiptBillBO receiptAndContCancelReceiptBillBO);

    /**
     * @param receiptCompleteContainerCommitBillBO
     * @return java.lang.Boolean
     * <AUTHOR>
     * @describe: 退货入库完成容器
     * @date 2023/6/14 14:29
     */
    Result<Boolean> commitReceiptByContainer(ReceiptCompleteContainerCommitBillBO receiptCompleteContainerCommitBillBO);

    /**
     * @param receiptParam
     * @param fieldNameList
     * @return com.dt.component.common.result.Result<java.util.List < com.dt.domain.bill.dto.ReceiptDTO>>
     * <AUTHOR>
     * @describe: 收货作业批次
     * @date 2024/5/9 14:54
     */
    Result<List<ReceiptDTO>> getAppointColumnList(ReceiptParam receiptParam, List<String> fieldNameList);
}
