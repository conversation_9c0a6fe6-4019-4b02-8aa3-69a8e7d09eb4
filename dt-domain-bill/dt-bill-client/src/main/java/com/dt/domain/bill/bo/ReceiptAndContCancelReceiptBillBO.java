package com.dt.domain.bill.bo;

import com.dt.domain.bill.dto.*;
import com.dt.domain.bill.dto.message.MessageMqDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/12/15 10:31
 */
@Data
public class ReceiptAndContCancelReceiptBillBO implements Serializable {

    private List<AsnDTO> asnDTOList;
    private List<AsnLogDTO> asnLogDTOList;
    private List<AsnDetailDTO> asnDetailDTOList;
    private ReceiptDTO receiptDTO;
    private List<ReceiptDetailDTO> receiptDetailDTOList;
    private List<ShelfDTO> shelfDTOList;
    private List<ShelfDetailDTO> shelfDetailDTOList;
    private MessageMqDTO messageMqDTO;
}
