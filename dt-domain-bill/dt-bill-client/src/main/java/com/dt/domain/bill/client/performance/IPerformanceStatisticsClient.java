package com.dt.domain.bill.client.performance;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.domain.bill.dto.performance.PerformanceStatisticsDTO;
import com.dt.domain.bill.param.performance.PerformanceStatisticsParam;

import java.util.List;

/**
 * <p>
 * 人员操作统计 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-02-27
 */
public interface IPerformanceStatisticsClient {

    /**
     * 新增人员操作统计
     *
     * @param performanceStatisticsDTO
     * @return
     */
    Result<Boolean> save(PerformanceStatisticsDTO performanceStatisticsDTO);

    /**
     * 批量新增人员操作统计
     *
     * @param performanceStatisticsDTOList
     * @return
     */
    Result<Boolean> saveBatch(List<PerformanceStatisticsDTO> performanceStatisticsDTOList);

    /**
     * 修改人员操作统计
     *
     * ID | Code 二选一
     * @param performanceStatisticsDTO
     * @return
     */
    Result<Boolean> modify(PerformanceStatisticsDTO performanceStatisticsDTO);

    /**
     * 批量修改人员操作统计
     *
     * @param performanceStatisticsDTOList
     * @return
     */
    Result<Boolean> modifyBatch(List<PerformanceStatisticsDTO> performanceStatisticsDTOList);

    /**
     * 根据条件查询是否存在
     *
     * @param param
     * @return
     */
    Result<Boolean> checkExits(PerformanceStatisticsParam param);

    /**
     * 获取人员操作统计
     *
     * @param param
     * @return
     */
    Result<PerformanceStatisticsDTO> get(PerformanceStatisticsParam param);

    /**
     * 获取人员操作统计列表
     * @param param
     * @return
     */
    Result<List<PerformanceStatisticsDTO>> getList(PerformanceStatisticsParam param);

    /**
     * 分页获取人员操作统计
     *
     * @param param
     * @return
     */
    Result<Page<PerformanceStatisticsDTO>> getPage(PerformanceStatisticsParam param);

    /**
     * 功能描述:  删除
     * 创建时间:  2021/1/8 11:25 上午
     *
     * @param param:
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     */
    Result<Boolean> remove(PerformanceStatisticsParam param);



}

