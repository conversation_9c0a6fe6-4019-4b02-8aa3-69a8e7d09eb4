package com.dt.domain.bill.param.pre;

import com.dt.component.common.param.BaseSearchParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;


/**
 * <p>
 * 预包计划商品批次明细
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="PrePackagePlanDetail对象", description="预包计划商品批次明细")
public class PrePackagePlanDetailParam extends BaseSearchParam  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    /**
     * 仓库编码
     */
    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    /**
     * 货主编码
     */
    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    /**
     * 预包计划号
     */
    @ApiModelProperty(value = "预包计划号")
    private String prePlanCode;
    private List<String> prePlanCodeList;

    /**
     * 预报条码
     */
    @ApiModelProperty(value = "预报条码")
    private String preUpcCode;

    /**
     * 预报条码
     */
    @ApiModelProperty(value = "预报条码")
    private List<String> preUpcCodeList;

    /**
     * 商品代码
     */
    @ApiModelProperty(value = "商品代码")
    private String skuCode;

    /**
     * 商品代码
     */
    @ApiModelProperty(value = "商品代码")
    private List<String> skuCodeList;

    /**
     * 计划数量 默认:0
     */
    @ApiModelProperty(value = "计划数量 默认:0")
    private BigDecimal skuQty;

    /**
     * 商品属性
     */
    @ApiModelProperty(value = "商品属性")
    private String skuQuality;

    /**
     * 来源库位
     */
    @ApiModelProperty(value = "来源库位")
    private String locationCode;

    /**
     * 批次ID
     */
    @ApiModelProperty(value = "批次ID")
    private String skuLotNo;

    /**
     * 单据状态(创建、审核中、审核驳回、审核成功、待复核、上架中、完成、取消)
     */
    @ApiModelProperty(value = "单据状态(创建、审核中、审核驳回、审核成功、待复核、上架中、完成、取消)")
    private String status;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "修改人")
    private String updatedBy;
}