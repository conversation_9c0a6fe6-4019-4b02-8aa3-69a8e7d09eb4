package com.dt.domain.bill.client.rs;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.domain.bill.dto.rs.SalesReturnLocationDTO;
import com.dt.domain.bill.param.rs.SalesReturnLocationParam;

import java.util.List;

/**
 * <p>
 * 退货库位 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-25
 */
public interface ISalesReturnLocationClient {

    /**
     * 新增退货库位
     *
     * @param salesReturnLocationDTO
     * @return
     */
    Result<Boolean> save(SalesReturnLocationDTO salesReturnLocationDTO);

    /**
     * 批量新增退货库位
     *
     * @param salesReturnLocationDTOList
     * @return
     */
    Result<Boolean> saveBatch(List<SalesReturnLocationDTO> salesReturnLocationDTOList);

    /**
     * 修改退货库位
     *
     * ID | Code 二选一
     * @param salesReturnLocationDTO
     * @return
     */
    Result<Boolean> modify(SalesReturnLocationDTO salesReturnLocationDTO);

    /**
     * 批量修改退货库位
     *
     * @param salesReturnLocationDTOList
     * @return
     */
    Result<Boolean> modifyBatch(List<SalesReturnLocationDTO> salesReturnLocationDTOList);

    /**
     * 根据条件查询是否存在
     *
     * @param param
     * @return
     */
    Result<Boolean> checkExits(SalesReturnLocationParam param);

    /**
     * 获取退货库位
     *
     * @param param
     * @return
     */
    Result<SalesReturnLocationDTO> get(SalesReturnLocationParam param);

    /**
     * 获取退货库位列表
     * @param param
     * @return
     */
    Result<List<SalesReturnLocationDTO>> getList(SalesReturnLocationParam param);

    /**
     * 分页获取退货库位
     *
     * @param param
     * @return
     */
    Result<Page<SalesReturnLocationDTO>> getPage(SalesReturnLocationParam param);

    /**
     * 功能描述:  删除
     * 创建时间:  2021/1/8 11:25 上午
     *
     * @param param:
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     */
    Result<Boolean> remove(SalesReturnLocationParam param);

}

