package com.dt.domain.bill.param.shelf;

import com.dt.component.common.param.BaseSearchParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 下架明细
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "OffShelfDetail对象", description = "下架明细")
public class OffShelfDetailParam extends BaseSearchParam  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    /**
     * 仓库编码
     */
    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    /**
     * 货主编码
     */
    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    /**
     * 商品编码
     */
    @ApiModelProperty(value = "商品编码")
    private String skuCode;
    private List<String> skuCodeList;

    /**
     * 下架库位
     */
    @ApiModelProperty(value = "下架库位")
    private String locationCode;

    /**
     * 批次ID
     */
    @ApiModelProperty(value = "批次ID")
    private String skuLotNo;
    private List<String> skuLotNoList;

    /**
     * 下架单号
     */
    @ApiModelProperty(value = "下架单号")
    private String offShelfCode;

    /**
     * 下架人
     */
    @ApiModelProperty(value = "下架人")
    private String offShelfBy;

    /**
     * 状态码
     */
    @ApiModelProperty(value = "状态码")
    private String status;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "修改人")
    private String updatedBy;
    @ApiModelProperty("库存操作批次")
    private String batchSerialNo;

}