package com.dt.domain.bill.client;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.domain.bill.dto.PackageInspectionDTO;
import com.dt.domain.bill.param.PackageInspectionParam;

import java.util.List;

/**
 * <p>
 * 包裹抽检 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-26
 */
public interface IPackageInspectionClient {

    /**
     * 新增包裹抽检
     *
     * @param packageSampleInspectionDTO
     * @return
     */
    Result<Boolean> save(PackageInspectionDTO packageSampleInspectionDTO);
    Result<Boolean> saveOrUpdate(PackageInspectionDTO packageSampleInspectionDTO);
    /**
     * 批量新增包裹抽检
     *
     * @param packageSampleInspectionDTOList
     * @return
     */
    Result<Boolean> saveBatch(List<PackageInspectionDTO> packageSampleInspectionDTOList);

    /**
     * 修改包裹抽检
     *
     * ID | Code 二选一
     * @param packageSampleInspectionDTO
     * @return
     */
    Result<Boolean> modify(PackageInspectionDTO packageSampleInspectionDTO);

    /**
     * 批量修改包裹抽检
     *
     * @param packageSampleInspectionDTOList
     * @return
     */
    Result<Boolean> modifyBatch(List<PackageInspectionDTO> packageSampleInspectionDTOList);

    /**
     * 根据条件查询是否存在
     *
     * @param param
     * @return
     */
    Result<Boolean> checkExits(PackageInspectionParam param);

    /**
     * 获取包裹抽检
     *
     * @param param
     * @return
     */
    Result<PackageInspectionDTO> get(PackageInspectionParam param);

    /**
     * 获取包裹抽检列表
     * @param param
     * @return
     */
    Result<List<PackageInspectionDTO>> getList(PackageInspectionParam param);

    /**
     * 分页获取包裹抽检
     *
     * @param param
     * @return
     */
    Result<Page<PackageInspectionDTO>> getPage(PackageInspectionParam param);

    /**
     * 功能描述:  删除
     * 创建时间:  2021/1/8 11:25 上午
     *
     * @param param:
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     */
    Result<Boolean> remove(PackageInspectionParam param);

    Result<Integer> findInspectionCount(PackageInspectionParam param);

    Result<Boolean> removeByDTOList(List<PackageInspectionDTO> packageInspectionDTOList);
}

