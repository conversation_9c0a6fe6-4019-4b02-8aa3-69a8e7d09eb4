package com.dt.domain.bill.client;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.domain.bill.dto.OrderInterceptDTO;
import com.dt.domain.bill.param.OrderInterceptParam;

import java.util.List;

public interface IOrderInterceptClient {


    /**
     * 新单信息
     * @param param
     * @return
     */
    Result<Boolean> save(OrderInterceptDTO param);

    /**
     * 修改
     * @param param
     * @return
     */
    Result<Boolean> update(OrderInterceptDTO param);

    /**
     * 根据条件查询是否存在
     * @param param
     * @return
     */
    Result<Boolean> checkExits(OrderInterceptParam param);


    /**
     * 分页获取拣选单
     * @param param
     * @return
     */
    Result<Page<OrderInterceptDTO>> getPage(OrderInterceptParam param);

    Result<List<OrderInterceptDTO>> getList(OrderInterceptParam param);

    /**
     * 根据条件 计数
     * @param param
     * @return
     */
    Result<Integer> count(OrderInterceptParam param);
    /**
     * 获取出库单列表
     * @param param
     * @return
     */
    Result<List<String>> getShipmentCodeList(OrderInterceptParam param);
    /**
     * @author: WuXian
     * description:
     * create time: 2021/8/25 15:01
     *
     * @param orderInterceptParam
     * @param tableFields
     * @return com.dt.component.common.result.Result<java.util.List < com.dt.domain.bill.dto.OrderInterceptDTO>>
     */
    Result<List<OrderInterceptDTO>> getAppointMultipleParam(OrderInterceptParam orderInterceptParam, List<String> tableFields);

    Result<Integer> getCountExport(OrderInterceptParam arderInterceptParam);
}
