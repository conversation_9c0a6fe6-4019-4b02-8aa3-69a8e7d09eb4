package com.dt.domain.bill.client.rec;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.domain.bill.bo.ReceiptExtraImportCommitBillBO;
import com.dt.domain.bill.dto.rec.ReceiptExtraDTO;
import com.dt.domain.bill.param.rec.ReceiptExtraParam;

import java.util.List;

/**
 * <p>
 * 收货作业批次 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-02-20
 */
public interface IReceiptExtraClient {

    /**
     * 新增收货作业批次
     *
     * @param receiptExtraDTO
     * @return
     */
    Result<Boolean> save(ReceiptExtraDTO receiptExtraDTO);

    /**
     * 批量新增收货作业批次
     *
     * @param receiptExtraDTOList
     * @return
     */
    Result<Boolean> saveBatch(List<ReceiptExtraDTO> receiptExtraDTOList);

    /**
     * 修改收货作业批次
     *
     * ID | Code 二选一
     * @param receiptExtraDTO
     * @return
     */
    Result<Boolean> modify(ReceiptExtraDTO receiptExtraDTO);

    /**
     * 批量修改收货作业批次
     *
     * @param receiptExtraDTOList
     * @return
     */
    Result<Boolean> modifyBatch(List<ReceiptExtraDTO> receiptExtraDTOList);

    /**
     * 根据条件查询是否存在
     *
     * @param param
     * @return
     */
    Result<Boolean> checkExits(ReceiptExtraParam param);

    /**
     * 获取收货作业批次
     *
     * @param param
     * @return
     */
    Result<ReceiptExtraDTO> get(ReceiptExtraParam param);

    /**
     * 获取收货作业批次列表
     * @param param
     * @return
     */
    Result<List<ReceiptExtraDTO>> getList(ReceiptExtraParam param);

    /**
     * 分页获取收货作业批次
     *
     * @param param
     * @return
     */
    Result<Page<ReceiptExtraDTO>> getPage(ReceiptExtraParam param);

    /**
     * 功能描述:  删除
     * 创建时间:  2021/1/8 11:25 上午
     *
     * @param param:
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     */
    Result<Boolean> remove(ReceiptExtraParam param);

    /**
     *
     * @param receiptExtraDTO
     * @return
     */
    Result<Boolean> commitReceiptExtra(ReceiptExtraDTO receiptExtraDTO);

    /**
     * 多货清空容器
     * @param receiptExtraParam
     * @return
     */
    Result<Boolean> deleteReceipt(ReceiptExtraParam receiptExtraParam);

    Result<Boolean> modifyCommitContext(ReceiptExtraDTO receiptExtraDTO);

    Result<Boolean> modifyCommitContContext(ReceiptExtraImportCommitBillBO receiptExtraImportCommitBillBO);
}

