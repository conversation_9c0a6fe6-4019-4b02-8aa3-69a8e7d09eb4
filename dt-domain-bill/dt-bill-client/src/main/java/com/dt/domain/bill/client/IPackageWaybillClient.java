package com.dt.domain.bill.client;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.domain.bill.dto.PackageWaybillDTO;
import com.dt.domain.bill.param.PackageWaybillParam;

import java.util.List;

/**
 * <p>
 * 包裹面单管理
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-11
 */
public interface IPackageWaybillClient {
    /**
     * 新增包裹面单信息
     * @param param
     * @return
     */
    Result<Boolean> save(PackageWaybillParam param);
    Result<Boolean> saveOrUpdate(PackageWaybillParam param);

    /**
     * 修改包裹面单信息
     * id | code | idList | codeList 四选一
     * @param param
     * @return
     */
    Result<Boolean> modify(PackageWaybillParam param);

    /**
     * 根据条件查询是否存在
     * @param param
     * @return
     */
    Result<Boolean> checkExits(PackageWaybillParam param);

    /**
     * 获取包裹面单信息
     * @param param
     * @return
     */
    Result<PackageWaybillDTO> get(PackageWaybillParam param);

    /**
     * 获取包裹面单信息
     * @param param
     * @return
     */
    Result<PackageWaybillDTO> getDetail(PackageWaybillParam param);

    /**
     * 获取包裹面单列表
     * @param param
     * @return
     */
    Result<List<PackageWaybillDTO>> getList(PackageWaybillParam param);

    /**
     * 分页获取包裹面单
     * @param param
     * @return
     */
    Result<Page<PackageWaybillDTO>> getPage(PackageWaybillParam param);

    /**
     * 获取包裹面单列表
     * @param param
     * @return
     */
    Result<List<PackageWaybillDTO>> getDetailList(PackageWaybillParam param);

}
