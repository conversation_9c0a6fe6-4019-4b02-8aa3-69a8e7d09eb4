package com.dt.domain.bill.client;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.domain.bill.dto.*;
import com.dt.domain.bill.param.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/10/14 19:41
 */
public interface IPackageClient {


    /**
     * 功能描述:  初始化包裹日汇总
     * 创建时间:  2021/3/2 5:35 下午
     *
     * @return com.dt.component.common.result.Result<java.util.List < com.dt.domain.bill.dto.PackageDTO>>
     * <AUTHOR>
     */
    Result<List<PackageDTO>> initCount();

    /**
     * 新增出库单明细信息
     *
     * @param param
     * @return
     */
    Result<Boolean> save(PackageParam param);

    Result<Boolean> saveOrUpdate(PackageParam param);

    /**
     * 修改出库单明细信息
     * id | idList 四选一
     *
     * @param param
     * @return
     */
    Result<Boolean> modify(PackageParam param);

    Result<Boolean> modifyBatch(PackageBatchParam param);

    Result<Boolean> modifyBatch(List<PackageDTO> packageDTOList);

    /**
     * 根据条件查询是否存在
     *
     * @param param
     * @return
     */
    Result<Boolean> checkExits(PackageParam param);

    /**
     * 获取出库单明细信息
     *
     * @param param
     * @return
     */
    Result<PackageDTO> get(PackageParam param);

    /**
     * 包裹号或快递单号查询
     *
     * @param param
     * @return
     */
    Result<List<PackageDTO>> getPackageCodeOrExpressNo(PackageParam param);

    /**
     * 获取出库单明细信息
     *
     * @param param
     * @return
     */
    Result<PackageDTO> getDetail(PackageParam param);

    /**
     * 获取出库单明细列表
     *
     * @param param
     * @return
     */
    Result<List<PackageDTO>> getList(PackageParam param);

    Result<Page<PackageDTO>> getExportPage(PackageParam param);

    /**
     * 分页获取出库单明细
     *
     * @param param
     * @return
     */
    Result<Page<PackageDTO>> getPage(PackageParam param);

    /**
     * 分页
     *
     * @param searchPackageParam
     * @return
     */
    public Result<Page<PackageDTO>> queryPage(PackageParam searchPackageParam);

    /**
     * 详情
     *
     * @param searchPackageParam
     * @return
     */
    public Result<PackageDTO> getDetailAndEntityDetail(PackageParam searchPackageParam);

    /**
     * 波次汇单查询包裹数据
     *
     * @param param
     * @return
     */
    Result<List<CollectWaveDTO>> getCollectWaveList(CollectWaveBillParam param);

//    /**
//     * 波次汇单查询包裹明细数据
//     *
//     * @param param
//     * @return
//     */
//    Result<List<PackageDetailDTO>> getCollectWavePackageDetailList(PackageDetailParam param);
//
//    /**
//     * 波次汇单查询
//     *
//     * @param param
//     * @return
//     */
//    Result<List<PackageDTO>> getCollectWaveCommitList(CollectWaveBillParam param);

    /**
     * 查询明细
     *
     * @param param
     * @return
     */
    Result<List<PackageDetailDTO>> getPackageDetailListByCode(PackageParam param);

    /**
     * 查询单个包裹
     *
     * @param param
     * @return
     */
    Result<PackageDTO> getPackageByCode(PackageParam param);

    /**
     * 汇单修改包裹
     *
     * @param packageDTOS
     */
    Result<Boolean> submitUpdateCollect(List<PackageDTO> packageDTOS);

    /**
     * 波次汇单查询
     *
     * @param param
     * @return
     */
    Result<List<PackageDTO>> getCollectWaveList(PackageParam param);

    /**
     * 修改包裹主单
     *
     * @param packageDTO
     * @return
     */
    Result<Boolean> updatePackageDTO(PackageDTO packageDTO);

    /**
     * 修改包裹明细
     *
     * @param packageDetailDTOS
     * @return
     */
    Result<Boolean> updatePackageDetailDTOS(List<PackageDetailDTO> packageDetailDTOS);

    /**
     * 秒杀波次汇单查询
     *
     * @param param
     * @return
     */
    Result<List<CollectWaveDTO>> querySpikeCollectWave(CollectWaveBillParam param);


    /**
     * 查询指定范围数据
     *
     * @param param
     * @return
     */
    Result<List<PackageDetailDTO>> getCollectWaveToPick(PackageDetailParam param);

    /**
     * 波次汇拣选单修改包裹状态
     *
     * @param packageDTOS
     * @return
     */
    Result<Boolean> updateCollectPackage(List<PackageDTO> packageDTOS);

    /**
     * 波次查询包裹数据
     *
     * @param packageParam
     * @return
     */
    Result<List<PackageDTO>> queryList(PackageParam packageParam);

    /**
     * 获取包裹明细
     *
     * @param param
     * @return
     */
    Result<List<PackageDetailDTO>> getPackageDetailListByListCode(PackageDetailParam param);

    /**
     * 修改包裹通知状态
     *
     * @param packageDTO
     * @return
     */
    Result<Boolean> modifyNotifyPackage(PackageDTO packageDTO);

    /**
     * 汇单修改包裹状态
     *
     * @param packageDTOS
     * @return
     */
    Result<Boolean> submitCollectWave(List<PackageDTO> packageDTOS);

    /**
     * 汇单分配查询
     *
     * @param param
     * @return
     */
    Result<List<PackageDTO>> getAllocationWave(PackageParam param);

    /**
     * 插入包裹日志
     *
     * @param packageLogDTO
     * @return
     */
    Result<Boolean> savePackageLog(PackageLogDTO packageLogDTO);

    /**
     * 批量插入日志
     *
     * @param packageLogDTOList
     * @return
     */
    Result<Boolean> savePackLogList(List<PackageLogDTO> packageLogDTOList);

    Result<List<PackageDTO>> getNotifyPackageList(NotifyParams notifyParams);

    /**
     * 功能描述:  批量修改预处理状态
     * 创建时间:  2021/6/9 6:23 下午
     *
     * @param packageParam:
     * @return void
     * <AUTHOR>
     */
    Result<Boolean> modifyPretreatmentStatus(PackageParam packageParam);

    /**
     * @param packageParam
     * @param tableFields
     * @return java.util.List<com.dt.domain.bill.dto.PackageDTO>
     * @author: WuXian
     * description:  包裹查询指定字段
     * create time: 2021/8/19 14:13
     */
    Result<List<PackageDTO>> getAppointMultipleParam(PackageParam packageParam, List<String> tableFields);

    /**
     * @param packageDetailParam
     * @param filedList
     * @return com.dt.component.common.result.Result<java.util.List < com.dt.domain.bill.dto.PackageDetailDTO>>
     * @author: WuXian
     * description:
     * create time: 2021/8/10 9:45
     */
    Result<List<PackageDetailDTO>> getCollectWavePackageDetailListAppointColumn(PackageDetailParam packageDetailParam, List<String> filedList);

    /**
     * @param packageParam
     * @return java.util.List<java.util.Map < java.lang.String, java.lang.Object>>
     * @author: WuXian
     * description:
     * create time: 2021/12/30 13:47
     */
    Result<List<CollectWaveAnalysisDTO>> getWaveCollectFrontAnalysisGroupBy(PackageParam packageParam);

    /**
     * @param packageParam
     * @return com.dt.component.common.result.Result<java.lang.Integer>
     * @author: WuXian
     * description:
     * create time: 2021/12/17 9:58
     */
    Result<Integer> getWaveNavigationHead(PackageParam packageParam);

    /**
     * @param waveNavigationParam
     * @return java.util.List<com.dt.domain.bill.dto.WaveNavigationQueryHeadDTO>
     * @author: WuXian
     * description:  波次导航获取
     * create time: 2021/12/17 10:29
     */
    Result<List<Map<String, Object>>> getWaveNavigationBodyGroup(WaveNavigationParam waveNavigationParam);

    /**
     * @param waveNavigationParam
     * @return com.dt.component.common.result.Result<java.lang.Integer>
     * @author: WuXian
     * description:  最晚发货时间
     * create time: 2021/12/17 16:50
     */
    Result<Integer> getWaveNavigationBodyByLastShipTime(WaveNavigationParam waveNavigationParam);

    /**
     * @param packageParam
     * @return com.dt.component.common.result.Result<com.dt.domain.bill.dto.PackageDTO>
     * @author: WuXian
     * description:
     * create time: 2021/12/20 15:15
     */
    Result<PackageDTO> getWaveNavigationByAnalysisSkuLimitOne(PackageParam packageParam);

    /**
     * @param packageParam
     * @return java.util.List<com.dt.domain.bill.dto.CollectWaveDTO>
     * @author: WuXian
     * description:
     * create time: 2021/12/22 11:42
     */
    Result<List<CollectWaveDTO>> getWaveNavigationCollectGroup(PackageParam packageParam);

    /**
     * @param packageParam
     * @return java.util.List<com.dt.domain.bill.dto.CollectWaveDTO>
     * @author: WuXian
     * description: 波次导航分组查询包裹
     * create time: 2021/12/22 11:42
     */
    Result<List<PackageDTO>> getWaveNavigationCollectWaveList(PackageParam packageParam);

    Result<Integer> getExportCountNum(PackageParam packageParam);

    Result<Page<PackageDTO>> getPageNew(PackageParam packageParam);

    /**
     * @param packageParam
     * @return com.dt.component.common.result.Result<java.util.List < com.dt.domain.bill.dto.CollectWaveAnalysisPackStructDTO>>
     * <AUTHOR>
     * @describe:
     * @date 2023/3/28 16:05
     */
    Result<List<CollectWaveAnalysisPackStructDTO>> getCollectWaveAnalysisPackStruct(PackageParam packageParam);

    Result<List<PackageLogDTO>> getPackLog(PackageParam packageParam);

    /**
     * @param packageParam
     * @return com.dt.component.common.result.Result<java.util.Map < java.lang.String, java.lang.Long>>
     * <AUTHOR>
     * @describe: 获取包裹打印次数
     * @date 2025/3/24 13:23
     */
    Result<Map<String, Long>> getPackPrintNum(PackageParam packageParam);
}
