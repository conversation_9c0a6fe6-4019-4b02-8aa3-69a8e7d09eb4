package com.dt.domain.bill.param.material;

import com.dt.component.common.param.BaseSearchParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;


/**
 * <p>
 * 包材管理
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="PackageMaterialGroup对象", description="包材管理")
public class PackageMaterialGroupParam extends BaseSearchParam  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    /**
     * 仓库编码
     */
    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    /**
     * 货主编码
     */
    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    /**
     * 包材组编码 不允许修改，唯一
     */
    @ApiModelProperty(value = "包材组编码 不允许修改，唯一")
    private String code;
    @ApiModelProperty(value = "货主编码")
    private List<String> cargoCodeList;
    @ApiModelProperty(value = "包材组编码 不允许修改，唯一")
    private List<String> codeList;
    /**
     * 包材组名称
     */
    @ApiModelProperty(value = "包材组名称")
    private String name;
    @ApiModelProperty(value = "包材条码，逗号给开")
    private List<String> barCodeList;
    /**
     * 包材条码，逗号给开
     */
    @ApiModelProperty(value = "包材条码，逗号给开")
    private String barCode;

    /**
     * 状态码 ，取值启用、停用，默认为启用状态(1:启用，-1:停用)
     */
    @ApiModelProperty(value = "状态码 ，取值启用、停用，默认为启用状态(1:启用，-1:停用)")
    private Integer status;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "修改人")
    private String updatedBy;


}