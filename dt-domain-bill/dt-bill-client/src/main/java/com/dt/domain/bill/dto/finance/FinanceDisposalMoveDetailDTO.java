package com.dt.domain.bill.dto.finance;

import com.dt.component.common.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 * 金融监管处置单明细
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "FinanceDisposalMoveDetail对象", description = "金融监管处置单明细")
public class FinanceDisposalMoveDetailDTO extends BaseDTO  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    /**
     * 仓库编码
     */
    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    /**
     * 来源货主编码
     */
    @ApiModelProperty(value = "来源货主编码")
    private String originCargoCode;

    /**
     * 目标货主编码
     */
    @ApiModelProperty(value = "目标货主编码")
    private String targetCargoCode;

    /**
     * 监管处置单号
     */
    @ApiModelProperty(value = "监管处置单号")
    private String disposalCode;

    /**
     * 来源商品编码
     */
    @ApiModelProperty(value = "来源商品编码")
    private String originSkuCode;

    /**
     * 来源wms批次编码
     */
    @ApiModelProperty(value = "来源wms批次编码")
    private String originSkuLotNo;

    /**
     * 来源数量 默认:0
     */
    @ApiModelProperty(value = "来源数量 默认:0")
    private BigDecimal originQty;

    /**
     * 来源库位
     */
    @ApiModelProperty(value = "来源库位")
    private String originLocationCode;

    /**
     * 目标商品编码
     */
    @ApiModelProperty(value = "目标商品编码")
    private String targetSkuCode;

    /**
     * 目标wms批次编码
     */
    @ApiModelProperty(value = "目标wms批次编码")
    private String targetSkuLotNo;

    /**
     * 目标数量 默认:0
     */
    @ApiModelProperty(value = "目标数量 默认:0")
    private BigDecimal targetQty;

    /**
     * 目标库位
     */
    @ApiModelProperty(value = "目标库位")
    private String targetLocationCode;

    /**
     * 单据状态
     */
    @ApiModelProperty(value = "单据状态")
    private String status;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "修改人")
    private String updatedBy;
}