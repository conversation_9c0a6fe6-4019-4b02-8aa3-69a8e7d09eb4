package com.dt.domain.bill.param.cw;

import com.dt.component.common.param.BaseSearchParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 托盘记录
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "RegRecord对象", description = "托盘记录")
public class RegRecordParam extends BaseSearchParam  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    /**
     * 仓库编码
     */
    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    /**
     * 托盘号
     */
    @ApiModelProperty(value = "托盘号")
    private String reg;
    private List<String> regList;

    /**
     * 箱数
     */
    @ApiModelProperty(value = "箱数")
    private Integer boxCount;

    /**
     * 总件数
     */
    @ApiModelProperty(value = "总件数")
    private Integer totalCount;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "修改人")
    private String updatedBy;
}