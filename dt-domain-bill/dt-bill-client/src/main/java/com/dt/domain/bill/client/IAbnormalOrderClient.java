package com.dt.domain.bill.client;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.domain.bill.dto.AbnormalOrderDTO;
import com.dt.domain.bill.param.AbnormalOrderParam;

import java.util.List;

public interface IAbnormalOrderClient {
    /**
     * 新增异常单信息
     * @param param
     * @return
     */
    Result<Boolean> save(AbnormalOrderParam param);

    Result<Boolean>  saveOrUpdate(AbnormalOrderParam param);

    /**
     * 修改异常单信息
     * id | idList 二选一
     * @param param
     * @return
     */
    Result<Boolean> modify(AbnormalOrderParam param);

    /**
     * 根据条件查询是否存在
     * @param param
     * @return
     */
    Result<Boolean> checkExits(AbnormalOrderParam param);

    /**
     * 获取异常单信息
     * @param param
     * @return
     */
    Result<AbnormalOrderDTO> get(AbnormalOrderParam param);

    /**
     * 获取异常单列表
     * @param param
     * @return
     */
    Result<List<AbnormalOrderDTO>> getList(AbnormalOrderParam param);

    /**
     * 分页获取异常单
     * @param param
     * @return
     */
    //Result<Page<AbnormalOrderDTO>> getPage(AbnormalOrderParam param);

    Result<Page<AbnormalOrderDTO>> queryPage(AbnormalOrderParam param);

    /**
     * 存储异常单
     * @param abnormalOrderDTO
     */
    Result<Boolean> insert(AbnormalOrderDTO abnormalOrderDTO);

    Result<Boolean> modifyCancel(AbnormalOrderParam param);
    /**
     * 批量删除
     * @param param
     * @return
     */
    Result<Boolean> batchDelete(AbnormalOrderParam param);
}
