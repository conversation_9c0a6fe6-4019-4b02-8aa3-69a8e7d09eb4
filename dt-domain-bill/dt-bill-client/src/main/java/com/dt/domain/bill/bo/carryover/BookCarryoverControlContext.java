package com.dt.domain.bill.bo.carryover;

import lombok.Data;

import java.io.Serializable;

@Data
public class BookCarryoverControlContext implements Serializable {
    public static final String PROCESS_DONE = "DONE";
    public String moveDetailCreated = "";
    public String transferDetailCreated = "";
    public String shelfDetailCreated = "";
    public String splitShelfDetailCreated = "";
    public String prePackageShelfDetailCreated = "";
    public String outStockDetailCreated = "";
    public String homingDetailCreated = "";
    public String homingShelfDetailCreated = "";
    public String sameUserSameWarehouse = "";
    public String cwIn = "";
    public String cwTransfer = "";
    // 记录处理过最大的原始明细ID
    public Long maxProcessedOriginDetailId;
}
