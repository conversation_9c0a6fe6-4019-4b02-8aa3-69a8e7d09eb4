package com.dt.domain.bill.param.pre;

import com.dt.component.common.param.BaseSearchParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;


/**
 * <p>
 * 预包计划表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="PrePackagePlan对象", description="预包计划表")
public class PrePackagePlanParam extends BaseSearchParam  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    /**
     * 仓库编码
     */
    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    /**
     * 货主编码
     */
    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    /**
     * 预包计划号
     */
    @ApiModelProperty(value = "预包计划号")
    private String prePlanCode;
    private List<String> prePlanCodeList;
    /**
     * 预报条码
     */
    @ApiModelProperty(value = "预报条码")
    private String preUpcCode;
    private List<String> preUpcCodeList;
    /**
     * 业务类型(组包,预包)
     */
    @ApiModelProperty(value = "业务类型(组包,预包)")
    private String type;

    /**
     * 计划数量 默认:0
     */
    @ApiModelProperty(value = "计划数量 默认:0")
    private BigDecimal planQty;

    /**
     * 完成数量 默认:0
     */
    @ApiModelProperty(value = "完成数量 默认:0")
    private BigDecimal completeQty;

    /**
     * 包材条码
     */
    @ApiModelProperty(value = "包材条码")
    private String packMaterialUpcCode;

    /**
     * 预包计划批次
     */
    @ApiModelProperty(value = "预包计划批次")
    private String preLotNo;

    /**
     * 完成时间 (时间戳)
     */
    @ApiModelProperty(value = "完成时间 (时间戳)")
    private Long completeTime;
    private Long completeTimeEqStart;
    private Long completeTimeEqEnd;

    /**
     * 复核完成时间 (时间戳)
     */
    @ApiModelProperty(value = "复核完成时间 (时间戳)")
    private Long checkCompleteTime;

    /**
     * 审核结果说明
     */
    @ApiModelProperty(value = "审核结果说明")
    private String message;

    /**
     * 单据状态(创建、审核中、审核驳回、审核成功、待复核、上架中、完成、取消)
     */
    @ApiModelProperty(value = "单据状态(创建、审核中、审核驳回、审核成功、待复核、上架中、完成、取消)")
    private String status;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "修改人")
    private String updatedBy;

    @ApiModelProperty(value = "容器分配时间")
    private Long allocateTime;
}