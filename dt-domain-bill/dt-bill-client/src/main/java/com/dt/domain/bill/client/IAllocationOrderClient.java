package com.dt.domain.bill.client;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.domain.bill.dto.AllocationBillDTO;
import com.dt.domain.bill.dto.AllocationOrderDTO;
import com.dt.domain.bill.param.AllocationBillParam;
import com.dt.domain.bill.param.AllocationOrderParam;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/10/20 10:23
 */
public interface IAllocationOrderClient {

    /**
     * 新增交接单信息
     *
     * @param param
     * @return
     */
    Result<Boolean> save(AllocationOrderParam param);

    /**
     * 修改交接单信息
     * id | code | idList | codeList 四选一
     *
     * @param param
     * @return
     */
    Result<Boolean> modify(AllocationOrderParam param);

    /**
     * 根据条件查询是否存在
     *
     * @param param
     * @return
     */
    Result<Boolean> checkExits(AllocationOrderParam param);

    /**
     * 获取交接单信息
     *
     * @param param
     * @return
     */
    Result<AllocationOrderDTO> get(AllocationOrderParam param);

    /**
     * 获取交接单信息
     *
     * @param param
     * @return
     */
    Result<AllocationOrderDTO> getDetail(AllocationOrderParam param);

    /**
     * 获取交接单列表
     *
     * @param param
     * @return
     */
    Result<List<AllocationOrderDTO>> getList(AllocationOrderParam param);

    /**
     * 获取出库明细分配信息
     *
     * @param param
     * @return
     */
    Result<Page<AllocationOrderDTO>> getShipmentOrderPage(AllocationOrderParam param);

    Result<List<AllocationOrderDTO>> getShipmentOrderList(AllocationOrderParam param);

    /**
     * 分页获取交接单
     *
     * @param param
     * @return
     */
    Result<Page<AllocationOrderDTO>> getPage(AllocationOrderParam param);

    /**
     * 波次生成分配单
     *
     * @param allocationOrderDTOS
     * @return
     */
    Result<Boolean> submitSaveCollect(List<AllocationOrderDTO> allocationOrderDTOS);

    /**
     * 拣选区库存到拣选暂存位
     *
     * @param param
     * @return
     */
    Result<List<AllocationBillDTO>> getPackGroupByLocationAndSkuLotNo(AllocationBillParam param);

    /**
     * @param waveCode
     * @param packageCode
     * @param packUid
     * @return com.dt.component.common.result.Result<java.util.List < java.util.Map>>
     * @author: WuXian
     * description: 自定义sql查询分配记录(商品分组)
     * create time: 2021/12/8 13:38
     */
    Result<List<Map>> findAllocationOrderDetailList(String waveCode, String packageCode, String packUid);

    /**
     *
     * @param allocationOrderDTOList
     * @return
     */
    Result<Boolean> modifyBatch(List<AllocationOrderDTO> allocationOrderDTOList);

    Result<List<AllocationOrderDTO>> getAppointMultipleParam(AllocationOrderParam allocationOrderParam, List<String> convertToFieldNameList);
}
