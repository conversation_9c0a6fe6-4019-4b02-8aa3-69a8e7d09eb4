package com.dt.domain.bill.client;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.domain.bill.dto.PackageRandomCheckDTO;
import com.dt.domain.bill.param.PackageRandomCheckParam;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/10/14 19:44
 */
public interface IPackageRandomCheckClient {

    /**
     * 新增包裹复核抽检信息
     * @param param
     * @return
     */
    Result<Boolean> save(PackageRandomCheckParam param);

    /**
     * 修改包裹复核抽检信息
     * id | code | idList | codeList 四选一
     * @param param
     * @return
     */
    Result<Boolean> modify(PackageRandomCheckParam param);

    /**
     * 根据条件查询是否存在
     * @param param
     * @return
     */
    Result<Boolean> checkExits(PackageRandomCheckParam param);

    /**
     * 获取包裹复核抽检信息
     * @param param
     * @return
     */
    Result<PackageRandomCheckDTO> get(PackageRandomCheckParam param);

    /**
     * 获取包裹复核抽检信息
     * @param param
     * @return
     */
    Result<PackageRandomCheckDTO> getDetail(PackageRandomCheckParam param);

    /**
     * 获取包裹复核抽检列表
     * @param param
     * @return
     */
    Result<List<PackageRandomCheckDTO>> getList(PackageRandomCheckParam param);

    /**
     * 分页获取包裹复核抽检
     * @param param
     * @return
     */
    Result<Page<PackageRandomCheckDTO>> getPage(PackageRandomCheckParam param);
    
}
