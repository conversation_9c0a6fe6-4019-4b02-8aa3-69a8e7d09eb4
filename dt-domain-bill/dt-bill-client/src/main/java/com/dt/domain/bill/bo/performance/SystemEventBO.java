package com.dt.domain.bill.bo.performance;

import com.dt.component.common.param.BaseSearchParam;
import com.dt.domain.bill.dto.performance.SystemEventDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;



/**
 * <p>
 * 系统事件
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-02
 */
@Data
@ApiModel(value="SystemEvent对象", description="系统事件")
public class SystemEventBO extends BaseSearchParam  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "系统事件")
    private SystemEventDTO systemEventDTO;
}