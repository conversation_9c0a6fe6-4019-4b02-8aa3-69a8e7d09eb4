package com.dt.domain.bill.dto.receipt;

import com.dt.component.common.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import java.math.BigDecimal;

/**
* <p>
    * 收货凭据表
    * </p>
*
* <AUTHOR>
* @since 2022-02-18
*/
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ReceiptBillRecord对象", description="收货凭据表")
public class ReceiptBillRecordDTO extends BaseDTO  implements java.io.Serializable  {

private static final long serialVersionUID = 1L;

    /**
    * 仓库编码 取值仓库档案
    */
    @ApiModelProperty(value = "仓库编码 取值仓库档案")
    private String warehouseCode;

    /**
    * 收货单号
    */
    @ApiModelProperty(value = "收货单号")
    private String billNo;

    /**
    * 供应商
    */
    @ApiModelProperty(value = "供应商")
    private String supplyName;

    /**
    * 库区
    */
    @ApiModelProperty(value = "库区")
    private String zoneName;

    /**
    * 入库负责人
    */
    @ApiModelProperty(value = "入库负责人")
    private String recBy;

    /**
    * 入库时间
    */
    @ApiModelProperty(value = "入库时间")
    private Long recDate;

    /**
    * 检验人员
    */
    @ApiModelProperty(value = "检验人员")
    private String checkBy;

    /**
    * 检验人数
    */
    @ApiModelProperty(value = "检验人数")
    private Integer checkByNum;

    /**
    * 入库总数量
    */
    @ApiModelProperty(value = "入库总数量")
    private BigDecimal recSkuQty;

    /**
    * 产品名称
    */
    @ApiModelProperty(value = "产品名称")
    private String goodsName;

    /**
    * 产品编码
    */
    @ApiModelProperty(value = "产品编码")
    private String goodsCode;

    /**
    * 型号规格
    */
    @ApiModelProperty(value = "型号规格")
    private String unitName;

    /**
    * 单据类型
    */
    @ApiModelProperty(value = "单据类型")
    private String status;

    /**
    * 备注
    */
    @ApiModelProperty(value = "备注")
    private String remark;

@ApiModelProperty(value = "创建人")
private String createdBy;

@ApiModelProperty(value = "修改人")
private String updatedBy;
}