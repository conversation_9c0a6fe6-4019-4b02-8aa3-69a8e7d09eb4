package com.dt.domain.bill.client;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.domain.bill.dto.ReceiptDetailDTO;
import com.dt.domain.bill.param.ReceiptDetailBatchParam;
import com.dt.domain.bill.param.ReceiptDetailParam;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/10/14 19:41
 */
public interface IReceiptDetailClient {

    /**
     * 新增出库单明细信息
     * @param param
     * @return
     */
    Result<Boolean> save(ReceiptDetailParam param);

    /**
     * 新增，修改出库单
     * @param param
     * @return
     */
    Result<Boolean> saveOrUpdateBatch(ReceiptDetailBatchParam param);

    /**
     * 批量提交
     * @param param
     * @return
     */
    Result<Boolean> saveBatch(ReceiptDetailBatchParam param);


    /**
     * 修改出库单明细信息
     * id | idList 四选一
     * @param param
     * @return
     */
    Result<Boolean> modify(ReceiptDetailParam param);

    /**
     * 根据条件查询是否存在
     * @param param
     * @return
     */
    Result<Boolean> checkExits(ReceiptDetailParam param);

    /**
     * 获取出库单明细信息
     * @param param
     * @return
     */
    Result<ReceiptDetailDTO> get(ReceiptDetailParam param);

    /**
     * 获取出库单明细信息
     * @param param
     * @return
     */
    Result<ReceiptDetailDTO> getDetail(ReceiptDetailParam param);

    /**
     * 获取出库单明细列表
     * @param param
     * @return
     */
    Result<List<ReceiptDetailDTO>> getList(ReceiptDetailParam param);

    /**
     * 分页获取出库单明细
     * @param param
     * @return
     */
    Result<Page<ReceiptDetailDTO>> getPage(ReceiptDetailParam param);

    /**
     *
     * @param param
     * @return
     */
    Result<Boolean> modifyBatch(ReceiptDetailBatchParam param);

    /**
     *
     * @param receiptDetailDTOList
     * @return
     */
    Result<Boolean> modifyBatch(List<ReceiptDetailDTO> receiptDetailDTOList);
}
