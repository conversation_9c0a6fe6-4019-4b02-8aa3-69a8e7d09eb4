package com.dt.domain.bill.client;

import com.dt.component.common.result.Result;
import com.dt.domain.bill.dto.CollectWaveTaskDTO;
import com.dt.domain.bill.param.CollectWaveTaskParam;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/10/21 12:01
 */
public interface ICollectWaveTaskClient {

    /**
     * 新增波次任务信息
     * @param param
     * @return
     */
    Result<Boolean> save(CollectWaveTaskDTO param);

    /**
     * 修改波次任务信息
     * id | code | idList | codeList 四选一
     * @param param
     * @return
     */
    Result<Boolean> modify(CollectWaveTaskParam param);


    /**
     * 获取波次任务信息
     * @param param
     * @return
     */
    Result<CollectWaveTaskDTO> get(CollectWaveTaskParam param);

    /**
     * 获取波次任务列表
     * @param param
     * @return
     */
    Result<List<CollectWaveTaskDTO>> getList(CollectWaveTaskParam param);

    /**
     * 波次任务提交
     * @param collectWaveTaskDTO
     * @return
     */
    Result<Boolean> submitWaveTask(CollectWaveTaskDTO collectWaveTaskDTO);

    /**
     * 获取波次任务
     * @param param
     * @return
     */
    Result<List<CollectWaveTaskDTO>> getWaveTask(CollectWaveTaskParam param);

    /**
     * 修改波次任务
     * @param collectWaveTaskDTO
     * @return
     */
    Result<Boolean> modifyWaveTask(CollectWaveTaskDTO collectWaveTaskDTO);

    /**
     * 修改状态码和数量
     * @param collectWaveTaskDTO
     * @return
     */
    Result<Boolean> updateStatusAndNum(CollectWaveTaskDTO collectWaveTaskDTO);

    Result<Integer> count(CollectWaveTaskParam collectCountParam);

    Result<List<CollectWaveTaskDTO>> getListAppointMultipleParam(CollectWaveTaskParam collectLockTaskParam, List<String> convertToFieldNameList);

    Result<Boolean> partialPhysicalDeleteById(List<Long> idList);
}
