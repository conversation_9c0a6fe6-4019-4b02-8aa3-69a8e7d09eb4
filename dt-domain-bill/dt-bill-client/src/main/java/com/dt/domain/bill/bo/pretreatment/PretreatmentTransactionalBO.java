package com.dt.domain.bill.bo.pretreatment;


import com.dt.domain.bill.dto.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 功能描述:  预处理环节BO对象 下沉到底层做事务的提交
 * 创建时间:  2021/9/13 9:23 上午
 *
 * <AUTHOR>
 */
@Data
public class PretreatmentTransactionalBO  implements java.io.Serializable  {

    @ApiModelProperty("出库单")
    private ShipmentOrderDTO shipmentOrderDTO;
    @ApiModelProperty("异常单")
    private AbnormalOrderDTO abnormalOrderDTO;
    @ApiModelProperty("异常单")
    private List<AbnormalOrderDTO> abnormalOrderDTOList;
    @ApiModelProperty("包裹")
    private List<PackageDTO> packageDTOList;
    @ApiModelProperty("包裹明细")
    private List<PackageDetailDTO> packageDetailDTOList;
    @ApiModelProperty("包裹日志")
    private List<PackageLogDTO> packageLogDTOList;

}