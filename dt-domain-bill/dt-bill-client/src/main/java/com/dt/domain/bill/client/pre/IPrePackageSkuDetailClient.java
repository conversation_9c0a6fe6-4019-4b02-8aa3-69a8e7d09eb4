package com.dt.domain.bill.client.pre;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.domain.bill.dto.pre.PrePackageSkuDetailDTO;
import com.dt.domain.bill.param.pre.PrePackageSkuDetailParam;

import java.util.List;

/**
 * <p>
 * 预包条码 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-25
 */
public interface IPrePackageSkuDetailClient {

    /**
     * 新增预包条码
     *
     * @param prePackageSkuDetailDTO
     * @return
     */
    Result<Boolean> save(PrePackageSkuDetailDTO prePackageSkuDetailDTO);

    /**
     * 批量新增预包条码
     *
     * @param prePackageSkuDetailDTOList
     * @return
     */
    Result<Boolean> saveBatch(List<PrePackageSkuDetailDTO> prePackageSkuDetailDTOList);

    /**
     * 修改预包条码
     *
     * ID | Code 二选一
     * @param prePackageSkuDetailDTO
     * @return
     */
    Result<Boolean> modify(PrePackageSkuDetailDTO prePackageSkuDetailDTO);

    /**
     * 批量修改预包条码
     *
     * @param prePackageSkuDetailDTOList
     * @return
     */
    Result<Boolean> modifyBatch(List<PrePackageSkuDetailDTO> prePackageSkuDetailDTOList);

    /**
     * 根据条件查询是否存在
     *
     * @param param
     * @return
     */
    Result<Boolean> checkExits(PrePackageSkuDetailParam param);

    /**
     * 获取预包条码
     *
     * @param param
     * @return
     */
    Result<PrePackageSkuDetailDTO> get(PrePackageSkuDetailParam param);

    /**
     * 获取预包条码列表
     * @param param
     * @return
     */
    Result<List<PrePackageSkuDetailDTO>> getList(PrePackageSkuDetailParam param);

    /**
     * 分页获取预包条码
     *
     * @param param
     * @return
     */
    Result<Page<PrePackageSkuDetailDTO>> getPage(PrePackageSkuDetailParam param);

    /**
     * 功能描述:  删除
     * 创建时间:  2021/1/8 11:25 上午
     *
     * @param param:
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     */
    Result<Boolean> remove(PrePackageSkuDetailParam param);

}

