package com.dt.domain.bill.client;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.domain.bill.dto.TransferDetailDTO;
import com.dt.domain.bill.param.TransferDetailBatchParam;
import com.dt.domain.bill.param.TransferDetailParam;

import java.util.List;

/**
 * 转移单
 * Created by nobody on 2020/12/28 17:55
 */
public interface ITransferDetailClient {

    /**
     * 新增转移单明细
     * @param transferDetailBatchParam
     * @return
     */
    Result<Boolean> batchAdd(TransferDetailBatchParam transferDetailBatchParam);

    /**
     * 更新转移单明细
     * @param transferDetailBatchParam
     * @return
     */
    Result<Boolean> batchUpdate(TransferDetailBatchParam transferDetailBatchParam);

    /**
     * 删除历史转移单明细
     * @param transferDetailParam
     * @return
     */
    Result<Boolean> del(TransferDetailParam transferDetailParam);

    /**
     * 列表
     * @param transferDetailParam
     * @return
     */
    Result<List<TransferDetailDTO>> list(TransferDetailParam transferDetailParam);

    /**
     * 分页
     * @param transferDetailParam
     * @return
     */
    Result<Page<TransferDetailDTO>> page(TransferDetailParam transferDetailParam);
}
