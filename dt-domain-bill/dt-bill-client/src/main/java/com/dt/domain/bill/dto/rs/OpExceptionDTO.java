package com.dt.domain.bill.dto.rs;

import com.danding.encrypt.annotation.DecryptField;
import com.danding.encrypt.annotation.DesensitizeField;
import com.danding.encrypt.annotation.EncryptField;
import com.danding.encrypt.constant.RegexConstant;
import com.dt.component.common.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;

/**
 * <p>
 * 淘天用异常单
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "OpException对象", description = "淘天用异常单")
public class OpExceptionDTO extends BaseDTO  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    /**
     * 货主编码
     */
    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    /**
     * 异常类型
     */
    @ApiModelProperty(value = "异常类型")
    private String abnormalType;

    /**
     * 异常单号
     */
    @ApiModelProperty(value = "异常单号")
    private String abnormalOrderNo;

    /**
     * 场景
     */
    @ApiModelProperty(value = "场景")
    private String scenarioType;

    /**
     * 对应业务单号
     */
    @ApiModelProperty(value = "对应业务单号")
    private String orderCode;

    /**
     * 异常单据的状态
     */
    @ApiModelProperty(value = "异常单据的状态")
    private String status;

    /**
     * 运单号
     */
    @ApiModelProperty(value = "运单号")
    private String mailMo;

    /**
     * 物流公司名称
     */
    @ApiModelProperty(value = "物流公司名称")
    private String logisticsName;
    @ApiModelProperty(value = "物流公司编码")
    private String logisticsCode;

    /**
     * 寄件人信息
     */
    @ApiModelProperty(value = "寄件人信息")
    private String senderInfo;

    /**
     * 寄件人电话(用于搜索）
     */
    @ApiModelProperty(value = "寄件人电话(用于搜索）")
    //@DecryptField
   // @DesensitizeField(regex = RegexConstant.NAME, replace = RegexConstant.NAME_REPLACE)
    @EncryptField
    @DecryptField
    private String senderName;

    /**
     * 寄件人电话(用于搜索)
     */
    @ApiModelProperty(value = "寄件人电话(用于搜索)")
    //@DecryptField
    //@DesensitizeField(regex = RegexConstant.PHONE, replace = RegexConstant.PHONE_REPLACE)
    @EncryptField
    @DecryptField
    private String senderPhone;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    private String extendProps;

    /**
     * 匹配指令类型
     */
    @ApiModelProperty(value = "匹配指令类型")
    private String instructionType;

    @ApiModelProperty(value = "最后一次收到指令的实际")
    private Long lastInstructionTime;
    @ApiModelProperty(value = "异常处理回告状态")
    private Integer ctbHandledState;
    @ApiModelProperty(value = "异常提报回告状态")
    private Integer ctbRegisterState;
    /**
     * 收集人信息
     */
    @ApiModelProperty(value = "收集人信息")
    @EncryptField
    @DecryptField
    private String receiverInfo;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "修改人")
    private String updatedBy;


    @ApiModelProperty(value = "仓库")
    private String warehouseCode;

    /**
     * 销售平台来源
     */
    private String saleSource;

    @ApiModelProperty(value = "退货类型")
    private Integer returnType;


    private String senderPovince;
    private String senderCity;
    private String senderArea;
    private String senderTown;
    //@DecryptField
   // @DesensitizeField(regex = RegexConstant.ADDRESS, replace = RegexConstant.ADDRESS_REPLACE)
    @EncryptField
    @DecryptField
    private String senderDetailAddress;
    private String orgReturnNo;
    private String extraJson;
    /**
     * 推货理由
     */
    private String returnReason;

    public String getOneOrderCode() {
        String orderCode = this.orderCode;
        if(StringUtils.isEmpty(orderCode)){
            return orderCode;
        }else{
            String[] orderCodeStr = orderCode.split(",");
            return orderCodeStr[0];
        }
    }

    public String addOrderCode(String oldOrderCode, String targetSalesReturnOrderNo) {
        if(StringUtils.isEmpty(oldOrderCode)){
            return targetSalesReturnOrderNo;
        }else {
            if(oldOrderCode.contains(targetSalesReturnOrderNo)){
                // 已包含了就不再追加
                return oldOrderCode;
            }else{
                return oldOrderCode + "," + targetSalesReturnOrderNo;
            }
        }
    }
}