package com.dt.domain.bill.client;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.domain.bill.dto.PackageCheckDTO;
import com.dt.domain.bill.dto.PackageCheckDetailDTO;
import com.dt.domain.bill.param.PackageCheckParam;


import javax.sound.midi.SoundbankResource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/10/14 19:43
 */
public interface IPackageCheckClient {

    /**
     * 新增包裹复核信息
     * @param param
     * @return
     */
    Result<Boolean> save(PackageCheckParam param);

    /**
     * 修改包裹复核信息
     * id | code | idList | codeList 四选一
     * @param param
     * @return
     */
    Result<Boolean> modify(PackageCheckParam param);

    /**
     * 根据条件查询是否存在
     * @param param
     * @return
     */
    Result<Boolean> checkExits(PackageCheckParam param);

    /**
     * 获取包裹复核信息
     * @param param
     * @return
     */
    Result<PackageCheckDTO> get(PackageCheckParam param);

    /**
     * 获取包裹复核信息
     * @param param
     * @return
     */
    Result<PackageCheckDTO> getDetail(PackageCheckParam param);

    /**
     * 获取包裹复核列表
     * @param param
     * @return
     */
    Result<List<PackageCheckDTO>> getList(PackageCheckParam param);

    /**
     * 分页获取包裹复核
     * @param param
     * @return
     */
    Result<Page<PackageCheckDTO>> getPage(PackageCheckParam param);

    /**
     * 获取明细
     * @param param
     * @return
     */
    Result<List<PackageCheckDetailDTO>> getDetailList(PackageCheckParam param);

    /**
     * 移除明细ID
     * @param packageCheckDetailDTO
     * @return
     */
    Result<Boolean> removePackDetailById(PackageCheckDetailDTO packageCheckDetailDTO);

    /**
     * 修改明细
     * @param packageCheckDetailDTO
     * @return
     */
    Result<Boolean> modifyPackDetailById(PackageCheckDetailDTO packageCheckDetailDTO);
    /**
     * @param packageCheckParam
     * @param filedList
     * @return java.util.List<com.dt.domain.bill.dto.PackageCheckDTO>
     * @author: WuXian
     * description: 复核记录查询指定字段返回
     * create time: 2021/10/22 15:35
     */
    Result<List<PackageCheckDTO>> getPackageCheckListAppointColumn(PackageCheckParam packageCheckParam, List<String> filedList);

    /**
     *
     * @param packageCheckParam
     * @return
     */
    Result<Integer> getExportCountNum(PackageCheckParam packageCheckParam);
}
