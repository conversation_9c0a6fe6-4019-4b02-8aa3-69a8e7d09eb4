package com.dt.domain.bill.client;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.domain.bill.dto.ShipmentOrderDetailDTO;
import com.dt.domain.bill.param.ShipmentOrderDetailBatchParam;
import com.dt.domain.bill.param.ShipmentOrderDetailParam;
import com.dt.domain.bill.param.ShipmentOrderParam;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/10/14 19:41
 */
public interface IShipmentOrderDetailClient {

    /**
     * 新增出库单明细信息
     * @param param
     * @return
     */
    Result<Boolean> save(ShipmentOrderDetailParam param);

    /**
     *
     * @param param
     * @return
     */
    Result<Boolean>  batchModify(ShipmentOrderDetailBatchParam param);

    /**
     * 修改出库单明细信息
     * id | idList 四选一
     * @param param
     * @return
     */
    Result<Boolean> modify(ShipmentOrderDetailParam param);

    /**
     * 根据条件查询是否存在
     * @param param
     * @return
     */
    Result<Boolean> checkExits(ShipmentOrderDetailParam param);

    /**
     * 获取出库单明细信息
     * @param param
     * @return
     */
    Result<ShipmentOrderDetailDTO> get(ShipmentOrderDetailParam param);

    /**
     * 获取出库单明细信息
     * @param param
     * @return
     */
    Result<ShipmentOrderDetailDTO> getDetail(ShipmentOrderDetailParam param);

    /**
     * 获取出库单明细列表
     * @param param
     * @return
     */
    Result<List<ShipmentOrderDetailDTO>> getList(ShipmentOrderDetailParam param);

    Result<List<ShipmentOrderDetailDTO>> getListExport(ShipmentOrderParam param) ;

    Result<Boolean> doSplitDetail(List<ShipmentOrderDetailDTO> shipmentOrderDetailDTOList);
    /**
     * 分页获取出库单明细
     * @param param
     * @return
     */
    Result<Page<ShipmentOrderDetailDTO>> getPage(ShipmentOrderDetailParam param);

    /**
     * 批量修改
     * @param shipmentOrderDetailDTOList
     * @return
     */
    Result<Boolean> modifyBatchDetail(List<ShipmentOrderDetailDTO> shipmentOrderDetailDTOList);

    /**
     * 提交数据
     * @param shipmentOrderDetailDTOList
     * @param deleteShipmentOrderDetailDTOList
     * @return
     */
    Result<Boolean> splitSaveBatchDetail(List<ShipmentOrderDetailDTO> shipmentOrderDetailDTOList,List<ShipmentOrderDetailDTO> deleteShipmentOrderDetailDTOList);

    Result<List<ShipmentOrderDetailDTO>> getAppointMultipleParam(ShipmentOrderDetailParam shipmentOrderDetailParam, List<String> convertToFieldNameList);
}
