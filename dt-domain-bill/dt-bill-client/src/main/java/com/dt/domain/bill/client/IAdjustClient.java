package com.dt.domain.bill.client;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.domain.bill.bo.AdjustBO;
import com.dt.domain.bill.dto.AdjustDTO;
import com.dt.domain.bill.param.AdjustParam;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
 * <p>
 * 移位单管理
 * </p>
 *
 * <AUTHOR> xy
 * @since 2020-09-11
 */
public interface IAdjustClient {
    
    @ApiOperation("完成调整单")
    Result<Boolean> adjustComplete(AdjustBO adjustBO);

    /**
     * 新增移位单信息
     * @param param
     * @return
     */
    Result<Boolean> save(AdjustParam param);

    /**
     * 修改移位单信息
     * id | code | idList | codeList 四选一
     * @param param
     * @return
     */
    Result<Boolean> modify(AdjustParam param);

    /**
     * 根据条件查询是否存在
     * @param param
     * @return
     */
    Result<Boolean> checkExits(AdjustParam param);

    /**
     * 获取移位单信息
     * @param param
     * @return
     */
    Result<AdjustDTO> get(AdjustParam param);

    /**
     * 获取移位单信息
     * @param param
     * @return
     */
    Result<AdjustDTO> getDetail(AdjustParam param);

    /**
     * 获取移位单列表
     * @param param
     * @return
     */
    Result<List<AdjustDTO>> getList(AdjustParam param);

    /**
     * 分页获取移位单
     * @param param
     * @return
     */
    Result<Page<AdjustDTO>> getPage(AdjustParam param);

}
