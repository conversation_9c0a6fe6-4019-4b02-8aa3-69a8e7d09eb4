package com.dt.domain.bill.dto.rs;

import com.dt.component.common.dto.BaseDTO;
import com.dt.domain.bill.dto.ShelfDTO;
import com.dt.domain.bill.dto.ShelfDetailDTO;
import com.dt.domain.bill.dto.message.MessageMqDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * 销退质检单
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "SalesReturnInspect对象", description = "销退质检单")
public class SalesReturnInspectDTO extends BaseDTO  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    /**
     * 容器编码
     */
    @ApiModelProperty(value = "容器编码")
    private String contCode;

    /**
     * 销退质检单号
     */
    @ApiModelProperty(value = "销退质检单号")
    private String inspectOrderNo;

    /**
     * 实体仓编码
     */
    @ApiModelProperty(value = "实体仓编码")
    private String realWarehouseCode;

    /**
     * 实体仓名称
     */
    @ApiModelProperty(value = "实体仓名称")
    private String realWarehouseName;

    /**
     * 保税类型
     */
    @ApiModelProperty(value = "保税类型")
    private String taxType;

    /**
     * 质检状态
     */
    @ApiModelProperty(value = "质检状态")
    private Integer status;

    /**
     * 质检结果
     */
    @ApiModelProperty(value = "质检结果")
    private Integer inspectionResult;

    /**
     * 异常原因
     */
    @ApiModelProperty(value = "异常原因")
    private String abnormalCause;

    /**
     * 包裹数量
     */
    @ApiModelProperty(value = "包裹数量")
    private Integer packageCount;

    /**
     * 上架数量
     */
    @ApiModelProperty(value = "上架数量")
    private Integer shelfCount;

    /**
     * 出库数量
     */
    @ApiModelProperty(value = "出库数量")
    private Integer outCount;

    /**
     * 完成时间
     */
    @ApiModelProperty(value = "完成时间")
    private Long completeTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private Long overTime;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "修改人")
    private String updatedBy;

    @ApiModelProperty(value = "仓库")
    private String warehouseCode;

    SalesReturnInspectDetailDTO salesReturnInspectDetailDTO;

    SalesReturnOrderDTO salesReturnOrderDTO;

    List<SalesReturnOrderReceiveDTO> salesReturnOrderReceiveDTOList;

    List<SalesReturnOrderDetailDTO> salesReturnOrderDetailDTOList;

    List<MessageMqDTO> messageMqDTOList;

    private List<ShelfDTO> shelfDTOList;
    private List<ShelfDetailDTO> shelfDetailDTOList;
}