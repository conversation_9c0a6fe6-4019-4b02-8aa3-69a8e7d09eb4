package com.dt.domain.bill.client;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.domain.bill.dto.AsnDetailDTO;
import com.dt.domain.bill.param.AsnDetailBatchParam;
import com.dt.domain.bill.param.AsnDetailParam;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/9/21 15:00
 */
public interface IAsnDetailBillClient {

    /**
     * 新增信息
     * @param param
     * @return
     */
    Result<Boolean> save(AsnDetailParam param);

    /**
     * 新增，修改
     * @param param
     * @return
     */
    Result<Boolean> saveOrUpdateBatch(AsnDetailBatchParam param);

    /**
     * 批量提交
     * @param param
     * @return
     */
    Result<Boolean> saveBatch(AsnDetailBatchParam param);


    /**
     * 修改信息
     * id | idList 四选一
     * @param param
     * @return
     */
    Result<Boolean> modify(AsnDetailParam param);

    /**
     * 根据条件查询是否存在
     * @param param
     * @return
     */
    Result<Boolean> checkExits(AsnDetailParam param);

    /**
     * 明细信息
     * @param param
     * @return
     */
    Result<AsnDetailDTO> get(AsnDetailParam param);

    /**
     * 明细信息
     * @param param
     * @return
     */
    Result<AsnDetailDTO> getDetail(AsnDetailParam param);

    /**
     * 明细列表
     * @param param
     * @return
     */
    Result<List<AsnDetailDTO>> getList(AsnDetailParam param);


    /**
     * 分页明细
     * @param param
     * @return
     */
    Result<Page<AsnDetailDTO>> getPage(AsnDetailParam param);

    /**
     *
     * @param param
     * @return
     */
    Result<Boolean> modifyBatch(AsnDetailBatchParam param);

    Result<Boolean> modifyBatch(List<AsnDetailDTO> asnDetailDTOList);
}
