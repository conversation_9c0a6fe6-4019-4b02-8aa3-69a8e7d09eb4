package com.dt.domain.bill.param.collect;

import com.dt.component.common.param.BaseSearchParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * 任务锁表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "CollectLockTask对象", description = "任务锁表")
public class CollectLockTaskParam extends BaseSearchParam  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    /**
     * 仓库编码
     */
    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    /**
     * 货主编码
     */
    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    /**
     * 单据号
     */
    @ApiModelProperty(value = "单据号")
    private String billNo;
    private List<String> billNoList;

    /**
     * 单据类型
     */
    @ApiModelProperty(value = "单据类型")
    private String operateType;

    /**
     * 状态码 10 拣选单任务创建,20 库存完成,30 拣选单 失败(通知取消库存),40 完成
     */
    @ApiModelProperty(value = "状态码 10 拣选单任务创建,20 库存完成,30 拣选单 失败(通知取消库存),40 完成")
    private String status;
    private List<String> statusList;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "修改人")
    private String updatedBy;

    @ApiModelProperty(value = "查询限制条数")
    private Integer limitNum;
}