package com.dt.domain.bill.param;

import com.dt.component.common.param.BaseSearchParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2020/10/14 20:19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "对象", description = "")
public class PickCheckParam extends BaseSearchParam  implements java.io.Serializable  {

    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    @ApiModelProperty(value = "拣选单号")
    private String pickCode;

    @ApiModelProperty(value = "拣选单复合单号")
    private String pickCheckCode;

    @ApiModelProperty(value = "包裹单号")
    private String packageCode;

    @ApiModelProperty(value = "序号")
    private Integer lineSeq;

    @ApiModelProperty(value = "运单号")
    private String expressNo;

    @ApiModelProperty(value = "出库单号")
    private String shipmentOrderCode;

    @ApiModelProperty(value = "C端单号-(客户原始单号)")
    private String poNo;

    @ApiModelProperty(value = "上游单号--进销存(现有ERP单号,目前前端显示客户单号)")
    private String soNo;

    @ApiModelProperty(value = "商品编码")
    private String skuCode;

    @ApiModelProperty(value = "商品条形码")
    private String upcCode;

    @ApiModelProperty(value = "商品质量属性")
    private String skuQuality;

    @ApiModelProperty(value = "扫描数量")
    private BigDecimal qty;

    @ApiModelProperty(value = "质检台号")
    private String benchCode;

    @ApiModelProperty(value = "复核人")
    private String checkBy;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "修改人")
    private String updatedBy;




}