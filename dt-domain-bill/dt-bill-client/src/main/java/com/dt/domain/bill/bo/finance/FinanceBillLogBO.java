package com.dt.domain.bill.bo.finance;

import com.dt.component.common.param.BaseSearchParam;
import com.dt.domain.bill.dto.finance.FinanceBillLogDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.math.BigDecimal;


/**
* <p>
    * 金融单据日志表表
    * </p>
*
* <AUTHOR>
* @since 2022-05-13
*/
@Data
@ApiModel(value="FinanceBillLog对象", description="金融单据日志表表")
public class FinanceBillLogBO extends BaseSearchParam  implements java.io.Serializable  {

private static final long serialVersionUID = 1L;

@ApiModelProperty(value = "金融单据日志表表")
private FinanceBillLogDTO financeBillLogDTO;
}