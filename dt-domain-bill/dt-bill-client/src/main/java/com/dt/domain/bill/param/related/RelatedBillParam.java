package com.dt.domain.bill.param.related;

import com.dt.component.common.param.BaseSearchParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * 单据关联单号
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "RelatedBill对象", description = "单据关联单号")
public class RelatedBillParam extends BaseSearchParam  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    /**
     * 货主编码
     */
    @ApiModelProperty(value = "货主编码")
    private String cargoCode;
    private List<String> cargoCodeList;

    /**
     * 单据号
     */
    @ApiModelProperty(value = "单据号")
    private String billNo;
    private List<String> billNoList;

    /**
     * 关联单号
     */
    @ApiModelProperty(value = "关联单号")
    private String relatedNo;
    private List<String> relatedNoList;

    /**
     * 类型枚举
     */
    @ApiModelProperty(value = "类型枚举")
    private String type;
    private List<String> typeList;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "修改人")
    private String updatedBy;
}