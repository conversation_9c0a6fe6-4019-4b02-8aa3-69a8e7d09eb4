package com.dt.domain.bill.client;

import com.dt.component.common.result.Result;
import com.dt.domain.bill.bo.*;


/**
 * <AUTHOR>
 * @date 2020/10/26 18:13
 */
public interface IBillContextClient {

    /**
     * 拣选单领取
     *
     * @param pickReceiveBO
     * @return
     */
    Result<Boolean> confirmPickBillReceive(PickReceiveBO pickReceiveBO);

    /**
     * 包裹复核-后置提交
     *
     * @param packCheckBackBO
     * @return
     */
    Result<Boolean> submitPackCheckBackBO(PackCheckBackBO packCheckBackBO);

    /**
     * b2b包裹复核-后置提交
     *
     * @param b2bPackCheckBO
     * @return
     */
    Result<Boolean> submitB2bPackCheckBackBO(B2bPackCheckBO b2bPackCheckBO);

    /**
     * 包裹单拦截
     *
     * @param interceptionPackBO
     * @return
     */
    Result<Boolean> submitPackIntercept(InterceptionPackBO interceptionPackBO);

    /**
     * 完成运单号-提交数据
     *
     * @param homingCompletePackBO
     */
    Result<Boolean> submitReturnOrderAndPack(HomingCompletePackBO homingCompletePackBO);

    /**
     * 拆包提交
     *
     * @param commitPackBO
     * @return
     */
    Result<Boolean> commitPackBO(CommitPackBO commitPackBO);

    /**
     * @param abnormalRelieveBO
     * @return
     */
    Result<Boolean> relieveAbnormal(AbnormalRelieveBO abnormalRelieveBO);

    /**
     * @param batchCheckBO
     * @return
     */
    Result<Boolean> submitBatchPack(BatchCheckBO batchCheckBO);

    /**
     * @param abnormalRelieveBO
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * @author: WuXian
     * description:  异常单-解除所有汇单异常
     * create time: 2021/12/2 9:16
     */
    Result<Boolean> relieveAbnormalAll(AbnormalRelieveBOAll abnormalRelieveBO);

    /**
     * @param weighCommitBillBO
     * @return java.lang.Boolean
     * @author: WuXian
     * description:  称重出库
     * create time: 2021/12/2 9:17
     */
    Result<Boolean> handoverWeightCommit(WeighCommitBillBO weighCommitBillBO);

    /**
     * @param batchOutBoundBillBO
     * @return java.lang.Boolean
     * @author: WuXian
     * description:  秒杀批量出库
     * create time: 2021/9/15 9:00
     */
    Result<Boolean> handoverBatchOutBound(BatchOutBoundBillBO batchOutBoundBillBO);

    /**
     * @param oneClickOutBoundBillBO
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * @author: WuXian
     * description:  一键出库
     * create time: 2021/12/2 10:44
     */
    Result<Boolean> oneClickOutBound(OneClickOutBoundBillBO oneClickOutBoundBillBO);

    /**
     * @param waveCollectCommitBO
     * @return java.lang.Boolean
     * @author: WuXian
     * description: 波次汇单提交数据
     * create time: 2021/12/23 14:04
     */
    Result<Boolean> waveCollectCommit(WaveCollectCommitBO waveCollectCommitBO);

    /**
     * @param pickRestoreCommitBO
     * @return java.lang.Boolean
     * @author: WuXian
     * description:
     * create time: 2021/12/30 10:08
     */
    Result<Boolean> confirmPickBillRestore(PickRestoreCommitBO pickRestoreCommitBO);

    void confirmPickSplitPart(PickSplitPartCommitBO pickSplitPartCommitBO);

    /**
     * @param mergePickBO
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     * @describe:
     * @date 2022/8/11 16:30
     */
    Result<Boolean> cancelMergePick(CancelMergePickBO mergePickBO);

    /**
     * @param mergePickBO
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     * @describe:
     * @date 2022/8/11 16:30
     */
    Result<Boolean> mergePick(MergePickBO mergePickBO);
    /**
     * @param cancelAllocationBO
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     * @describe:
     * @date 2023/6/20 17:04
     */
    Result<Boolean> cancelAllocation(CancelAllocationBillBO cancelAllocationBO);
    /**
     * @param pickCancelBillBO
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     * @describe:
     * @date 2023/6/20 17:20
     */
    Result<Boolean> cancelPicking(PickCancelBillBO pickCancelBillBO);
    /**
     * @param pickRestorePartCommitBO
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     * @describe: 部分拣货
     * @date 2024/11/15 13:26
     */
    Result<Boolean> confirmPickBillRestoreByPart(PickRestorePartCommitBO pickRestorePartCommitBO);
    /**
     * @param zeroOutStockBO
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     * @describe: 淘天零出库
     * @date 2024/11/27 15:35
     */
    Result<Boolean> zeroOutStock(ZeroOutStockBO zeroOutStockBO);
    /**
     * @param fixMergeOutShipBO
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     * @describe:
     * @date 2025/1/15 9:33
     */
    Result<Boolean> fixMergeShipOut(FixMergeOutShipBO fixMergeOutShipBO);
}
