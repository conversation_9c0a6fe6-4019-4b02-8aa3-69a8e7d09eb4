package com.dt.domain.bill.client;

import com.dt.component.common.result.Result;
import com.dt.domain.bill.dto.ShelfDetailDTO;
import com.dt.domain.bill.param.ShelfBatchParam;
import com.dt.domain.bill.param.ShelfDetailBatchParam;

import java.util.List;

public interface IShelfDetailClient {

    /**
     * modify batch
     * @param param
     * @return
     */
    Result<Boolean> modifyBatch(ShelfDetailBatchParam param);

    Result<Boolean> modifyBatch(List<ShelfDetailDTO> shelfDetailDTOList);
}
