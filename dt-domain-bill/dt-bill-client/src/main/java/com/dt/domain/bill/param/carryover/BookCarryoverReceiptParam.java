package com.dt.domain.bill.param.carryover;

import com.dt.component.common.param.BaseSearchParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import java.math.BigDecimal;
import java.util.List;

/**
* <p>
    * 账册结转单海关回执
    * </p>
*
* <AUTHOR>
* @since 2023-04-19
*/
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="BookCarryoverReceipt对象", description="账册结转单海关回执")
public class BookCarryoverReceiptParam extends BaseSearchParam  implements java.io.Serializable  {

private static final long serialVersionUID = 1L;

    /**
    * 仓库编码
    */
    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    /**
    * 结转单号
    */
    @ApiModelProperty(value = "结转单号")
    private String carryoverCode;
    private List<String> carryoverCodeList;

    /**
    * 清关单号
    */
    @ApiModelProperty(value = "清关单号")
    private String inventoryOrderSn;

    /**
    * 核注单号
    */
    @ApiModelProperty(value = "核注单号")
    private String endorsementSn;

    /**
    * 核注类型
    */
    @ApiModelProperty(value = "核注类型")
    private String endorsementType;

    /**
    * 核注状态
    */
    @ApiModelProperty(value = "核注状态")
    private String endorsementStatus;

@ApiModelProperty(value = "创建人")
private String createdBy;

@ApiModelProperty(value = "修改人")
private String updatedBy;
}