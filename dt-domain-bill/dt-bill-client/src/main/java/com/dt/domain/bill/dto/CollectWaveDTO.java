package com.dt.domain.bill.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2020/10/16 16:08
 */
@Data
public class CollectWaveDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    @ApiModelProperty(value = "快递编码")
    private String carrierCode;

    @ApiModelProperty(value = "平台")
    private String salePlatform;

    @ApiModelProperty(value = "店铺编码")
    private String saleShopId;

    @ApiModelProperty(value = "正次品")
    private String skuQuality;

    @ApiModelProperty(value = "平台")
    private String businessType;

    @ApiModelProperty(value = "结构")
    private String packageStruct;

    @ApiModelProperty(value = "分析sku")
    private String analysisSku;

    @ApiModelProperty(value = "秒杀分析")
    private String spikeAnalysisSku;

    @ApiModelProperty(value = "快递网点编码")
    private String expressBranch;

    @ApiModelProperty(value = "快递网点编码名称")
    private String expressBranchName;

    @ApiModelProperty(value = "数量")
    private Long num;

    @ApiModelProperty(value = "截取数量")
    private Long limitNum;
}
