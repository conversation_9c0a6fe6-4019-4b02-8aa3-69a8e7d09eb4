package com.dt.domain.bill.param.tally;

import com.dt.component.common.param.BaseSearchParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import java.math.BigDecimal;
import java.util.List;

/**
* <p>
    * 理货效期码
    * </p>
*
* <AUTHOR>
* @since 2023-03-31
*/
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="TallyValidity对象", description="理货效期码")
public class TallyValidityParam extends BaseSearchParam  implements java.io.Serializable  {

private static final long serialVersionUID = 1L;

    /**
    * 仓库编码 取值仓库档案
    */
    @ApiModelProperty(value = "仓库编码 取值仓库档案")
    private String warehouseCode;
    /**
     * 货主编码 取值货主档案
     */
    @ApiModelProperty(value = "货主编码 取值货主档案")
    private String cargoCode;
    private List<String> cargoCodeList;

    /**
     * 单据号
     */
    @ApiModelProperty(value = "单据号")
    private String billNo;
    private List<String> billNoList;

    /**
     * 商品代码
     */
    @ApiModelProperty(value = "商品代码")
    private String skuCode;
    private List<String> skuCodeList;

    private String upcCode;
    private List<String> upcCodeList;

    /**
     * 效期码
     */
    @ApiModelProperty(value = "效期码")
    private String validityCode;
    private List<String> validityCodeList;

    /**
     * 生产日期
     */
//    @ApiModelProperty(value = "生产日期")
//    private Long manufDate;

    /**
     * 失效日期
     */
    @ApiModelProperty(value = "失效日期")
    private Long expireDate;
    private Long expireDateStart;
    private Long expireDateEnd;

    /**
     * 状态码
     */
    @ApiModelProperty(value = "状态码")
    private String status;
    private List<String> statusList;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "修改人")
    private String updatedBy;
}