package com.dt.domain.bill.client.material;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.domain.bill.dto.material.StructMaterialRecordDTO;
import com.dt.domain.bill.param.material.StructMaterialRecordParam;

import java.util.List;

/**
 * <p>
 * 包裹结构使用包材记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-22
 */
public interface IStructMaterialRecordClient {

    /**
     * 新增包裹结构使用包材记录
     *
     * @param structMaterialRecordDTO
     * @return
     */
    Result<Boolean> save(StructMaterialRecordDTO structMaterialRecordDTO);

    /**
     * 批量新增包裹结构使用包材记录
     *
     * @param structMaterialRecordDTOList
     * @return
     */
    Result<Boolean> saveBatch(List<StructMaterialRecordDTO> structMaterialRecordDTOList);

    /**
     * 修改包裹结构使用包材记录
     *
     * ID | Code 二选一
     * @param structMaterialRecordDTO
     * @return
     */
    Result<Boolean> modify(StructMaterialRecordDTO structMaterialRecordDTO);

    /**
     * 批量修改包裹结构使用包材记录
     *
     * @param structMaterialRecordDTOList
     * @return
     */
    Result<Boolean> modifyBatch(List<StructMaterialRecordDTO> structMaterialRecordDTOList);

    /**
     * 根据条件查询是否存在
     *
     * @param param
     * @return
     */
    Result<Boolean> checkExits(StructMaterialRecordParam param);

    /**
     * 获取包裹结构使用包材记录
     *
     * @param param
     * @return
     */
    Result<StructMaterialRecordDTO> get(StructMaterialRecordParam param);

    /**
     * 获取包裹结构使用包材记录列表
     * @param param
     * @return
     */
    Result<List<StructMaterialRecordDTO>> getList(StructMaterialRecordParam param);

    /**
     * 分页获取包裹结构使用包材记录
     *
     * @param param
     * @return
     */
    Result<Page<StructMaterialRecordDTO>> getPage(StructMaterialRecordParam param);

    /**
     * 功能描述:  删除
     * 创建时间:  2021/1/8 11:25 上午
     *
     * @param param:
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     */
    Result<Boolean> remove(StructMaterialRecordParam param);

}

