package com.dt.domain.bill.client.performance;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.domain.bill.dto.performance.PerformanceDetailDTO;
import com.dt.domain.bill.param.performance.PerformanceDetailParam;

import java.util.List;

/**
 * <p>
 * 人员操作详情 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-02-25
 */
public interface IPerformanceDetailClient {

    /**
     * 新增人员操作详情
     *
     * @param performanceDetailDTO
     * @return
     */
    Result<Boolean> save(PerformanceDetailDTO performanceDetailDTO);

    /**
     * 批量新增人员操作详情
     *
     * @param performanceDetailDTOList
     * @return
     */
    Result<Boolean> saveBatch(List<PerformanceDetailDTO> performanceDetailDTOList);

    /**
     * 修改人员操作详情
     *
     * ID | Code 二选一
     * @param performanceDetailDTO
     * @return
     */
    Result<Boolean> modify(PerformanceDetailDTO performanceDetailDTO);

    /**
     * 批量修改人员操作详情
     *
     * @param performanceDetailDTOList
     * @return
     */
    Result<Boolean> modifyBatch(List<PerformanceDetailDTO> performanceDetailDTOList);

    /**
     * 根据条件查询是否存在
     *
     * @param param
     * @return
     */
    Result<Boolean> checkExits(PerformanceDetailParam param);

    /**
     * 获取人员操作详情
     *
     * @param param
     * @return
     */
    Result<PerformanceDetailDTO> get(PerformanceDetailParam param);

    /**
     * 获取人员操作详情列表
     * @param param
     * @return
     */
    Result<List<PerformanceDetailDTO>> getList(PerformanceDetailParam param);

    /**
     * 分页获取人员操作详情
     *
     * @param param
     * @return
     */
    Result<Page<PerformanceDetailDTO>> getPage(PerformanceDetailParam param);

    /**
     * 功能描述:  删除
     * 创建时间:  2021/1/8 11:25 上午
     *
     * @param param:
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     */
    Result<Boolean> remove(PerformanceDetailParam param);

}

