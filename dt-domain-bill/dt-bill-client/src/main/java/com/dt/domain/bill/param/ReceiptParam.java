package com.dt.domain.bill.param;

import com.dt.component.common.param.BaseSearchParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/9/21 15:15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="Receipt对象", description="收货作业批次")
public class ReceiptParam extends BaseSearchParam  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "作业批次号 唯一")
    private String recId;
    private List<String> recIdList;

    /**
     * 到货通知单号
     */
    private String asnId;
    private List<String> asnIdList;
    /**
     * 客户单号
     */
    @ApiModelProperty(value = "C端单号-(客户原始单号)")
    private String poNo;
    private List<String> poNoList;

    @ApiModelProperty(value = "理货报告编码")
    private String tallyCode;
    private List<String> tallyCodeList;

    /**
     * 收货作业批次，根据批次号查询
     */
    private String skuLot;
    private List<String> skuLotNoList;

    /**
     * 上游单号
     */
    @ApiModelProperty(value = "上游单号--进销存(现有ERP单号,目前前端显示客户单号)")
    private String soNo;
    private List<String> soNoList;
    /**
     * 货主编码
     */
    private String cargoCode;
    private List<String> cargoCodeList;
    /**
     * 仓库编码
     */
    private String warehouseCode;
    private List<String> warehouseCodeList;
    /**
     * 状态码
     */
    private String status;
    private List<String> statusList;
    /**
     * 单据类型
     */
    private String type;
    private List<String> typeList;
    /**
     * 商品编码
     */
    private String skuCode;
    private List<String> skuCodeList;
    /**
     * upc编码
     */
    private String upcCode;
    private List<String> upcCodeList;
    /**
     * 容器号
     */
    private String contCode;
    private List<String> contCodeList;

    @ApiModelProperty(value = "通知状态")
    private Integer notifyStatus;
    private List<Integer> notifyStatusList;

    @ApiModelProperty(value = "上架完成时间开始")
    private Long completeShelfDateStart;
    @ApiModelProperty(value = "上架完成时间结束")
    private Long completeShelfDateEnd;

    @ApiModelProperty(value = "收货类型")
    private String receiptType;
    private List<String> receiptTypeList;

    @ApiModelProperty(value = "回传标记")
    private String backFlag;

    @ApiModelProperty(value = "正次品")
    private String skuQuality;

    @ApiModelProperty(value = "托盘码")
    private String palletCode;
    private List<String> palletCodeList;

}