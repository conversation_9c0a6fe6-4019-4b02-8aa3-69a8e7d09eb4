package com.dt.domain.bill.bo;

import com.dt.domain.bill.dto.AdjustDTO;
import com.dt.domain.bill.dto.AdjustDetailDTO;
import com.dt.domain.bill.dto.message.MessageMqDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class AdjustBO implements Serializable {
    private AdjustDTO adjustDTO;
    private List<AdjustDetailDTO> adjustDetailDTOList;
    private List<MessageMqDTO> messageMqDTOList;
}
