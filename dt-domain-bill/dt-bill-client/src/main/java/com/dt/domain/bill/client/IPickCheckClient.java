package com.dt.domain.bill.client;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.domain.bill.dto.PickCheckDTO;
import com.dt.domain.bill.param.PickCheckParam;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/10/14 19:45
 */
public interface IPickCheckClient {

    /**
     * 新增拣选单复核信息
     * @param param
     * @return
     */
    Result<Boolean> save(PickCheckParam param);

    /**
     * 修改拣选单复核信息
     * id | code | idList | codeList 四选一
     * @param param
     * @return
     */
    Result<Boolean> modify(PickCheckParam param);

    /**
     * 根据条件查询是否存在
     * @param param
     * @return
     */
    Result<Boolean> checkExits(PickCheckParam param);

    /**
     * 获取拣选单复核信息
     * @param param
     * @return
     */
    Result<PickCheckDTO> get(PickCheckParam param);

    /**
     * 获取拣选单复核信息
     * @param param
     * @return
     */
    Result<PickCheckDTO> getDetail(PickCheckParam param);

    /**
     * 获取拣选单复核列表
     * @param param
     * @return
     */
    Result<List<PickCheckDTO>> getList(PickCheckParam param);

    /**
     * 分页获取拣选单复核
     * @param param
     * @return
     */
    Result<Page<PickCheckDTO>> getPage(PickCheckParam param);
    
}
