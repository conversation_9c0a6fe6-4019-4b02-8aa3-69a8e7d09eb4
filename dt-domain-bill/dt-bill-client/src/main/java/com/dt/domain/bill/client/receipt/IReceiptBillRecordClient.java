package com.dt.domain.bill.client.receipt;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.domain.bill.dto.receipt.ReceiptBillRecordDTO;
import com.dt.domain.bill.param.receipt.ReceiptBillRecordParam;

import java.util.List;

/**
 * <p>
 * 收货凭据表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-18
 */
public interface IReceiptBillRecordClient {

    /**
     * 新增收货凭据表
     *
     * @param receiptBillRecordDTO
     * @return
     */
    Result<Boolean> save(ReceiptBillRecordDTO receiptBillRecordDTO);

    /**
     * 批量新增收货凭据表
     *
     * @param receiptBillRecordDTOList
     * @return
     */
    Result<Boolean> saveBatch(List<ReceiptBillRecordDTO> receiptBillRecordDTOList);

    /**
     * 修改收货凭据表
     *
     * ID | Code 二选一
     * @param receiptBillRecordDTO
     * @return
     */
    Result<Boolean> modify(ReceiptBillRecordDTO receiptBillRecordDTO);

    /**
     * 批量修改收货凭据表
     *
     * @param receiptBillRecordDTOList
     * @return
     */
    Result<Boolean> modifyBatch(List<ReceiptBillRecordDTO> receiptBillRecordDTOList);

    /**
     * 根据条件查询是否存在
     *
     * @param param
     * @return
     */
    Result<Boolean> checkExits(ReceiptBillRecordParam param);

    /**
     * 获取收货凭据表
     *
     * @param param
     * @return
     */
    Result<ReceiptBillRecordDTO> get(ReceiptBillRecordParam param);

    /**
     * 获取收货凭据表列表
     * @param param
     * @return
     */
    Result<List<ReceiptBillRecordDTO>> getList(ReceiptBillRecordParam param);

    /**
     * 分页获取收货凭据表
     *
     * @param param
     * @return
     */
    Result<Page<ReceiptBillRecordDTO>> getPage(ReceiptBillRecordParam param);

    /**
     * 功能描述:  删除
     * 创建时间:  2021/1/8 11:25 上午
     *
     * @param param:
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     */
    Result<Boolean> remove(ReceiptBillRecordParam param);

}

