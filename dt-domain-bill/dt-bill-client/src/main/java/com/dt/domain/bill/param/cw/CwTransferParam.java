package com.dt.domain.bill.param.cw;

import com.dt.component.common.param.BaseSearchParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * CW调拨
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "CwTransfer对象", description = "CW调拨")
public class CwTransferParam extends BaseSearchParam  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    /**
     * 仓库编码
     */
    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    /**
     * CW调拨编码
     */
    @ApiModelProperty(value = "CW调拨编码")
    private String cwTransferCode;

    /**
     * 来源客户
     */
    @ApiModelProperty(value = "来源客户")
    private String originCustomName;

    /**
     * 目标客户
     */
    @ApiModelProperty(value = "目标客户")
    private String targetCustomName;

    /**
     * 调拨参考号
     */
    @ApiModelProperty(value = "调拨参考号")
    private String cwTransferNumber;

    /**
     * 调拨类型
     */
    @ApiModelProperty(value = "调拨类型")
    private String cwTransferType;

    /**
     * 清关状态【初始化、服务完成、清关完成】
     */
    @ApiModelProperty(value = "清关状态【初始化、服务完成、清关完成】")
    private String customsClearanceStatus;

    /**
     * 调拨种类数
     */
    @ApiModelProperty(value = "调拨种类数")
    private Integer collectSkuCount;

    /**
     * 调拨总数
     */
    @ApiModelProperty(value = "调拨总数")
    private BigDecimal collectCount;

    /**
     * 采集完成时间
     */
    @ApiModelProperty(value = "采集完成时间")
    private Long collectEndTime;

    /**
     * 出库完成时间
     */
    @ApiModelProperty(value = "出库完成时间")
    private Long outEndTime;

    /**
     * 上架完成时间
     */
    @ApiModelProperty(value = "上架完成时间")
    private Long shelfEndTime;

    /**
     * 货主编码
     */
    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "修改人")
    private String updatedBy;

    private List<String> lotNumberList;
    private List<Integer> statusList;
    private List<String> cwTransferNumberList;
    private List<String> cwTransferCodeList;
    private List<String> cwTransferTypeList;
    private Long collectEndTimeStart;
    private Long collectEndTimeEnd;
    private Long outEndTimeStart;
    private Long outEndTimeEnd;
    private Long shelfEndTimeStart;
    private Long shelfEndTimeEnd;
    private List<String> cwGoodCodeList;

    @ApiModelProperty(value = "货主编码")
    private String locationCode;

    private String lotNumber;
    private String skuCode;
    private BigDecimal shelfQty;

    @ApiModelProperty(value = "托盘,箱码")
    private String regOrSerial;

    @ApiModelProperty(value = "CW产品")
    private String cwGoodCode;

    @ApiModelProperty(value = "采集单位")
    private String unit;
}