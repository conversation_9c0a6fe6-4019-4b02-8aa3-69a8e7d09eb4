package com.dt.domain.bill.client.pre;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.domain.bill.bo.pre.PrePackageSkuBO;
import com.dt.domain.bill.dto.pre.PrePackageSkuDTO;
import com.dt.domain.bill.param.pre.PrePackageSkuParam;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
 * <p>
 * 预包商品条码 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-25
 */
public interface IPrePackageSkuClient {
    
    @ApiOperation("禁用启用")
    Result<Boolean> statusEnable(PrePackageSkuParam param);
    
    @ApiOperation("新增预包条码")
    Result<Boolean> add(PrePackageSkuBO param);
    
    @ApiOperation("修改")
    Result<Boolean> modify(PrePackageSkuBO param);

    /**
     * 新增预包商品条码
     *
     * @param prePackageSkuDTO
     * @return
     */
    Result<Boolean> save(PrePackageSkuDTO prePackageSkuDTO);

    /**
     * 批量新增预包商品条码
     *
     * @param prePackageSkuDTOList
     * @return
     */
    Result<Boolean> saveBatch(List<PrePackageSkuDTO> prePackageSkuDTOList);

    /**
     * 修改预包商品条码
     *
     * ID | Code 二选一
     * @param prePackageSkuDTO
     * @return
     */
    Result<Boolean> modify(PrePackageSkuDTO prePackageSkuDTO);

    /**
     * 批量修改预包商品条码
     *
     * @param prePackageSkuDTOList
     * @return
     */
    Result<Boolean> modifyBatch(List<PrePackageSkuDTO> prePackageSkuDTOList);

    /**
     * 根据条件查询是否存在
     *
     * @param param
     * @return
     */
    Result<Boolean> checkExits(PrePackageSkuParam param);

    /**
     * 获取预包商品条码
     *
     * @param param
     * @return
     */
    Result<PrePackageSkuDTO> get(PrePackageSkuParam param);

    /**
     * 获取预包商品条码列表
     * @param param
     * @return
     */
    Result<List<PrePackageSkuDTO>> getList(PrePackageSkuParam param);

    /**
     * 分页获取预包商品条码
     *
     * @param param
     * @return
     */
    Result<Page<PrePackageSkuDTO>> getPage(PrePackageSkuParam param);

    /**
     * 功能描述:  删除
     * 创建时间:  2021/1/8 11:25 上午
     *
     * @param param:
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     */
    Result<Boolean> remove(PrePackageSkuParam param);

}

