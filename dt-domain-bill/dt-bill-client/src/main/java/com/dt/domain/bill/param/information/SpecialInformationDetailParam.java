package com.dt.domain.bill.param.information;

import com.dt.component.common.param.BaseSearchParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import java.math.BigDecimal;

/**
* <p>
    * 特殊信息采集明细
    * </p>
*
* <AUTHOR>
* @since 2024-08-13
*/
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="SpecialInformationDetail对象", description="特殊信息采集明细")
public class SpecialInformationDetailParam extends BaseSearchParam {

private static final long serialVersionUID = 1L;

    /**
    * 货主编码
    */
    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    /**
    * 采集信息编码
    */
    @ApiModelProperty(value = "采集信息编码")
    private String specialNo;

    /**
    * 商品代码
    */
    @ApiModelProperty(value = "商品代码")
    private String skuCode;

    /**
    * 商品名称
    */
    @ApiModelProperty(value = "商品名称")
    private String skuName;

    /**
    * 条码
    */
    @ApiModelProperty(value = "条码")
    private String upcCode;

    /**
    * 托盘号（REG）
    */
    @ApiModelProperty(value = "托盘号（REG）")
    private String reg;

    /**
    * 箱码（SERIAL）
    */
    @ApiModelProperty(value = "箱码（SERIAL）")
    private String serial;

    /**
    * 批次号（LOTNUMBER）
    */
    @ApiModelProperty(value = "批次号（LOTNUMBER）")
    private String lotNumber;

    /**
    * 装箱日期（生产日期）
    */
    @ApiModelProperty(value = "装箱日期（生产日期）")
    private Long manufDate;

    /**
    * 到期日期（失效日期）
    */
    @ApiModelProperty(value = "到期日期（失效日期）")
    private Long expireDate;

    /**
    * 商品属性
    */
    @ApiModelProperty(value = "商品属性")
    private String skuQuality;

    /**
    * 采集箱数
    */
    @ApiModelProperty(value = "采集箱数")
    private BigDecimal boxQty;

    /**
    * 状态
    */
    @ApiModelProperty(value = "状态")
    private Integer status;

    /**
    * 采集人
    */
    @ApiModelProperty(value = "采集人")
    private String collectBy;

    /**
    * 采集时间 (时间戳)
    */
    @ApiModelProperty(value = "采集时间 (时间戳)")
    private Long collectTime;

    /**
    * 拓展字段
    */
    @ApiModelProperty(value = "拓展字段")
    private String extraJson;

@ApiModelProperty(value = "创建人")
private String createdBy;

@ApiModelProperty(value = "修改人")
private String updatedBy;
}
