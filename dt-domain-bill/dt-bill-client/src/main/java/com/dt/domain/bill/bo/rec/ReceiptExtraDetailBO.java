package com.dt.domain.bill.bo.rec;

import com.dt.component.common.param.BaseSearchParam;
import com.dt.domain.bill.dto.rec.ReceiptExtraDetailDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;



/**
 * <p>
 * 收货作业批次容器明细
 * </p>
 *
 * <AUTHOR>
 * @since 2021-02-20
 */
@Data
@ApiModel(value="ReceiptExtraDetail对象", description="收货作业批次容器明细")
public class ReceiptExtraDetailBO extends BaseSearchParam  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "收货作业批次容器明细")
    private ReceiptExtraDetailDTO receiptExtraDetailDTO;
}