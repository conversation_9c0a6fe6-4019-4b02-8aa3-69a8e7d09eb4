package com.dt.domain.bill.client.rs;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.domain.bill.client.rs.bo.SalesReturnOrderBO;
import com.dt.domain.bill.dto.rs.SalesReturnOrderDTO;
import com.dt.domain.bill.param.rs.SalesReturnOrderParam;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
 * <p>
 * 销退单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-25
 */
public interface ISalesReturnOrderClient {

    /**
     * 新增销退单
     *
     * @param salesReturnOrderDTO
     * @return
     */
    Result<Boolean> save(SalesReturnOrderDTO salesReturnOrderDTO);
    
    @ApiOperation("保存销退单")
    Result<Boolean> save(SalesReturnOrderBO salesReturnOrderBO);

    /**
     * 批量新增销退单
     *
     * @param salesReturnOrderDTOList
     * @return
     */
    Result<Boolean> saveBatch(List<SalesReturnOrderDTO> salesReturnOrderDTOList);

    /**
     * 修改销退单
     *
     * ID | Code 二选一
     * @param salesReturnOrderDTO
     * @return
     */
    Result<Boolean> modify(SalesReturnOrderDTO salesReturnOrderDTO);

    /**
     * 批量修改销退单
     *
     * @param salesReturnOrderDTOList
     * @return
     */
    Result<Boolean> modifyBatch(List<SalesReturnOrderDTO> salesReturnOrderDTOList);

    /**
     * 根据条件查询是否存在
     *
     * @param param
     * @return
     */
    Result<Boolean> checkExits(SalesReturnOrderParam param);

    /**
     * 获取销退单
     *
     * @param param
     * @return
     */
    Result<SalesReturnOrderDTO> get(SalesReturnOrderParam param);

    /**
     * 获取销退单列表
     * @param param
     * @return
     */
    Result<List<SalesReturnOrderDTO>> getList(SalesReturnOrderParam param);

    /**
     * 分页获取销退单
     *
     * @param param
     * @return
     */
    Result<Page<SalesReturnOrderDTO>> getPage(SalesReturnOrderParam param);

    /**
     * 功能描述:  删除
     * 创建时间:  2021/1/8 11:25 上午
     *
     * @param param:
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     */
    Result<Boolean> remove(SalesReturnOrderParam param);

}

