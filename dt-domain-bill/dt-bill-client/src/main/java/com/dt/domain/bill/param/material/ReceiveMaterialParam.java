package com.dt.domain.bill.param.material;

import com.dt.component.common.param.BaseSearchParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import java.math.BigDecimal;
import java.util.List;

/**
* <p>
    * 耗材领用表
    * </p>
*
* <AUTHOR>
* @since 2022-11-10
*/
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ReceiveMaterial对象", description="耗材领用表")
public class ReceiveMaterialParam extends BaseSearchParam  implements java.io.Serializable  {

private static final long serialVersionUID = 1L;

    /**
    * 仓库编码
    */
    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    /**
    * 领用单据号唯一，不允许修改
    */
    @ApiModelProperty(value = "领用单据号唯一，不允许修改")
    private String receiveMaterialCode;
    private List<String> receiveMaterialCodeList;

    /**
    * 状态码
    */
    @ApiModelProperty(value = "状态码")
    private String status;
    private List<String> statusList;
    private List<String> upcCodeList;

    /**
    * 计划品种数 默认:0
    */
    @ApiModelProperty(value = "计划品种数 默认:0")
    private Integer type;

    /**
    * 计划数量
    */
    @ApiModelProperty(value = "计划数量")
    private Integer expQty;

    /**
    * 领用总数
    */
    @ApiModelProperty(value = "领用总数")
    private Integer receiveQty;

    /**
    * 完成领用时间 (时间戳)
    */
    @ApiModelProperty(value = "完成领用时间 (时间戳)")
    private Long completeTimeStart;
    private Long completeTimeEnd;

    /**
    * 备注
    */
    @ApiModelProperty(value = "备注")
    private String remark;

@ApiModelProperty(value = "创建人")
private String createdBy;

@ApiModelProperty(value = "修改人")
private String updatedBy;
}