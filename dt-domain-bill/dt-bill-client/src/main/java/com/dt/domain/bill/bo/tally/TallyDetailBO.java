package com.dt.domain.bill.bo.tally;

import com.dt.component.common.param.BaseSearchParam;
import com.dt.domain.bill.dto.tally.TallyDetailDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;



/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-02-16
 */
@Data
@ApiModel(value="TallyDetail对象", description="")
public class TallyDetailBO extends BaseSearchParam  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "")
    private TallyDetailDTO tallyDetailDTO;
}