package com.dt.domain.bill.bo;

import com.dt.domain.bill.dto.pre.PrePackagePlanDTO;
import com.dt.domain.bill.dto.pre.PrePackagePlanDetailDTO;
import com.dt.domain.bill.dto.pre.PrePackageShelfDTO;
import com.dt.domain.bill.dto.pre.PrePackageShelfDetailDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/8/27 11:44
 */
@Data
public class PrePackagePlanCommitAndShelfBillBO implements Serializable {

    @ApiModelProperty(value = "预包计划主表")
    PrePackagePlanDTO prePackagePlanDTO;

    @ApiModelProperty(value = "预包计划明细")
    List<PrePackagePlanDetailDTO> packagePlanDetailDTOList;

    @ApiModelProperty(value = "预包上架单主表")
    PrePackageShelfDTO prePackageShelfDTO;

    @ApiModelProperty(value = "预包上架单主表明细")
    List<PrePackageShelfDetailDTO> prePackageShelfDetailDTOList;
}
