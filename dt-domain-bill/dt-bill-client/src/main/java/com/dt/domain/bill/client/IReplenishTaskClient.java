package com.dt.domain.bill.client;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.domain.bill.dto.ReplenishTaskDTO;
import com.dt.domain.bill.param.ReplenishTaskBatchParam;
import com.dt.domain.bill.param.ReplenishTaskParam;

import java.util.List;

public interface IReplenishTaskClient {
    /**
     * 新增补货指引信息
     * @param param
     * @return
     */
    Result<Boolean> save(ReplenishTaskParam param);
    Result<Boolean> saveBatch(ReplenishTaskBatchParam param);
    Result<Boolean>  saveOrUpdateBatch(ReplenishTaskBatchParam param);

    /**
     * 修改补货指引信息
     * id | idList 二选一
     * @param param
     * @return
     */
    Result<Boolean> modify(ReplenishTaskParam param);

    /**
     * 根据包裹列表取消补货指引
     * @param param
     * @return
     */
    Result<Boolean> modifyByPackageCodeList(ReplenishTaskParam param);

    /**
     * 根据条件查询是否存在
     * @param param
     * @return
     */
    Result<Boolean> checkExits(ReplenishTaskParam param);

    /**
     * 获取补货指引信息
     * @param param
     * @return
     */
    Result<ReplenishTaskDTO> get(ReplenishTaskParam param);

    /**
     * 获取补货指引列表
     * @param param
     * @return
     */
    Result<List<ReplenishTaskDTO>> getList(ReplenishTaskParam param);

    Result<List<ReplenishTaskDTO>> getListStatistics(ReplenishTaskParam param);
    /**
     * 分页获取补货指引
     * @param param
     * @return
     */
    Result<Page<ReplenishTaskDTO>> getPage(ReplenishTaskParam param);

    /**
     * 批量删除
     *
     * @param param
     * @return
     */
    Result<Boolean> batchDelete(ReplenishTaskParam param);
}
