package com.dt.domain.bill.client.carryover;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.domain.bill.dto.carryover.BookCarryoverReceiptDTO;
import com.dt.domain.bill.param.carryover.BookCarryoverReceiptParam;

import java.util.List;

/**
 * <p>
 * 账册结转单海关回执 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-19
 */
public interface IBookCarryoverReceiptClient {

    /**
     * 新增账册结转单海关回执
     *
     * @param bookCarryoverReceiptDTO
     * @return
     */
    Result<Boolean> save(BookCarryoverReceiptDTO bookCarryoverReceiptDTO);

    /**
     * 批量新增账册结转单海关回执
     *
     * @param bookCarryoverReceiptDTOList
     * @return
     */
    Result<Boolean> saveBatch(List<BookCarryoverReceiptDTO> bookCarryoverReceiptDTOList);

    /**
     * 修改账册结转单海关回执
     *
     * ID | Code 二选一
     * @param bookCarryoverReceiptDTO
     * @return
     */
    Result<Boolean> modify(BookCarryoverReceiptDTO bookCarryoverReceiptDTO);

    /**
     * 批量修改账册结转单海关回执
     *
     * @param bookCarryoverReceiptDTOList
     * @return
     */
    Result<Boolean> modifyBatch(List<BookCarryoverReceiptDTO> bookCarryoverReceiptDTOList);

    /**
     * 根据条件查询是否存在
     *
     * @param param
     * @return
     */
    Result<Boolean> checkExits(BookCarryoverReceiptParam param);

    /**
     * 获取账册结转单海关回执
     *
     * @param param
     * @return
     */
    Result<BookCarryoverReceiptDTO> get(BookCarryoverReceiptParam param);

    /**
     * 获取账册结转单海关回执列表
     * @param param
     * @return
     */
    Result<List<BookCarryoverReceiptDTO>> getList(BookCarryoverReceiptParam param);

    /**
     * 分页获取账册结转单海关回执
     *
     * @param param
     * @return
     */
    Result<Page<BookCarryoverReceiptDTO>> getPage(BookCarryoverReceiptParam param);

    /**
     * 功能描述:  删除
     * 创建时间:  2021/1/8 11:25 上午
     *
     * @param param:
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     */
    Result<Boolean> remove(BookCarryoverReceiptParam param);

}

