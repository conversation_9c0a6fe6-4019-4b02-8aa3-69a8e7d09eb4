package com.dt.domain.bill.param;

import com.dt.component.common.param.BaseSearchParam;
import com.dt.domain.bill.dto.ShelfDetailDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * 上架明细批量操作对象
 * </p>
 *
 * <AUTHOR> xy
 * @since 2020-09-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ShelfDetailBatch 批量对象", description="批量对象")
public class ShelfDetailBatchParam extends BaseSearchParam  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "批量操作明细")
    private List<ShelfDetailDTO> detailList;

}