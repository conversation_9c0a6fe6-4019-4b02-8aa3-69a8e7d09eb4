package com.dt.domain.bill.client;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.domain.bill.dto.HandoverDetailDTO;
import com.dt.domain.bill.param.HandoverDetailBatchParam;
import com.dt.domain.bill.param.HandoverDetailParam;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/10/14 19:41
 */
public interface IHandoverDetailClient {

    /**
     * 新增出库单交接明细信息
     * @param param
     * @return
     */
    Result<Boolean> save(HandoverDetailParam param);

    /**
     * 新增，修改出库单交接
     * @param param
     * @return
     */
    Result<Boolean> saveOrUpdateBatch(HandoverDetailBatchParam param);

    /**
     * 批量提交
     * @param param
     * @return
     */
    Result<Boolean> saveBatch(HandoverDetailBatchParam param);

    /**
     *
     * @param param
     * @return
     */
    Result<Boolean> saveBatch(List<HandoverDetailDTO> param);


    /**
     * 修改出库单交接明细信息
     * id | idList 四选一
     * @param param
     * @return
     */
    Result<Boolean> modify(HandoverDetailParam param);

    /**
     * 根据条件查询是否存在
     * @param param
     * @return
     */
    Result<Boolean> checkExits(HandoverDetailParam param);

    /**
     * 获取出库单交接明细信息
     * @param param
     * @return
     */
    Result<HandoverDetailDTO> get(HandoverDetailParam param);

    /**
     * 获取出库单交接明细信息
     * @param param
     * @return
     */
    Result<HandoverDetailDTO> getDetail(HandoverDetailParam param);

    /**
     * 获取出库单交接明细列表
     * @param param
     * @return
     */
    Result<List<HandoverDetailDTO>> getList(HandoverDetailParam param);

    /**
     * 分页获取出库单交接明细
     * @param param
     * @return
     */
    Result<Page<HandoverDetailDTO>> getPage(HandoverDetailParam param);

    /**
     *
     * @param param
     * @return
     */
    Result<Boolean> modifyBatch(HandoverDetailBatchParam param);

    Result<List<HandoverDetailDTO>> getAppointColumnList(HandoverDetailParam handoverDetailParam, List<String> convertToFieldNameList);

    /**
     * @param handoverDetailParam
     * @return com.dt.component.common.result.Result<java.lang.Integer>
     * @author: WuXian
     * description:
     * create time: 2022/4/18 13:28
     */
    Result<Integer> getCount(HandoverDetailParam handoverDetailParam);
}
