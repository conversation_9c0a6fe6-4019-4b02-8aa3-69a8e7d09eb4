package com.dt.domain.bill.dto.inventory;

import com.dt.component.common.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 盘点计划表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "InventoryPlan对象", description = "盘点计划表")
public class InventoryPlanDTO extends BaseDTO  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    /**
     * 仓库编码 不允许修改
     */
    @ApiModelProperty(value = "仓库编码 不允许修改")
    private String warehouseCode;

    /**
     * 盘点计划编码
     */
    @ApiModelProperty(value = "盘点计划编码")
    private String inventoryPlanCode;

    /**
     * 盘点计划类型
     */
    @ApiModelProperty(value = "盘点计划类型")
    private String inventoryPlanType;

    /**
     * 盘点计划名称
     */
    @ApiModelProperty(value = "盘点计划名称")
    private String inventoryPlanName;

    /**
     * 是否包含空库位
     */
    @ApiModelProperty(value = "是否包含空库位")
    private Integer hasEmptyLocation;

    /**
     * 商品类型
     */
    @ApiModelProperty(value = "商品类型")
    private String skuType;

    @ApiModelProperty(value = "库位,库区,巷道,货主,商品条码汇总json")
    private String summaryJson;

    /**
     * 最小动碰次数(盘点范围 动碰次数大于此值进入盘点范围)
     */
    @ApiModelProperty(value = "最小动碰次数(盘点范围 动碰次数大于此值进入盘点范围)")
    private Integer minTouchNum;

    /**
     * 动碰次数最高的前几位(盘点范围 动碰次数从高到低的前count位)
     */
    @ApiModelProperty(value = "动碰次数最高的前几位(盘点范围 动碰次数从高到低的前count位)")
    private Integer maxTouchCount;

    /**
     * 动碰时间开始(盘点范围)
     */
    @ApiModelProperty(value = "动碰时间开始(盘点范围)")
    private Long touchTimeStart;

    /**
     * 动碰时间结束(盘点范围)
     */
    @ApiModelProperty(value = "动碰时间结束(盘点范围)")
    private Long touchTimeEnd;

    /**
     * 最大库位数(盘点范围 根据动碰次数 从高到底排序)
     */
    @ApiModelProperty(value = "最大库位数(盘点范围 根据动碰次数 从高到底排序)")
    private Integer maxLocationCount;

    /**
     * 盘点方式
     */
    @ApiModelProperty(value = "盘点方式")
    private String inventoryWay;

    /**
     * 盘点工具
     */
    @ApiModelProperty(value = "盘点工具")
    private String inventoryTool;

    /**
     * 盘点深度
     */
    @ApiModelProperty(value = "盘点深度")
    private String inventoryDepth;

    /**
     * 重复盘点
     */
    @ApiModelProperty(value = "重复盘点")
    private String inventoryAgain;

    /**
     * 盘点进度
     */
    @ApiModelProperty(value = "盘点进度")
    private Integer inventoryRate;

    /**
     * 盘点单据状态
     */
    @ApiModelProperty(value = "盘点单据状态")
    private String status;

    /**
     * 盘点未完成预警时间
     */
    @ApiModelProperty(value = "盘点未完成预警时间")
    private Long warningTime;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "修改人")
    private String updatedBy;

    @ApiModelProperty(value = "切割维度")
    private String splitRule;

    @ApiModelProperty(value = "盘点方法")
    private String inventoryMethod;
}