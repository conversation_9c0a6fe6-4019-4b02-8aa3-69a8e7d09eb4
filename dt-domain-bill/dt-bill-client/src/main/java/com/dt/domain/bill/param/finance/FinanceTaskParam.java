package com.dt.domain.bill.param.finance;

import com.dt.component.common.param.BaseSearchParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import java.math.BigDecimal;

/**
* <p>
    * 供应链金融任务
    * </p>
*
* <AUTHOR>
* @since 2022-05-19
*/
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="FinanceTask对象", description="供应链金融任务")
public class FinanceTaskParam extends BaseSearchParam  implements java.io.Serializable  {

private static final long serialVersionUID = 1L;

    /**
    * 仓库编码
    */
    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    /**
    * 货主编码
    */
    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    /**
    * 单据号
    */
    @ApiModelProperty(value = "单据号")
    private String billNo;

    /**
    * 单据类型
    */
    @ApiModelProperty(value = "单据类型")
    private String billType;

    /**
    * 操作类型
    */
    @ApiModelProperty(value = "操作类型")
    private String operationType;

    /**
    * 状态码
    */
    @ApiModelProperty(value = "状态码")
    private String status;

    /**
    * 重试次数
    */
    @ApiModelProperty(value = "重试次数")
    private Integer retryNum;

    /**
    * 备注
    */
    @ApiModelProperty(value = "备注")
    private String remark;

@ApiModelProperty(value = "创建人")
private String createdBy;

@ApiModelProperty(value = "修改人")
private String updatedBy;
}