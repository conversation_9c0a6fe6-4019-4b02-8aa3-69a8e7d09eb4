package com.dt.domain.bill.client.trucking;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.domain.bill.dto.trucking.TruckingDTO;
import com.dt.domain.bill.param.trucking.TruckingParam;

import java.util.List;

/**
 * <p>
 * 装载主表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-05
 */
public interface ITruckingClient {

    /**
     * 新增装载主表
     *
     * @param truckingDTO
     * @return
     */
    Result<Boolean> save(TruckingDTO truckingDTO);

    /**
     * 批量新增装载主表
     *
     * @param truckingDTOList
     * @return
     */
    Result<Boolean> saveBatch(List<TruckingDTO> truckingDTOList);

    /**
     * 修改装载主表
     *
     * ID | Code 二选一
     * @param truckingDTO
     * @return
     */
    Result<Boolean> modify(TruckingDTO truckingDTO);

    /**
     * 批量修改装载主表
     *
     * @param truckingDTOList
     * @return
     */
    Result<Boolean> modifyBatch(List<TruckingDTO> truckingDTOList);

    /**
     * 根据条件查询是否存在
     *
     * @param param
     * @return
     */
    Result<Boolean> checkExits(TruckingParam param);

    /**
     * 获取装载主表
     *
     * @param param
     * @return
     */
    Result<TruckingDTO> get(TruckingParam param);

    /**
     * 获取装载主表列表
     * @param param
     * @return
     */
    Result<List<TruckingDTO>> getList(TruckingParam param);

    /**
     * 分页获取装载主表
     *
     * @param param
     * @return
     */
    Result<Page<TruckingDTO>> getPage(TruckingParam param);

    /**
     * 功能描述:  删除
     * 创建时间:  2021/1/8 11:25 上午
     *
     * @param param:
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     */
    Result<Boolean> remove(TruckingParam param);
    /**
     * @param truckingDTO
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     * @describe:
     * @date 2022/9/6 11:14
     */
    Result<Boolean> modifyTruckingDATOAndAddDeatil(TruckingDTO truckingDTO);
    /**
     * @param truckingDTO
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     * @describe:
     * @date 2022/9/6 11:14
     */
    Result<Boolean> modifyTruckingAndRemoveDetail(TruckingDTO truckingDTO);
    /**
     * @param truckingParam
     * @return com.dt.component.common.result.Result<java.lang.Integer>
     * <AUTHOR>
     * @describe:
     * @date 2022/9/6 13:22
     */
    Result<Integer> getExportCountNum(TruckingParam truckingParam);
}

