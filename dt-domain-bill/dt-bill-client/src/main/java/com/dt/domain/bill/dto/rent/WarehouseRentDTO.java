package com.dt.domain.bill.dto.rent;

import com.dt.component.common.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * 仓租数据
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "WarehouseRent对象", description = "仓租数据")
public class WarehouseRentDTO extends BaseDTO  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    /**
     * 仓库编码
     */
    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    /**
     * 货主编码
     */
    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    /**
     * 仓租单号
     */
    @ApiModelProperty(value = "仓租单号")
    private String wsStoreNo;

    /**
     * 字节仓租单号
     */
    @ApiModelProperty(value = "字节仓租单号")
    private String outWsStoreNo;

    /**
     * 字节仓库编码
     */
    @ApiModelProperty(value = "字节仓库编码")
    private String outWarehouseCode;

    /**
     * 字节店铺编码
     */
    @ApiModelProperty(value = "字节店铺编码")
    private String outShopCode;

    /**
     * 字节货主编码
     */
    @ApiModelProperty(value = "字节货主编码")
    private String outCargoCode;

    /**
     * 计费日期
     */
    @ApiModelProperty(value = "计费日期")
    private Long outChargeTime;

    @ApiModelProperty(value = "字节计费类型")
    private String outChargeType;

    @ApiModelProperty(value = "字节货主类型")
    private String outCargoType;

    @ApiModelProperty(value = "字节服务商编码")
    private String serviceProviderCode;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private String status;

    /**
     * 通知状态
     */
    @ApiModelProperty(value = "通知状态")
    private Integer notifyStatus;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private Integer notifyCount;

    /**
     * 通知时间
     */
    @ApiModelProperty(value = "通知时间")
    private Long notifyTime;

    /**
     * 拓传字段json
     */
    @ApiModelProperty(value = "拓传字段json")
    private String extraJson;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "修改人")
    private String updatedBy;

    List<WarehouseRentDetailDTO> detailDTOList;
}