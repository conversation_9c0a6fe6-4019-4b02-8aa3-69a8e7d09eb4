package com.dt.domain.bill.param;

import com.dt.component.common.param.BaseSearchParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2020/10/14 20:06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "对象", description = "")
public class PackageRandomCheckParam extends BaseSearchParam  implements java.io.Serializable  {

    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    @ApiModelProperty(value = "抽检包裹号")
    private String packageCheckCode;

    @ApiModelProperty(value = "交接单号")
    private String handoverCode;

    @ApiModelProperty(value = "容器编码")
    private String contCode;

    @ApiModelProperty(value = "承运商编码")
    private String carrierCode;

    @ApiModelProperty(value = "抽检结果")
    private String checkResult;

    @ApiModelProperty(value = "抽检描叙")
    private String checkMsg;

    @ApiModelProperty(value = "抽检人")
    private String checkBy;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "修改人")
    private String updatedBy;


}