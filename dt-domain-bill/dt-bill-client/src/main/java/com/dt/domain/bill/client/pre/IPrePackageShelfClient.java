package com.dt.domain.bill.client.pre;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.domain.bill.bo.PrePackageShelfCompleteBillBO;
import com.dt.domain.bill.dto.pre.PrePackageShelfDTO;
import com.dt.domain.bill.param.pre.PrePackageShelfParam;

import java.util.List;

/**
 * <p>
 * 预包上架管理 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-27
 */
public interface IPrePackageShelfClient {

    /**
     * 新增预包上架管理
     *
     * @param prePackageShelfDTO
     * @return
     */
    Result<Boolean> save(PrePackageShelfDTO prePackageShelfDTO);

    /**
     * 批量新增预包上架管理
     *
     * @param prePackageShelfDTOList
     * @return
     */
    Result<Boolean> saveBatch(List<PrePackageShelfDTO> prePackageShelfDTOList);

    /**
     * 修改预包上架管理
     *
     * ID | Code 二选一
     * @param prePackageShelfDTO
     * @return
     */
    Result<Boolean> modify(PrePackageShelfDTO prePackageShelfDTO);

    /**
     * 批量修改预包上架管理
     *
     * @param prePackageShelfDTOList
     * @return
     */
    Result<Boolean> modifyBatch(List<PrePackageShelfDTO> prePackageShelfDTOList);

    /**
     * 根据条件查询是否存在
     *
     * @param param
     * @return
     */
    Result<Boolean> checkExits(PrePackageShelfParam param);

    /**
     * 获取预包上架管理
     *
     * @param param
     * @return
     */
    Result<PrePackageShelfDTO> get(PrePackageShelfParam param);

    /**
     * 获取预包上架管理列表
     * @param param
     * @return
     */
    Result<List<PrePackageShelfDTO>> getList(PrePackageShelfParam param);

    /**
     * 分页获取预包上架管理
     *
     * @param param
     * @return
     */
    Result<Page<PrePackageShelfDTO>> getPage(PrePackageShelfParam param);

    /**
     * 功能描述:  删除
     * 创建时间:  2021/1/8 11:25 上午
     *
     * @param param:
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     */
    Result<Boolean> remove(PrePackageShelfParam param);

    Result<Boolean> completePreShelf(PrePackageShelfCompleteBillBO prePackageShelfCompleteBillBO);
}

