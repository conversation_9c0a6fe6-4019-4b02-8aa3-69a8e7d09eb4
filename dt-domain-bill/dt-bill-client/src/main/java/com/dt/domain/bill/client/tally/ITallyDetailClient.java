package com.dt.domain.bill.client.tally;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.domain.bill.dto.tally.TallyDetailDTO;
import com.dt.domain.bill.param.tally.TallyDetailParam;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-02-16
 */
public interface ITallyDetailClient {

    /**
     * 新增
     *
     * @param tallyDetailDTO
     * @return
     */
    Result<Boolean> save(TallyDetailDTO tallyDetailDTO);

    /**
     * 批量新增
     *
     * @param tallyDetailDTOList
     * @return
     */
    Result<Boolean> saveBatch(List<TallyDetailDTO> tallyDetailDTOList);

    /**
     * 修改
     *
     * ID | Code 二选一
     * @param tallyDetailDTO
     * @return
     */
    Result<Boolean> modify(TallyDetailDTO tallyDetailDTO);

    /**
     * 批量修改
     *
     * @param tallyDetailDTOList
     * @return
     */
    Result<Boolean> modifyBatch(List<TallyDetailDTO> tallyDetailDTOList);

    /**
     * 根据条件查询是否存在
     *
     * @param param
     * @return
     */
    Result<Boolean> checkExits(TallyDetailParam param);

    /**
     * 获取
     *
     * @param param
     * @return
     */
    Result<TallyDetailDTO> get(TallyDetailParam param);

    /**
     * 获取列表
     * @param param
     * @return
     */
    Result<List<TallyDetailDTO>> getList(TallyDetailParam param);

    /**
     * 分页获取
     *
     * @param param
     * @return
     */
    Result<Page<TallyDetailDTO>> getPage(TallyDetailParam param);

    /**
     * 功能描述:  删除
     * 创建时间:  2021/1/8 11:25 上午
     *
     * @param param:
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     */
    Result<Boolean> remove(TallyDetailParam param);

    Result<List<TallyDetailDTO>> getAppointMultipleParamList(TallyDetailParam tallyDetailParam, List<String> tableFields);

    Result<Integer> getCount(TallyDetailParam tallyDetailParam);

    Result<Page<TallyDetailDTO>> getGroupPage(TallyDetailParam tallyDetailParam);

    Result<Boolean> removeTallyDetail(List<TallyDetailDTO> tallyDetailDTOList);
}

