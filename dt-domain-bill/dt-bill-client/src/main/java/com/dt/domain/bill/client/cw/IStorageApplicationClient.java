package com.dt.domain.bill.client.cw;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.domain.bill.bo.cw.StorageApplicationBO;
import com.dt.domain.bill.dto.cw.StorageApplicationDTO;
import com.dt.domain.bill.param.cw.StorageApplicationParam;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
 * <p>
 * 入库申请 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-16
 */
public interface IStorageApplicationClient {

    @ApiOperation("持久化")
    Result<Boolean> persist(StorageApplicationBO storageApplicationBO);

    /**
     * 新增入库申请
     *
     * @param storageApplicationDTO
     * @return
     */
    Result<Boolean> save(StorageApplicationDTO storageApplicationDTO);

    /**
     * 批量新增入库申请
     *
     * @param storageApplicationDTOList
     * @return
     */
    Result<Boolean> saveBatch(List<StorageApplicationDTO> storageApplicationDTOList);

    /**
     * 修改入库申请
     *
     * ID | Code 二选一
     * @param storageApplicationDTO
     * @return
     */
    Result<Boolean> modify(StorageApplicationDTO storageApplicationDTO);

    /**
     * 批量修改入库申请
     *
     * @param storageApplicationDTOList
     * @return
     */
    Result<Boolean> modifyBatch(List<StorageApplicationDTO> storageApplicationDTOList);

    /**
     * 根据条件查询是否存在
     *
     * @param param
     * @return
     */
    Result<Boolean> checkExits(StorageApplicationParam param);

    /**
     * 获取入库申请
     *
     * @param param
     * @return
     */
    Result<StorageApplicationDTO> get(StorageApplicationParam param);

    /**
     * 获取入库申请列表
     * @param param
     * @return
     */
    Result<List<StorageApplicationDTO>> getList(StorageApplicationParam param);

    /**
     * 分页获取入库申请
     *
     * @param param
     * @return
     */
    Result<Page<StorageApplicationDTO>> getPage(StorageApplicationParam param);

    /**
     * 功能描述:  删除
     * 创建时间:  2021/1/8 11:25 上午
     *
     * @param param:
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     */
    Result<Boolean> remove(StorageApplicationParam param);

}

