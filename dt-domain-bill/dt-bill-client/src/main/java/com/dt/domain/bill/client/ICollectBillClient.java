package com.dt.domain.bill.client;

import com.dt.component.common.result.Result;
import com.dt.domain.bill.bo.CollectPickBO;
import com.dt.domain.bill.bo.CollectSubmitBO;
import com.dt.domain.bill.bo.CollectWaveAbnormalBO;

/**
 * <AUTHOR>
 * @date 2020/10/22 11:20
 */
public interface ICollectBillClient {
    /**
     * 汇单-生成拣选单
     * @param collectPickBO
     * @return
     */
    Result<Boolean> submitCollectPickBillBO(CollectPickBO collectPickBO);

    /**
     * 汇单-分配库存
     * @param collectSubmitBO
     * @return
     */
    Result<Boolean> submitCollectWaveBillBO(CollectSubmitBO collectSubmitBO);

    /**
     * 汇单分配异常单
     * @param collectWaveAbnormalBO
     * @return
     */
    Result<Boolean> submitCollectAbnormalOrder(CollectWaveAbnormalBO collectWaveAbnormalBO);
}
