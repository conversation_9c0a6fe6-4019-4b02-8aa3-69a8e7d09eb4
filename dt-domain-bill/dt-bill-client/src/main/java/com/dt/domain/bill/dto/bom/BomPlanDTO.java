package com.dt.domain.bill.dto.bom;

import com.dt.component.common.dto.BaseDTO;
import com.dt.domain.bill.dto.ShelfDTO;
import com.dt.domain.bill.dto.message.MessageMqDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * bom生产计划
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "BomPlan对象", description = "bom生产计划")
public class BomPlanDTO extends BaseDTO {

    private static final long serialVersionUID = 1L;

    /**
     * 货主编码
     */
    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    /**
     * 计划单号
     */
    @ApiModelProperty(value = "计划单号")
    private String bomPlanCode;

    /**
     * 外部单号
     */
    @ApiModelProperty(value = "外部单号")
    private String outNo;

    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型")
    private String businessType;

    /**
     * 商品代码[bom]
     */
    @ApiModelProperty(value = "商品代码[bom]")
    private String bomSkuCode;

    /**
     * 商品条码[bom]
     */
    @ApiModelProperty(value = "商品条码[bom]")
    private String bomUpcCode;

    /**
     * 商品名称[bom]
     */
    @ApiModelProperty(value = "商品名称[bom]")
    private String bomSkuName;

    /**
     * BOM计划生产数量
     */
    @ApiModelProperty(value = "BOM计划生产数量")
    private BigDecimal bomSkuQty;

    /**
     * 已生产数量
     */
    @ApiModelProperty(value = "已生产数量")
    private BigDecimal completeSkuQty;

    /**
     * 完成时间
     */
    @ApiModelProperty(value = "完成时间")
    private Long completeTime;

    /**
     * 来源[枚举]
     */
    @ApiModelProperty(value = "来源[枚举]")
    private String originFrom;

    /**
     * 单据状态
     */
    @ApiModelProperty(value = "单据状态")
    private String status;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 拓展字段
     */
    @ApiModelProperty(value = "拓展字段")
    private String extraJson;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "修改人")
    private String updatedBy;

    @ApiModelProperty(value = "仓库")
    private String warehouseCode;


    @ApiModelProperty(value = "来源库位")
    private String locationCode;

    //提交参数
    MessageMqDTO messageMqDTO;

    ShelfDTO shelfDTO;

    List<BomPlanStockDetailDTO> bomPlanStockDetailDTOList;
}
