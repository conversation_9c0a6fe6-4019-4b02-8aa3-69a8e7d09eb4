package com.dt.domain.bill.dto.entrance;

import com.dt.component.common.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import java.math.BigDecimal;

/**
* <p>
    * 一线入境到货登记
    * </p>
*
* <AUTHOR>
* @since 2022-03-15
*/
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="EntranceRegistrationDetail对象", description="一线入境到货登记")
public class EntranceRegistrationDetailDTO extends BaseDTO  implements java.io.Serializable  {

private static final long serialVersionUID = 1L;

    /**
    * 仓库编码 取值仓库档案
    */
    @ApiModelProperty(value = "仓库编码 取值仓库档案")
    private String warehouseCode;

    /**
    * 货主
    */
    @ApiModelProperty(value = "货主")
    private String cargoName;

    /**
    * 运输方式
    */
    @ApiModelProperty(value = "运输方式")
    private String transportType;

    /**
    * 发货地
    */
    @ApiModelProperty(value = "发货地")
    private String dispatchPlace;

    /**
    * 货代公司
    */
    @ApiModelProperty(value = "货代公司")
    private String forwardCompany;

    /**
    * 提单号
    */
    @ApiModelProperty(value = "提单号")
    private String ladingNo;

    @ApiModelProperty(value = "到货通知单")
    private String asnId;

    /**
    * 标箱
    */
    @ApiModelProperty(value = "标箱")
    private String teu;

    /**
    * 集装箱号
    */
    @ApiModelProperty(value = "集装箱号")
    private String containerNo;

    /**
    * 托数
    */
    @ApiModelProperty(value = "托数")
    private Integer padCount;

    /**
    * 核注清单编号
    */
    @ApiModelProperty(value = "核注清单编号")
    private String checkListNo;

    /**
    * 报关单号
    */
    @ApiModelProperty(value = "报关单号")
    private String declarationNo;

    /**
    * 报关日期
    */
    @ApiModelProperty(value = "报关日期")
    private Long declarationDate;

    /**
    * 品名
    */
    @ApiModelProperty(value = "品名")
    private String goodsName;

    /**
    * 类目
    */
    @ApiModelProperty(value = "类目")
    private String category;

    /**
    * 预计到货数量
    */
    @ApiModelProperty(value = "预计到货数量")
    private Integer estimatedDeliveredQuantity;

    /**
    * 预计到货毛重(KG)
    */
    @ApiModelProperty(value = "预计到货毛重(KG)")
    private BigDecimal estimatedGrossWeight;

    /**
    * 预计到港日期
    */
    @ApiModelProperty(value = "预计到港日期")
    private Long estimatedArriveDate;

    /**
    * 实际到港日期
    */
    @ApiModelProperty(value = "实际到港日期")
    private Long arriveDate;

    /**
    * 到货港口/机场
    */
    @ApiModelProperty(value = "到货港口/机场")
    private String arrivePort;

    /**
    * 预计到库日期
    */
    @ApiModelProperty(value = "预计到库日期")
    private Long estimatedArriveWmsDate;

    /**
    * 车辆数量
    */
    @ApiModelProperty(value = "车辆数量")
    private Integer carCount;

    /**
    * 备注
    */
    @ApiModelProperty(value = "备注")
    private String remark;

@ApiModelProperty(value = "创建人")
private String createdBy;

@ApiModelProperty(value = "修改人")
private String updatedBy;



    @ApiModelProperty(value = "状态")
    private String status;

    @ApiModelProperty(value = "卸货时间")
    private Long dischargeTime;

    @ApiModelProperty(value = "收货时间")
    private Long receiptTime;



    @ApiModelProperty(value = "库位编码")
    private String locationCode;
}