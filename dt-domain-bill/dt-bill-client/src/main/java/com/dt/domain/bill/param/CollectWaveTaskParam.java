package com.dt.domain.bill.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/10/21 12:02
 */
@Data
public class CollectWaveTaskParam  implements Serializable{

    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    private String carrierCode;

    private String waveCode;

    private String salePlatform;

    @ApiModelProperty(value = "业务类型")
    private String businessType;

    private String saleShopId;

    private Long num;

    @ApiModelProperty(value = "")
    private String status;
    private List<String> statusList;

    @ApiModelProperty(value = "")
    private String pickRuleCode;

    @ApiModelProperty(value = "快递网点编码")
    private String expressBranch;
    private String expressBranchNotBlank;

    private Long createdTimeStart;
    private Long createdTimeEnd;
    private Integer limitNum;

}
