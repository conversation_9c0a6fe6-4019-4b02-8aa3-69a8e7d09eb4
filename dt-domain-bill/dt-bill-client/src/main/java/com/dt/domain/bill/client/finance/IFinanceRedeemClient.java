package com.dt.domain.bill.client.finance;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.domain.bill.bo.finance.FinanceRedeemBO;
import com.dt.domain.bill.bo.finance.FinanceRedeemReleaseBO;
import com.dt.domain.bill.dto.finance.FinanceRedeemDTO;
import com.dt.domain.bill.param.finance.FinanceRedeemParam;

import java.util.List;

/**
 * <p>
 * 金融监管赎回单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-13
 */
public interface IFinanceRedeemClient {

    /**
     * 新增金融监管赎回单
     *
     * @param financeRedeemDTO
     * @return
     */
    Result<Boolean> save(FinanceRedeemDTO financeRedeemDTO);

    /**
     * 批量新增金融监管赎回单
     *
     * @param financeRedeemDTOList
     * @return
     */
    Result<Boolean> saveBatch(List<FinanceRedeemDTO> financeRedeemDTOList);

    /**
     * 修改金融监管赎回单
     * <p>
     * ID | Code 二选一
     *
     * @param financeRedeemDTO
     * @return
     */
    Result<Boolean> modify(FinanceRedeemDTO financeRedeemDTO);

    /**
     * 批量修改金融监管赎回单
     *
     * @param financeRedeemDTOList
     * @return
     */
    Result<Boolean> modifyBatch(List<FinanceRedeemDTO> financeRedeemDTOList);

    /**
     * 根据条件查询是否存在
     *
     * @param param
     * @return
     */
    Result<Boolean> checkExits(FinanceRedeemParam param);

    /**
     * 获取金融监管赎回单
     *
     * @param param
     * @return
     */
    Result<FinanceRedeemDTO> get(FinanceRedeemParam param);

    /**
     * 获取金融监管赎回单列表
     *
     * @param param
     * @return
     */
    Result<List<FinanceRedeemDTO>> getList(FinanceRedeemParam param);

    /**
     * 分页获取金融监管赎回单
     *
     * @param param
     * @return
     */
    Result<Page<FinanceRedeemDTO>> getPage(FinanceRedeemParam param);

    /**
     * 功能描述:  删除
     * 创建时间:  2021/1/8 11:25 上午
     *
     * @param param:
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     */
    Result<Boolean> remove(FinanceRedeemParam param);

    /**
     * @param financeRedeemReleaseBO
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * @author: WuXian
     * description:
     * create time: 2022/5/18 10:41
     */
    Result<Boolean> redeemCommitReleaseStock(FinanceRedeemReleaseBO financeRedeemReleaseBO);

    Result<Boolean> redeemCommit(FinanceRedeemBO financeRedeemBO);

    /**
     * @param financeRedeemDTO
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * @author: WuXian
     * description:
     * create time: 2022/5/24 10:26
     */
    Result<Boolean> modifyFail(FinanceRedeemDTO financeRedeemDTO);
}

