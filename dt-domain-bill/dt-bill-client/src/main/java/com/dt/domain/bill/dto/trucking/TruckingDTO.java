package com.dt.domain.bill.dto.trucking;

import com.dt.component.common.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * 装载主表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "Trucking对象", description = "装载主表")
public class TruckingDTO extends BaseDTO  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    /**
     * 仓库编码 不允许修改
     */
    @ApiModelProperty(value = "仓库编码 不允许修改")
    private String warehouseCode;

    /**
     * 装载单号
     */
    @ApiModelProperty(value = "装载单号")
    private String truckingCode;

    /**
     * 车牌号
     */
    @ApiModelProperty(value = "车牌号")
    private String carNo;

    /**
     * 包裹数量
     */
    @ApiModelProperty(value = "包裹数量")
    private Integer packQty;

    /**
     * 状态码 10:装载中 20:装载完成 00:取消
     */
    @ApiModelProperty(value = "状态码 10:装载中 20:装载完成 00:取消")
    private String status;

    /**
     * 创建时间 (时间戳)
     */
    @ApiModelProperty(value = "创建时间 (时间戳)")
    private Long completeTime;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "修改人")
    private String updatedBy;

    List<TruckingDetailDTO> detailDTOList;
}