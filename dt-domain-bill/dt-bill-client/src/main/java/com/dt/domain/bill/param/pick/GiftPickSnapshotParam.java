package com.dt.domain.bill.param.pick;

import com.dt.component.common.param.BaseSearchParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import java.math.BigDecimal;
import java.util.List;

/**
* <p>
    * 赠品快照
    * </p>
*
* <AUTHOR>
* @since 2024-05-08
*/
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="GiftPickSnapshot对象", description="赠品快照")
public class GiftPickSnapshotParam extends BaseSearchParam  implements java.io.Serializable  {

private static final long serialVersionUID = 1L;

    /**
    * 拣选单号
    */
    @ApiModelProperty(value = "拣选单号")
    private String pickCode;
    private List<String> pickCodeList;

    /**
    * 包裹单号
    */
    @ApiModelProperty(value = "包裹单号")
    private String packageCode;
    private List<String> packageCodeList;

    /**
    * 货主编码
    */
    @ApiModelProperty(value = "货主编码")
    private String cargoCode;
    private List<String> cargoCodeList;

    /**
    * 货主名称
    */
    @ApiModelProperty(value = "货主名称")
    private String cargoName;

    /**
    * 商品编码
    */
    @ApiModelProperty(value = "商品编码")
    private String skuCode;
    private List<String> skuCodeList;

    /**
    * 商品名称
    */
    @ApiModelProperty(value = "商品名称")
    private String skuName;

    /**
    * 商品条形码
    */
    @ApiModelProperty(value = "商品条形码")
    private String upcCode;

    /**
    * 条码【耗材】
    */
    @ApiModelProperty(value = "条码【耗材】")
    private String barCode;
    private List<String> barCodeList;

    /**
    * 编码【耗材】
    */
    @ApiModelProperty(value = "编码【耗材】")
    private String materialCode;

    /**
    * 赠品名称
    */
    @ApiModelProperty(value = "赠品名称")
    private String barName;

    /**
    * 数量
    */
    @ApiModelProperty(value = "数量")
    private BigDecimal qty;

    /**
    * 拓展字段
    */
    @ApiModelProperty(value = "拓展字段")
    private String extraJson;

@ApiModelProperty(value = "创建人")
private String createdBy;

@ApiModelProperty(value = "修改人")
private String updatedBy;
}