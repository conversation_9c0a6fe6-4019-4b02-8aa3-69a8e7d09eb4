package com.dt.domain.bill.param.trucking;

import com.dt.component.common.param.BaseSearchParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * 装载明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "TruckingDetail对象", description = "装载明细表")
public class TruckingDetailParam extends BaseSearchParam  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    /**
     * 仓库编码 不允许修改
     */
    @ApiModelProperty(value = "仓库编码 不允许修改")
    private String warehouseCode;

    /**
     * 装载单号
     */
    @ApiModelProperty(value = "装载单号")
    private String truckingCode;
    private List<String> truckingCodeList;

    /**
     * 交接单号
     */
    @ApiModelProperty(value = "交接单号")
    private String handoverCode;
    private List<String> handoverCodeList;

    /**
     * 快递单号
     */
    @ApiModelProperty(value = "快递单号")
    private String carrierCode;
    private List<String> carrierCodeList;

    /**
     * 容器号/托盘号
     */
    @ApiModelProperty(value = "容器号/托盘号")
    private String contCode;
    private List<String> contCodeList;

    /**
     * 包裹数量
     */
    @ApiModelProperty(value = "包裹数量")
    private Integer packQty;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "修改人")
    private String updatedBy;
}