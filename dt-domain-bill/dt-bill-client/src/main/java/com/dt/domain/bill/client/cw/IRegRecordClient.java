package com.dt.domain.bill.client.cw;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.domain.bill.dto.cw.RegRecordDTO;
import com.dt.domain.bill.param.cw.RegRecordParam;

import java.util.List;

/**
 * <p>
 * 托盘记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-16
 */
public interface IRegRecordClient {

    /**
     * 新增托盘记录
     *
     * @param regRecordDTO
     * @return
     */
    Result<Boolean> save(RegRecordDTO regRecordDTO);

    /**
     * 批量新增托盘记录
     *
     * @param regRecordDTOList
     * @return
     */
    Result<Boolean> saveBatch(List<RegRecordDTO> regRecordDTOList);

    /**
     * 修改托盘记录
     *
     * ID | Code 二选一
     * @param regRecordDTO
     * @return
     */
    Result<Boolean> modify(RegRecordDTO regRecordDTO);

    /**
     * 批量修改托盘记录
     *
     * @param regRecordDTOList
     * @return
     */
    Result<Boolean> modifyBatch(List<RegRecordDTO> regRecordDTOList);

    /**
     * 根据条件查询是否存在
     *
     * @param param
     * @return
     */
    Result<Boolean> checkExits(RegRecordParam param);

    /**
     * 获取托盘记录
     *
     * @param param
     * @return
     */
    Result<RegRecordDTO> get(RegRecordParam param);

    /**
     * 获取托盘记录列表
     * @param param
     * @return
     */
    Result<List<RegRecordDTO>> getList(RegRecordParam param);

    /**
     * 分页获取托盘记录
     *
     * @param param
     * @return
     */
    Result<Page<RegRecordDTO>> getPage(RegRecordParam param);

    /**
     * 功能描述:  删除
     * 创建时间:  2021/1/8 11:25 上午
     *
     * @param param:
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     */
    Result<Boolean> remove(RegRecordParam param);

}

