package com.dt.domain.bill.client.rs.bo;

import com.dt.domain.bill.dto.message.MessageMqDTO;
import com.dt.domain.bill.dto.rs.SalesReturnHandoverDTO;
import com.dt.domain.bill.dto.rs.SalesReturnHandoverDetailDTO;
import com.dt.domain.bill.dto.rs.SalesReturnOrderDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class SalesReturnHandoverBO implements Serializable {
    private List<SalesReturnHandoverDTO> salesReturnHandoverDTOList;
    private List<SalesReturnHandoverDetailDTO> salesReturnHandoverDetailDTOList;
    private List<Long> removeDetailIdList;
    private List<SalesReturnOrderDTO> salesReturnOrderDTOList;
    private List<MessageMqDTO> messageMqDTOList;
    private List<SalesReturnHandoverDetailDTO> salesReturnHandoverDetailDTOListForDelete;
}
