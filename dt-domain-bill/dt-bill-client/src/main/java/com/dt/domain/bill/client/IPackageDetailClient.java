package com.dt.domain.bill.client;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.domain.bill.dto.PackageDetailDTO;
import com.dt.domain.bill.dto.ReplenishSkuDTO;
import com.dt.domain.bill.param.PackageDetailBatchParam;
import com.dt.domain.bill.param.PackageDetailParam;


import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/10/14 19:41
 */
public interface IPackageDetailClient {

    /**
     * 新增信息
     * @param param
     * @return
     */
    Result<Boolean> save(PackageDetailParam param);

    /**
     * 新增，修改
     * @param param
     * @return
     */
    Result<Boolean> saveOrUpdateBatch(PackageDetailBatchParam param);

    /**
     * 批量提交
     * @param param
     * @return
     */
    Result<Boolean> saveBatch(PackageDetailBatchParam param);


    /**
     * 修改信息
     * id | idList 四选一
     * @param param
     * @return
     */
    Result<Boolean> modify(PackageDetailParam param);

    /**
     * 根据条件查询是否存在
     * @param param
     * @return
     */
    Result<Boolean> checkExits(PackageDetailParam param);

    /**
     * 明细信息
     * @param param
     * @return
     */
    Result<PackageDetailDTO> get(PackageDetailParam param);

    /**
     * 明细信息
     * @param param
     * @return
     */
    Result<PackageDetailDTO> getDetail(PackageDetailParam param);

    /**
     * 明细列表
     * @param param
     * @return
     */
    Result<List<PackageDetailDTO>> getList(PackageDetailParam param);

    Result<List<PackageDetailDTO>> getExportList(PackageDetailParam param);
    /**
     * 分页明细
     * @param param
     * @return
     */
    Result<Page<PackageDetailDTO>> getPage(PackageDetailParam param);

    /**
     *
     * @param param
     * @return
     */
    Result<Boolean> modifyBatch(PackageDetailBatchParam param);

    /**
     *
     * @param packageDetailDTOList
     * @return
     */
    Result<Boolean> modifyBatchDetail(List<PackageDetailDTO> packageDetailDTOList);

    Result<List<PackageDetailDTO>> getAppointColumnList(PackageDetailParam packageDetailParam, List<String> convertToFieldNameList);
    /**
     * @param packageDetailParam
     * @return com.dt.component.common.result.Result<java.util.List<com.dt.domain.bill.dto.ReplenishSkuDTO>>
     * <AUTHOR>
     * @describe:
     * @date 2022/12/6 11:39
     */
    Result<List<ReplenishSkuDTO>> queryReplenishTaskData(PackageDetailParam packageDetailParam);
}
