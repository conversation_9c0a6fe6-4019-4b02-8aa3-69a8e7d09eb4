package com.dt.domain.bill.dto;

import com.dt.component.common.dto.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @Create 2021/02/25  18:55
 * @Describe
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class SkuLotCodeDTO extends BaseDTO  implements java.io.Serializable  {
    @ApiModelProperty(value = "批次ID")
    private String code;

    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    @ApiModelProperty(value = "商品编码")
    private String skuCode;

    @ApiModelProperty(value = "入库日期")
    private Long receiveDate;
    private String receiveDateFormat;

    @ApiModelProperty(value = "生产日期")
    private Long manufDate;
    private String manufDateFormat;

    @ApiModelProperty(value = "失效/过期日期")
    private Long expireDate;
    private String expireDateFormat;

    @ApiModelProperty(value = "商品属性")
    private String skuQuality;

    @ApiModelProperty(value = "生产批次号")
    private String productionNo;

    @ApiModelProperty(value = "禁售日期")
    private Long withdrawDate;
    private String withdrawDateFormat;
}