package com.dt.domain.bill.client;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.domain.bill.bo.MoveBO;
import com.dt.domain.bill.dto.MoveDTO;
import com.dt.domain.bill.dto.MoveDetailDTO;
import com.dt.domain.bill.param.MoveDetailBatchParam;
import com.dt.domain.bill.param.MoveDetailParam;
import com.dt.domain.bill.param.MoveParam;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
 * <p>
 * 移位单管理
 * </p>
 *
 * <AUTHOR> xy
 * @since 2020-09-11
 */
public interface IMoveClient {
    
    @ApiOperation("保存pda移位单")
    Result<Boolean> savePdaMove(MoveBO moveBO);
    
    @ApiOperation("开始移位单")
    Result<Boolean> modify(MoveBO moveBO);
    
    @ApiOperation("完成pda移位单")
    Result<Boolean> completePdaMove(MoveBO moveBO);
    
    /**
     * 新增移位单信息
     * @param param
     * @return
     */
    Result<Boolean> save(MoveParam param);
    
    @ApiOperation("保存")
    Result<Boolean> save(MoveBO moveBO);

    /**
     * 修改移位单信息
     * id | code | idList | codeList 四选一
     * @param param
     * @return
     */
    Result<Boolean> modify(MoveParam param);

    /**
     * 根据条件查询是否存在
     * @param param
     * @return
     */
    Result<Boolean> checkExits(MoveParam param);

    /**
     * 获取移位单信息
     * @param param
     * @return
     */
    Result<MoveDTO> get(MoveParam param);

    /**
     * 获取移位单信息
     * @param param
     * @return
     */
    Result<MoveDTO> getDetail(MoveParam param);

    /**
     * 获取移位单列表
     * @param param
     * @return
     */
    Result<List<MoveDTO>> getList(MoveParam param);

    /**
     * 分页获取移位单
     * @param param
     * @return
     */
    Result<Page<MoveDTO>> getPage(MoveParam param);

    /**
     * 获取移位单列表
     * @param param
     * @return
     */
    Result<List<MoveDTO>> getDetailList(MoveParam param);


    /**
     * 查询移位明细列表
     * @param param
     * @return
     */
    Result<MoveDetailDTO> getMoveDetail(MoveDetailParam param);

    /**
     * 查询移位明细
     * @param param
     * @return
     */
    Result<List<MoveDetailDTO>> getMoveDetailList(MoveDetailParam param);

    Result<Boolean> saveOrUpdate(MoveDetailBatchParam param);

    /**
     * 获取移位单最大行号
     * @param shelfCode
     * @return
     */
    Result<Integer> getMaxLineSeq(String shelfCode);

    /**
     * 新增移位单
     * @param moveDTO
     * @return
     */
    Result<Boolean> saveDTO(MoveDTO moveDTO);

    /**
     * 修改移位单
     * @param moveDTO
     * @return
     */
    Result<Boolean> modifyDTO(MoveDTO moveDTO);
}
