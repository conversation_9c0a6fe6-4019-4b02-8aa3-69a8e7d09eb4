package com.dt.domain.bill.dto.sn;

import com.dt.component.common.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 * 收货扫描SN
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "ScanSnReceive对象", description = "收货扫描SN")
public class ScanSnReceiveDTO extends BaseDTO  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    /**
     * 货主编码
     */
    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    /**
     * 扫描码
     */
    @ApiModelProperty(value = "扫描码")
    private String snCode;

    /**
     * 入库单号
     */
    @ApiModelProperty(value = "入库单号")
    private String asnId;

    /**
     * 收货作业批次
     */
    @ApiModelProperty(value = "收货作业批次")
    private String recId;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String poNo;

    /**
     * ERP单号
     */
    @ApiModelProperty(value = "ERP单号")
    private String soNo;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String skuLotNo;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private String status;

    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    private BigDecimal qty;

    /**
     * 商品编码
     */
    @ApiModelProperty(value = "商品编码")
    private String skuCode;

    /**
     * 商品条码
     */
    @ApiModelProperty(value = "商品条码")
    private String upcCode;

    /**
     * 商品名
     */
    @ApiModelProperty(value = "商品名")
    private String skuName;

    /**
     * 拓展字段
     */
    @ApiModelProperty(value = "拓展字段")
    private String extraJson;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "修改人")
    private String updatedBy;

    @ApiModelProperty(value = "仓库")
    private String warehouseCode;
}