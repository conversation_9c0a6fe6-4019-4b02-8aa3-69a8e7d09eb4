package com.dt.domain.bill.client;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.domain.bill.bo.HomingCompleteShelfBillBO;
import com.dt.domain.bill.bo.ShelfBO;
import com.dt.domain.bill.bo.ShelfReleaseBO;
import com.dt.domain.bill.dto.ShelfDTO;
import com.dt.domain.bill.dto.ShelfDetailDTO;
import com.dt.domain.bill.param.ShelfBatchParam;
import com.dt.domain.bill.param.ShelfDetailBatchParam;
import com.dt.domain.bill.param.ShelfDetailParam;
import com.dt.domain.bill.param.ShelfParam;

import java.util.List;
/**
 * <p>
 * 上架单管理
 * </p>
 *
 * <AUTHOR> xy
 * @since 2020-09-11
 */
public interface IShelfClient {

    /**
     * 完成上架
     * @param shelfBO
     * @return
     */
    Result<Boolean> complete(ShelfBO shelfBO);
    
    /**
     * 新增上架单信息
     * @param param
     * @return
     */
    Result<Boolean> save(ShelfParam param);

    /**
     * 修改上架单信息
     * id | code | idList | codeList 四选一
     * @param param
     * @return
     */
    Result<Boolean> modify(ShelfParam param);

    Result<Boolean> modifyBatch(ShelfBatchParam param);

    /**
     * 根据条件查询是否存在
     * @param param
     * @return
     */
    Result<Boolean> checkExits(ShelfParam param);

    /**
     * 获取上架单信息
     * @param param
     * @return
     */
    Result<ShelfDTO> get(ShelfParam param);

    /**
     * 获取上架单信息
     * @param param
     * @return
     */
    Result<ShelfDTO> getDetail(ShelfParam param);

    /**
     * 获取上架单列表
     * @param param
     * @return
     */
    Result<List<ShelfDTO>> getList(ShelfParam param);

    /**
     * 分页获取上架单
     * @param param
     * @return
     */
    Result<Page<ShelfDTO>> getPage(ShelfParam param);

    /**
     * 获取上架单列表
     * @param param
     * @return
     */
    Result<List<ShelfDTO>> getDetailList(ShelfParam param);


    /**
     * 查询上架明细列表
     * @param param
     * @return
     */
    Result<ShelfDetailDTO> getShelfDetail(ShelfDetailParam param);

    /**
     * 查询上架明细
     * @param param
     * @return
     */
    Result<List<ShelfDetailDTO>> getShelfDetailList(ShelfDetailParam param);

    Result<Boolean> saveOrUpdate(ShelfDetailBatchParam param);

    /**
     * 收货完成提交上架单和上架单明细
     * @param shelf
     * @return
     */
    Result<Boolean> commintShelf(ShelfDTO shelf);

    /**
     * 获取上架单最大行号
     * @param shelfCode
     * @return
     */
    Result<Integer> getMaxLineSeq(String shelfCode);

    Result<Boolean> modifyBatch(List<ShelfDTO> shelfDTOList);

    Result<Boolean> commitShelfAndHoming(HomingCompleteShelfBillBO homingCompleteShelfBillBO);

    /**
     * 上架单审核、清关完成后最终完成上架单
     * 同时会处理收货作业批次
     */
    Result<Boolean> completeShelfAfterRelease(ShelfReleaseBO shelfReleaseBO);

    Result<Boolean> modifyByDTO(ShelfDTO shelfDTO);
}
