package com.dt.domain.bill.bo.finance;

import com.dt.component.common.param.BaseSearchParam;
import com.dt.domain.bill.dto.finance.FinanceBillLogDTO;
import com.dt.domain.bill.dto.finance.FinanceSupervisionDTO;
import com.dt.domain.bill.dto.finance.FinanceSupervisionDetailDTO;
import com.dt.domain.bill.dto.finance.FinanceSupervisionMoveDetailDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;


/**
 * <p>
 * 金融监管单
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-13
 */
@Data
@ApiModel(value = "FinanceSupervision对象", description = "金融监管单")
public class FinanceSupervisionBO extends BaseSearchParam  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "金融监管单")
    private FinanceSupervisionDTO financeSupervisionDTO;

    private List<FinanceSupervisionDetailDTO> financeSupervisionDetailDTOList;
    
    private List<FinanceSupervisionMoveDetailDTO> financeSupervisionMoveDetailDTOList;

    private List<FinanceBillLogDTO> financeBillLogDTOList;
}