package com.dt.domain.bill.client.device;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.domain.bill.dto.device.DeviceRecordDTO;
import com.dt.domain.bill.param.device.DeviceRecordParam;

import java.util.List;

/**
 * <p>
 * 设备数据回传信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-13
 */
public interface IDeviceRecordClient {

    /**
     * 新增设备数据回传信息
     *
     * @param deviceRecordDTO
     * @return
     */
    Result<Boolean> save(DeviceRecordDTO deviceRecordDTO);

    /**
     * 批量新增设备数据回传信息
     *
     * @param deviceRecordDTOList
     * @return
     */
    Result<Boolean> saveBatch(List<DeviceRecordDTO> deviceRecordDTOList);

    /**
     * 修改设备数据回传信息
     *
     * ID | Code 二选一
     * @param deviceRecordDTO
     * @return
     */
    Result<Boolean> modify(DeviceRecordDTO deviceRecordDTO);

    /**
     * 批量修改设备数据回传信息
     *
     * @param deviceRecordDTOList
     * @return
     */
    Result<Boolean> modifyBatch(List<DeviceRecordDTO> deviceRecordDTOList);

    /**
     * 根据条件查询是否存在
     *
     * @param param
     * @return
     */
    Result<Boolean> checkExits(DeviceRecordParam param);

    /**
     * 获取设备数据回传信息
     *
     * @param param
     * @return
     */
    Result<DeviceRecordDTO> get(DeviceRecordParam param);

    /**
     * 获取设备数据回传信息列表
     * @param param
     * @return
     */
    Result<List<DeviceRecordDTO>> getList(DeviceRecordParam param);

    /**
     * 分页获取设备数据回传信息
     *
     * @param param
     * @return
     */
    Result<Page<DeviceRecordDTO>> getPage(DeviceRecordParam param);

    /**
     * 功能描述:  删除
     * 创建时间:  2021/1/8 11:25 上午
     *
     * @param param:
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     */
    Result<Boolean> remove(DeviceRecordParam param);
    /**
     * @param deviceRecordParam
     * @return com.dt.component.common.result.Result<com.dt.domain.bill.dto.device.DeviceRecordDTO>
     * <AUTHOR>
     * @describe: 获取特定的参数
     * @date 2024/6/13 15:14
     */
    Result<DeviceRecordDTO> getLastDeviceRecord(DeviceRecordParam deviceRecordParam);
}

