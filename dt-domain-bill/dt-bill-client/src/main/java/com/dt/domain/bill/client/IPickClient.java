package com.dt.domain.bill.client;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.domain.bill.bo.PickConfirmBO;
import com.dt.domain.bill.dto.BillPrintAgainDTO;
import com.dt.domain.bill.dto.PickDTO;
import com.dt.domain.bill.dto.PickDetailDTO;
import com.dt.domain.bill.param.PickBatchParam;
import com.dt.domain.bill.param.PickParam;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/10/14 19:41
 */
public interface IPickClient {

    /**
     * 新增拣选单信息
     *
     * @param param
     * @return
     */
    Result<Boolean> save(PickParam param);

    /**
     * 修改拣选单信息
     * id | code | idList | codeList 四选一
     *
     * @param param
     * @return
     */
    Result<Boolean> modify(PickParam param);

    /**
     * @param pickBatchParam
     * @return
     */
    Result<Boolean> modifyBatch(PickBatchParam pickBatchParam);

    /**
     * 功能描述:  批量修改拣选单详情
     * 创建时间:  2020/12/25 11:16 上午
     *
     * @param param:
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     */
    Result<Boolean> modifyPickDetailBatch(PickBatchParam param);

    /**
     * 根据条件查询是否存在
     *
     * @param param
     * @return
     */
    Result<Boolean> checkExits(PickParam param);

    /**
     * 获取拣选单信息
     *
     * @param param
     * @return
     */
    Result<PickDTO> get(PickParam param);

    /**
     * 获取拣选单信息
     *
     * @param param
     * @return
     */
    Result<PickDTO> getPickAndDetailList(PickParam param);

    /**
     * 获取拣选单列表
     *
     * @param param
     * @return
     */
    Result<List<PickDTO>> getList(PickParam param);

    Result<List<PickDTO>> getAgainList(PickParam param);

    /**
     * 分页获取拣选单
     *
     * @param param
     * @return
     */
    Result<Page<PickDTO>> getPage(PickParam param);

    /**
     * 明细分页
     *
     * @param param
     * @return
     */
    Result<Page<PickDetailDTO>> getDetailPage(PickParam param);

    /**
     * 汇单
     *
     * @param pickDTO
     */
    Result<Boolean> submitCollectPick(List<PickDTO> pickDTO);

    /**
     * @param param
     * @return
     */
    Result<PickDetailDTO> getPickDetailByPackCode(PickParam param);

    /**
     * 获取拣选单明细
     *
     * @param searchPickOrderParam
     * @return
     */
    Result<List<PickDetailDTO>> getPickDetailList(PickParam searchPickOrderParam);

    /**
     * 获取拣选单明细
     *
     * @param shipmentOrderCode ,packageCode
     * @return
     */

    Result<List<PickDetailDTO>> getPickDetailList2(String shipmentOrderCode, String packageCode);

    /**
     * 拣选单确认
     *
     * @param pickDTOList
     * @return
     */
    Result<Boolean> confirmPickBill(List<PickDTO> pickDTOList);

//    /**
//     * 拣选单领取
//     * @param pickReceiveBO
//     * @return
//     */
//    Result<Boolean> confirmPickBillReceive(PickReceiveBO pickReceiveBO);

    /**
     * 拣选单交回
     *
     * @param pickDTO
     * @return
     */
    Result<Boolean> confirmPickRestore(PickDTO pickDTO);

    /**
     * 修改拣选单号
     *
     * @param pickDTO
     * @return
     */
    Result<Boolean> update(PickDTO pickDTO);

    /**
     * 查询指定的包裹
     *
     * @param pickParam
     * @return
     */
    Result<PickDetailDTO> queryPackByExpressNoOrPackageCode(PickParam pickParam);

    Result<List<PickDetailDTO>> queryPackByExpressNoOrPackageCodeOrShipmentOrder(PickParam pickParam);

    /**
     * 单据补打分页查询
     *
     * @param pickParam
     * @return
     */
    Result<Page<BillPrintAgainDTO>> getBillPrintAgainPage(PickParam pickParam);

    /**
     * 批量修改
     *
     * @param pickDTOList
     * @return
     */
    Result<Boolean> modifyBatch(List<PickDTO> pickDTOList);

    /**
     * @param pickParam
     * @param filedList
     * @return java.util.List<com.dt.domain.bill.dto.PickDetailDTO>
     * @author: WuXian
     * description:  拣选单明细指定字段查询
     * create time: 2021/10/22 15:42
     */
    Result<List<PickDTO>> getPickListAppointColumn(PickParam pickParam, List<String> filedList);

    Result<Integer> getExportCountNum(PickParam pickParam);

    /**
     * @param pickConfirmBO
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     * @describe:
     * @date 2024/5/8 13:48
     */
    Result<Boolean> confirmPickBillAndGiftSnapshot(PickConfirmBO pickConfirmBO);
}
