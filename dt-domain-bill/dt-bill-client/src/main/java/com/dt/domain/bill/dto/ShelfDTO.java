package com.dt.domain.bill.dto;

import com.dt.component.common.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 上架管理
 * </p>
 *
 * <AUTHOR> xy
 * @since 2020-09-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="Shelf对象", description="上架管理")
public class ShelfDTO extends BaseDTO  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    @ApiModelProperty(value = "上架编号")

    private String code;
    @ApiModelProperty(value = "容器编码")
    private String contCode;

    @ApiModelProperty(value = "单据状态")
    private String status;

    @ApiModelProperty(value = "单据来源，收货作业批次单号")
    private String billNo;

    @ApiModelProperty(value = "商品属性")
    private String skuQuality;

    @ApiModelProperty(value = "商品品种数")
    private Integer skuType;

    @ApiModelProperty(value = "上架商品品种数")
    private Integer shelfSkuType;

    @ApiModelProperty(value = "商品数量")
    private BigDecimal skuQty;

    @ApiModelProperty(value = "上架数量")
    private BigDecimal shelfSkuQty;

    @ApiModelProperty(value = "单位")
    private String packageUnitCode;

    @ApiModelProperty(value = "上架方式")
    private String opType;

    @ApiModelProperty(value = "打印状态 字典组：PRINT_STATUS默认未打印")
    private String printStatus;

    @ApiModelProperty(value = "打印日期")
    private Long printDate;

    @ApiModelProperty(value = "完成日期")
    private Long completeDate;

    @ApiModelProperty(value = "上架人")
    private String opBy;

    @ApiModelProperty(value = "上架单类型")
    private String type;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "更新人")
    private String updatedBy;

    @ApiModelProperty(value = "托盘码")
    private String palletCode;

    @ApiModelProperty(value = "标记")
    private Integer mark;

    @ApiModelProperty(value = "上架单明细")
    private List<ShelfDetailDTO> detailList;
    @ApiModelProperty(value = "预上架完成日期")
    private Long preCompleteDate;

}