package com.dt.domain.bill.param;

import com.dt.component.common.param.BaseSearchParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by nobody on 2020/12/25 13:20
 */
@Data
public class PickDetailParam extends BaseSearchParam implements Serializable {
    private String packageCode;
    private List<String> packageCodeList;
    private String pickCode;
    private List<String> pickCodeList;
    private String shipmentOrderCode;
    private List<String> shipmentOrderCodeList;
    private String expressNo;
    private List<String> expressNoList;
    private String cargoCode;
    private List<String> cargoCodeList;
    private String packageStatus;
    private List<String> packageStatusList;

    @ApiModelProperty(value = "明细 拣选单标识 10:原始明细,20:聚合明细")
    private String detailFlag;

    @ApiModelProperty(value = "原始拣选单号")
    private String originPickCode;
    private List<String> originPickCodeList;

}
