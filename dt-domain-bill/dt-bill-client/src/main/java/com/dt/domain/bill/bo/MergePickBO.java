package com.dt.domain.bill.bo;

import com.dt.domain.bill.dto.PickDTO;
import com.dt.domain.bill.dto.PickDetailDTO;
import com.dt.domain.bill.dto.pick.GiftPickSnapshotDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class MergePickBO implements Serializable {

    @ApiModelProperty(value = "拣选单")
    private List<PickDTO> pickDTOList;

    @ApiModelProperty(value = "拣选单明细")
    private List<PickDetailDTO> pickDetailDTOList;

    @ApiModelProperty(value = "拣选单赠品")
    List<GiftPickSnapshotDTO> giftPickSnapshotDTOList;

}
