package com.dt.domain.bill.param.pre;

import com.dt.component.common.param.BaseSearchParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
* <p>
    * 预包上架管理明细
    * </p>
*
* <AUTHOR>
* @since 2021-08-27
*/
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="PrePackageShelfDetail对象", description="预包上架管理明细")
public class PrePackageShelfDetailParam extends BaseSearchParam  implements java.io.Serializable  {

private static final long serialVersionUID = 1L;

    /**
    * 仓库编码
    */
    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    /**
    * 货主编码
    */
    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    /**
    * 商品编码
    */
    @ApiModelProperty(value = "商品编码")
    private String skuCode;

    /**
    * 行号
    */
    @ApiModelProperty(value = "行号")
    private String lineSeq;

    /**
    * 上架单号
    */
    @ApiModelProperty(value = "上架单号")
    private String preShelfCode;
    private List<String> preShelfCodeList;

    /**
    * 商品数量
    */
    @ApiModelProperty(value = "商品数量")
    private BigDecimal skuQty;

    /**
    * 上架数量
    */
    @ApiModelProperty(value = "上架数量")
    private BigDecimal shelfSkuQty;

    /**
    * 来源库区
    */
    @ApiModelProperty(value = "来源库区")
    private String originZoneCode;

    /**
    * 来源库区类型
    */
    @ApiModelProperty(value = "来源库区类型")
    private String originZoneType;

    /**
    * 来源库位
    */
    @ApiModelProperty(value = "来源库位")
    private String originLocationCode;

    /**
    * 推荐目标库位
    */
    @ApiModelProperty(value = "推荐目标库位")
    private String recLocationCode;

    /**
    * 目标库区
    */
    @ApiModelProperty(value = "目标库区")
    private String targetZoneCode;

    /**
    * 目标库区类型
    */
    @ApiModelProperty(value = "目标库区类型")
    private String targetZoneType;

    /**
    * 实际目标库位
    */
    @ApiModelProperty(value = "实际目标库位")
    private String targetLocationCode;

    /**
    * 批次ID
    */
    @ApiModelProperty(value = "批次ID")
    private String skuLotNo;

    /**
    * 预包明细批次ID
    */
    @ApiModelProperty(value = "预包明细批次ID")
    private String preDetailLotNo;

    /**
    * 状态码
    */
    @ApiModelProperty(value = "状态码")
    private String status;

@ApiModelProperty(value = "创建人")
private String createdBy;

@ApiModelProperty(value = "修改人")
private String updatedBy;
}