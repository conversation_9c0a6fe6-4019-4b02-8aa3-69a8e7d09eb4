package com.dt.domain.bill.client.finance;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.domain.bill.bo.finance.FinanceDisposalBO;
import com.dt.domain.bill.bo.finance.FinanceDisposalReleaseBO;
import com.dt.domain.bill.dto.finance.FinanceDisposalDTO;
import com.dt.domain.bill.param.finance.FinanceDisposalParam;

import java.util.List;

/**
 * <p>
 * 金融监管处置单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-13
 */
public interface IFinanceDisposalClient {

    /**
     * 新增金融监管处置单
     *
     * @param financeDisposalDTO
     * @return
     */
    Result<Boolean> save(FinanceDisposalDTO financeDisposalDTO);

    /**
     * 批量新增金融监管处置单
     *
     * @param financeDisposalDTOList
     * @return
     */
    Result<Boolean> saveBatch(List<FinanceDisposalDTO> financeDisposalDTOList);

    /**
     * 修改金融监管处置单
     * <p>
     * ID | Code 二选一
     *
     * @param financeDisposalDTO
     * @return
     */
    Result<Boolean> modify(FinanceDisposalDTO financeDisposalDTO);

    /**
     * 批量修改金融监管处置单
     *
     * @param financeDisposalDTOList
     * @return
     */
    Result<Boolean> modifyBatch(List<FinanceDisposalDTO> financeDisposalDTOList);

    /**
     * 根据条件查询是否存在
     *
     * @param param
     * @return
     */
    Result<Boolean> checkExits(FinanceDisposalParam param);

    /**
     * 获取金融监管处置单
     *
     * @param param
     * @return
     */
    Result<FinanceDisposalDTO> get(FinanceDisposalParam param);

    /**
     * 获取金融监管处置单列表
     *
     * @param param
     * @return
     */
    Result<List<FinanceDisposalDTO>> getList(FinanceDisposalParam param);

    /**
     * 分页获取金融监管处置单
     *
     * @param param
     * @return
     */
    Result<Page<FinanceDisposalDTO>> getPage(FinanceDisposalParam param);

    /**
     * 功能描述:  删除
     * 创建时间:  2021/1/8 11:25 上午
     *
     * @param param:
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     */
    Result<Boolean> remove(FinanceDisposalParam param);

    /**
     * @param financeDisposalReleaseBO
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * @author: WuXian
     * description:
     * create time: 2022/5/18 12:14
     */
    Result<Boolean> disposalCommitReleaseStock(FinanceDisposalReleaseBO financeDisposalReleaseBO);

    Result<Boolean> disposalCommit(FinanceDisposalBO financeDisposalBO);

    /**
     * @param financeDisposalDTO
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * @author: WuXian
     * description:
     * create time: 2022/5/19 15:55
     */
    Result<Boolean> modifyCallBackStatus(FinanceDisposalDTO financeDisposalDTO);

    /**
     * @param financeDisposalDTO
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * @author: WuXian
     * description:
     * create time: 2022/5/24 10:36
     */
    Result<Boolean> modifyFail(FinanceDisposalDTO financeDisposalDTO);
}

