package com.dt.domain.bill.client;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.domain.bill.dto.AdjustDetailDTO;
import com.dt.domain.bill.param.AdjustDetailBatchParam;
import com.dt.domain.bill.param.AdjustDetailParam;

import java.util.List;

/**
 * <p>
 * 调整单管理
 * </p>
 *
 * <AUTHOR> xy
 * @since 2020-09-11
 */
public interface IAdjustDetailClient {
    /**
     * 新增调整单信息
     * @param param
     * @return
     */
    Result<Boolean> save(AdjustDetailParam param);

    /**
     * 修改调整单信息
     * id | code | idList | codeList 四选一
     * @param param
     * @return
     */
    Result<Boolean> modify(AdjustDetailParam param);

    /**
     * 根据条件查询是否存在
     * @param param
     * @return
     */
    Result<Boolean> checkExits(AdjustDetailParam param);

    /**
     * 获取调整单信息
     * @param param
     * @return
     */
    Result<AdjustDetailDTO> get(AdjustDetailParam param);

    /**
     * 获取调整单信息
     * @param param
     * @return
     */
    Result<AdjustDetailDTO> getDetail(AdjustDetailParam param);

    /**
     * 获取调整单列表
     * @param param
     * @return
     */
    Result<List<AdjustDetailDTO>> getList(AdjustDetailParam param);

    /**
     * 分页获取调整单
     * @param param
     * @return
     */
    Result<Page<AdjustDetailDTO>> getPage(AdjustDetailParam param);

    /**
     * 批量完成明细
     * @param param
     * @return
     */
    Result<Boolean> saveOrUpdate(AdjustDetailBatchParam param);

}
