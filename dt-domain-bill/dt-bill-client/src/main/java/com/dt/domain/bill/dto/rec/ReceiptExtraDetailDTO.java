package com.dt.domain.bill.dto.rec;

import com.dt.component.common.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;


/**
 * <p>
 * 收货作业批次容器明细
 * </p>
 *
 * <AUTHOR>
 * @since 2021-02-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ReceiptExtraDetail对象", description="收货作业批次容器明细")
public class ReceiptExtraDetailDTO extends BaseDTO  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    /**
     * 仓库编码
     */
    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    /**
     * 货主编码
     */
    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    /**
     * 收货作业批次
     */
    @ApiModelProperty(value = "收货作业批次")
    private String recExtraId;

    /**
     * asn明细ID
     */
    @ApiModelProperty(value = "asn明细ID")
    private Long pUid;

    /**
     * 上游行号
     */
    @ApiModelProperty(value = "拓展行号")
    private String extNo;

    /**
     * 到货通知单号
     */
    @ApiModelProperty(value = "到货通知单号")
    private String asnId;

    /**
     * 客户单号
     */
    @ApiModelProperty(value = "客户单号")
    private String poNo;

    /**
     * 容器号
     */
    @ApiModelProperty(value = "容器号")
    private String contCode;

    /**
     * 行号
     */
    @ApiModelProperty(value = "行号")
    private String lineSeq;

    /**
     * 商品代码
     */
    @ApiModelProperty(value = "商品代码")
    private String skuCode;

    /**
     * 商品条码
     */
    @ApiModelProperty(value = "商品条码")
    private String upcCode;

    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String skuName;

    /**
     * 总体积(m³)
     */
    @ApiModelProperty(value = "总体积(m³)")
    private BigDecimal volume;

    /**
     * 总毛重(KG)
     */
    @ApiModelProperty(value = "总毛重(KG)")
    private BigDecimal grossWeight;

    /**
     * 总净重(KG)
     */
    @ApiModelProperty(value = "总净重(KG)")
    private BigDecimal netWeight;

    /**
     * 商品数量
     */
    @ApiModelProperty(value = "商品数量")
    private BigDecimal skuQty;

    /**
     * 批次ID 不允许为空，默认标准规则(批次规则档案)
     */
    @ApiModelProperty(value = "批次ID 不允许为空，默认标准规则(批次规则档案)")
    private String skuLotNo;

    /**
     * 入库日期
     */
    @ApiModelProperty(value = "入库日期")
    private Long receiveDate;

    /**
     * 入库日期格式化
     */
    @ApiModelProperty(value = "入库日期格式化")
    private String receiveDateFormat;

    /**
     * 生产日期
     */
    @ApiModelProperty(value = "生产日期")
    private Long manufDate;

    /**
     * 生产日期格式化
     */
    @ApiModelProperty(value = "生产日期格式化")
    private String manufDateFormat;

    /**
     * 商品属性
     */
    @ApiModelProperty(value = "商品属性")
    private String skuQuality;

    /**
     * 生产批次号
     */
    @ApiModelProperty(value = "生产批次号")
    private String productionNo;

    /**
     * 失效日期
     */
    @ApiModelProperty(value = "失效日期")
    private Long expireDate;

    /**
     * 失效日期格式化
     */
    @ApiModelProperty(value = "失效日期格式化")
    private String expireDateFormat;

    /**
     * 禁售日期
     */
    @ApiModelProperty(value = "禁售日期")
    private Long withdrawDate;

    /**
     * 禁售日期格式化
     */
    @ApiModelProperty(value = "禁售日期格式化")
    private String withdrawDateFormat;

    /**
     * 收货库位
     */
    @ApiModelProperty(value = "收货库位")
    private String locationCode;

    @ApiModelProperty(value = "收货上架目标库位")
    private String targetLocationCode;

    /**
     * 状态码
     */
    @ApiModelProperty(value = "状态码")
    private String status;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "修改人")
    private String updatedBy;

    @ApiModelProperty(value = "多货(多件(OVERSHIP)，多品(OVERSHIP_SKU))")
    private String extraGoods;

    @ApiModelProperty(value = "是否拓传")
    private String callBackUpper;

    @ApiModelProperty(value = "残次等级")
    private String inventoryType;
}