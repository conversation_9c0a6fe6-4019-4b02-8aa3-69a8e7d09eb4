package com.dt.domain.bill.param.finance;

import com.dt.component.common.param.BaseSearchParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 金融监管赎回单
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "FinanceRedeem对象", description = "金融监管赎回单")
public class FinanceRedeemParam extends BaseSearchParam  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    /**
     * 仓库编码
     */
    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    /**
     * 货主编码
     */
    @ApiModelProperty(value = "货主编码")
    private String cargoCode;
    private List<String> cargoCodeList;

    /**
     * 监管赎回单号
     */
    @ApiModelProperty(value = "监管赎回单号")
    private String redeemCode;
    private List<String> redeemCodeList;

    /**
     * erp单号单号
     */
    @ApiModelProperty(value = "erp单号单号")
    private String erpCode;
    private List<String> erpCodeList;

    /**
     * 数量 默认:0
     */
    @ApiModelProperty(value = "数量 默认:0")
    private BigDecimal qty;

    /**
     * 监管赎回时间 (时间戳)
     */
    @ApiModelProperty(value = "监管赎回时间 (时间戳)")
    private Long redeemTime;
    private Long redeemTimeStart;
    private Long redeemTimeEnd;

    /**
     * 审核结果说明
     */
    @ApiModelProperty(value = "审核结果说明")
    private String message;

    /**
     * 单据状态
     */
    @ApiModelProperty(value = "单据状态")
    private String status;
    private List<String> statusList;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "修改人")
    private String updatedBy;

    @ApiModelProperty(value = "商品编码")
    private List<String> skuCodeList;

    @ApiModelProperty(value = "商品条码")
    private List<String> upcCodeList;

    @ApiModelProperty(value = "批次编码")
    private List<String> skuLotNoList;
}