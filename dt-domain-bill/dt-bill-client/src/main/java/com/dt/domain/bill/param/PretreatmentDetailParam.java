package com.dt.domain.bill.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel("预处理请求PARAM")
public class PretreatmentDetailParam implements Serializable {

    @ApiModelProperty("商品编码列表")
    private List<String> skuCodeList;

    @ApiModelProperty("商品条码列表")
    private List<String> skuUpcCodeList;

    @ApiModelProperty("出库单状态列表")
    private List<String> statusList;

}
