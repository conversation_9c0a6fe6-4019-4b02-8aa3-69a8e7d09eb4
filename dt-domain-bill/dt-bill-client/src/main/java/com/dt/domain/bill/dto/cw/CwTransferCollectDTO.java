package com.dt.domain.bill.dto.cw;

import com.dt.component.common.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 * CW调拨调拨
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "CwTransferCollect对象", description = "CW调拨调拨")
public class CwTransferCollectDTO extends BaseDTO  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    /**
     * 仓库编码
     */
    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    /**
     * CW调拨编码
     */
    @ApiModelProperty(value = "CW调拨编码")
    private String cwTransferCode;

    /**
     * 货主编码
     */
    @ApiModelProperty(value = "货主编码")
    private String cargoCode;


    /**
     * 库位编码
     */
    @ApiModelProperty(value = "库位编码")
    private String locationCode;

    /**
     * 产品
     */
    @ApiModelProperty(value = "产品")
    private String cwGoodCode;

    @ApiModelProperty(value = "商品代码")
    private String skuCode;

    @ApiModelProperty(value = "批次ID")
    private String skuLotNo;
    private String targetSkuLotNo;

    @ApiModelProperty(value = "库存操作标识")
    private String batchSerialNo;
    private String targetBatchSerialNo;

    @ApiModelProperty(value = "商品名称")
    private String skuName;

    /**
     * 条码
     */
    @ApiModelProperty(value = "条码")
    private String upcCode;

    /**
     * 统一料号
     */
    @ApiModelProperty(value = "统一料号")
    private String itemCode;

    /**
     * 托盘号
     */
    @ApiModelProperty(value = "托盘号")
    private String reg;

    /**
     * 箱码
     */
    @ApiModelProperty(value = "箱码")
    private String serial;

    /**
     * 批次号
     */
    @ApiModelProperty(value = "批次号")
    private String lotNumber;

    @ApiModelProperty(value = "生产日期")
    private Long manufDate;

    @ApiModelProperty(value = "失效日期")
    private Long expireDate;

    /**
     * 包装单位
     */
    @ApiModelProperty(value = "包装单位")
    private String unit;

    /**
     * 特殊包装单位(UTN)
     */
    @ApiModelProperty(value = "特殊包装单位(UTN)")
    private String specialUnit;

    /**
     * 包装件数
     */
    @ApiModelProperty(value = "包装件数")
    private BigDecimal specialUnitCount;

    /**
     * 采集件数
     */
    @ApiModelProperty(value = "采集件数")
    private BigDecimal collectCount;

    /**
     * 目标库位编码
     */
    @ApiModelProperty(value = "目标库位编码")
    private String targetLocationCode;

    /**
     * 上架件数
     */
    @ApiModelProperty(value = "上架件数")
    private BigDecimal shelfQty;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "修改人")
    private String updatedBy;

}