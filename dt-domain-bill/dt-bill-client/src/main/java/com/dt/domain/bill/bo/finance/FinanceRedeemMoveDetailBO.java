package com.dt.domain.bill.bo.finance;

import com.dt.component.common.param.BaseSearchParam;
import com.dt.domain.bill.dto.finance.FinanceRedeemMoveDetailDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.math.BigDecimal;


/**
* <p>
    * 金融监管赎回单移位明细
    * </p>
*
* <AUTHOR>
* @since 2022-05-13
*/
@Data
@ApiModel(value="FinanceRedeemMoveDetail对象", description="金融监管赎回单移位明细")
public class FinanceRedeemMoveDetailBO extends BaseSearchParam  implements java.io.Serializable  {

private static final long serialVersionUID = 1L;

@ApiModelProperty(value = "金融监管赎回单移位明细")
private FinanceRedeemMoveDetailDTO financeRedeemMoveDetailDTO;
}