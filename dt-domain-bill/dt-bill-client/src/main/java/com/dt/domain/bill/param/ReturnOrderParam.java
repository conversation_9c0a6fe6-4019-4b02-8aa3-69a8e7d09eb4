package com.dt.domain.bill.param;

import com.dt.component.common.param.BaseSearchParam;
import com.dt.domain.bill.dto.ReturnOrderDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> xy
 * @since 2020-10-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ReturnOrder对象", description="归位单")
public class ReturnOrderParam extends BaseSearchParam  implements java.io.Serializable  {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "仓库编码 取值仓库档案")
    private String warehouseCode;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    @ApiModelProperty(value = "归位单号")
    private String retOrderCode;

    private String workbenchCode;
    @ApiModelProperty(value = "状态")
    private String status;

    @ApiModelProperty(value = "包裹数")
    private Integer packageQty;

    @ApiModelProperty(value = "品种数")
    private Integer skuTypeQty;

    @ApiModelProperty(value = "上架完成时间")
    private Long completeShelfTime;
    private Long completeShelfTimeEqStart;
    private Long completeShelfTimeEqEnd;

    private BigDecimal expReturnQty;

    @ApiModelProperty(value = "已归位上架数量")
    private BigDecimal completeOnShelfQty;

    @ApiModelProperty(value = "归位单号")
    private List<String> retOrderCodeList;

    private Long completeShelfTimeStart;
    private Long completeShelfTimeEnd;

    @ApiModelProperty(value = "容器号，质检台")
    private String contCode;
    @ApiModelProperty(value = "运单号")
    private String expressNo;

    @ApiModelProperty(value = "状态码")
    private List<String> statusList;
    @ApiModelProperty(value = "出库单号")
    private String shipmentOrderCode;
    @ApiModelProperty(value = "上游单号--进销存(现有ERP单号,目前前端显示客户单号)")
    private String soNo;
    @ApiModelProperty(value = "包裹号")
    private String packageCode;
    @ApiModelProperty(value = "商品编码")
    private String skuCode;

    @ApiModelProperty(value = "商品批次")
    private String skuLotNo;

    @ApiModelProperty(value = "归位单")
    private ReturnOrderDTO returnOrderDTO;

}