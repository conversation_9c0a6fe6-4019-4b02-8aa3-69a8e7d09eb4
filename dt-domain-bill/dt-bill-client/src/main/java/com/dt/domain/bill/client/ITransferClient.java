package com.dt.domain.bill.client;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.domain.bill.bo.TransferPersistBO;
import com.dt.domain.bill.dto.TransferDTO;
import com.dt.domain.bill.param.TransferParam;

import java.util.List;

/**
 * 转移单
 * Created by nobody on 2020/12/28 17:55
 */
public interface ITransferClient {

    /**
     * 新增转移单
     * @param transferParam
     * @return
     */
    Result<Boolean> add(TransferParam transferParam);

    /**
     * 修改转移单
     * @param transferDTO
     * @return
     */
    Result<Boolean> update(TransferDTO transferDTO);
    
    Result<Boolean> update(TransferPersistBO transferBO);

    /**
     * 查找转移单
     * @param transferParam
     * @return
     */
    Result<TransferDTO> get(TransferParam transferParam);

    /**
     * 转移单分页
     * @param transferParam
     * @return
     */
    Result<Page<TransferDTO>> page(TransferParam transferParam);

    /**
     * 列表
     * @param transferParam
     * @return
     */
    Result<List<TransferDTO>> list(TransferParam transferParam);
}
