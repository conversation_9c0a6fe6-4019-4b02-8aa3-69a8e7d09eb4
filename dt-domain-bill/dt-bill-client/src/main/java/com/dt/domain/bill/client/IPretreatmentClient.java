package com.dt.domain.bill.client;

import com.dt.component.common.result.Result;
import com.dt.domain.bill.param.PretreatmentDetailParam;
import com.dt.domain.bill.param.PretreatmentParam;

import java.util.List;

public interface IPretreatmentClient {


    /**
     * 查询可预分配单据数量
     * @param param
     * @return
     */
    Result<List<Long>> getPretreatmentCount(PretreatmentParam param);

    /**
     * 根据商品信息和单据状态查询出库单单号列表
     * @param param
     * @return
     */
    Result<List<String>> getShipmentCodeList(PretreatmentDetailParam param);
}
