package com.dt.domain.bill.dto.sourceCode;

import com.dt.component.common.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;


/**
 * <p>
 * 出库溯源码表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "OutSourceCode对象", description = "出库溯源码表")
public class OutSourceCodeDTO extends BaseDTO  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    /**
     * 仓库编码
     */
    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    /**
     * 货主编码
     */
    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    /**
     * 溯源码
     */
    @ApiModelProperty(value = "溯源码")
    private String snCode;

    /**
     * 出库单号
     */
    @ApiModelProperty(value = "出库单号")
    private String shipmentOrderCode;

    /**
     * 包裹号
     */
    @ApiModelProperty(value = "包裹号")
    private String packageCode;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String poNo;

    /**
     * 客户单号
     */
    @ApiModelProperty(value = "客户单号")
    private String soNo;

    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    private BigDecimal qty;

    /**
     * 商品编码
     */
    @ApiModelProperty(value = "商品编码")
    private String skuCode;

    /**
     * 商品条码
     */
    @ApiModelProperty(value = "商品条码")
    private String upcCode;

    /**
     * 商品名
     */
    @ApiModelProperty(value = "商品名")
    private String skuName;

    /**
     * 运单号
     */
    @ApiModelProperty(value = "运单号")
    private String expressNo;

    /**
     * 快递公司编码
     */
    @ApiModelProperty(value = "快递公司编码")
    private String carrierCode;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String carrierName;

    /**
     * 出库时间
     */
    @ApiModelProperty(value = "出库时间")
    private Long outStockDate;

    @ApiModelProperty(value = "扫码类型")
    private String scanType;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "修改人")
    private String updatedBy;
}