package com.dt.domain.bill.client.material;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.domain.bill.dto.material.StructMaterialDTO;
import com.dt.domain.bill.param.material.StructMaterialParam;

import java.util.List;

/**
 * <p>
 * 包裹结构绑定包材 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-22
 */
public interface IStructMaterialClient {

    /**
     * 新增包裹结构绑定包材
     *
     * @param structMaterialDTO
     * @return
     */
    Result<Boolean> save(StructMaterialDTO structMaterialDTO);

    /**
     * 批量新增包裹结构绑定包材
     *
     * @param structMaterialDTOList
     * @return
     */
    Result<Boolean> saveBatch(List<StructMaterialDTO> structMaterialDTOList);

    /**
     * 修改包裹结构绑定包材
     *
     * ID | Code 二选一
     * @param structMaterialDTO
     * @return
     */
    Result<Boolean> modify(StructMaterialDTO structMaterialDTO);

    /**
     * 批量修改包裹结构绑定包材
     *
     * @param structMaterialDTOList
     * @return
     */
    Result<Boolean> modifyBatch(List<StructMaterialDTO> structMaterialDTOList);

    /**
     * 根据条件查询是否存在
     *
     * @param param
     * @return
     */
    Result<Boolean> checkExits(StructMaterialParam param);

    /**
     * 获取包裹结构绑定包材
     *
     * @param param
     * @return
     */
    Result<StructMaterialDTO> get(StructMaterialParam param);

    /**
     * 获取包裹结构绑定包材列表
     * @param param
     * @return
     */
    Result<List<StructMaterialDTO>> getList(StructMaterialParam param);

    /**
     * 分页获取包裹结构绑定包材
     *
     * @param param
     * @return
     */
    Result<Page<StructMaterialDTO>> getPage(StructMaterialParam param);

    /**
     * 功能描述:  删除
     * 创建时间:  2021/1/8 11:25 上午
     *
     * @param param:
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     */
    Result<Boolean> remove(StructMaterialParam param);

}

