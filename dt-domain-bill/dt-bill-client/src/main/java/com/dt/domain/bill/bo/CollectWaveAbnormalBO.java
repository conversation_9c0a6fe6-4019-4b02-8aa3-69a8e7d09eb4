package com.dt.domain.bill.bo;

import com.dt.domain.bill.dto.AbnormalOrderDTO;
import com.dt.domain.bill.dto.CollectWaveTaskDTO;
import com.dt.domain.bill.dto.PackageDTO;
import com.dt.domain.bill.dto.ReplenishTaskDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/11/13 15:02
 */
@Data
public class CollectWaveAbnormalBO implements Serializable {

    AbnormalOrderDTO abnormalOrderDTO;

    PackageDTO packageDTO;

//    CollectWaveTaskDTO collectWaveTaskDTO;

    @ApiModelProperty(value = "补货指引")
    List<ReplenishTaskDTO> replenishTaskDTOList;
    
}
