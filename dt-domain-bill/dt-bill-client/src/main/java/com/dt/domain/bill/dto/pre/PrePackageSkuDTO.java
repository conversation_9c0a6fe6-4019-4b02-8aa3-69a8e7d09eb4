package com.dt.domain.bill.dto.pre;

import com.dt.component.common.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 预包商品条码
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "PrePackageSku对象", description = "预包商品条码")
public class PrePackageSkuDTO extends BaseDTO  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    /**
     * 仓库编码
     */
    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    /**
     * 货主编码
     */
    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    /**
     * 预报条码
     */
    @ApiModelProperty(value = "预报条码")
    private String preUpcCode;

    /**
     * 预包名称
     */
    @ApiModelProperty(value = "预包名称")
    private String preUpcName;

    /**
     * 商品代码+数量(拼接升序MD5转小写)
     */
    @ApiModelProperty(value = "商品代码+数量(拼接升序MD5转小写)")
    private String preSkuStructure;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private String status;
    @ApiModelProperty(value = "档案状态")
    private String docStatus;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "修改人")
    private String updatedBy;
}