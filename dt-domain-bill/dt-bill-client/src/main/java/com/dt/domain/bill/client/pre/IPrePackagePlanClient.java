package com.dt.domain.bill.client.pre;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.domain.bill.bo.PrePackageContainerAndShelfBillBO;
import com.dt.domain.bill.bo.PrePackagePlanCommitAndShelfBillBO;
import com.dt.domain.bill.dto.pre.PrePackagePlanDTO;
import com.dt.domain.bill.param.pre.PrePackagePlanParam;

import java.util.List;

/**
 * <p>
 * 预包计划表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-25
 */
public interface IPrePackagePlanClient {

    /**
     * 新增预包计划表
     *
     * @param prePackagePlanDTO
     * @return
     */
    Result<Boolean> save(PrePackagePlanDTO prePackagePlanDTO);

    /**
     * 批量新增预包计划表
     *
     * @param prePackagePlanDTOList
     * @return
     */
    Result<Boolean> saveBatch(List<PrePackagePlanDTO> prePackagePlanDTOList);

    /**
     * 修改预包计划表
     *
     * ID | Code 二选一
     * @param prePackagePlanDTO
     * @return
     */
    Result<Boolean> modify(PrePackagePlanDTO prePackagePlanDTO);

    /**
     * 批量修改预包计划表
     *
     * @param prePackagePlanDTOList
     * @return
     */
    Result<Boolean> modifyBatch(List<PrePackagePlanDTO> prePackagePlanDTOList);

    /**
     * 根据条件查询是否存在
     *
     * @param param
     * @return
     */
    Result<Boolean> checkExits(PrePackagePlanParam param);

    /**
     * 获取预包计划表
     *
     * @param param
     * @return
     */
    Result<PrePackagePlanDTO> get(PrePackagePlanParam param);

    /**
     * 获取预包计划表列表
     * @param param
     * @return
     */
    Result<List<PrePackagePlanDTO>> getList(PrePackagePlanParam param);

    /**
     * 分页获取预包计划表
     *
     * @param param
     * @return
     */
    Result<Page<PrePackagePlanDTO>> getPage(PrePackagePlanParam param);

    /**
     * 功能描述:  删除
     * 创建时间:  2021/1/8 11:25 上午
     *
     * @param param:
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     */
    Result<Boolean> remove(PrePackagePlanParam param);
    /**
     * @param prePackagePlanDTO
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     * @describe:
     * @date 2023/6/26 15:06
     */
    Result<Boolean> commitModifyPlanAndAddDetail(PrePackagePlanDTO prePackagePlanDTO);

    Result<Boolean> commitModifyPlanAndAddShelf(PrePackagePlanCommitAndShelfBillBO prePackagePlanCommitAndShelfBillBO);

    Result<Boolean> commitModifyPlanAndAddPreShelf(PrePackageContainerAndShelfBillBO prePackageContainerAndShelfBillBO);
}

