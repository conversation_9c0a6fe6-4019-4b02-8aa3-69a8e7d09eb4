package com.dt.domain.bill.client;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.domain.bill.dto.HandoverDTO;
import com.dt.domain.bill.dto.HandoverDetailDTO;
import com.dt.domain.bill.param.HandoverBatchParam;
import com.dt.domain.bill.param.HandoverParam;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/10/14 19:43
 */
public interface IHandoverClient {

    /**
     * 新增交接单信息
     * @param param
     * @return
     */
    Result<Boolean> save(HandoverParam param);

    /**
     * 修改交接单信息
     * id | code | idList | codeList 四选一
     * @param param
     * @return
     */
    Result<Boolean> modify(HandoverParam param);

    /**
     * 修改
     * @param param
     * @return
     */
    Result<Boolean> modify(HandoverDTO param);

    Result<Boolean> modifyBatch(HandoverBatchParam param);

    /**
     * 根据条件查询是否存在
     * @param param
     * @return
     */
    Result<Boolean> checkExits(HandoverParam param);

    /**
     * 获取交接单信息
     * @param param
     * @return
     */
    Result<HandoverDTO> get(HandoverParam param);

    /**
     * 获取交接单信息
     * @param param
     * @return
     */
    Result<HandoverDTO> getDetail(HandoverParam param);

    /**
     * 获取交接单列表
     * @param param
     * @return
     */
    Result<List<HandoverDTO>> getList(HandoverParam param);
    Result<List<HandoverDTO>> getExportList(HandoverParam param);
    /**
     * 分页获取交接单
     * @param param
     * @return
     */
    Result<Page<HandoverDTO>> getPage(HandoverParam param);

    /**
     * 获取详情
     * @param param
     * @return
     */
    Result<List<HandoverDetailDTO>> getDetailList(HandoverParam param);

    Result<List<HandoverDetailDTO>> getAppointMultipleParam(HandoverParam param, List<String> tableFields);

    Result<Integer> getMaxLineSeq(String handoverCode);

    Result<Integer> getCountExport(HandoverParam handoverParam);
}
