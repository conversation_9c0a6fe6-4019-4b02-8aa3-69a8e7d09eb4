package com.dt.domain.bill.dto;

import com.dt.component.common.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="出库单", description="出库单")
public class ShipmentOrderDetailExtraJsonDTO extends BaseDTO  implements java.io.Serializable  {

    @ApiModelProperty(value = "拓传json")
    private List<ShipmentOrderDetailExtraJsonConsumableInfoDTO> consumablesMaterials;


}