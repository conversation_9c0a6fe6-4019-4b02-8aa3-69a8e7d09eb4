package com.dt.domain.bill.param;

import com.dt.domain.bill.dto.PickDTO;
import com.dt.domain.bill.dto.PickDetailDTO;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/10/14 19:56
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "对象", description = "")
public class PickBatchParam implements Serializable {
    private List<PickDTO> pickList;

    private List<PickDetailDTO> pickDetailList;
}
