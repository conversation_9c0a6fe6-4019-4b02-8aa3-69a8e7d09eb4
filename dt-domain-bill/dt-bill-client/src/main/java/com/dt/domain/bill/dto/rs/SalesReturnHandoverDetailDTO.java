package com.dt.domain.bill.dto.rs;

import com.dt.component.common.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 交接单详情
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "SalesReturnHandoverDetail对象", description = "交接单详情")
public class SalesReturnHandoverDetailDTO extends BaseDTO  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    /**
     * 交接单
     */
    @ApiModelProperty(value = "交接单")
    private String handoverNo;

    /**
     * 快递单号
     */
    @ApiModelProperty(value = "快递单号")
    private String expressNo;
//    @ApiModelProperty(value = "快递单号【不带前缀】")
//    private String innerExpressNo;

    /**
     * 销退单
     */
    @ApiModelProperty(value = "销退单")
    private String salesReturnOrderNo;

    /**
     * 1 是 2 否
     */
    @ApiModelProperty(value = "1 是 2 否")
    private Integer damage;

    /**
     * 1 是 2 否
     */
    @ApiModelProperty(value = "1 是 2 否")
    private Integer reject;
    @ApiModelProperty("拒收原因")
    private String rejectReason;
    @ApiModelProperty("拒收附件")
    private List<String> rejectImageList;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "修改人")
    private String updatedBy;

    @ApiModelProperty(value = "仓库")
    private String warehouseCode;

    @ApiModelProperty(value = "附加指令")
    private String additionalOrder;

    @ApiModelProperty(value = "拓展字段")
    private String extraJson;

    /**
     * 退货类型
     */
    @ApiModelProperty(value = "退货类型")
    private Integer returnType;

    @ApiModelProperty(value = "商家退回要求")
    private String returnRequire;
    private String returnRequireDesc;

    public SalesReturnHandoverDetailExtraDTO salesReturnHandoverDetailExtraDTO() {
        return SalesReturnHandoverDetailExtraDTO.fromJson(extraJson);
    }

    public void salesReturnHandoverDetailExtraDTO(SalesReturnHandoverDetailExtraDTO salesReturnHandoverDetailExtraDTO) {
        setExtraJson(salesReturnHandoverDetailExtraDTO.toJson());
    }
}