package com.dt.domain.bill.param.rs;

import com.dt.component.common.param.BaseSearchParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import java.math.BigDecimal;

/**
* <p>
    * 淘天用异常单
    * </p>
*
* <AUTHOR>
* @since 2024-03-13
*/
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="OpExceptionReview对象", description="淘天用异常单")
public class OpExceptionReviewParam extends BaseSearchParam  implements java.io.Serializable  {

private static final long serialVersionUID = 1L;

    /**
    * 货主编码
    */
    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    /**
    * 指令类型
    */
    @ApiModelProperty(value = "指令类型")
    private String instructionType;

    /**
    * 异常单号
    */
    @ApiModelProperty(value = "异常单号")
    private String abnormalOrderNo;

    /**
    * 收集人信息
    */
    @ApiModelProperty(value = "收集人信息")
    private String receiverInfo;

    /**
    * 扩展属性
    */
    @ApiModelProperty(value = "扩展属性")
    private String extendProps;

    /**
    * 处理明细
    */
    @ApiModelProperty(value = "处理明细")
    private String instructionLines;

@ApiModelProperty(value = "创建人")
private String createdBy;

@ApiModelProperty(value = "修改人")
private String updatedBy;
}