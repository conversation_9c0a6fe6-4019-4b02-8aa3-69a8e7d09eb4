package com.dt.domain.bill.fba.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * fba信息采集
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_fba_information")
@ApiModel(value="FbaInformation对象", description="fba信息采集")
public class FbaInformation extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "FBA/序列号")
    private String billNo;

    @ApiModelProperty(value = "FBA")
    private String fbaNo;

    @ApiModelProperty(value = "装箱序号")
    private String boxNo;

    @ApiModelProperty(value = "商品sku")
    private String skuCode;

    @ApiModelProperty(value = "是否扫描 NO 否 YES 是")
    private String scanType;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "拓展字段")
    private String extraJson;

    @ApiModelProperty(value = "复核时间")
    private Long checkTime;

    @ApiModelProperty(value = "柜号")
    private String cabinetCode;


}
