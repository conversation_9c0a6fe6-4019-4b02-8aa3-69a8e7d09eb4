package com.dt.domain.bill.handover.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dt.domain.bill.handover.entity.Handover;
import com.dt.domain.bill.handover.mapper.HandoverMapper;
import com.dt.domain.bill.handover.service.IHandoverService;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR> xy
 * @since 2020-10-12
 */
@Service
public class HandoverServiceImpl extends ServiceImpl<HandoverMapper, Handover> implements IHandoverService {

}
