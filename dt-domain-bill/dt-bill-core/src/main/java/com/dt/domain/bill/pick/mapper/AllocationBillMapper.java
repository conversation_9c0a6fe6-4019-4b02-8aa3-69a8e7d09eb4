package com.dt.domain.bill.pick.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dt.domain.bill.pick.entity.AllocationBill;
import com.dt.domain.bill.pick.entity.AllocationBillCoreParam;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/11/24 15:33
 */
public interface AllocationBillMapper  extends BaseMapper {
    /**
     * 拣选区库存到拣选暂存位
     * @param convert
     * @return
     */
    List<AllocationBill> getPackGroupByLocationAndSkuLotNo(AllocationBillCoreParam convert);
}
