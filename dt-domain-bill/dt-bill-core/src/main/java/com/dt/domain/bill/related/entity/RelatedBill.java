package com.dt.domain.bill.related.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 单据关联单号
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_related_bill")
@ApiModel(value="RelatedBill对象", description="单据关联单号")
public class RelatedBill extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    @ApiModelProperty(value = "单据号")
    private String billNo;

    @ApiModelProperty(value = "关联单号")
    private String relatedNo;

    @ApiModelProperty(value = "类型枚举")
    private String type;


}