package com.dt.domain.bill.finance.service.impl;

import com.dt.domain.bill.finance.entity.FinanceDisposalMoveDetail;
import com.dt.domain.bill.finance.mapper.FinanceDisposalMoveDetailMapper;
import com.dt.domain.bill.finance.service.IFinanceDisposalMoveDetailService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 金融监管处置单明细 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-13
 */
@Service
public class FinanceDisposalMoveDetailServiceImpl extends ServiceImpl<FinanceDisposalMoveDetailMapper, FinanceDisposalMoveDetail> implements IFinanceDisposalMoveDetailService {

}
