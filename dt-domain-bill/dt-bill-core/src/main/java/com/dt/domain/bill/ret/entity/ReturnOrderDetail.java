package com.dt.domain.bill.ret.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> x<PERSON>
 * @since 2020-11-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_return_order_detail")
@ApiModel(value="ReturnOrderDetail对象", description="")
public class ReturnOrderDetail extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

//    @ApiModelProperty(value = "仓库编码 取值仓库档案")
//    private String warehouseCode;

    @ApiModelProperty(value = "货主编码 取值货主档案")
    private String cargoCode;

    @ApiModelProperty(value = "归位单号")
    private String retOrderCode;

    @ApiModelProperty(value = "快递公司编码")
    private String carrierCode;

    @ApiModelProperty(value = "快递公司名称")
    private String carrierName;

    @ApiModelProperty(value = "容器号")
    private String contCode;

    @ApiModelProperty(value = "运单号")
    private String expressNo;

    @ApiModelProperty(value = "出库单号")
    private String shipmentOrderCode;

    @ApiModelProperty(value = "上游单号--进销存(现有ERP单号,目前前端显示客户单号)")
    private String soNo;

    @ApiModelProperty(value = "包裹号")
    private String packageCode;

    @ApiModelProperty(value = "商品条码")
    private String upcCode;

    @ApiModelProperty(value = "商品代码")
    private String skuCode;

    @ApiModelProperty(value = "商品名称")
    private String skuName;

    private BigDecimal expReturnQty;

    @ApiModelProperty(value = "已归位上架数量")
    private BigDecimal completeOnShelfQty;

    @ApiModelProperty(value = "商品属性(正品/次品）")
    private String skuQuality;

    @ApiModelProperty(value = "批次号")
    private String skuLotNo;

    @ApiModelProperty(value = "状态")
    private String status;


}