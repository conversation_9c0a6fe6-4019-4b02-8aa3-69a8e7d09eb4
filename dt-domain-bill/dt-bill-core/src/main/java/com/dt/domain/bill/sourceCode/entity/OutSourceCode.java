package com.dt.domain.bill.sourceCode.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 * 出库溯源码表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_out_source_code")
@ApiModel(value = "OutSourceCode对象", description = "出库溯源码表")
public class OutSourceCode extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    @ApiModelProperty(value = "溯源码")
    private String snCode;

    @ApiModelProperty(value = "出库单号")
    private String shipmentOrderCode;

    @ApiModelProperty(value = "包裹号")
    private String packageCode;

    private String poNo;

    @ApiModelProperty(value = "客户单号")
    private String soNo;

    @ApiModelProperty(value = "数量")
    private BigDecimal qty;

    @ApiModelProperty(value = "商品编码")
    private String skuCode;

    @ApiModelProperty(value = "商品条码")
    private String upcCode;

    @ApiModelProperty(value = "商品名")
    private String skuName;

    @ApiModelProperty(value = "运单号")
    private String expressNo;

    @ApiModelProperty(value = "快递公司编码")
    private String carrierCode;

    private String carrierName;

    @ApiModelProperty(value = "出库时间")
    private Long outStockDate;

    @ApiModelProperty(value = "扫码类型")
    private String scanType;


}