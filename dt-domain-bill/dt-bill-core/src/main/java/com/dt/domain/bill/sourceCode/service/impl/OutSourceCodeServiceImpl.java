package com.dt.domain.bill.sourceCode.service.impl;

import com.dt.domain.bill.sourceCode.entity.OutSourceCode;
import com.dt.domain.bill.sourceCode.mapper.OutSourceCodeMapper;
import com.dt.domain.bill.sourceCode.service.IOutSourceCodeService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 出库溯源码表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-25
 */
@Service
public class OutSourceCodeServiceImpl extends ServiceImpl<OutSourceCodeMapper, OutSourceCode> implements IOutSourceCodeService {

}
