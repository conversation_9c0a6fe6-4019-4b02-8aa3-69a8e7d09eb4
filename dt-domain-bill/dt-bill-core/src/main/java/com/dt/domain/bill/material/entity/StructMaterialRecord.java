package com.dt.domain.bill.material.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 包裹结构使用包材记录
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_struct_material_record")
@ApiModel(value="StructMaterialRecord对象", description="包裹结构使用包材记录")
public class StructMaterialRecord extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    @ApiModelProperty(value = "包裹结构")
    private String struct;

    @ApiModelProperty(value = "包裹结构")
    private String structSignature;

    @ApiModelProperty(value = "是否4PL")
    private Integer is4pl;

    @ApiModelProperty(value = "固定包材")
    private String materialCode;

    @ApiModelProperty(value = "使用数量")
    private Integer usageQuantity;


}