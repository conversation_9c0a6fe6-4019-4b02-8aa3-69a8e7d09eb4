package com.dt.domain.bill.material.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 耗材领用表明细
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_receive_material_detail")
@ApiModel(value="ReceiveMaterialDetail对象", description="耗材领用表明细")
public class ReceiveMaterialDetail extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;
 
    @ApiModelProperty(value = "领用单据号唯一，不允许修改")
    private String receiveMaterialCode;

    @ApiModelProperty(value = "商品条形码")
    private String upcCode;

    @ApiModelProperty(value = "包耗材条码名称")
    private String upcName;

    @ApiModelProperty(value = "计划领用数量")
    private BigDecimal expQty;

    @ApiModelProperty(value = "实际领用总数")
    private BigDecimal receiveQty;

    @ApiModelProperty(value = "包耗材属性")
    private String upcType;

    @ApiModelProperty(value = "规格单位")
    private String upcUnit;

    @ApiModelProperty(value = "领用成本价")
    private BigDecimal costPrice;

    @ApiModelProperty(value = "备注")
    private String remark;


}