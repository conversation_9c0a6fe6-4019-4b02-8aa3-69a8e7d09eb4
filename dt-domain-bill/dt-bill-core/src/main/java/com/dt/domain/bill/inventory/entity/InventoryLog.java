package com.dt.domain.bill.inventory.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 盘点相关日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_inventory_log")
@ApiModel(value="InventoryLog对象", description="盘点相关日志表")
public class InventoryLog extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "单据编码")
    private String code;

    @ApiModelProperty(value = "单据类型")
    private String type;

    @ApiModelProperty(value = "操作人")
    private String opBy;

    @ApiModelProperty(value = "操作时间")
    private Long opDate;

    @ApiModelProperty(value = "原始报文")
    private String opRemark;

    @ApiModelProperty(value = "操作内容")
    private String opContent;


}