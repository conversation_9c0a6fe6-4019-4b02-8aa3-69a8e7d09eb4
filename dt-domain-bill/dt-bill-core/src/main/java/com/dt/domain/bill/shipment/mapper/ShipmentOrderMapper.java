package com.dt.domain.bill.shipment.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.dt.domain.bill.replenish.entity.ReplenishTaskStatistics;
import com.dt.domain.bill.shipment.entity.ShipmentOrder;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 出库单 Mapper 接口
 * </p>
 *
 * <AUTHOR> xy
 * @since 2020-10-14
 */
public interface ShipmentOrderMapper extends BaseMapper<ShipmentOrder> {

    /**
     * 功能描述:  初始化出库单日汇总数据
     * 创建时间:  2021/3/2 4:53 下午
     *
     * @return java.util.List<com.dt.domain.bill.shipment.entity.ShipmentOrder>
     * <AUTHOR>
     */
    @Select("SELECT\n" +
            "\tdt_shipment_order.warehouse_code AS warehouseCode,\n" +
            "\tdt_shipment_order.cargo_code AS cargoCode,\n" +
            "\tdt_shipment_order.business_type AS businessType,\n" +
            "\tdt_shipment_order.created_time AS createdTime,\n" +
            "\tdt_shipment_order.`status` AS `status`,\n" +
            "\tcount( 0 ) AS version \n" +
            "FROM\n" +
            "\tdt_shipment_order \n" +
            "GROUP BY\n" +
            "\tdt_shipment_order.warehouse_code,\n" +
            "\tdt_shipment_order.cargo_code,\n" +
            "\tdt_shipment_order.business_type,\n" +
            "\tFROM_UNIXTIME( dt_shipment_order.created_time / 1000, '%Y-%m-%d' ),\n" +
            "\tdt_shipment_order.`status` \n" +
            "ORDER BY\n" +
            "\tdt_shipment_order.created_time;")
    List<ShipmentOrder> initCount();


}
