package com.dt.domain.bill.pick.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 * 拣选分配表
 * </p>
 *
 * <AUTHOR> xy
 * @since 2020-10-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_allocation_order")
@ApiModel(value="AllocationOrder对象", description="拣选分配表")
public class AllocationOrder extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "分配单编码")
    private String allocationOrderCode;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    @ApiModelProperty(value = "波次号")
    private String waveCode;

    @ApiModelProperty(value = "包裹编码")
    private String packageCode;

    @ApiModelProperty(value = "出库单号")
    private String shipmentOrderCode;

    @ApiModelProperty(value = "拣选单号")
    private String pickCode;

    @ApiModelProperty(value = "包裹明细")
    private Long pUid;

    @ApiModelProperty(value = "UUID")
    private String packUid;

    @ApiModelProperty(value = "库区编码")
    private String zoneCode;

    @ApiModelProperty(value = "巷道编码")
    private String tunnelCode;

    @ApiModelProperty(value = "库位编码")
    private String locationCode;

    @ApiModelProperty(value = "库位拣货序号")
    private Long pickSeq;

    @ApiModelProperty(value = "取值分配的货品批次号")
    private String skuLotNo;

    @ApiModelProperty(value = "商品编码")
    private String skuCode;

    @ApiModelProperty(value = "计划数量")
    private BigDecimal expQty;

    @ApiModelProperty(value = "实发数量")
    private BigDecimal realQty;

    @ApiModelProperty(value = "拣选数量")
    private BigDecimal pickQty;

    @ApiModelProperty(value = "分拣选数量")
    private BigDecimal splitQty;

    @ApiModelProperty(value = "状态码")
    private String status;

    @ApiModelProperty(value = "10:原始明细,20:预包商品,30:预包剩余商品明细,40:预包商品对应子商品 AllocationIsPreEnum")
    private String isPre;

}