package com.dt.domain.bill.rent.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 仓租商品体积
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_warehouse_rent_sku_volume")
@ApiModel(value="WarehouseRentSkuVolume对象", description="仓租商品体积")
public class WarehouseRentSkuVolume extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    @ApiModelProperty(value = "上游单号")
    private String billNo;

    @ApiModelProperty(value = "商品编码")
    private String skuCode;

    @ApiModelProperty(value = "状态")
    private String status;

    @ApiModelProperty(value = "长(cm) 默认为0，保留一位小数")
    private BigDecimal length;

    @ApiModelProperty(value = "宽(cm) 默认为0，保留一位小数")
    private BigDecimal width;

    @ApiModelProperty(value = "高(cm) 默认为0，保留一位小数")
    private BigDecimal height;

    @ApiModelProperty(value = "拓传字段json")
    private String extraJson;

    @ApiModelProperty(value = "备注")
    private String remark;


}