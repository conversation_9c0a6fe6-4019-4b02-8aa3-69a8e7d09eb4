package com.dt.domain.bill.message.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 待办事项
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_to_do_message")
@ApiModel(value="ToDoMessage对象", description="待办事项")
public class ToDoMessage extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "待办事项编号")
    private String billNo;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    @ApiModelProperty(value = "业务编码")
    private String billCode;

    @ApiModelProperty(value = "当前业务编码下唯一编码")
    private String serialNo;

    @ApiModelProperty(value = "业务类型")
    private String businessType;

    @ApiModelProperty(value = "处理状态")
    private String status;

    @ApiModelProperty(value = "业务下发时间 (时间戳)")
    private Long insertTime;

    @ApiModelProperty(value = "处理时间 (时间戳)")
    private Long opTime;

    @ApiModelProperty(value = "操作人")
    private String opBy;

    @ApiModelProperty(value = "业务描述")
    private String remark;

    @ApiModelProperty(value = "拓展字段")
    private String extraJson;


}
