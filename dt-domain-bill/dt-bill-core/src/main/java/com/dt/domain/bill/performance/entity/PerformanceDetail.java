package com.dt.domain.bill.performance.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 * 人员操作详情
 * </p>
 *
 * <AUTHOR>
 * @since 2021-02-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_performance_detail")
@ApiModel(value="PerformanceDetail对象", description="人员操作详情")
public class PerformanceDetail extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    @ApiModelProperty(value = "员工")
    private String worker;

    @ApiModelProperty(value = "操作类型")
    private String type;

    @ApiModelProperty(value = "时间")
    private Long workTime;

    @ApiModelProperty(value = "日期")
    private Long workDate;

    @ApiModelProperty(value = "单据编号")
    private String billNo;

    private String detailBillNo;

    @ApiModelProperty(value = "统计目标")
    private String statisticsTarget;

    @ApiModelProperty(value = "数量")
    private BigDecimal quantity;

    @ApiModelProperty(value = "拣选单类型")
    private String pickType;

    @ApiModelProperty(value = "散单还是批量")
    private String singleOrBatch;
}