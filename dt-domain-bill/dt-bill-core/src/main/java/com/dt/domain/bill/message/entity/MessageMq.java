package com.dt.domain.bill.message.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 消息信息
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_message_mq")
@ApiModel(value="MessageMq对象", description="消息信息")
public class MessageMq extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    @ApiModelProperty(value = "单据号")
    private String billNo;

    @ApiModelProperty(value = "单据类型")
    private String billType;

    @ApiModelProperty(value = "操作类型")
    private String operationType;

    @ApiModelProperty(value = "状态码")
    private String status;

    @ApiModelProperty(value = "备注")
    private String remark;

    private String batchSerialNo;
}