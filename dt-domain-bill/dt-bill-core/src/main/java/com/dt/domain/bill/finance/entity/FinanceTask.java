package com.dt.domain.bill.finance.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 供应链金融任务
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_finance_task")
@ApiModel(value="FinanceTask对象", description="供应链金融任务")
public class FinanceTask extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    @ApiModelProperty(value = "单据号")
    private String billNo;

    @ApiModelProperty(value = "单据类型")
    private String billType;

    @ApiModelProperty(value = "操作类型")
    private String operationType;

    @ApiModelProperty(value = "状态码")
    private String status;

    @ApiModelProperty(value = "重试次数")
    private Integer retryNum;

    @ApiModelProperty(value = "备注")
    private String remark;


}