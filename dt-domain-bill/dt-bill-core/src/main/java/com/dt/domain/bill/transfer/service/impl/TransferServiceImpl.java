package com.dt.domain.bill.transfer.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dt.domain.bill.transfer.entity.Transfer;
import com.dt.domain.bill.transfer.mapper.TransferMapper;
import com.dt.domain.bill.transfer.service.ITransferService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 库存转移 服务实现类
 * </p>
 *
 * <AUTHOR> xy
 * @since 2020-09-11
 */
@Service
public class TransferServiceImpl extends ServiceImpl<TransferMapper, Transfer> implements ITransferService {

}
