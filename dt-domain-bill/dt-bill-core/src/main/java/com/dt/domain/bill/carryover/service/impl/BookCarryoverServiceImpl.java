package com.dt.domain.bill.carryover.service.impl;

import com.dt.domain.bill.carryover.entity.BookCarryover;
import com.dt.domain.bill.carryover.mapper.BookCarryoverMapper;
import com.dt.domain.bill.carryover.service.IBookCarryoverService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 账册结转单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-19
 */
@Service
public class BookCarryoverServiceImpl extends ServiceImpl<BookCarryoverMapper, BookCarryover> implements IBookCarryoverService {

}
