package com.dt.domain.bill.handover.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dt.domain.bill.handover.entity.HandoverDetail;
import com.dt.domain.bill.handover.mapper.HandoverDetailMapper;
import com.dt.domain.bill.handover.service.IHandoverDetailService;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR> xy
 * @since 2020-10-12
 */
@Service
public class HandoverDetailServiceImpl extends ServiceImpl<HandoverDetailMapper, HandoverDetail> implements IHandoverDetailService {


}
