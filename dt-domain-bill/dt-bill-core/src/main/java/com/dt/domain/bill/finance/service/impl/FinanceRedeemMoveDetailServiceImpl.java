package com.dt.domain.bill.finance.service.impl;

import com.dt.domain.bill.finance.entity.FinanceRedeemMoveDetail;
import com.dt.domain.bill.finance.mapper.FinanceRedeemMoveDetailMapper;
import com.dt.domain.bill.finance.service.IFinanceRedeemMoveDetailService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 金融监管赎回单移位明细 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-13
 */
@Service
public class FinanceRedeemMoveDetailServiceImpl extends ServiceImpl<FinanceRedeemMoveDetailMapper, FinanceRedeemMoveDetail> implements IFinanceRedeemMoveDetailService {

}
