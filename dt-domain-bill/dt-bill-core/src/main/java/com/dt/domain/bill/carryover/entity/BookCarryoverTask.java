package com.dt.domain.bill.carryover.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 账册结转单生成任务
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_book_carryover_task")
@ApiModel(value="BookCarryoverTask对象", description="账册结转单生成任务")
public class BookCarryoverTask extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "任务编码")
    private String taskCode;

    @ApiModelProperty(value = "内部使用，控制结转单任务生成")
    private String controlContext;

    @ApiModelProperty(value = "统计开始时间")
    private Long statisticStartTime;

    @ApiModelProperty(value = "统计结束时间")
    private Long statisticEndTime;

    @ApiModelProperty(value = "BookCarryoverTaskStatusEnum")
    private String status;


}