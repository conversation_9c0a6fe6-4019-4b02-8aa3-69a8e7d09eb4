package com.dt.domain.bill.receipt.service.impl;

import com.dt.domain.bill.receipt.entity.ReceiptBillRecord;
import com.dt.domain.bill.receipt.mapper.ReceiptBillRecordMapper;
import com.dt.domain.bill.receipt.service.IReceiptBillRecordService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 收货凭据表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-18
 */
@Service
public class ReceiptBillRecordServiceImpl extends ServiceImpl<ReceiptBillRecordMapper, ReceiptBillRecord> implements IReceiptBillRecordService {

}
