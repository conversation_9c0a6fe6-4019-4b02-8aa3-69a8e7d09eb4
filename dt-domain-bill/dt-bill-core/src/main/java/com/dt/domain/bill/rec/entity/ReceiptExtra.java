package com.dt.domain.bill.rec.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 收货多货区批次
 * </p>
 *
 * <AUTHOR>
 * @since 2021-02-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_receipt_extra")
@ApiModel(value="ReceiptExtra对象", description="收货多货区批次")
public class ReceiptExtra extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    @ApiModelProperty(value = "作业批次号 唯一")
    private String recExtraId;

    @ApiModelProperty(value = "理货报告编码")
    private String tallyCode;

    @ApiModelProperty(value = "到货通知单号")
    private String asnId;

    @ApiModelProperty(value = "客户单号")
    private String poNo;

    @ApiModelProperty(value = "上游单号")
    private String soNo;

    @ApiModelProperty(value = "业务类型")
    private String type;

    @ApiModelProperty(value = "单据状态")
    private String status;

    @ApiModelProperty(value = "容器号")
    private String contCode;

    @ApiModelProperty(value = "品种数")
    private Integer recSkuType;

    @ApiModelProperty(value = "商品数")
    private BigDecimal recSkuQty;

    @ApiModelProperty(value = "单位")
    private String packageUnitCode;

    @ApiModelProperty(value = "上架完成时间")
    private Long completeShelfDate;

    @ApiModelProperty(value = "收货类型")
    private String receiptType;

    @ApiModelProperty(value = "sku质量")
    private String skuQuality;

    @ApiModelProperty(value = "收货方式 字典组：REC_MODE")
    private String recFlag;

    @ApiModelProperty(value = "质检台号")
    private String benchCode;

    private Integer notifyStatus;

    @ApiModelProperty(value = "通知时间")
    private Long notifyTime;

    @ApiModelProperty(value = "回执标志字符串")
    private String backFlag;


}