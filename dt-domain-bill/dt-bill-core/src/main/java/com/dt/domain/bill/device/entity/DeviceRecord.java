package com.dt.domain.bill.device.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 设备数据回传信息
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_device_record")
@ApiModel(value="DeviceRecord对象", description="设备数据回传信息")
public class DeviceRecord extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "设备事件号，系统生成")
    private String deviceEventNo;

    @ApiModelProperty(value = "设备编号")
    private String deviceCode;

    @ApiModelProperty(value = "第三方设备编号")
    private String thirdDeviceCode;

    @ApiModelProperty(value = "设备采集时间")
    private String devTime;

    @ApiModelProperty(value = "设备名称")
    private String devName;

    @ApiModelProperty(value = "采集温度")
    private String temperature;

    @ApiModelProperty(value = "采集湿度")
    private String humidity;

    @ApiModelProperty(value = "回传通知状态")
    private Integer notifyStatus;

    private Integer notifyCount;

    @ApiModelProperty(value = "回传通知时间")
    private Long notifyTime;

    @ApiModelProperty(value = "拓展单号")
    private String extraNo;

    @ApiModelProperty(value = "拓展字段")
    private String extraJson;

    @ApiModelProperty(value = "备注")
    private String remark;


}