package com.dt.domain.bill.asn.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 到货通知单-收货方
 * </p>
 *
 * <AUTHOR> x<PERSON>
 * @since 2020-09-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_asn_receiver")
@ApiModel(value="AsnReceiver对象", description="到货通知单-收货方")
public class AsnReceiver extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

//    @ApiModelProperty(value = "仓库编码")
//    private String warehouseCode;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    @ApiModelProperty(value = "到货通知单号")
    private String asnId;

    @ApiModelProperty(value = "收货方名称")
    private String custName;

    @ApiModelProperty(value = "收货人")
    private String custLinkName;

    @ApiModelProperty(value = "联系电话")
    private String custPhone;

    @ApiModelProperty(value = "国家")
    private String custCountry;

    @ApiModelProperty(value = "省份")
    private String custProvince;

    @ApiModelProperty(value = "城市")
    private String custCity;

    @ApiModelProperty(value = "区县")
    private String custDistrict;

    @ApiModelProperty(value = "收货详细地址")
    private String custDetail;

    @ApiModelProperty(value = "状态码 ")
    private Integer status;



}