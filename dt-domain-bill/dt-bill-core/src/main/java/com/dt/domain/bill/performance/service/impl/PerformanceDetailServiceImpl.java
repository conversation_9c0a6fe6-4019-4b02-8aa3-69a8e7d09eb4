package com.dt.domain.bill.performance.service.impl;

import com.dt.domain.bill.performance.entity.PerformanceDetail;
import com.dt.domain.bill.performance.mapper.PerformanceDetailMapper;
import com.dt.domain.bill.performance.service.IPerformanceDetailService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 人员操作详情 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-02-25
 */
@Service
public class PerformanceDetailServiceImpl extends ServiceImpl<PerformanceDetailMapper, PerformanceDetail> implements IPerformanceDetailService {

}
