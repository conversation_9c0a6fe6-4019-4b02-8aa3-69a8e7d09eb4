package com.dt.domain.bill.shipment.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dt.domain.bill.replenish.entity.ReplenishTask;
import com.dt.domain.bill.shipment.entity.ShipmentOrder;
import com.dt.domain.bill.shipment.mapper.ShipmentOrderMapper;
import com.dt.domain.bill.shipment.service.IShipmentOrderService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 出库单 服务实现类
 * </p>
 *
 * <AUTHOR> xy
 * @since 2020-10-14
 */
@Service
public class ShipmentOrderServiceImpl extends ServiceImpl<ShipmentOrderMapper, ShipmentOrder> implements IShipmentOrderService {

    @Override
    public List<ShipmentOrder> initCount() {
       return baseMapper.initCount();
    }

}
