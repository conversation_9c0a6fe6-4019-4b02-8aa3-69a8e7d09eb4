package com.dt.domain.bill.shipment.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dt.domain.bill.shipment.entity.ShipmentOrderLog;
import com.dt.domain.bill.shipment.mapper.ShipmentOrderLogMapper;
import com.dt.domain.bill.shipment.service.IShipmentOrderLogService;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR> xy
 * @since 2020-10-14
 */
@Service
public class ShipmentOrderLogServiceImpl extends ServiceImpl<ShipmentOrderLogMapper, ShipmentOrderLog> implements IShipmentOrderLogService {

}
