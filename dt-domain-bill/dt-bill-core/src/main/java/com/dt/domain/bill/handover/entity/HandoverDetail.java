package com.dt.domain.bill.handover.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> xy
 * @since 2020-10-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_handover_detail")
@ApiModel(value="HandoverDetail对象", description="交接单明细")
public class HandoverDetail extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

//    @ApiModelProperty(value = "仓库编码")
//    private String warehouseCode;

    @ApiModelProperty(value = "包裹顺序号")
    private Integer lineSeq;

    @ApiModelProperty(value = "包裹单号")
    private String packageCode;

    @ApiModelProperty(value = "交接单号")
    private String handoverCode;

    @ApiModelProperty(value = "承运商编码")
    private String carrierCode;

    @ApiModelProperty(value = "包裹重量")
    private BigDecimal packageWeight;

    @ApiModelProperty(value = "出库单号")
    private String shipmentOrderCode;

    @ApiModelProperty(value = "快递单号")
    private String expressNo;
    @ApiModelProperty(value = "C端单号-(客户原始单号)")
    private String poNo;

    @ApiModelProperty(value = "上游单号--进销存(现有ERP单号,目前前端显示客户单号)")
    private String soNo;


}