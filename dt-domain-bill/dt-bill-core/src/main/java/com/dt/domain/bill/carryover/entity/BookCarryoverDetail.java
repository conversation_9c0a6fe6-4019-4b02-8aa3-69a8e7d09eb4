package com.dt.domain.bill.carryover.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 账册结转单明细
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_book_carryover_detail")
@ApiModel(value="BookCarryoverDetail对象", description="账册结转单明细")
public class BookCarryoverDetail extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    @ApiModelProperty(value = "任务编码")
    private String taskCode;

    private String sourceType;

    @ApiModelProperty(value = "结转单号")
    private String carryoverCode;

    @ApiModelProperty(value = "商品编码")
    private String skuCode;

    @ApiModelProperty(value = "统一料号")
    private String itemCode;

    @ApiModelProperty(value = "商品条形码")
    private String upcCode;

    @ApiModelProperty(value = "商品名")
    private String skuName;

    @ApiModelProperty(value = "变动数量")
    private BigDecimal changeQty;

    @ApiModelProperty(value = "来源企业")
    private String originEnterprise;

    @ApiModelProperty(value = "目标企业")
    private String targetEnterprise;

    @ApiModelProperty(value = "来源库区")
    private String originZoneCode;

    @ApiModelProperty(value = "目标库区")
    private String targetZoneCode;


}