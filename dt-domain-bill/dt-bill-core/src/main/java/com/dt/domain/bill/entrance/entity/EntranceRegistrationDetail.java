package com.dt.domain.bill.entrance.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 * 一线入境到货登记
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_entrance_registration_detail")
@ApiModel(value="EntranceRegistrationDetail对象", description="一线入境到货登记")
public class EntranceRegistrationDetail extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "货主")
    private String cargoName;

    @ApiModelProperty(value = "运输方式")
    private String transportType;

    @ApiModelProperty(value = "发货地")
    private String dispatchPlace;

    @ApiModelProperty(value = "货代公司")
    private String forwardCompany;

    @ApiModelProperty(value = "提单号")
    private String ladingNo;

    @ApiModelProperty(value = "到货通知单")
    private String asnId;

    @ApiModelProperty(value = "标箱")
    private String teu;

    @ApiModelProperty(value = "集装箱号")
    private String containerNo;
    

    @ApiModelProperty(value = "托数")
    private Integer padCount;

    @ApiModelProperty(value = "核注清单编号")
    private String checkListNo;

    @ApiModelProperty(value = "报关单号")
    private String declarationNo;

    @ApiModelProperty(value = "报关日期")
    private Long declarationDate;

    @ApiModelProperty(value = "品名")
    private String goodsName;

    @ApiModelProperty(value = "类目")
    private String category;

    @ApiModelProperty(value = "预计到货数量")
    private Integer estimatedDeliveredQuantity;

    @ApiModelProperty(value = "预计到货毛重(KG)")
    private BigDecimal estimatedGrossWeight;

    @ApiModelProperty(value = "预计到港日期")
    private Long estimatedArriveDate;

    @ApiModelProperty(value = "实际到港日期")
    private Long arriveDate;

    @ApiModelProperty(value = "到货港口/机场")
    private String arrivePort;

    @ApiModelProperty(value = "预计到库日期")
    private Long estimatedArriveWmsDate;

    @ApiModelProperty(value = "车辆数量")
    private Integer carCount;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "状态")
    private String status;

    @ApiModelProperty(value = "卸货时间")
    private Long dischargeTime;

    @ApiModelProperty(value = "收货时间")
    private Long receiptTime;

    @ApiModelProperty(value = "库位编码")
    private String locationCode;
}