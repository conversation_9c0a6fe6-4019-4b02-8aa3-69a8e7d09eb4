package com.dt.domain.bill.trajectory.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dt.domain.bill.trajectory.entity.Trajectory;
import com.dt.domain.bill.trajectory.mapper.TrajectoryMapper;
import com.dt.domain.bill.trajectory.service.ITrajectoryService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 线路/轨迹流向管理 服务实现类
 * </p>
 *
 * <AUTHOR> xy
 * @since 2020-09-11
 */
@Service
public class TrajectoryServiceImpl extends ServiceImpl<TrajectoryMapper, Trajectory> implements ITrajectoryService {

}
