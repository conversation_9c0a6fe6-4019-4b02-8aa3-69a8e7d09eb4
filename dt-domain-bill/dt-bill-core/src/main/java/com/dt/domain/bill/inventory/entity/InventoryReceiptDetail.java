package com.dt.domain.bill.inventory.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 盘点单明细
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_inventory_receipt_detail")
@ApiModel(value="InventoryReceiptDetail对象", description="盘点单明细")
public class InventoryReceiptDetail extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "盘点单据编码")
    private String inventoryReceiptCode;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    @ApiModelProperty(value = "商品编码")
    private String skuCode;

    @ApiModelProperty(value = "商品条码")
    private String upcCode;

    @ApiModelProperty(value = "外部批次编码")
    private String externalSkuLotNo;

    @ApiModelProperty(value = "商品属性")
    private String skuQuality;

    @ApiModelProperty(value = "库位编码")
    private String locationCode;

    @ApiModelProperty(value = "一盘数")
    private Integer firstPlateNum;

    @ApiModelProperty(value = "一盘账面数")
    private Integer firstPlateAccountNum;

    @ApiModelProperty(value = "一盘人")
    private String firstPlatePerson;

    @ApiModelProperty(value = "二盘数")
    private Integer secondPlateNum;

    @ApiModelProperty(value = "二盘账面数")
    private Integer secondPlateAccountNum;

    @ApiModelProperty(value = "二盘人")
    private String secondPlatePerson;

    @ApiModelProperty(value = "三盘数")
    private Integer thirdPlateNum;

    @ApiModelProperty(value = "三盘账面数")
    private Integer thirdPlateAccountNum;

    @ApiModelProperty(value = "三盘人")
    private String thirdPlatePerson;

    @ApiModelProperty(value = "差异数")
    private Integer differenceNum;

    @ApiModelProperty(value = "状态")
    private String status;

    @ApiModelProperty(value = "最终差异数")
    private Integer finalDifferenceNum;

    @ApiModelProperty(value = "生产日期")
    private String manufDate;

    @ApiModelProperty(value = "失效日期")
    private String expireDate;

    /**
     * 最近一次盘点时间
     */
    private Long latestInventoryTime;

    @ApiModelProperty(value = "批次ID")
    private String skuLotNo;

    @ApiModelProperty(value = "生产批次号")
    private String productionNo;

    @ApiModelProperty(value = "入库日期")
    private String receiveDate;

    @ApiModelProperty(value = "托盘号")
    private String palletCode;

    @ApiModelProperty(value = "箱码")
    private String boxCode;

    @ApiModelProperty(value = "残次等级")
    private String inventoryType;
}