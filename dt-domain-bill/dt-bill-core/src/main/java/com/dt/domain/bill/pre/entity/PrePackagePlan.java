package com.dt.domain.bill.pre.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 * 预包计划表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_pre_package_plan")
@ApiModel(value="PrePackagePlan对象", description="预包计划表")
public class PrePackagePlan extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    @ApiModelProperty(value = "预包计划号")
    private String prePlanCode;

    @ApiModelProperty(value = "预报条码")
    private String preUpcCode;

    @ApiModelProperty(value = "业务类型(组包,预包)")
    private String type;

    @ApiModelProperty(value = "计划数量 默认:0")
    private BigDecimal planQty;

    @ApiModelProperty(value = "完成数量 默认:0")
    private BigDecimal completeQty;

    @ApiModelProperty(value = "包材条码")
    private String packMaterialUpcCode;

    @ApiModelProperty(value = "预包计划批次")
    private String preLotNo;

    @ApiModelProperty(value = "完成时间 (时间戳)")
    private Long completeTime;

    @ApiModelProperty(value = "复核完成时间 (时间戳)")
    private Long checkCompleteTime;

    @ApiModelProperty(value = "审核结果说明")
    private String message;

    @ApiModelProperty(value = "单据状态(创建、审核中、审核驳回、审核成功、待复核、上架中、完成、取消)")
    private String status;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "容器分配时间")
    private Long allocateTime;

    @ApiModelProperty(value = "锁定数量")
    private BigDecimal lockQty;


}