package com.dt.domain.bill.shelf.service.impl;

import com.dt.domain.bill.shelf.entity.OffShelf;
import com.dt.domain.bill.shelf.mapper.OffShelfMapper;
import com.dt.domain.bill.shelf.service.IOffShelfService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 下架管理 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-19
 */
@Service
public class OffShelfServiceImpl extends ServiceImpl<OffShelfMapper, OffShelf> implements IOffShelfService {

}
