package com.dt.domain.bill.finance.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 金融监管赎回单明细
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_finance_redeem_detail")
@ApiModel(value="FinanceRedeemDetail对象", description="金融监管赎回单明细")
public class FinanceRedeemDetail extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    @ApiModelProperty(value = "监管单号")
    private String redeemCode;

    @ApiModelProperty(value = "商品编码")
    private String skuCode;

    @ApiModelProperty(value = "wms批次编码")
    private String skuLotNo;

    @ApiModelProperty(value = "数量 默认:0")
    private BigDecimal qty;

    @ApiModelProperty(value = "单据状态")
    private String status;

    @ApiModelProperty(value = "备注")
    private String remark;


}