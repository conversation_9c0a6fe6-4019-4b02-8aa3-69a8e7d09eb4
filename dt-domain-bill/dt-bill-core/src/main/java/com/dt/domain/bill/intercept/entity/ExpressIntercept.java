package com.dt.domain.bill.intercept.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 快递拦截
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_express_intercept")
@ApiModel(value="ExpressIntercept对象", description="快递拦截")
public class ExpressIntercept extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    @ApiModelProperty(value = "出库单号")
    private String shipmentOrderCode;

    @ApiModelProperty(value = "客户单号")
    private String poNo;

    @ApiModelProperty(value = "上游单号")
    private String soNo;

    @ApiModelProperty(value = "店铺")
    private String saleShop;

    @ApiModelProperty(value = "销售平台")
    private String salePlatform;

    @ApiModelProperty(value = "业务类型")
    private String businessType;

    @ApiModelProperty(value = "快递公司编码")
    private String carrierCode;

    @ApiModelProperty(value = "快递单号")
    private String expressNo;

    @ApiModelProperty(value = "汇单时间")
    private Long collectTime;

    @ApiModelProperty(value = "收货省份名称")
    private String receiverProvName;

    @ApiModelProperty(value = "收货人市名称")
    private String receiverCityName;

    @ApiModelProperty(value = "收货人区名称")
    private String receiverAreaName;


}