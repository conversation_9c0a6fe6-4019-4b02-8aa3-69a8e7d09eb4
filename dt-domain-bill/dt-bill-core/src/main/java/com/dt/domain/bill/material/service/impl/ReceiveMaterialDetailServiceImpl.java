package com.dt.domain.bill.material.service.impl;

import com.dt.domain.bill.material.entity.ReceiveMaterialDetail;
import com.dt.domain.bill.material.mapper.ReceiveMaterialDetailMapper;
import com.dt.domain.bill.material.service.IReceiveMaterialDetailService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 耗材领用表明细 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-10
 */
@Service
public class ReceiveMaterialDetailServiceImpl extends ServiceImpl<ReceiveMaterialDetailMapper, ReceiveMaterialDetail> implements IReceiveMaterialDetailService {

}
