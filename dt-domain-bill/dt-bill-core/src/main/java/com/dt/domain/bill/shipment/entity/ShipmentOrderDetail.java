package com.dt.domain.bill.shipment.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 * 出库单明细表
 * </p>
 *
 * <AUTHOR> xy
 * @since 2020-10-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_shipment_order_detail")
@ApiModel(value="ShipmentOrderDetail对象", description="出库单明细表")
public class ShipmentOrderDetail extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

//    @ApiModelProperty(value = "仓库编码")
//    private String warehouseCode;

    @ApiModelProperty(value = "引用主表编码")
    private String shipmentOrderCode;

    @ApiModelProperty(value = "行号")
    private String lineSeq;

    @ApiModelProperty(value = "状态")
    private String status;

    @ApiModelProperty(value = "商品代码")
    private String skuCode;

    @ApiModelProperty(value = "商品条码")
    private String upcCode;

    @ApiModelProperty(value = "商品名称")
    private String skuName;

    @ApiModelProperty(value = "计划商品数量")
    private BigDecimal expSkuQty;

    private String packageUnitCode;

    @ApiModelProperty(value = "预配数量")
    private BigDecimal expQty;

    @ApiModelProperty(value = "分配数量")
    private BigDecimal assignQty;

    @ApiModelProperty(value = "拣货数量")
    private BigDecimal pickQty;

    @ApiModelProperty(value = "复核数量")
    private BigDecimal checkQty;

    @ApiModelProperty(value = "出库数量")
    private BigDecimal outStockQty;

    @ApiModelProperty(value = "批次ID")
    private String skuLotNo;

    @ApiModelProperty(value = "拣货区")
    private String zoneCode;

    @ApiModelProperty(value = "库位")
    private String locationCode;

    @ApiModelProperty(value = "被拆分相关明细ID")
    private Long refSplitDetailId;

    @ApiModelProperty(value = "拆分前数据字段值")
    private BigDecimal sourceExpSkuQty;

    @ApiModelProperty(value = "入库日期")
    private Long receiveDate;

    @ApiModelProperty(value = "入库日期格式化")
    private String receiveDateFormat;

    @ApiModelProperty(value = "生产日期")
    private Long manufDate;

    @ApiModelProperty(value = "生产日期格式化")
    private String manufDateFormat;

    @ApiModelProperty(value = "失效日期")
    private Long expireDate;

    @ApiModelProperty(value = "失效日期格式化")
    private String expireDateFormat;

    @ApiModelProperty(value = "批次商品属性")
    private String skuQuality;

    @ApiModelProperty(value = "外部批次ID")
    private String externalSkuLotNo;

    @ApiModelProperty(value = "生产批次号")
    private String productionNo;

    @ApiModelProperty(value = "禁售日期")
    private Long withdrawDate;

    @ApiModelProperty(value = "禁售日期格式化")
    private String withdrawDateFormat;

    @ApiModelProperty(value = "分配规则")
    private String allocationRuleCode;

    @ApiModelProperty(value = "周转规则")
    private String turnoverRuleCode;

    @ApiModelProperty(value = "是否赠品: 1是 2不是")
    private Integer freeFlag;

    @ApiModelProperty(value = "拓传json")
    private String extraJson;

    @ApiModelProperty(value = "残次等级")
    private String inventoryType;

    @ApiModelProperty(value = "禁售比对时间")
    private Long withdrawCompareDate;


}