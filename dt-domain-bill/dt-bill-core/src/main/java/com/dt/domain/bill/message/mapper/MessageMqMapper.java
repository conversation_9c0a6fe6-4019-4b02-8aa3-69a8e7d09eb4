package com.dt.domain.bill.message.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dt.domain.bill.message.entity.MessageMq;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 消息信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-03
 */
public interface MessageMqMapper extends BaseMapper<MessageMq> {
    /**
     * @param idList
     * @return java.lang.Boolean
     * <AUTHOR>
     * @describe: 物理删除
     * @date 2024/4/2 16:42
     */
    Boolean partialPhysicalDeleteById(@Param("idList") List<Long> idList);
}
