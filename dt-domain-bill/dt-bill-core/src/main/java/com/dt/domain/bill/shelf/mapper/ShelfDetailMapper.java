package com.dt.domain.bill.shelf.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dt.domain.bill.shelf.entity.ShelfDetail;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 上架管理明细 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-17
 */
public interface ShelfDetailMapper extends BaseMapper<ShelfDetail> {

    Integer getMaxLineSeq(@Param("shelfCode") String shelfCode);
}
