package com.dt.domain.bill.carryover.service.impl;

import com.dt.domain.bill.carryover.entity.BookCarryoverOriginDetail;
import com.dt.domain.bill.carryover.mapper.BookCarryoverOriginDetailMapper;
import com.dt.domain.bill.carryover.service.IBookCarryoverOriginDetailService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 账册结转单原始明细 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-22
 */
@Service
public class BookCarryoverOriginDetailServiceImpl extends ServiceImpl<BookCarryoverOriginDetailMapper, BookCarryoverOriginDetail> implements IBookCarryoverOriginDetailService {

}
