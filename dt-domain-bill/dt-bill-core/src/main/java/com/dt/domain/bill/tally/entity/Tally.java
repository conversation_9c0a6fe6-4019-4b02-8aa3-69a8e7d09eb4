package com.dt.domain.bill.tally.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-02-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_tally")
@ApiModel(value="Tally对象", description="")
public class Tally extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "货主编码 取值货主档案")
    private String cargoCode;

    @ApiModelProperty(value = "理货编号")
    private String tallyCode;

    @ApiModelProperty(value = "理货单据")
    private String billNo;

    @ApiModelProperty(value = "理货类型（参照ASN的type类型）")
    private String type;

    @ApiModelProperty(value = "理货人员")
    private String tallyBy;

    @ApiModelProperty(value = "理货方式")
    private String opType;

    @ApiModelProperty(value = "理货时间")
    private Long opDate;

    @ApiModelProperty(value = "单据类型")
    private String status;

    @ApiModelProperty(value = "打印次数")
    private Integer printNum;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "备注")
    private String reason;

    @ApiModelProperty(value = "取消备注")
    private String cancelRemark;

    @ApiModelProperty(value = "ERP编号")
    private String erpCode;

    /**
     * 托数
     */
    private Integer torr;

    @ApiModelProperty(value = "放置分区")
    private String physicalPartition;

    @ApiModelProperty(value = "理货审核时间")
    private Long authTime;

    @ApiModelProperty(value = "承租方企业")
    private String lesseeEnterprise;


    @ApiModelProperty(value = "外部单号")
    private String outOrderNo;

    @ApiModelProperty(value = "标记")
    private Integer mark;

    @ApiModelProperty(value = "拓传json")
    private String extraJson;

}