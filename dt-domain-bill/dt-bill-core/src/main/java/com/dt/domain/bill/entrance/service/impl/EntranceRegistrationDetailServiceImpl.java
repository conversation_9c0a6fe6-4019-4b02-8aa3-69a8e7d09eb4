package com.dt.domain.bill.entrance.service.impl;

import com.dt.domain.bill.entrance.entity.EntranceRegistrationDetail;
import com.dt.domain.bill.entrance.mapper.EntranceRegistrationDetailMapper;
import com.dt.domain.bill.entrance.service.IEntranceRegistrationDetailService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 一线入境到货登记 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-15
 */
@Service
public class EntranceRegistrationDetailServiceImpl extends ServiceImpl<EntranceRegistrationDetailMapper, EntranceRegistrationDetail> implements IEntranceRegistrationDetailService {

}
