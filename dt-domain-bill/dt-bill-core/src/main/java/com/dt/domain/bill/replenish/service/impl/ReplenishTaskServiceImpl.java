package com.dt.domain.bill.replenish.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dt.domain.bill.replenish.entity.ReplenishTask;
import com.dt.domain.bill.replenish.entity.ReplenishTaskStatistics;
import com.dt.domain.bill.replenish.mapper.ReplenishTaskMapper;
import com.dt.domain.bill.replenish.service.IReplenishTaskService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR> xy
 * @since 2020-10-12
 */
@Service
public class ReplenishTaskServiceImpl extends ServiceImpl<ReplenishTaskMapper, ReplenishTask> implements IReplenishTaskService {

    @Override
    public IPage<ReplenishTaskStatistics> getPage(IPage<ReplenishTaskStatistics> page, Wrapper<ReplenishTask> queryWrapper) {
       return this.baseMapper.getPage(page,queryWrapper);
    }

    @Override
    public List<ReplenishTaskStatistics> getListStatistics(Wrapper<ReplenishTask> queryWrapper) {
        return this.baseMapper.getListStatistics(queryWrapper);
    }

    @Override
    public void deleteById(Long id) {
        this.baseMapper.deleteById(id);
    }
}
