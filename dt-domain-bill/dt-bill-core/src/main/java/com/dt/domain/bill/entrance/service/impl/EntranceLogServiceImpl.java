package com.dt.domain.bill.entrance.service.impl;

import com.dt.domain.bill.entrance.entity.EntranceLog;
import com.dt.domain.bill.entrance.mapper.EntranceLogMapper;
import com.dt.domain.bill.entrance.service.IEntranceLogService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * entrance log 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-16
 */
@Service
public class EntranceLogServiceImpl extends ServiceImpl<EntranceLogMapper, EntranceLog> implements IEntranceLogService {

}
