package com.dt.domain.bill.upc.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 每日包材使用记录
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_material_use")
@ApiModel(value="MaterialUse对象", description="每日包材使用记录")
public class MaterialUse extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;
    
    @ApiModelProperty(value = "包耗材条码")
    private String upcCode;

    @ApiModelProperty(value = "业务单号")
    private String date;

    @ApiModelProperty(value = "使用数量")
    private BigDecimal qty;

    @ApiModelProperty(value = "状态 10 待处理 20 已处理")
    private Integer status;


}