package com.dt.domain.bill.shipment.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 出库单包材表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_shipment_order_material")
@ApiModel(value="ShipmentOrderMaterial对象", description="出库单包材表")
public class ShipmentOrderMaterial extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

//    @ApiModelProperty(value = "仓库编码")
//    private String warehouseCode;

    @ApiModelProperty(value = "引用主表编码")
    private String shipmentOrderCode;

    @ApiModelProperty(value = "推荐包材条码")
    private String recPackUpcCode;

    @ApiModelProperty(value = "实际使用包材条码")
    private String actualPackUpcCode;




}