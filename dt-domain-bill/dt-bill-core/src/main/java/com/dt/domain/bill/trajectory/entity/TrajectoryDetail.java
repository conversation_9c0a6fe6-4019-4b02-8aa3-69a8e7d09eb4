package com.dt.domain.bill.trajectory.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 线路/轨迹流向管理:明细
 * </p>
 *
 * <AUTHOR> x<PERSON>
 * @since 2020-09-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_trajectory_detail")
@ApiModel(value="TrajectoryDetail对象", description="线路/轨迹流向管理:明细")
public class TrajectoryDetail extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "线路编码 不允许修改")
    private String trajectoryCode;

    @ApiModelProperty(value = "承运商编码")
    private String carrierCode;

    @ApiModelProperty(value = "状态码 ，取值启用、停用，默认为启用状态(1:启用，-1:停用)")
    private Integer status;


}