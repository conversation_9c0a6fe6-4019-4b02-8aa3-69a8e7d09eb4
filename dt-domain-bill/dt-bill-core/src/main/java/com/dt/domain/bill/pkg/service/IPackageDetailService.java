package com.dt.domain.bill.pkg.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dt.domain.bill.pkg.entity.PackageDetail;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-14
 */
public interface IPackageDetailService extends IService<PackageDetail> {
    public List<String> getReferShipmentOrderCode(@Param(Constants.WRAPPER) Wrapper query);
    public List<PackageDetail> getListForOrderBy(List<String>packageCodeList);

}
