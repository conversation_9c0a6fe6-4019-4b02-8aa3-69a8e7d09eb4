package com.dt.domain.bill.rs.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dt.component.common.enums.rs.OpAbnormalTypeEnum;
import com.dt.component.common.enums.rs.OpExceptionStateEnum;
import com.dt.domain.bill.rs.entity.OpException;
import com.dt.domain.bill.rs.mapper.OpExceptionMapper;
import com.dt.domain.bill.rs.service.IOpExceptionService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;

/**
 * <p>
 * 淘天用异常单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-13
 */
@Service
public class OpExceptionServiceImpl extends ServiceImpl<OpExceptionMapper, OpException> implements IOpExceptionService {

    @Override
    public List<OpException> getByOrderCode(String orderCode) {
        return baseMapper.getByOrderCode(orderCode);
    }

    /**
     * 1. 当异常类型为【无销退单/多货登记】，并且超16天未处理的情况下，系统按照一定条件，检索复核条件的异常登记，自动生成调整单
     *
     *      异常登记的【创建时间年月日】 < 当前时间（年月日） - 16天，按自然日，不精确到时分秒，也就是推算16天前的自然日内创建的都应该在第17个自然日进行盘盈（原本要求是15天，但是仓库考虑到实际情况，多给1天的处理时间，盘盈的时效要求并没有那么高）
     *      异常类型 = 无销退单/多货登记
     *      单据状态为【异常登记】
     *      异常登记类型为【无销退单】时，【单据号】为空；（异常类型为多货登记时，无这一条限制）
     * @return
     */
    @Override
    public List<OpException> getListNotProcessedIn15Days() {
        Long end = getTimestamp15DaysAgo();
        LambdaQueryWrapper<OpException> wrapper = new LambdaQueryWrapper<OpException>()
                .eq(OpException::getStatus, OpExceptionStateEnum.REGISTER.getCode())
                .lt(OpException::getCreatedTime, end)
                .and(
                        wq -> wq
                                .eq(OpException::getAbnormalType, OpAbnormalTypeEnum.T_1005.getCode())
                                .and(innerWq -> innerWq
                                        .isNull(OpException::getOrderCode).or().eq(OpException::getOrderCode, ""))
                                .or()
                                .eq(OpException::getAbnormalType, OpAbnormalTypeEnum.T_1006.getCode())
                );
        return list(wrapper);
    }

    public static long getTimestamp15DaysAgo() {
        // 获取当前日期
        LocalDate now = LocalDate.now();
        // 获取15天前的日期，并将时间设置为凌晨
        LocalDate fourteenDaysAgo = now.minusDays(15);
        LocalDateTime fourteenDaysAgoStartOfDay = fourteenDaysAgo.atStartOfDay();

        // 将LocalDateTime转换为Instant
        Instant fourteenDaysAgoInstant = fourteenDaysAgoStartOfDay.atZone(ZoneId.systemDefault()).toInstant();
        // 返回15天前的凌晨时间戳
        return fourteenDaysAgoInstant.toEpochMilli();
    }

}
