package com.dt.domain.bill.shipment.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dt.domain.bill.shipment.entity.ShipmentOrderDetail;
import com.dt.domain.bill.shipment.mapper.ShipmentOrderDetailMapper;
import com.dt.domain.bill.shipment.service.IShipmentOrderDetailService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 出库单明细表 服务实现类
 * </p>
 *
 * <AUTHOR> xy
 * @since 2020-10-14
 */
@Service
public class ShipmentOrderDetailServiceImpl extends ServiceImpl<ShipmentOrderDetailMapper, ShipmentOrderDetail> implements IShipmentOrderDetailService {

}
