package com.dt.domain.bill.collect.mapper;

import com.dt.domain.bill.collect.entity.CollectLockTask;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 任务锁表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-19
 */
public interface CollectLockTaskMapper extends BaseMapper<CollectLockTask> {

    Boolean partialPhysicalDeleteById(@Param("idList") List<Long> idList);
}
