package com.dt.domain.bill.collect.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 任务锁表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_collect_lock_task")
@ApiModel(value="CollectLockTask对象", description="任务锁表")
public class CollectLockTask extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    @ApiModelProperty(value = "单据号")
    private String billNo;

    @ApiModelProperty(value = "单据类型")
    private String operateType;

    @ApiModelProperty(value = "状态码 10 拣选单任务创建,20 库存完成,30 拣选单 失败(通知取消库存),40 完成")
    private String status;

    @ApiModelProperty(value = "备注")
    private String remark;


}