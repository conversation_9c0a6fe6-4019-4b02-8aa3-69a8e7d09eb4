package com.dt.domain.bill.adjust.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 * 库存调整明细
 * </p>
 *
 * <AUTHOR> xy
 * @since 2020-09-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_adjust_detail")
@ApiModel(value="AdjustDetail对象", description="库存调整明细")
public class AdjustDetail extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "调整单号")
    private String adjustCode;

//    @ApiModelProperty(value = "仓库编码")
//    private String warehouseCode;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    @ApiModelProperty(value = "商品编码")
    private String skuCode;

    @ApiModelProperty(value = "批次ID")
    private String skuLotNo;

    @ApiModelProperty(value = "库位 取值：库位档案，不包含系统默认共用库区中的库位")
    private String locationCode;

    @ApiModelProperty(value = "调整前数量(可用)")
    private BigDecimal availableQty;

    @ApiModelProperty(value = "目标数量")
    private BigDecimal adjustQty;

    @ApiModelProperty(value = "调整后数量(可用)")
    private BigDecimal targetQty;

    @ApiModelProperty(value = "状态码 取值启用、停用，默认为启用状态(1:启用，-1:停用)")
    private String status;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "调整原因")
    private String reason;

    @ApiModelProperty(value = "责任方")
    private String rp;

    @ApiModelProperty(value = "证明材料")
    private String evidenceInfo;

    @ApiModelProperty(value = "行号")
    private String lineSeq;

}