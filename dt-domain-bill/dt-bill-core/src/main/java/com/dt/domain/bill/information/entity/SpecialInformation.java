package com.dt.domain.bill.information.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 特殊信息采集
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_special_information")
@ApiModel(value="SpecialInformation对象", description="特殊信息采集")
public class SpecialInformation extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    @ApiModelProperty(value = "单据号")
    private String billNo;

    @ApiModelProperty(value = "上游单号")
    private String outNo;

    @ApiModelProperty(value = "采集信息编码")
    private String specialNo;

    @ApiModelProperty(value = "采集种类数")
    private Integer collectQty;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "拓展字段")
    private String extraJson;


}
