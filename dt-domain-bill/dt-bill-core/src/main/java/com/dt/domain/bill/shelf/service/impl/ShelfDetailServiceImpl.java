package com.dt.domain.bill.shelf.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dt.domain.bill.shelf.entity.ShelfDetail;
import com.dt.domain.bill.shelf.mapper.ShelfDetailMapper;
import com.dt.domain.bill.shelf.service.IShelfDetailService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 上架管理明细 服务实现类
 * </p>
 *
 * <AUTHOR> xy
 * @since 2020-11-17
 */
@Service
public class ShelfDetailServiceImpl extends ServiceImpl<ShelfDetailMapper, ShelfDetail> implements IShelfDetailService {

}
