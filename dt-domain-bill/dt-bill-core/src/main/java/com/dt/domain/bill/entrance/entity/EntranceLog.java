package com.dt.domain.bill.entrance.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * entrance log
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_entrance_log")
@ApiModel(value="EntranceLog对象", description="entrance log")
public class EntranceLog extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    @ApiModelProperty(value = "提单号")
    private String ladingNo;

    private String msg;

    @ApiModelProperty(value = "操作人")
    private String opBy;

    @ApiModelProperty(value = "备注")
    private String remark;


}