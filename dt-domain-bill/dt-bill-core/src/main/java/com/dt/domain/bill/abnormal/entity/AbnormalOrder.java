package com.dt.domain.bill.abnormal.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR> xy
 * @since 2020-10-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_abnormal_order")
@ApiModel(value="AbnormalOrder对象", description="异常单")
public class AbnormalOrder extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

//    @ApiModelProperty(value = "仓库编码")
//    private String warehouseCode;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    @ApiModelProperty(value = "单据编号")
    private String billNo;

    @ApiModelProperty(value = "单据类型")
    private String billType;

    @ApiModelProperty(value = "义务类型")
    private String businessType;

    @ApiModelProperty(value = "订单业务类型")
    private String orderBusinessType;

    @ApiModelProperty(value = "状态码")
    private String status;

    @ApiModelProperty(value = "错误代码")
    private String errorStatus;

    @ApiModelProperty(value = "错误描述")
    private String errorMsg;

    @ApiModelProperty(value = "重试次数")
    private Integer retryCount;

    @ApiModelProperty(value = "客户单号")
    private String poNo;

    @ApiModelProperty(value = "ERP单号")
    private String soNo;

    @ApiModelProperty(value = "承运商编码")
    private String carrierCode;

}