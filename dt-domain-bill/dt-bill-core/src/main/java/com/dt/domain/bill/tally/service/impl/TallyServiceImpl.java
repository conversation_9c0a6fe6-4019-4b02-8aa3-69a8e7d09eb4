package com.dt.domain.bill.tally.service.impl;

import com.dt.domain.bill.tally.entity.Tally;
import com.dt.domain.bill.tally.mapper.TallyMapper;
import com.dt.domain.bill.tally.service.ITallyService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-02-16
 */
@Service
public class TallyServiceImpl extends ServiceImpl<TallyMapper, Tally> implements ITallyService {

}
