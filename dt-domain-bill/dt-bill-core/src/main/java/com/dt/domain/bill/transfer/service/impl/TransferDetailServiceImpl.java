package com.dt.domain.bill.transfer.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dt.domain.bill.transfer.entity.TransferDetail;
import com.dt.domain.bill.transfer.mapper.TransferDetailMapper;
import com.dt.domain.bill.transfer.service.ITransferDetailService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 库存转移明细 服务实现类
 * </p>
 *
 * <AUTHOR> xy
 * @since 2020-09-11
 */
@Service
public class TransferDetailServiceImpl extends ServiceImpl<TransferDetailMapper, TransferDetail> implements ITransferDetailService {

}
