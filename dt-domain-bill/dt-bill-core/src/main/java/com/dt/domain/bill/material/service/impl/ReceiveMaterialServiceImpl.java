package com.dt.domain.bill.material.service.impl;

import com.dt.domain.bill.material.entity.ReceiveMaterial;
import com.dt.domain.bill.material.mapper.ReceiveMaterialMapper;
import com.dt.domain.bill.material.service.IReceiveMaterialService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 耗材领用表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-10
 */
@Service
public class ReceiveMaterialServiceImpl extends ServiceImpl<ReceiveMaterialMapper, ReceiveMaterial> implements IReceiveMaterialService {

}
