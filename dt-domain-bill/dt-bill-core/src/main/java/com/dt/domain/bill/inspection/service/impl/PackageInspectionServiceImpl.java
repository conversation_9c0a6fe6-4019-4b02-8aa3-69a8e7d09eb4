package com.dt.domain.bill.inspection.service.impl;

import com.dt.domain.bill.inspection.entity.PackageInspection;
import com.dt.domain.bill.inspection.mapper.PackageInspectionMapper;
import com.dt.domain.bill.inspection.service.IPackageInspectionService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 包裹抽检 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-26
 */
@Service
public class PackageInspectionServiceImpl extends ServiceImpl<PackageInspectionMapper, PackageInspection> implements IPackageInspectionService {

}
