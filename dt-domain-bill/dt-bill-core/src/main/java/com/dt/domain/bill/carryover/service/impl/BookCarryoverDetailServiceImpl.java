package com.dt.domain.bill.carryover.service.impl;

import com.dt.domain.bill.carryover.entity.BookCarryoverDetail;
import com.dt.domain.bill.carryover.mapper.BookCarryoverDetailMapper;
import com.dt.domain.bill.carryover.service.IBookCarryoverDetailService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 账册结转单明细 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-19
 */
@Service
public class BookCarryoverDetailServiceImpl extends ServiceImpl<BookCarryoverDetailMapper, BookCarryoverDetail> implements IBookCarryoverDetailService {

}
