package com.dt.domain.bill.carryover.service.impl;

import com.dt.domain.bill.carryover.entity.BookCarryoverReceipt;
import com.dt.domain.bill.carryover.mapper.BookCarryoverReceiptMapper;
import com.dt.domain.bill.carryover.service.IBookCarryoverReceiptService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 账册结转单海关回执 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-19
 */
@Service
public class BookCarryoverReceiptServiceImpl extends ServiceImpl<BookCarryoverReceiptMapper, BookCarryoverReceipt> implements IBookCarryoverReceiptService {

}
