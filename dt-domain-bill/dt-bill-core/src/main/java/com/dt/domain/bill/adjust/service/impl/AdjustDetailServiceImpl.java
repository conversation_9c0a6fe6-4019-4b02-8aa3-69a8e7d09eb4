package com.dt.domain.bill.adjust.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dt.domain.bill.adjust.entity.AdjustDetail;
import com.dt.domain.bill.adjust.mapper.AdjustDetailMapper;
import com.dt.domain.bill.adjust.service.IAdjustDetailService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 库存调整明细 服务实现类
 * </p>
 *
 * <AUTHOR> xy
 * @since 2020-09-11
 */
@Service
public class AdjustDetailServiceImpl extends ServiceImpl<AdjustDetailMapper, AdjustDetail> implements IAdjustDetailService {

}
