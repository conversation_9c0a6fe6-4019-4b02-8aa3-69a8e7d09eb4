package com.dt.domain.bill.rs.service.impl;

import com.dt.domain.bill.rs.entity.SalesReturnOrder;
import com.dt.domain.bill.rs.mapper.SalesReturnOrderMapper;
import com.dt.domain.bill.rs.service.ISalesReturnOrderService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 销退单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-25
 */
@Service
public class SalesReturnOrderServiceImpl extends ServiceImpl<SalesReturnOrderMapper, SalesReturnOrder> implements ISalesReturnOrderService {

}
