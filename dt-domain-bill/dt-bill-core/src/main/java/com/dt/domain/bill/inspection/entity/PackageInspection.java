package com.dt.domain.bill.inspection.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 包裹抽检
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_package_inspection")
@ApiModel(value="PackageInspection对象", description="包裹抽检")
public class PackageInspection extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

//    @ApiModelProperty(value = "仓库编码")
//    private String warehouseCode;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    @ApiModelProperty(value = "抽检包裹号")
    private String packageCode;

    @ApiModelProperty(value = "拣选单号")
    private String pickCode;
    @ApiModelProperty(value = "客户号")
    private String soNo;
    @ApiModelProperty(value = "出库单号")
    private String shipmentOrderNo;
    @ApiModelProperty(value = "运单号")
    private String expressNo;
    @ApiModelProperty(value = "承运商编码")
    private String carrierCode;

    @ApiModelProperty(value = "抽检结果 SUCCESS FAIL")
    private String result;

    @ApiModelProperty(value = "描述")
    private String msg;

    @ApiModelProperty(value = "抽检人")
    private String inspector;


}