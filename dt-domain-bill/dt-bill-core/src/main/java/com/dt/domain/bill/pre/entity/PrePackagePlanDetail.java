package com.dt.domain.bill.pre.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 * 预包计划商品批次明细
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_pre_package_plan_detail")
@ApiModel(value="PrePackagePlanDetail对象", description="预包计划商品批次明细")
public class PrePackagePlanDetail extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    @ApiModelProperty(value = "预包计划号")
    private String prePlanCode;

    @ApiModelProperty(value = "预报条码")
    private String preUpcCode;

    @ApiModelProperty(value = "商品代码")
    private String skuCode;

    @ApiModelProperty(value = "计划数量 默认:0")
    private BigDecimal skuQty;

    @ApiModelProperty(value = "商品属性")
    private String skuQuality;

    @ApiModelProperty(value = "来源库位")
    private String locationCode;

    @ApiModelProperty(value = "库存占用优先级")
    private Integer stockOccupyPriority;

    @ApiModelProperty(value = "批次ID")
    private String skuLotNo;

    @ApiModelProperty(value = "单据状态(创建、审核中、审核驳回、审核成功、待复核、上架中、完成、取消)")
    private String status;

    @ApiModelProperty(value = "备注")
    private String remark;


}