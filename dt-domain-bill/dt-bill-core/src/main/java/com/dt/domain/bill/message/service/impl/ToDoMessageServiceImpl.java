package com.dt.domain.bill.message.service.impl;

import com.dt.domain.bill.message.entity.ToDoMessage;
import com.dt.domain.bill.message.mapper.ToDoMessageMapper;
import com.dt.domain.bill.message.service.IToDoMessageService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 待办事项 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-07
 */
@Service
public class ToDoMessageServiceImpl extends ServiceImpl<ToDoMessageMapper, ToDoMessage> implements IToDoMessageService {

}
