package com.dt.domain.bill.material.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 出库单回传耗材消耗记录
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_material_use_record")
@ApiModel(value="MaterialUseRecord对象", description="出库单回传耗材消耗记录")
public class MaterialUseRecord extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    @ApiModelProperty(value = "出库单编码")
    private String shipmentOrderCode;

    @ApiModelProperty(value = "包裹编码")
    private String packageCode;

    @ApiModelProperty(value = "C端单号-(客户原始单号)")
    private String poNo;

    @ApiModelProperty(value = "上游单号--进销存(现有ERP单号,目前前端显示客户单号)")
    private String soNo;

    @ApiModelProperty(value = "耗材编码")
    private String materialCode;

    @ApiModelProperty(value = "耗材条码")
    private String barCode;

    @ApiModelProperty(value = "商品代码")
    private String skuCode;

    @ApiModelProperty(value = "耗材数量")
    private Integer materialNum;

    @ApiModelProperty(value = "状态码")
    private Integer status;

    @ApiModelProperty(value = "是否4PL")
    private Integer is4pl;

    @ApiModelProperty(value = "是否扫描的耗材 1:否 2:是")
    private Integer scanTag;

    @ApiModelProperty(value = "耗材是否计重")
    private Integer materialAddWeight;


}