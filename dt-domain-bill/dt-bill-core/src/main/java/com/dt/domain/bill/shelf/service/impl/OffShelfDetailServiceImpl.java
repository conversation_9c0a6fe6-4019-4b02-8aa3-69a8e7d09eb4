package com.dt.domain.bill.shelf.service.impl;

import com.dt.domain.bill.shelf.entity.OffShelfDetail;
import com.dt.domain.bill.shelf.mapper.OffShelfDetailMapper;
import com.dt.domain.bill.shelf.service.IOffShelfDetailService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 下架明细 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-19
 */
@Service
public class OffShelfDetailServiceImpl extends ServiceImpl<OffShelfDetailMapper, OffShelfDetail> implements IOffShelfDetailService {

}
