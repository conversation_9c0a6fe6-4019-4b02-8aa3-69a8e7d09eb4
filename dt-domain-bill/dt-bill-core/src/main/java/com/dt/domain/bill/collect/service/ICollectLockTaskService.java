package com.dt.domain.bill.collect.service;

import com.dt.domain.bill.collect.entity.CollectLockTask;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 任务锁表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-19
 */
public interface ICollectLockTaskService extends IService<CollectLockTask> {
    /**
     * @param idList
     * @return java.lang.Boolean
     * <AUTHOR>
     * @describe: 物理删除
     * @date 2024/4/2 17:50
     */
    Boolean partialPhysicalDeleteById(List<Long> idList);
}
