package com.dt.domain.bill.inventory.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 盘点计划表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_inventory_plan")
@ApiModel(value="InventoryPlan对象", description="盘点计划表")
public class InventoryPlan extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "盘点计划编码")
    private String inventoryPlanCode;

    @ApiModelProperty(value = "盘点计划类型")
    private String inventoryPlanType;

    @ApiModelProperty(value = "盘点计划名称")
    private String inventoryPlanName;

    @ApiModelProperty(value = "是否包含空库位")
    private Integer hasEmptyLocation;

    @ApiModelProperty(value = "商品类型")
    private String skuType;

    @ApiModelProperty(value = "库位,库区,巷道,货主,商品条码汇总json")
    private String summaryJson;

    @ApiModelProperty(value = "最小动碰次数(盘点范围 动碰次数大于此值进入盘点范围)")
    private Integer minTouchNum;

    @ApiModelProperty(value = "动碰次数最高的前几位(盘点范围 动碰次数从高到低的前count位)")
    private Integer maxTouchCount;

    @ApiModelProperty(value = "动碰时间开始(盘点范围)")
    private Long touchTimeStart;

    @ApiModelProperty(value = "动碰时间结束(盘点范围)")
    private Long touchTimeEnd;

    @ApiModelProperty(value = "最大库位数(盘点范围 根据动碰次数 从高到底排序)")
    private Integer maxLocationCount;

    @ApiModelProperty(value = "盘点方式")
    private String inventoryWay;

    @ApiModelProperty(value = "盘点工具")
    private String inventoryTool;

    @ApiModelProperty(value = "盘点深度")
    private String inventoryDepth;

    @ApiModelProperty(value = "重复盘点")
    private String inventoryAgain;

    @ApiModelProperty(value = "盘点进度")
    private Integer inventoryRate;

    @ApiModelProperty(value = "盘点单据状态")
    private String status;

    @ApiModelProperty(value = "盘点未完成预警时间")
    private Long warningTime;

    @ApiModelProperty(value = "切割维度")
    private String splitRule;

    @ApiModelProperty(value = "盘点方法")
    private String inventoryMethod;

}