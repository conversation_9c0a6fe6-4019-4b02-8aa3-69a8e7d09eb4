package com.dt.domain.bill.cw.service.impl;

import com.dt.domain.bill.cw.entity.StorageApplicationCabinet;
import com.dt.domain.bill.cw.mapper.StorageApplicationCabinetMapper;
import com.dt.domain.bill.cw.service.IStorageApplicationCabinetService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 入库申请详情 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-16
 */
@Service
public class StorageApplicationCabinetServiceImpl extends ServiceImpl<StorageApplicationCabinetMapper, StorageApplicationCabinet> implements IStorageApplicationCabinetService {

}
