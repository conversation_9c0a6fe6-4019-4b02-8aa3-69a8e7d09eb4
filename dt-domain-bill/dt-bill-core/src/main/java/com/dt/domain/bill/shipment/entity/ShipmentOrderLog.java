package com.dt.domain.bill.shipment.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> xy
 * @since 2020-10-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_shipment_order_log")
@ApiModel(value="ShipmentOrderLog对象", description="")
public class ShipmentOrderLog extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

//    @ApiModelProperty(value = "仓库编码")
//    private String warehouseCode;

    @ApiModelProperty(value = "引用主表编码")
    private String shipmentOrderCode;

    @ApiModelProperty(value = "操作说明")
    private String opContent;

    @ApiModelProperty(value = "备注")
    private String opRemark;

    @ApiModelProperty(value = "操作人")
    private String opBy;

    private Long opDate;



}