package com.dt.domain.bill.shipment.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dt.domain.bill.replenish.entity.ReplenishTask;
import com.dt.domain.bill.replenish.entity.ReplenishTaskStatistics;
import com.dt.domain.bill.shipment.entity.ShipmentOrder;

import java.util.List;

/**
 * <p>
 * 出库单 服务类
 * </p>
 *
 * <AUTHOR> xy
 * @since 2020-10-14
 */
public interface IShipmentOrderService extends IService<ShipmentOrder> {


    /**
     * 功能描述:  初始化出库单日汇总数据
     * 创建时间:  2021/3/2 4:50 下午
     *
     * @return java.util.List<com.dt.domain.bill.shipment.entity.ShipmentOrder>
     * <AUTHOR>
     */
    List<ShipmentOrder> initCount();

}
