package com.dt.domain.bill.performance.service.impl;

import com.dt.domain.bill.performance.entity.PerformanceStatistics;
import com.dt.domain.bill.performance.mapper.PerformanceStatisticsMapper;
import com.dt.domain.bill.performance.service.IPerformanceStatisticsService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 人员操作统计 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-02-27
 */
@Service
public class PerformanceStatisticsServiceImpl extends ServiceImpl<PerformanceStatisticsMapper, PerformanceStatistics> implements IPerformanceStatisticsService {

}
