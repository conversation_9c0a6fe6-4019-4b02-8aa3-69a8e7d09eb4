package com.dt.domain.bill.inventory.service.impl;

import com.dt.domain.bill.inventory.entity.InventoryReceipt;
import com.dt.domain.bill.inventory.mapper.InventoryReceiptMapper;
import com.dt.domain.bill.inventory.service.IInventoryReceiptService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 盘点单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-12
 */
@Service
public class InventoryReceiptServiceImpl extends ServiceImpl<InventoryReceiptMapper, InventoryReceipt> implements IInventoryReceiptService {

}
