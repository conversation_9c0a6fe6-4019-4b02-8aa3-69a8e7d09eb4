package com.dt.domain.bill.collect.service.impl;

import com.dt.domain.bill.collect.entity.CollectLockTask;
import com.dt.domain.bill.collect.mapper.CollectLockTaskMapper;
import com.dt.domain.bill.collect.service.ICollectLockTaskService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 任务锁表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-19
 */
@Service
public class CollectLockTaskServiceImpl extends ServiceImpl<CollectLockTaskMapper, CollectLockTask> implements ICollectLockTaskService {

    @Override
    public Boolean partialPhysicalDeleteById(List<Long> idList) {
        return baseMapper.partialPhysicalDeleteById(idList);
    }
}
