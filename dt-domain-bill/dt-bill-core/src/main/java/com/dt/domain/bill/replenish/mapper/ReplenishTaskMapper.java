package com.dt.domain.bill.replenish.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.dt.domain.bill.replenish.entity.ReplenishTask;
import com.dt.domain.bill.replenish.entity.ReplenishTaskStatistics;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR> x<PERSON>
 * @since 2020-10-12
 */
public interface ReplenishTaskMapper extends BaseMapper<ReplenishTask> {
    /**
     *
     * @param page
     * @param query
     * @return
     */
    IPage<ReplenishTaskStatistics> getPage(IPage<ReplenishTaskStatistics> page, @Param(Constants.WRAPPER) Wrapper query);

    /**
     *
     * @param query
     * @return
     */
    List<ReplenishTaskStatistics> getListStatistics(@Param(Constants.WRAPPER) Wrapper query);

    @Delete("DELETE FROM dt_replenish_task WHERE id = #{id}")
    void deleteById(@Param("id") Long id);
}
