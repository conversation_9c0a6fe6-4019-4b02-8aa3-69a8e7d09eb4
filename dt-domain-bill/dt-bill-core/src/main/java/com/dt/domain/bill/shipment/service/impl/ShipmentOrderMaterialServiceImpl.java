package com.dt.domain.bill.shipment.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dt.domain.bill.shipment.entity.ShipmentOrderMaterial;
import com.dt.domain.bill.shipment.mapper.ShipmentOrderMaterialMapper;
import com.dt.domain.bill.shipment.service.IShipmentOrderMaterialService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 出库单包材表 服务实现类
 * </p>
 *
 * <AUTHOR> xy
 * @since 2020-10-14
 */
@Service
public class ShipmentOrderMaterialServiceImpl extends ServiceImpl<ShipmentOrderMaterialMapper, ShipmentOrderMaterial> implements IShipmentOrderMaterialService {

}
