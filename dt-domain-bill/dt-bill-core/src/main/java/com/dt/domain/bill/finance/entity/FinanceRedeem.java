package com.dt.domain.bill.finance.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 金融监管赎回单
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_finance_redeem")
@ApiModel(value="FinanceRedeem对象", description="金融监管赎回单")
public class FinanceRedeem extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    @ApiModelProperty(value = "监管赎回单号")
    private String redeemCode;

    @ApiModelProperty(value = "erp单号单号")
    private String erpCode;

    @ApiModelProperty(value = "数量 默认:0")
    private BigDecimal qty;

    /**
     * 商品种类
     */
    @ApiModelProperty(value = "数量 默认:0")
    private Integer skuType;

    @ApiModelProperty(value = "监管赎回时间 (时间戳)")
    private Long redeemTime;

    @ApiModelProperty(value = "审核结果说明")
    private String message;

    @ApiModelProperty(value = "单据状态")
    private String status;

    @ApiModelProperty(value = "备注")
    private String remark;


}