package com.dt.domain.bill.replenish.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dt.domain.bill.replenish.entity.ReplenishTask;
import com.dt.domain.bill.replenish.entity.ReplenishTaskStatistics;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR> xy
 * @since 2020-10-12
 */
public interface IReplenishTaskService extends IService<ReplenishTask> {
      /**
       * 分页
       * @param page
       * @param queryWrapper
       * @return
       */
      IPage<ReplenishTaskStatistics> getPage(IPage<ReplenishTaskStatistics> page, Wrapper<ReplenishTask> queryWrapper);

      /**
       * 查询单个
       * @param queryWrapper
       * @return
       */
      List<ReplenishTaskStatistics> getListStatistics(Wrapper<ReplenishTask> queryWrapper);

      /**
       *
       * @param id
       */
      void deleteById(Long id);
}
