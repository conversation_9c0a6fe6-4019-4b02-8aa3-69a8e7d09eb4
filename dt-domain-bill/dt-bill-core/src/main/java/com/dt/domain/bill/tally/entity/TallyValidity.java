package com.dt.domain.bill.tally.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 理货效期码
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_tally_validity")
@ApiModel(value="TallyValidity对象", description="理货效期码")
public class TallyValidity extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "货主编码 取值货主档案")
    private String cargoCode;

    @ApiModelProperty(value = "单据号")
    private String billNo;

    @ApiModelProperty(value = "商品代码")
    private String skuCode;

    @ApiModelProperty(value = "效期码")
    private String validityCode;

//    @ApiModelProperty(value = "生产日期")
//    private Long manufDate;

    @ApiModelProperty(value = "失效日期")
    private Long expireDate;

    @ApiModelProperty(value = "状态码")
    private String status;

    @ApiModelProperty(value = "备注")
    private String remark;


}