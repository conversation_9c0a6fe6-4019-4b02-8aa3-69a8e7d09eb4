package com.dt.domain.bill.pkg.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR> xy
 * @since 2020-10-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_package_detail")
@ApiModel(value = "PackageDetail对象", description = "包裹明细")
public class PackageDetail extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

//    @ApiModelProperty(value = "仓库编码")
//    private String warehouseCode;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    @ApiModelProperty(value = "引用主表编码")
    private String packageCode;

    @ApiModelProperty(value = "UUID")
    private String packUid;

    @ApiModelProperty(value = "出库明细表ID")
    private Long pUid;

    @ApiModelProperty(value = "订单行号{取值订单明细行号}")
    private String lineSeq;

    @ApiModelProperty(value = "状态")
    private String status;

    @ApiModelProperty(value = "汇单状态码")
    private String collectStatus;

    @ApiModelProperty(value = "商品代码")
    private String skuCode;

    @ApiModelProperty(value = "商品质量")
    private String skuQuality;

    @ApiModelProperty(value = "外部批次ID")
    private String externalSkuLotNo;

    @ApiModelProperty(value = "失效日期")
    private Long expireDate;

    @ApiModelProperty(value = "商品条码")
    private String upcCode;

    @ApiModelProperty(value = "商品名称")
    private String skuName;

    @ApiModelProperty(value = "批次ID")
    private String skuLotNo;

    @ApiModelProperty(value = "商品数量")
    private BigDecimal skuQty;

    @ApiModelProperty(value = "预配数量")
    private BigDecimal expQty;

    @ApiModelProperty(value = "分配数量")
    private BigDecimal assignQty;

    @ApiModelProperty(value = "拣货数量")
    private BigDecimal pickQty;

    @ApiModelProperty(value = "复核数量")
    private BigDecimal checkQty;

    @ApiModelProperty(value = "出库数量")
    private BigDecimal outStockQty;

    @ApiModelProperty(value = "包装单位")
    private String packageUnitCode;

    @ApiModelProperty(value = "分配规则")
    private String allocationRuleCode;

    @ApiModelProperty(value = "周转规则")
    private String turnoverRuleCode;

    @ApiModelProperty(value = "是否赠品: 1是 2不是")
    private Integer freeFlag;

    @ApiModelProperty(value = "拣货库区类型")
    private String zoneType;

    @ApiModelProperty(value = "库位编码")
    private String locationCode;

    @ApiModelProperty(value = "是否预包商品 1:原始商品 2:预包商品 3:剩余商品  枚举：PackIsPreEnum")
    private String isPre;

    @ApiModelProperty(value = "残次等级")
    private String inventoryType;

    @ApiModelProperty(value = "禁售比对时间")
    private Long withdrawCompareDate;


}