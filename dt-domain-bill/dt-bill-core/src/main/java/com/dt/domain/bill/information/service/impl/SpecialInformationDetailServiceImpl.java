package com.dt.domain.bill.information.service.impl;

import com.dt.domain.bill.information.entity.SpecialInformationDetail;
import com.dt.domain.bill.information.mapper.SpecialInformationDetailMapper;
import com.dt.domain.bill.information.service.ISpecialInformationDetailService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 特殊信息采集明细 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-13
 */
@Service
public class SpecialInformationDetailServiceImpl extends ServiceImpl<SpecialInformationDetailMapper, SpecialInformationDetail> implements ISpecialInformationDetailService {

}
