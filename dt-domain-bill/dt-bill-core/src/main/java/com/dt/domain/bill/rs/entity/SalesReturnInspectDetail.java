package com.dt.domain.bill.rs.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 销退质检单详情
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_sales_return_inspect_detail")
@ApiModel(value="SalesReturnInspectDetail对象", description="销退质检单详情")
public class SalesReturnInspectDetail extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "销退质检单号")
    private String inspectOrderNo;

    @ApiModelProperty(value = "销退单号")
    private String salesReturnOrderNo;

    @ApiModelProperty(value = "快递单号")
    private String expressNo;

    @ApiModelProperty(value = "销退单状态")
    private Integer salesReturnStatus;

    @ApiModelProperty(value = "质检结果")
    private Integer inspectionResult;

    @ApiModelProperty(value = "异常原因")
    private String abnormalCause;

    @ApiModelProperty(value = "操作人")
    private String opBy;

    @ApiModelProperty(value = "质检时间")
    private Long inspectTime;


}