package com.dt.domain.bill.rec.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dt.domain.bill.rec.entity.ReceiptDetail;
import com.dt.domain.bill.rec.mapper.ReceiptDetailMapper;
import com.dt.domain.bill.rec.service.IReceiptDetailService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 收货作业批次容器明细 服务实现类
 * </p>
 *
 * <AUTHOR> xy
 * @since 2020-09-11
 */
@Service
public class ReceiptDetailServiceImpl extends ServiceImpl<ReceiptDetailMapper, ReceiptDetail> implements IReceiptDetailService {

}
