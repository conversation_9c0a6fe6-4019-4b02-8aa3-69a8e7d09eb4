package com.dt.domain.bill.rs.service.impl;

import com.dt.domain.bill.rs.entity.SalesReturnLocation;
import com.dt.domain.bill.rs.mapper.SalesReturnLocationMapper;
import com.dt.domain.bill.rs.service.ISalesReturnLocationService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 退货库位 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-25
 */
@Service
public class SalesReturnLocationServiceImpl extends ServiceImpl<SalesReturnLocationMapper, SalesReturnLocation> implements ISalesReturnLocationService {

}
