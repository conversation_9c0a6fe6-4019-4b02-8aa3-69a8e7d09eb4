package com.dt.domain.bill.inventory.service.impl;

import com.dt.domain.bill.inventory.entity.InventoryReceiptDetail;
import com.dt.domain.bill.inventory.mapper.InventoryReceiptDetailMapper;
import com.dt.domain.bill.inventory.service.IInventoryReceiptDetailService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 盘点单明细 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-12
 */
@Service
public class InventoryReceiptDetailServiceImpl extends ServiceImpl<InventoryReceiptDetailMapper, InventoryReceiptDetail> implements IInventoryReceiptDetailService {

}
