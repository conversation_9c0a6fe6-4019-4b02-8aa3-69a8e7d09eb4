package com.dt.domain.bill.message.service;

import com.dt.domain.bill.message.entity.MessageMq;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 消息信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-03
 */
public interface IMessageMqService extends IService<MessageMq> {
    /**
     * @param idList
     * @return java.lang.Boolean
     * <AUTHOR>
     * @describe: 物理删除
     * @date 2024/4/2 16:42
     */
    Boolean partialPhysicalDeleteById(List<Long> idList);
}
