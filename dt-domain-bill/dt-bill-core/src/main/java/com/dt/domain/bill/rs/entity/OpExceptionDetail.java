package com.dt.domain.bill.rs.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 淘天用异常单详情
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_op_exception_detail")
@ApiModel(value="OpExceptionDetail对象", description="淘天用异常单详情")
public class OpExceptionDetail extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "异常单号")
    private String abnormalOrderNo;

    private String abnormalLineId;

    @ApiModelProperty(value = "商品代码")
    private String skuCode;

    @ApiModelProperty(value = "商品条码")
    private String upcCode;

    @ApiModelProperty(value = "商品名称")
    private String skuName;

    @ApiModelProperty(value = "异常提报数量")
    private BigDecimal expSkuQty;

    @ApiModelProperty(value = "库存类型 ZP=正品;CC=残次;JS=机损;XS=箱损;ZT=在途库存;XQC=效期残;ZCC=收货暂存残-待退废;BLC=不良品/一级残次;ZCZP=收货暂存良-待退废;CZC=残值品/二级残次;FP=报废品/三级残次;LQC=临期品;XTZP=销退正品;默认为ZP")
    private String inventoryType;

    @ApiModelProperty(value = "本案")
    private String remark;

    @ApiModelProperty(value = "商品照片URL")
    private String attachmentUrls;
    private String sn;
    private String locationCode;
    private Long manufDate;
    private Long expireDate;





}