package com.dt.domain.bill.abnormal.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dt.domain.bill.abnormal.entity.AbnormalOrder;
import com.dt.domain.bill.abnormal.mapper.AbnormalOrderMapper;
import com.dt.domain.bill.abnormal.service.IAbnormalOrderService;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR> xy
 * @since 2020-10-12
 */
@Service
public class AbnormalOrderServiceImpl extends ServiceImpl<AbnormalOrderMapper, AbnormalOrder> implements IAbnormalOrderService {

}
