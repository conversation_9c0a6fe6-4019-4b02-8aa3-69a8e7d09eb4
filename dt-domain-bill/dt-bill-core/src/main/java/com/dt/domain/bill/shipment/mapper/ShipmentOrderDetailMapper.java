package com.dt.domain.bill.shipment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dt.domain.bill.shipment.entity.ShipmentOrderDetail;
import org.apache.ibatis.annotations.Delete;

/**
 * <p>
 * 出库单明细表 Mapper 接口
 * </p>
 *
 * <AUTHOR> xy
 * @since 2020-10-14
 */
public interface ShipmentOrderDetailMapper extends BaseMapper<ShipmentOrderDetail> {
    @Delete("delete from dt_shipment_order_detail where id = #{id}")
    boolean deleteDetail(Long id);
}
