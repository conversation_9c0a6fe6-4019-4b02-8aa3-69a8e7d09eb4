package com.dt.domain.bill.performance.service.impl;

import com.dt.domain.bill.performance.entity.SystemEvent;
import com.dt.domain.bill.performance.mapper.SystemEventMapper;
import com.dt.domain.bill.performance.service.ISystemEventService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 系统事件 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-02
 */
@Service
public class SystemEventServiceImpl extends ServiceImpl<SystemEventMapper, SystemEvent> implements ISystemEventService {

}
