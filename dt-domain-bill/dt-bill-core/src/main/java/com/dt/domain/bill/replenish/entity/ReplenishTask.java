package com.dt.domain.bill.replenish.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR> x<PERSON>
 * @since 2020-10-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_replenish_task")
@ApiModel(value = "ReplenishTask对象", description = "补货指引")
public class ReplenishTask extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

//    @ApiModelProperty(value = "仓库编码")
//    private String warehouseCode;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    @ApiModelProperty(value = "包裹号")
    private String packageCode;

    @ApiModelProperty(value = "出库单号")
    private String shipmentOrderCode;

    @ApiModelProperty(value = "C端单号-(客户原始单号)")
    private String poNo;

    @ApiModelProperty(value = "上游单号--进销存(现有ERP单号,目前前端显示客户单号)")
    private String soNo;

    @ApiModelProperty(value = "快递公司编码")
    private String carrierCode;

    private String carrierName;

    @ApiModelProperty(value = "快递单号")
    private String expressNo;

    @ApiModelProperty(value = "包裹结构")
    private String packageStruct;

    @ApiModelProperty(value = "指引类型")
    private String type;

    @ApiModelProperty(value = "商品编码")
    private String skuCode;

    @ApiModelProperty(value = "商品条形码")
    private String upcCode;

    @ApiModelProperty(value = "商品名")
    private String skuName;

    @ApiModelProperty(value = "商品质量属性(正残)")
    private String skuQuality;

    @ApiModelProperty(value = "需求数量")
    private BigDecimal qty;

    @ApiModelProperty(value = "已补数量")
    private BigDecimal repQty;

    @ApiModelProperty(value = "差额数量")
    private BigDecimal defQty;

    @ApiModelProperty(value = "状态")
    private String status;

    @ApiModelProperty(value = "订单创建时间")
    private Long orderCreatedTime;

    @ApiModelProperty(value = "库存占用类型")
    private String occupyType;
//
//    @ApiModelProperty(value = "库存占用类型")
//    private String storeOccupy;

    @ApiModelProperty(value = "业务类型")
    private String businessType;

    @ApiModelProperty(value = "外部批次ID")
    private String externalSkuLotNo;

    @ApiModelProperty(value = "批次ID")
    private String skuLotNo;

}