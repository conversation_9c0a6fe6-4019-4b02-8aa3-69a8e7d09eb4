package com.dt.domain.bill.replenish.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> x<PERSON>
 * @since 2020-10-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_replenish_task")
@ApiModel(value="ReplenishTask对象", description="补货指引")
public class ReplenishTaskStatistics extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    @ApiModelProperty(value = "包裹数字")
    private Integer packageCount;

    @ApiModelProperty(value = "指引类型")
    private String type;

    @ApiModelProperty(value = "商品编码")
    private String skuCode;

    @ApiModelProperty(value = "商品质量属性(正残)")
    private String skuQuality;

    @ApiModelProperty(value = "商品条形码")
    private String upcCode;

    @ApiModelProperty(value = "商品名")
    private String skuName;


    @ApiModelProperty(value = "需求数量")
    private BigDecimal qty;

    @ApiModelProperty(value = "已补数量")
    private BigDecimal repQty;

    @ApiModelProperty(value = "差额数量")
    private BigDecimal defQty;


}