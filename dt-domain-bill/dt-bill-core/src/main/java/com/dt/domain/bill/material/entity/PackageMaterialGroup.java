package com.dt.domain.bill.material.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 包材管理
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-23
 */

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_package_material_group")
@ApiModel(value="PackageMaterialGroup对象", description="包材管理")
public class PackageMaterialGroup extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;



    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    @ApiModelProperty(value = "包材组编码 不允许修改，唯一")
    private String code;

    @ApiModelProperty(value = "包材组名称")
    private String name;

    @ApiModelProperty(value = "包材条码，逗号给开")
    private String barCode;

    @ApiModelProperty(value = "状态码 ，取值启用、停用，默认为启用状态(1:启用，-1:停用)")
    private Integer status;


}