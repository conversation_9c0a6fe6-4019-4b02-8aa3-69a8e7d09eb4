package com.dt.domain.bill.tally.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.domain.bill.tally.entity.TallyDetail;
import com.dt.domain.bill.tally.mapper.TallyDetailMapper;
import com.dt.domain.bill.tally.service.ITallyDetailService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-02-16
 */
@Service
public class TallyDetailServiceImpl extends ServiceImpl<TallyDetailMapper, TallyDetail> implements ITallyDetailService {

    @Resource
    TallyDetailMapper tallyDetailMapper;
    @Override
    public IPage<TallyDetail> getPageGroupDetail(Page<TallyDetail> page, LambdaQueryWrapper<TallyDetail> queryWrapper) {
        return tallyDetailMapper.getPageGroupDetail(page,queryWrapper);
    }
}
