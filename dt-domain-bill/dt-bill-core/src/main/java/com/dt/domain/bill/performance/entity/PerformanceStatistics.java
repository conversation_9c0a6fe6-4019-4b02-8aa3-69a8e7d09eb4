package com.dt.domain.bill.performance.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 * 人员操作统计
 * </p>
 *
 * <AUTHOR>
 * @since 2021-02-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_performance_statistics")
@ApiModel(value="PerformanceStatistics对象", description="人员操作统计")
public class PerformanceStatistics extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    @ApiModelProperty(value = "员工")
    private String worker;

    @ApiModelProperty(value = "拣选单类型")
    private String pickType;

    @ApiModelProperty(value = "日期")
    private Long workDate;

    @ApiModelProperty(value = "拣货数量")
    private Integer pickCount;

    @ApiModelProperty(value = "拣货订单数量")
    private Integer pickOrderCount;

    @ApiModelProperty(value = "拣货SKU数量")
    private BigDecimal pickSkuCount;

    @ApiModelProperty(value = "复核订单数量")
    private Integer checkOrderCount;

    @ApiModelProperty(value = "复核包裹数量")
    private Integer checkPackageCount;

    @ApiModelProperty(value = "复核sku数量")
    private BigDecimal checkSkuCount;

    @ApiModelProperty(value = "抽检包裹数量")
    private Integer inspectionPackageCount;

    @ApiModelProperty(value = "称重包裹数量")
    private Integer weighingPackageCount;

    @ApiModelProperty(value = "入库sku数量")
    private BigDecimal inStockSkuCount;

    @ApiModelProperty(value = "上架sku数量")
    private BigDecimal shelfSkuCount;

    @ApiModelProperty(value = "移位sku数量")
    private BigDecimal moveSkuCount;

    @ApiModelProperty(value = "已处理的明细ID")
    private Long doneDetailId;

    @ApiModelProperty(value = "批量复核订单数量")
    private Integer batchCheckOrderCount;

    @ApiModelProperty(value = "批量复核包裹数量")
    private Integer batchCheckPackageCount;

    @ApiModelProperty(value = "批量复核sku数量")
    private BigDecimal batchCheckSkuCount;

    @ApiModelProperty(value = "批量称重包裹数量")
    private Integer batchWeighingPackageCount;

    @ApiModelProperty(value = "秒杀拣货单数")
    private  BigDecimal seckillPickCount;

    @ApiModelProperty(value = "秒杀拣货出库单数")
    private BigDecimal seckillPickOrderCount;

    @ApiModelProperty(value = "秒杀拣货sku件数")
    private BigDecimal seckillPickSkuCount;

    // 分拣批量单数
    private Integer batchSplitOrderCount;
    // 分拣杂单数
    private Integer splitOrderCount;
    // 分拣批量单sku件数
    private Integer batchSplitSkuCount;
    // 分拣杂单sku件数
    private Integer splitSkuCount;
}