package com.dt.domain.bill.carryover.service.impl;

import com.dt.domain.bill.carryover.entity.BookCarryoverTask;
import com.dt.domain.bill.carryover.mapper.BookCarryoverTaskMapper;
import com.dt.domain.bill.carryover.service.IBookCarryoverTaskService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 账册结转单生成任务 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-19
 */
@Service
public class BookCarryoverTaskServiceImpl extends ServiceImpl<BookCarryoverTaskMapper, BookCarryoverTask> implements IBookCarryoverTaskService {

}
