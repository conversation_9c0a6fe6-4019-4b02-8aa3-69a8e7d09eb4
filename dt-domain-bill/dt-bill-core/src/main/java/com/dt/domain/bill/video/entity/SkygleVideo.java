package com.dt.domain.bill.video.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 全视之眼视频
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_skygle_video")
@ApiModel(value="SkygleVideo对象", description="全视之眼视频")
public class SkygleVideo extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "质检台编码 不允许修改，唯一")
    private String workBench;

    @ApiModelProperty(value = "业务单号")
    private String billNo;

    @ApiModelProperty(value = "业务类型")
    private String businessType;

    @ApiModelProperty(value = "状态")
    private String status;

    @ApiModelProperty(value = "开始录制时间")
    private Long startTime;

    @ApiModelProperty(value = "结束录制时间")
    private Long endTime;

    @ApiModelProperty(value = "开始录制人员")
    private String startBy;

    @ApiModelProperty(value = "结束录制人员")
    private String endBy;

    @ApiModelProperty(value = "拓展信息")
    private String extraJson;


}