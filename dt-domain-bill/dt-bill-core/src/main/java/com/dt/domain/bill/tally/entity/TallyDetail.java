package com.dt.domain.bill.tally.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-02-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_tally_detail")
@ApiModel(value="TallyDetail对象", description="")
public class TallyDetail extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "货主编码 取值货主档案")
    private String cargoCode;

    @ApiModelProperty(value = "理货编号")
    private String tallyCode;

    @ApiModelProperty(value = "商品代码")
    private String skuCode;

    @ApiModelProperty(value = "商品条码")
    private String upcCode;

    @ApiModelProperty(value = "商品名称")
    private String skuName;

    @ApiModelProperty(value = "批次号(入库单不填)")
    private String skuLotNo;

    @ApiModelProperty(value = "商品属性")
    private String skuQuality;

    @ApiModelProperty(value = "生产日期")
    private Long manufDate;

    @ApiModelProperty(value = "失效日期")
    private Long expireDate;

    @ApiModelProperty(value = "商品数量")
    private BigDecimal skuQty;

    @ApiModelProperty(value = "默认正常(NORMAL),多件(OVERSHIP)，多品(OVERSHIP_SKU)")
    private String extraGoods;

    @ApiModelProperty(value = "是否拓传")
    private String callBackUpper;

    /**
     * 已收货商品数量
     */
    @ApiModelProperty(value = "已收货商品数量")
    //TODO 2023-04-07  废弃此字段,不做业务使用 解耦收货和理货报告的耦合
    private BigDecimal recQty;

    @ApiModelProperty(value = "状态码")
    private String status;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "效期码")
    private String validityCode;

    @ApiModelProperty(value = "托盘码")
    private String palletCode;

//------------------------
    @ApiModelProperty(value = "父行号")
    private String parentLineNo;

    @ApiModelProperty(value = "行号")
    private String lineNo;

    @ApiModelProperty(value = "异常类型")
    private String abnormalType;

    @ApiModelProperty(value = "是否真实收货")
    private String realReceive;

    @ApiModelProperty(value = "图片地址")
    private String imageUrlJson;

    @ApiModelProperty(value = "标记")
    private Integer mark;

    @ApiModelProperty(value = "残次等级")
    private String inventoryType;


}