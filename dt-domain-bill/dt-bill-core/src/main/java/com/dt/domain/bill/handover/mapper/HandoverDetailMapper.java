package com.dt.domain.bill.handover.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dt.domain.bill.handover.entity.HandoverDetail;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-12
 */
public interface HandoverDetailMapper extends BaseMapper<HandoverDetail> {
    Integer getMaxLineSeq(@Param("handoverCode") String handoverCode);

}
