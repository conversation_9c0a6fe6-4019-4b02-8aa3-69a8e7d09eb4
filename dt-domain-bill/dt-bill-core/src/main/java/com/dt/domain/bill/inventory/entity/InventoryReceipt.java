package com.dt.domain.bill.inventory.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 盘点单
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_inventory_receipt")
@ApiModel(value="InventoryReceipt对象", description="盘点单")
public class InventoryReceipt extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "盘点计划编码")
    private String inventoryPlanCode;

    @ApiModelProperty(value = "盘点计划类型")
    private String inventoryPlanType;

    @ApiModelProperty(value = "盘点单据编码")
    private String inventoryReceiptCode;

    @ApiModelProperty(value = "巷道编码")
    private String tunnelCode;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    @ApiModelProperty(value = "库区编码")
    private String zoneCode;

    @ApiModelProperty(value = "盘点单据状态")
    private String status;

    @ApiModelProperty(value = "一盘任务领取人")
    private String firstPlatePerson;

    @ApiModelProperty(value = "二盘任务领取人")
    private String secondPlatePerson;

    @ApiModelProperty(value = "三盘任务领取人")
    private String thirdPlatePerson;

    @ApiModelProperty(value = "计划品类数")
    private Integer planSkuTypeNum;

    @ApiModelProperty(value = "计划账面数")
    private Integer planAccountNum;

    @ApiModelProperty(value = "实盘品类数")
    private Integer actualSkuTypeNum;

    @ApiModelProperty(value = "实盘数")
    private Integer actualNum;

    @ApiModelProperty(value = "切割维度")
    private String splitRule;

}