package com.dt.domain.bill.material.service.impl;

import com.dt.domain.bill.material.entity.StructMaterial;
import com.dt.domain.bill.material.mapper.StructMaterialMapper;
import com.dt.domain.bill.material.service.IStructMaterialService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 包裹结构绑定包材 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-22
 */
@Service
public class StructMaterialServiceImpl extends ServiceImpl<StructMaterialMapper, StructMaterial> implements IStructMaterialService {

}
