package com.dt.domain.bill.finance.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 金融监管处置单
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_finance_disposal")
@ApiModel(value="FinanceDisposal对象", description="金融监管处置单")
public class FinanceDisposal extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;



    @ApiModelProperty(value = "来源货主编码")
    private String originCargoCode;

    @ApiModelProperty(value = "目标货主编码")
    private String targetCargoCode;

    @ApiModelProperty(value = "监管处置单号")
    private String disposalCode;

    @ApiModelProperty(value = "erp单号")
    private String erpCode;

    @ApiModelProperty(value = "处置单类型")
    private String type;

    @ApiModelProperty(value = "数量 默认:0")
    private BigDecimal qty;

    /**
     * 商品种类
     */
    @ApiModelProperty(value = "数量 默认:0")
    private Integer skuType;

    @ApiModelProperty(value = "监管处置时间 (时间戳)")
    private Long disposalTime;

    @ApiModelProperty(value = "审核结果说明")
    private String message;

    @ApiModelProperty(value = "单据状态")
    private String status;

    @ApiModelProperty(value = "通知状态")
    private Integer notifyStatus;

    @ApiModelProperty(value = "回传通知时间")
    private Long notifyTime;

    @ApiModelProperty(value = "备注")
    private String remark;


}