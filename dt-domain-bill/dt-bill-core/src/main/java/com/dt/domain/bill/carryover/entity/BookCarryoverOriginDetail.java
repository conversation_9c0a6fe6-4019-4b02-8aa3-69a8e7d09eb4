package com.dt.domain.bill.carryover.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.dt.component.mp.entity.BaseEntity;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 * 账册结转单原始明细
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-22
 */
@Data
@Accessors(chain = true)
@TableName("dt_book_carryover_origin_detail")
@ApiModel(value="BookCarryoverOriginDetail对象", description="账册结转单原始明细")
public class BookCarryoverOriginDetail  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "物理主键")
    @TableId(type = IdType.AUTO)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @ApiModelProperty(value = "仓库编码")
    @TableField(fill = FieldFill.INSERT)
    private String warehouseCode;
    

    @ApiModelProperty(value = "单据编号")
    private String billNo;

    @ApiModelProperty(value = "主键id")
    private Long detailId;

    @ApiModelProperty("单据类型")
    private String billType;
    
    @ApiModelProperty("料号")
    private String itemCode;

    @ApiModelProperty(value = "商品编码")
    private String skuCode;

    @ApiModelProperty(value = "变动数量")
    private BigDecimal changeQty;

    @ApiModelProperty(value = "来源库区")
    private String originZoneCode;

    @ApiModelProperty(value = "来源企业")
    private String originEnterprise;

    @ApiModelProperty(value = "目标库区")
    private String targetZoneCode;

    @ApiModelProperty(value = "目标企业")
    private String targetEnterprise;

    @ApiModelProperty(value = "统计时间")
    private Long statisticTime;

    @ApiModelProperty(value = "备注")
    private String remark;

}