package com.dt.domain.bill.inventory.service.impl;

import com.dt.domain.bill.inventory.entity.InventoryLog;
import com.dt.domain.bill.inventory.mapper.InventoryLogMapper;
import com.dt.domain.bill.inventory.service.IInventoryLogService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 盘点相关日志表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-11
 */
@Service
public class InventoryLogServiceImpl extends ServiceImpl<InventoryLogMapper, InventoryLog> implements IInventoryLogService {

}
