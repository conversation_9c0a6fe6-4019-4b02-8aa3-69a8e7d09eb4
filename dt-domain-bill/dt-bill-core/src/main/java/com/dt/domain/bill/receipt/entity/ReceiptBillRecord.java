package com.dt.domain.bill.receipt.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 收货凭据表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_receipt_bill_record")
@ApiModel(value="ReceiptBillRecord对象", description="收货凭据表")
public class ReceiptBillRecord extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "收货单号")
    private String billNo;

    @ApiModelProperty(value = "供应商")
    private String supplyName;

    @ApiModelProperty(value = "库区")
    private String zoneName;

    @ApiModelProperty(value = "入库负责人")
    private String recBy;

    @ApiModelProperty(value = "入库时间")
    private Long recDate;

    @ApiModelProperty(value = "检验人员")
    private String checkBy;

    @ApiModelProperty(value = "检验人数")
    private Integer checkByNum;

    @ApiModelProperty(value = "入库总数量")
    private BigDecimal recSkuQty;

    @ApiModelProperty(value = "产品名称")
    private String goodsName;

    @ApiModelProperty(value = "产品编码")
    private String goodsCode;

    @ApiModelProperty(value = "型号规格")
    private String unitName;

    @ApiModelProperty(value = "单据类型")
    private String status;

    @ApiModelProperty(value = "备注")
    private String remark;


}