package com.dt.domain.bill.trajectory.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dt.domain.bill.trajectory.entity.TrajectoryDetail;
import com.dt.domain.bill.trajectory.mapper.TrajectoryDetailMapper;
import com.dt.domain.bill.trajectory.service.ITrajectoryDetailService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 线路/轨迹流向管理:明细 服务实现类
 * </p>
 *
 * <AUTHOR> xy
 * @since 2020-09-11
 */
@Service
public class TrajectoryDetailServiceImpl extends ServiceImpl<TrajectoryDetailMapper, TrajectoryDetail> implements ITrajectoryDetailService {

}
