package com.dt.domain.bill.message.service.impl;

import com.dt.domain.bill.message.entity.MessageMq;
import com.dt.domain.bill.message.mapper.MessageMqMapper;
import com.dt.domain.bill.message.service.IMessageMqService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 消息信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-03
 */
@Service
public class MessageMqServiceImpl extends ServiceImpl<MessageMqMapper, MessageMq> implements IMessageMqService {

    @Override
    public Boolean partialPhysicalDeleteById(List<Long> idList) {
        return this.baseMapper.partialPhysicalDeleteById(idList);
    }
}
