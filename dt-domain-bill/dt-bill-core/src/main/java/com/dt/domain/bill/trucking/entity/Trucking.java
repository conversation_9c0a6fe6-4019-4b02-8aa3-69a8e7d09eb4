package com.dt.domain.bill.trucking.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 装载主表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_trucking")
@ApiModel(value="Trucking对象", description="装载主表")
public class Trucking extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "装载单号")
    private String truckingCode;

    @ApiModelProperty(value = "车牌号")
    private String carNo;

    @ApiModelProperty(value = "包裹数量")
    private Integer packQty;

    @ApiModelProperty(value = "状态码 10:装载中 20:装载完成 00:取消")
    private String status;

    @ApiModelProperty(value = "创建时间 (时间戳)")
    private Long completeTime;


}