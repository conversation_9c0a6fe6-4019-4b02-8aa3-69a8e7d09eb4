package com.dt.domain.bill.rs.service;

import com.dt.domain.bill.rs.entity.OpException;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 淘天用异常单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-13
 */
public interface IOpExceptionService extends IService<OpException> {

    List<OpException> getByOrderCode(String orderCode);

    List<OpException> getListNotProcessedIn15Days();
}
