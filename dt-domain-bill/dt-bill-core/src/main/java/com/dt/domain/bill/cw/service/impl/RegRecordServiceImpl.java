package com.dt.domain.bill.cw.service.impl;

import com.dt.domain.bill.cw.entity.RegRecord;
import com.dt.domain.bill.cw.mapper.RegRecordMapper;
import com.dt.domain.bill.cw.service.IRegRecordService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 托盘记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-16
 */
@Service
public class RegRecordServiceImpl extends ServiceImpl<RegRecordMapper, RegRecord> implements IRegRecordService {

}
