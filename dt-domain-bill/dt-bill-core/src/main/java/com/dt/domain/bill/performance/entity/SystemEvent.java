package com.dt.domain.bill.performance.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 系统事件
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_system_event")
@ApiModel(value="SystemEvent对象", description="系统事件")
public class SystemEvent extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    @ApiModelProperty(value = "员工")
    private String worker;

    @ApiModelProperty(value = "时间类型")
    private String type;

    @ApiModelProperty(value = "时间")
    private Long workTime;

    @ApiModelProperty(value = "日期")
    private Long workDate;

    @ApiModelProperty(value = "单据编号")
    private String billNo;

    private String detailBillNo;
}