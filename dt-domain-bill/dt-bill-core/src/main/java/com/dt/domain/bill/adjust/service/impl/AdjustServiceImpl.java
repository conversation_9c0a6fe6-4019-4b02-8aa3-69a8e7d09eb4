package com.dt.domain.bill.adjust.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dt.domain.bill.adjust.entity.Adjust;
import com.dt.domain.bill.adjust.mapper.AdjustMapper;
import com.dt.domain.bill.adjust.service.IAdjustService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 库存调整 服务实现类
 * </p>
 *
 * <AUTHOR> xy
 * @since 2020-09-11
 */
@Service
public class AdjustServiceImpl extends ServiceImpl<AdjustMapper, Adjust> implements IAdjustService {

}
