package com.dt.domain.bill.rent.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 仓租数据明细
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_warehouse_rent_detail")
@ApiModel(value="WarehouseRentDetail对象", description="仓租数据明细")
public class WarehouseRentDetail extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    @ApiModelProperty(value = "仓租单号")
    private String wsStoreNo;

    @ApiModelProperty(value = "单号")
    private String orderNo;

    @ApiModelProperty(value = "商品编码")
    private String skuCode;

    @ApiModelProperty(value = "字节商品编码(查询ERP)")
    private String outSkuCode;

    @ApiModelProperty(value = "结算库存数")
    private BigDecimal skuQty;

    @ApiModelProperty(value = "字节订单类型")
    private String orderType;

    @ApiModelProperty(value = "仓库库区类型")
    private String warehouseAreaType;

    @ApiModelProperty(value = "库龄")
    private Integer stockCycle;

    @ApiModelProperty(value = "长(mm/毫米) 默认为0，保留一位小数")
    private BigDecimal length;

    @ApiModelProperty(value = "宽(mm/毫米) 默认为0，保留一位小数")
    private BigDecimal width;

    @ApiModelProperty(value = "高(mm/毫米) 默认为0，保留一位小数")
    private BigDecimal height;

    @ApiModelProperty(value = "备注")
    private String remark;


}