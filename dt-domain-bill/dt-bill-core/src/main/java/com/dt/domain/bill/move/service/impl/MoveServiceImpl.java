package com.dt.domain.bill.move.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dt.domain.bill.move.entity.Move;
import com.dt.domain.bill.move.mapper.MoveMapper;
import com.dt.domain.bill.move.service.IMoveService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 移位管理 服务实现类
 * </p>
 *
 * <AUTHOR> xy
 * @since 2020-09-11
 */
@Service
public class MoveServiceImpl extends ServiceImpl<MoveMapper, Move> implements IMoveService {

}
