package com.dt.domain.bill.carryover.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 账册结转单海关回执
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_book_carryover_receipt")
@ApiModel(value="BookCarryoverReceipt对象", description="账册结转单海关回执")
public class BookCarryoverReceipt extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;



    @ApiModelProperty(value = "结转单号")
    private String carryoverCode;

    @ApiModelProperty(value = "清关单号")
    private String inventoryOrderSn;

    @ApiModelProperty(value = "核注单号")
    private String endorsementSn;

    @ApiModelProperty(value = "核注类型")
    private String endorsementType;

    @ApiModelProperty(value = "核注状态")
    private String endorsementStatus;


}