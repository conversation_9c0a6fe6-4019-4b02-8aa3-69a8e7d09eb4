package com.dt.domain.bill.move.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 * 移位明细管理
 * </p>
 *
 * <AUTHOR> xy
 * @since 2020-09-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_move_detail")
@ApiModel(value="MoveDetail对象", description="移位明细管理")
public class MoveDetail extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "移库单号")
    private String moveCode;

//    @ApiModelProperty(value = "仓库编码")
//    private String warehouseCode;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    @ApiModelProperty(value = "商品编码")
    private String skuCode;

    @ApiModelProperty(value = "商品属性")
    private String skuQuality;

    @ApiModelProperty(value = "批次ID 取值货品批次信息")
    private String skuLotNo;

    @ApiModelProperty(value = "来源库区 取值：库区档案，不包含系统默认共用库区")
    private String originZoneCode;

    @ApiModelProperty(value = "来源库位 取值：库位档案，不包含系统默认共用库区中的库位")
    private String originLocationCode;

    @ApiModelProperty(value = "目标库区 取值：库区档案，不包含系统默认共用库区")
    private String targetZoneCode;

    @ApiModelProperty(value = "目标库位 取值：库位档案，不包含系统默认共用库区中的库位")
    private String targetLocationCode;

    @ApiModelProperty(value = "计划数量")
    private BigDecimal expSkuQty;

    @ApiModelProperty(value = "实际数量")
    private BigDecimal actualSkuQty;

    @ApiModelProperty(value = "状态码 取值启用、停用，默认为启用状态(1:启用，-1:停用)")
    private String status;

    @ApiModelProperty(value = "用于库存做幂等")
    private String batchSerialNo;

}