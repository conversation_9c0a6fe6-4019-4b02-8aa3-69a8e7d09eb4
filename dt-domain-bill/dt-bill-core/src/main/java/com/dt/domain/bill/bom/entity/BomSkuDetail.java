package com.dt.domain.bill.bom.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * bom商品代码明细
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_bom_sku_detail")
@ApiModel(value="BomSkuDetail对象", description="bom商品代码明细")
public class BomSkuDetail extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    @ApiModelProperty(value = "商品代码[bom]")
    private String bomSkuCode;

    @ApiModelProperty(value = "子商品代码[bom]")
    private String childSkuCode;

    @ApiModelProperty(value = "子商品条码[bom]")
    private String childUpcCode;

    @ApiModelProperty(value = "子商品名称[bom]")
    private String childSkuName;

    @ApiModelProperty(value = "计划数量 默认:0")
    private BigDecimal skuQty;

    @ApiModelProperty(value = "状态")
    private String status;

    @ApiModelProperty(value = "备注")
    private String remark;


}
