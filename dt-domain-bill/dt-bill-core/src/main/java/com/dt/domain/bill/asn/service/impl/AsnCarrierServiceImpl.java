package com.dt.domain.bill.asn.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dt.domain.bill.asn.entity.AsnCarrier;
import com.dt.domain.bill.asn.mapper.AsnCarrierMapper;
import com.dt.domain.bill.asn.service.IAsnCarrierService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 到货通知单-承运商 服务实现类
 * </p>
 *
 * <AUTHOR> xy
 * @since 2020-09-11
 */
@Service
public class AsnCarrierServiceImpl extends ServiceImpl<AsnCarrierMapper, AsnCarrier> implements IAsnCarrierService {

}
