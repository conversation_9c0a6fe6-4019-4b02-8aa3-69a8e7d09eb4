package com.dt.domain.bill.carryover.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 账册结转单
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_book_carryover")
@ApiModel(value="BookCarryover对象", description="账册结转单")
public class BookCarryover extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "结转单号")
    private String carryoverCode;

    @ApiModelProperty(value = "任务编码")
    private String taskCode;

    @ApiModelProperty(value = "BookCarryoverStatusEnum")
    private String status;

    private String ccsSyncStatus;

    @ApiModelProperty(value = "清关企业(出)")
    private String originEnterprise;

    @ApiModelProperty(value = "清关企业(入)")
    private String targetEnterprise;

    @ApiModelProperty(value = "关联单据")
    private String billNo;

    @ApiModelProperty(value = "统计开始时间")
    private Long statisticStartTime;

    @ApiModelProperty(value = "统计结束时间")
    private Long statisticEndTime;

    @ApiModelProperty(value = "结转完成时间")
    private Long completeDate;


}