package com.dt.domain.bill.tally.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.domain.bill.tally.entity.TallyDetail;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-02-16
 */
public interface ITallyDetailService extends IService<TallyDetail> {

    IPage<TallyDetail> getPageGroupDetail(Page<TallyDetail> page, LambdaQueryWrapper<TallyDetail> queryWrapper);
}
