package com.dt.domain.bill.trucking.service.impl;

import com.dt.domain.bill.trucking.entity.TruckingDetail;
import com.dt.domain.bill.trucking.mapper.TruckingDetailMapper;
import com.dt.domain.bill.trucking.service.ITruckingDetailService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 装载明细表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-05
 */
@Service
public class TruckingDetailServiceImpl extends ServiceImpl<TruckingDetailMapper, TruckingDetail> implements ITruckingDetailService {

}
