package com.dt.domain.bill.rs.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.dt.component.mp.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 淘天用异常单
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_op_exception")
@ApiModel(value="OpException对象", description="淘天用异常单")
public class OpException extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    @ApiModelProperty(value = "异常类型")
    private String abnormalType;

    @ApiModelProperty(value = "异常单号")
    private String abnormalOrderNo;

    @ApiModelProperty(value = "场景")
    private String scenarioType;

    @ApiModelProperty(value = "对应业务单号")
    private String orderCode;

    @ApiModelProperty(value = "异常单据的状态")
    private String status;

    @ApiModelProperty(value = "运单号")
    private String mailMo;

    @ApiModelProperty(value = "物流公司名称")
    private String logisticsName;



    @ApiModelProperty(value = "寄件人电话(用于搜索）")
    private String senderName;

    @ApiModelProperty(value = "寄件人电话(用于搜索)")
    private String senderPhone;

    @ApiModelProperty(value = "扩展属性")
    private String extendProps;

    @ApiModelProperty(value = "匹配指令类型")
    private String instructionType;

    @ApiModelProperty(value = "最后一次收到指令的实际")
    private Long lastInstructionTime;
    @ApiModelProperty(value = "异常处理回告状态")
    private Integer ctbHandledState;
    @ApiModelProperty(value = "异常提报回告状态")
    private Integer ctbRegisterState;

    @ApiModelProperty(value = "收集人信息")
    @TableField("receiver_Info")
    private String receiverInfo;
    /**
     * 销售平台来源
     */
    private String saleSource;

    @ApiModelProperty(value = "退货类型")
    private Integer returnType;
    private String senderPovince;
    private String senderCity;
    private String senderArea;
    private String senderTown;
    private String senderDetailAddress;
    private String orgReturnNo;
    private String extraJson;
    private String logisticsCode;
    /**
     * 推货理由
     */
    private String returnReason;

}