package com.dt.domain.bill.intercept.service.impl;

import com.dt.domain.bill.intercept.entity.ExpressIntercept;
import com.dt.domain.bill.intercept.mapper.ExpressInterceptMapper;
import com.dt.domain.bill.intercept.service.IExpressInterceptService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 快递拦截 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-18
 */
@Service
public class ExpressInterceptServiceImpl extends ServiceImpl<ExpressInterceptMapper, ExpressIntercept> implements IExpressInterceptService {

}
