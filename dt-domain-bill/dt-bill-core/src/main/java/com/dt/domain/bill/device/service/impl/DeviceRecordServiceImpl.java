package com.dt.domain.bill.device.service.impl;

import com.dt.domain.bill.device.entity.DeviceRecord;
import com.dt.domain.bill.device.mapper.DeviceRecordMapper;
import com.dt.domain.bill.device.service.IDeviceRecordService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 设备数据回传信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-13
 */
@Service
public class DeviceRecordServiceImpl extends ServiceImpl<DeviceRecordMapper, DeviceRecord> implements IDeviceRecordService {

}
