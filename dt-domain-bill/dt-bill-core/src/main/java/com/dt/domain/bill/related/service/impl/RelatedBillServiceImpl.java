package com.dt.domain.bill.related.service.impl;

import com.dt.domain.bill.related.entity.RelatedBill;
import com.dt.domain.bill.related.mapper.RelatedBillMapper;
import com.dt.domain.bill.related.service.IRelatedBillService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 单据关联单号 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-17
 */
@Service
public class RelatedBillServiceImpl extends ServiceImpl<RelatedBillMapper, RelatedBill> implements IRelatedBillService {

}
