package com.dt.domain.bill.information.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 特殊信息采集明细
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_special_information_detail")
@ApiModel(value="SpecialInformationDetail对象", description="特殊信息采集明细")
public class SpecialInformationDetail extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    @ApiModelProperty(value = "采集信息编码")
    private String specialNo;

    @ApiModelProperty(value = "商品代码")
    private String skuCode;

    @ApiModelProperty(value = "商品名称")
    private String skuName;

    @ApiModelProperty(value = "条码")
    private String upcCode;

    @ApiModelProperty(value = "托盘号（REG）")
    private String reg;

    @ApiModelProperty(value = "箱码（SERIAL）")
    private String serial;

    @ApiModelProperty(value = "批次号（LOTNUMBER）")
    private String lotNumber;

    @ApiModelProperty(value = "装箱日期（生产日期）")
    private Long manufDate;

    @ApiModelProperty(value = "到期日期（失效日期）")
    private Long expireDate;

    @ApiModelProperty(value = "商品属性")
    private String skuQuality;

    @ApiModelProperty(value = "采集箱数")
    private BigDecimal boxQty;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "采集人")
    private String collectBy;

    @ApiModelProperty(value = "采集时间 (时间戳)")
    private Long collectTime;

    @ApiModelProperty(value = "拓展字段")
    private String extraJson;


}
