package com.dt.domain.bill.finance.service.impl;

import com.dt.domain.bill.finance.entity.FinanceDisposalDetail;
import com.dt.domain.bill.finance.mapper.FinanceDisposalDetailMapper;
import com.dt.domain.bill.finance.service.IFinanceDisposalDetailService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 金融监管处置单明细 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-13
 */
@Service
public class FinanceDisposalDetailServiceImpl extends ServiceImpl<FinanceDisposalDetailMapper, FinanceDisposalDetail> implements IFinanceDisposalDetailService {

}
