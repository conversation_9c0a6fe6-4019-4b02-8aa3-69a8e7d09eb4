package com.dt.domain.bill.finance.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 金融单据拦截单
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_finance_intercept_bill")
@ApiModel(value="FinanceInterceptBill对象", description="金融单据拦截单")
public class FinanceInterceptBill extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;



    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    @ApiModelProperty(value = "单据号")
    private String billNo;

    @ApiModelProperty(value = "单据类型")
    private String billType;

    @ApiModelProperty(value = "erp单号")
    private String erpCode;

    @ApiModelProperty(value = "单据状态")
    private String status;


}