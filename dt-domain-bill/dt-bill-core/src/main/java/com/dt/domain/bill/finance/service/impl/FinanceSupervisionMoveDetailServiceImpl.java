package com.dt.domain.bill.finance.service.impl;

import com.dt.domain.bill.finance.entity.FinanceSupervisionMoveDetail;
import com.dt.domain.bill.finance.mapper.FinanceSupervisionMoveDetailMapper;
import com.dt.domain.bill.finance.service.IFinanceSupervisionMoveDetailService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 金融监管单移位明细 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-13
 */
@Service
public class FinanceSupervisionMoveDetailServiceImpl extends ServiceImpl<FinanceSupervisionMoveDetailMapper, FinanceSupervisionMoveDetail> implements IFinanceSupervisionMoveDetailService {

}
