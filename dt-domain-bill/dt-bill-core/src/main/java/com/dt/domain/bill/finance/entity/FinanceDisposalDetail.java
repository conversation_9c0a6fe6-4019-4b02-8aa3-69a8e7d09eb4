package com.dt.domain.bill.finance.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 金融监管处置单明细
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_finance_disposal_detail")
@ApiModel(value="FinanceDisposalDetail对象", description="金融监管处置单明细")
public class FinanceDisposalDetail extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "来源货主编码")
    private String originCargoCode;

    @ApiModelProperty(value = "目标货主编码")
    private String targetCargoCode;

    @ApiModelProperty(value = "监管处置单号")
    private String disposalCode;

    @ApiModelProperty(value = "来源商品编码")
    private String originSkuCode;

    @ApiModelProperty(value = "来源wms批次编码")
    private String originSkuLotNo;

    @ApiModelProperty(value = "来源数量 默认:0")
    private BigDecimal originQty;

    @ApiModelProperty(value = "目标商品编码")
    private String targetSkuCode;

    @ApiModelProperty(value = "单据状态")
    private String status;

    @ApiModelProperty(value = "备注")
    private String remark;


}