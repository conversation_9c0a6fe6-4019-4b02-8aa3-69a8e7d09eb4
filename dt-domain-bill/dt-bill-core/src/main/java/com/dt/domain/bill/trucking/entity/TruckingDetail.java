package com.dt.domain.bill.trucking.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 装载明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_trucking_detail")
@ApiModel(value="TruckingDetail对象", description="装载明细表")
public class TruckingDetail extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "装载单号")
    private String truckingCode;

    @ApiModelProperty(value = "交接单号")
    private String handoverCode;

    @ApiModelProperty(value = "快递单号")
    private String carrierCode;

    @ApiModelProperty(value = "容器号/托盘号")
    private String contCode;

    @ApiModelProperty(value = "包裹数量")
    private Integer packQty;


}