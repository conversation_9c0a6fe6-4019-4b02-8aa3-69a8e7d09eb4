package com.dt.domain.bill.tally.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.domain.bill.tally.entity.TallyDetail;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-02-16
 */
public interface TallyDetailMapper extends BaseMapper<TallyDetail> {

    IPage<TallyDetail> getPageGroupDetail(Page<TallyDetail> page,@Param(Constants.WRAPPER) Wrapper query);
}
