package com.dt.domain.bill.transfer.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 * 库存转移明细
 * </p>
 *
 * <AUTHOR> xy
 * @since 2020-09-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_transfer_detail")
@ApiModel(value="TransferDetail对象", description="库存转移明细")
public class TransferDetail extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "转移单号")
    private String transferCode;

//    @ApiModelProperty(value = "仓库编码")
//    private String warehouseCode;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    @ApiModelProperty(value = "来源商品编码")
    private String originSkuCode;

    @ApiModelProperty(value = "来源批次ID")
    private String originSkuLotNo;

    @ApiModelProperty(value = "来源库位 取值：库区档案，不包含系统默认共用库区中的库位")
    private String originLoactionCode;

    @ApiModelProperty(value = "实物数量")
    private BigDecimal originQty;

    @ApiModelProperty(value = "目标商品编码")
    private String targetSkuCode;

    @ApiModelProperty(value = "目标库位")
    private String targetLocationCode;

    @ApiModelProperty(value = "目标批次ID")
    private String targetSkuLotNo;

    @ApiModelProperty(value = "转移数量")
    private BigDecimal changeQty;

    @ApiModelProperty(value = "来源入库日期")
    private Long originReceiveDate;

    @ApiModelProperty(value = "来源生产日期")
    private Long originManufDate;

    @ApiModelProperty(value = "来源过期日期")
    private Long originExpireDate;

    @ApiModelProperty(value = "来源商品属性")
    private String originSkuQuality;

    @ApiModelProperty(value = "来源生产批次号")
    private String originProductionNo;

    @ApiModelProperty(value = "来源禁售日期")
    private Long originWithdrawDate;

    @ApiModelProperty(value = "目标入库日期")
    private Long targetReceiveDate;

    @ApiModelProperty(value = "目标生产日期")
    private Long targetManufDate;

    @ApiModelProperty(value = "目标过期日期")
    private Long targetExpireDate;

    @ApiModelProperty(value = "目标商品属性")
    private String targetSkuQuality;

    @ApiModelProperty(value = "目标生产批次号")
    private String targetProductionNo;

    @ApiModelProperty(value = "目标禁售日期")
    private Long targetWithdrawDate;

    @ApiModelProperty(value = "状态码 字典:ABLE_STATUS")
    private Integer status;

    @ApiModelProperty(value = "时间标志")
    private String dateFlag;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "调整原因")
    private String reason;

    @ApiModelProperty(value = "责任方")
    private String rp;

    @ApiModelProperty(value = "证明材料")
    private String evidenceInfo;

    @ApiModelProperty(value = "行号")
    private String lineSeq;

    @ApiModelProperty(value = "残次等级")
    private String originInventoryType;
    private String targetInventoryType;
}