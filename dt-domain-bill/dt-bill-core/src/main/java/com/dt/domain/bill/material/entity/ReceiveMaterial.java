package com.dt.domain.bill.material.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 * 耗材领用表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_receive_material")
@ApiModel(value="ReceiveMaterial对象", description="耗材领用表")
public class ReceiveMaterial extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "领用单据号唯一，不允许修改")
    private String receiveMaterialCode;

    @ApiModelProperty(value = "状态码")
    private String status;

    @ApiModelProperty(value = "计划品种数 默认:0")
    private Integer type;

    @ApiModelProperty(value = "计划数量")
    private BigDecimal expQty;

    @ApiModelProperty(value = "领用总数")
    private BigDecimal receiveQty;

    @ApiModelProperty(value = "完成领用时间 (时间戳)")
    private Long completeTime;

    @ApiModelProperty(value = "备注")
    private String remark;


}