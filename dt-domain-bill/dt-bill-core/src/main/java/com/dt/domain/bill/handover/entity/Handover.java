package com.dt.domain.bill.handover.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> xy
 * @since 2020-10-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_handover")
@ApiModel(value="Handover对象", description="交接单")
public class Handover extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

//    @ApiModelProperty(value = "仓库编码")
//    private String warehouseCode;

    @ApiModelProperty(value = "交接单号")
    private String handoverCode;

    @ApiModelProperty(value = "容器编码")
    private String contCode;

    @ApiModelProperty(value = "承运商信息")
    private String carrierCode;

    @ApiModelProperty(value = "包裹数量")
    private Integer packageQty;

    @ApiModelProperty(value = "交接状态")
    private String status;

    @ApiModelProperty(value = "打印状态")
    private String printStatus;

    @ApiModelProperty(value = "交接时间")
    private Long handoverTime;

    @ApiModelProperty(value = "快递网点编码")
    private String expressBranch;

    @ApiModelProperty(value = "快递网点编码名称")
    private String expressBranchName;

    @ApiModelProperty(value = "交接单标识automationType枚举:自动化10，非自动化 20")
    private String automationType;

    @ApiModelProperty(value = "自动化交接出口")
    private String exitCode;
}