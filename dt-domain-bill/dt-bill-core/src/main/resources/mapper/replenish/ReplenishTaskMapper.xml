<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dt.domain.bill.replenish.mapper.ReplenishTaskMapper">
    <select id="getPage" resultType="com.dt.domain.bill.replenish.entity.ReplenishTaskStatistics">
        SELECT
        cargo_code,
        sku_quality,
        COUNT(DISTINCT package_code) AS package_count,
        sku_code,
        upc_code,
        sku_name,
        SUM(qty) AS qty,
        GROUP_CONCAT(DISTINCT type) AS type,
        SUM(def_qty) AS def_qty,
        SUM(rep_qty) AS rep_qty
        FROM
        (
        SELECT
        id,
        warehouse_code,
        cargo_code,
        package_code,
        shipment_order_code,
        type,
        STATUS,
        def_qty,
        rep_qty,
        sku_code,
        upc_code,
        sku_name,
        sku_quality,
        qty,
        version,
        deleted,
        created_by,
        created_time,
        updated_by,
        updated_time
        FROM
        dt_replenish_task

        <if test="ew.emptyOfWhere == false">
            ${ew.customSqlSegment}
        </if>

        ) AA GROUP BY cargo_code, sku_quality,sku_code,upc_code,sku_name

    </select>

    <select id="getListStatistics" resultType="com.dt.domain.bill.replenish.entity.ReplenishTaskStatistics">
        SELECT
        cargo_code,
        sku_quality,
        COUNT(DISTINCT package_code) AS package_count,
        sku_code,
        upc_code,
        sku_name,
        SUM(qty) AS qty,
        GROUP_CONCAT(DISTINCT type) AS type,
        SUM(def_qty) AS def_qty,
        SUM(rep_qty) AS rep_qty
        FROM
        (
        SELECT
        id,
        warehouse_code,
        cargo_code,
        package_code,
        shipment_order_code,
        type,
        STATUS,
        def_qty,
        rep_qty,
        sku_code,
        upc_code,
        sku_name,
        sku_quality,
        qty,
        version,
        deleted,
        created_by,
        created_time,
        updated_by,
        updated_time
        FROM
        dt_replenish_task

        <if test="ew.emptyOfWhere == false">
            ${ew.customSqlSegment}
        </if>
        ) AA GROUP BY cargo_code, sku_quality,sku_code,upc_code,sku_name

    </select>
</mapper>
