<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dt.domain.bill.message.mapper.MessageMqMapper">


    <!-- 部分物理删除用户 -->
    <update id="partialPhysicalDeleteById">
        delete from dt_message_mq
        WHERE id in
        <foreach collection="idList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </update>

</mapper>
