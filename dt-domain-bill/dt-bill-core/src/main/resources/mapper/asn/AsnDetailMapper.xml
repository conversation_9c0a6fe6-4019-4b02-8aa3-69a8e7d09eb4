<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dt.domain.bill.asn.mapper.AsnDetailMapper">

    <select id="getPageGroupDetail" resultType="com.dt.domain.bill.asn.entity.AsnDetail">
        <!--        SELECT-->
        <!--        id,-->
        <!--        cargo_code,-->
        <!--        sku_code,-->
        <!--        upc_code,-->
        <!--        sku_name,-->
        <!--        package_unit_code,-->
        <!--        sku_quality,-->
        <!--        external_sku_lot_no,-->
        <!--        exp_sku_qty-->
        <!--        FROM-->
        <!--        dt_asn_detail-->
        <!--        <if test="ew.emptyOfWhere == false">-->
        <!--            ${ew.customSqlSegment}-->
        <!--        </if>-->

        SELECT
        *
        FROM
        (
        SELECT
        asn_id,
        cargo_code,
        sku_code,
        upc_code,
        sku_name,
        package_unit_code,
        deleted,
        sum(exp_sku_qty) AS exp_sku_qty
        FROM
        dt_asn_detail where deleted=1
        GROUP BY
        asn_id,
        cargo_code,
        sku_code,
        upc_code,
        sku_name,
        package_unit_code,
        deleted
        ) as a
        <if test="ew.emptyOfWhere == false">
            ${ew.customSqlSegment}
        </if>

    </select>


    <select id="getPageGroupDetailByReTurn" resultType="com.dt.domain.bill.asn.entity.AsnDetail">
        SELECT
        id,
        cargo_code,
        ext_no,
        sku_code,
        upc_code,
        sku_name,
        package_unit_code,
        sku_quality,
        inventory_type,
        external_sku_lot_no,
        exp_sku_qty
        FROM
        dt_asn_detail
        <if test="ew.emptyOfWhere == false">
            ${ew.customSqlSegment}
        </if>
    </select>

    <select id="getSkuGroupPage" resultType="com.dt.domain.bill.asn.entity.AsnDetail">
        SELECT
        *
        FROM
        (
        SELECT
        asn_id,
        cargo_code,
        sku_code,
        upc_code,
        sku_name,
        sum(exp_sku_qty) AS exp_sku_qty
        FROM
        dt_asn_detail where deleted=1
        GROUP BY
        asn_id,
        cargo_code,
        sku_code,
        upc_code,
        sku_name
        ) as a
        <if test="ew.emptyOfWhere == false">
            ${ew.customSqlSegment}
        </if>
    </select>


</mapper>
