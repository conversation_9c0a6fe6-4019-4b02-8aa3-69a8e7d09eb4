<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dt.domain.bill.pick.mapper.AllocationOrderMapper">
   <resultMap id="AllocationOrderMap" type="com.dt.domain.bill.pick.entity.AllocationOrder">
      <result column="cargo_code" property="cargoCode"/>
      <result column="shipment_order_code" property="shipmentOrderCode"/>
      <result column="sku_code" property="skuCode"/>
      <result column="sku_lot_no" property="skuLotNo"/>
      <result column="location_code" property="locationCode"/>
      <result column="exp_qty" property="expQty"/>
      <result column="real_qty" property="realQty"/>
      <result column="pick_qty" property="pickQty"/>
   </resultMap>

   <select id="getShipmentOrderPage" resultMap="AllocationOrderMap">
      SELECT
         cargo_code,
         shipment_order_code,
         sku_code,
         sku_lot_no,
         location_code,
         sum(exp_qty) as exp_qty,
         sum(real_qty) as real_qty,
         sum(pick_qty) as pick_qty
      FROM
         dt_allocation_order
      <if test="ew.emptyOfWhere == false">
         ${ew.customSqlSegment}
      </if>
      GROUP BY
         shipment_order_code,
         sku_code,
         sku_lot_no,
         location_code
      ORDER BY shipment_order_code desc
   </select>

   <select id="getShipmentOrderList" resultMap="AllocationOrderMap">
      SELECT
      cargo_code,
      shipment_order_code,
      sku_code,
      sku_lot_no,
      location_code,
      sum(exp_qty) as exp_qty,
      sum(real_qty) as real_qty,
      sum(pick_qty) as pick_qty
      FROM
      dt_allocation_order
      <if test="ew.emptyOfWhere == false">
         ${ew.customSqlSegment}
      </if>
      GROUP BY
      shipment_order_code,
      sku_code,
      sku_lot_no,
      location_code
      ORDER BY shipment_order_code desc
   </select>
</mapper>
