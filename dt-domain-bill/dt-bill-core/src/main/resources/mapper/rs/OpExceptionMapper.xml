<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dt.domain.bill.rs.mapper.OpExceptionMapper">


    <select id="getByOrderCode" resultType="com.dt.domain.bill.rs.entity.OpException">
        select * from dt_op_exception where order_code LIKE CONCAT('%', #{orderCode}, '%')
    </select>
</mapper>
