<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dt.domain.bill.pick.mapper.PickDetailMapper">


    <select id="getBillPrintAgainPage" resultType="com.dt.domain.bill.pick.entity.PickDetail">
        SELECT
        cargo_code,
        package_code,
        express_no,
        package_status,
        pick_code,
        basket_no,
        created_time
        FROM
        (
        SELECT
            dt_pick_detail.cargo_code AS cargo_code,
            dt_pick_detail.package_code AS package_code,
            dt_pick_detail.express_no AS express_no,
            dt_pick_detail.package_status as package_status,
            pick_code,
            basket_no,
            dt_pick_detail.created_time AS created_time
            FROM
            dt_pick_detail
            <if test="ew.emptyOfWhere == false">
                ${ew.customSqlSegment}
            </if>
        ) AA  ORDER BY pick_code DESC,CONVERT(IFNULL(basket_no,'0'),SIGNED) ASC
    </select>
    <!--
    <select id="getBillPrintAgainPage" resultType="com.dt.domain.bill.pick.entity.PickDetail">
        SELECT
        cargo_code,
        package_code,
        express_no,
        package_status,
        pick_code,
        basket_no,
        created_time
        FROM
        (
        SELECT
        dt_pick_detail.cargo_code AS cargo_code,
        dt_pick_detail.package_code AS package_code,
        dt_pick_detail.express_no AS express_no,
        (SELECT  STATUS FROM dt_package WHERE dt_package.package_code = dt_pick_detail.package_code) AS package_status,
        pick_code,
        basket_no,
        dt_pick_detail.created_time AS created_time
        FROM
        dt_pick_detail
        <if test="ew.emptyOfWhere == false">
            ${ew.customSqlSegment}
        </if>

        ) AA  ORDER BY pick_code DESC,CONVERT(IFNULL(basket_no,'0'),SIGNED) ASC
    </select>
    -->
    <!--
    <select id="getBillPrintAgainPage" resultType="com.dt.domain.bill.pick.entity.PickDetail">
        SELECT
        cargo_code,
        package_code,
        express_no,
        package_status,
        pick_code,
        basket_no,
        created_time
        FROM
        (
        SELECT
        dt_pick_detail.cargo_code AS cargo_code,
        dt_pick_detail.package_code AS package_code,
        dt_pick_detail.express_no AS express_no,
        (SELECT  STATUS FROM dt_package WHERE dt_package.package_code = dt_pick_detail.package_code) AS package_status,
        pick_code,
        basket_no,
        dt_pick_detail.created_time AS created_time
        FROM
        dt_pick_detail
        <if test="ew.emptyOfWhere == false">
            ${ew.customSqlSegment}
        </if>

        ) AA  ORDER BY  created_time DESC
    </select>
        -->
    <!--
    <select id="getMarkBillPrintAgainPage" resultType="com.dt.domain.bill.pick.entity.PickDetail">
        SELECT
        cargo_code,
        package_code,
        express_no,
        package_status,
        pick_code,
        basket_no,
        created_time
        FROM
        (
        SELECT
        dt_pick_detail.cargo_code AS cargo_code ,
        dt_pick_detail.package_code AS package_code,
        dt_pick_detail.express_no AS express_no,
        AA.status AS package_status,
        pick_code,
        basket_no,
        dt_pick_detail.created_time AS created_time
        FROM
        dt_pick_detail LEFT FROM dt_package AA ON AA.package_code  = dt_pick_detail.package_code
        <if test="ew.emptyOfWhere == false">
            ${ew.customSqlSegment}
        </if>
        ) AA
    </select>-->

</mapper>
