<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dt.domain.bill.pick.mapper.AllocationBillMapper">

    <resultMap id="AllocationOrderMap" type="com.dt.domain.bill.pick.entity.AllocationBill">
        <result column="warehouse_code" property="warehouseCode"/>
        <result column="cargo_code" property="cargoCode"/>
        <result column="sku_code" property="skuCode"/>
        <result column="sku_lot_no" property="skuLotNo"/>
        <result column="location_code" property="locationCode"/>
        <result column="qty" property="qty"/>
    </resultMap>


    <select id="getPackGroupByLocationAndSkuLotNo" resultMap="AllocationOrderMap">
        SELECT warehouse_code,cargo_code,sku_code,sku_lot_no,location_code,sum(pick_qty) as qty
        FROM dt_allocation_order
        <where>

            <if test="warehouseCode !=null and warehouseCode !=''">
                AND warehouse_code = #{warehouseCode}
            </if>

            <if test="cargoCode !=null and cargoCode !=''">
                AND cargo_code = #{cargoCode}
            </if>

            <if test="packCodeList!=null and packCodeList.size()>0">
                AND package_code IN
                <foreach collection="packCodeList" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>

            <if test="waveCode !=null and waveCode !=''">
                AND wave_code = #{waveCode}
            </if>


        </where>
        group by warehouse_code,cargo_code,sku_code,sku_lot_no,location_code
    </select>


</mapper>
