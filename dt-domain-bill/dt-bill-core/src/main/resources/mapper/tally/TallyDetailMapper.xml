<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dt.domain.bill.tally.mapper.TallyDetailMapper">

    <select id="getPageGroupDetail" resultType="com.dt.domain.bill.tally.entity.TallyDetail">
        SELECT
        *
        FROM
        (
        SELECT
        tally_code,
        cargo_code,
        sku_code,
        sku_name,
        sku_quality,
        inventory_type,
        manuf_date,
        expire_date,
        extra_goods,
        call_back_upper,
        real_receive,
        sum(sku_qty) AS sku_qty
        FROM
        dt_tally_detail where deleted=1
        GROUP BY
        tally_code,
        cargo_code,
        sku_code,
        sku_name,
        sku_quality,
        inventory_type,
        manuf_date,
        expire_date,
        extra_goods,
        call_back_upper,
        real_receive
        ) as a
        <if test="ew.emptyOfWhere == false">
            ${ew.customSqlSegment}
        </if>

    </select>


</mapper>
