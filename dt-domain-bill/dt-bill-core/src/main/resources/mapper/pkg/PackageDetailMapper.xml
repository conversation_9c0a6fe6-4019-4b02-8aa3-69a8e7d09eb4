<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dt.domain.bill.pkg.mapper.PackageDetailMapper">
    <select id="getReferShipmentOrderCode" resultType="String">
        SELECT DISTINCT shipment_order_code  FROM  dt_package WHERE package_code in (
        SELECT package_code FROM dt_package_detail  <if test="ew.emptyOfWhere == false">
        ${ew.customSqlSegment}
    </if>
        )
    </select>


    <select id="getListForOrderBy" resultType="com.dt.domain.bill.pkg.entity.PackageDetail">
        SELECT BB.po_no,AA.id,
        AA.warehouse_code,
        AA.cargo_code,
        AA.package_code,
        AA.pack_uid,
        AA.p_uid,
        AA.line_seq,
        AA.status,
        AA.collect_status,
        AA.sku_code,
        AA.upc_code,
        AA.sku_name,
        AA.sku_lot_no,
        AA.sku_qty,
        AA.exp_qty,
        AA.assign_qty,
        AA.pick_qty,
        AA.check_qty,
        AA.out_stock_qty,
        AA.package_unit_code,
        AA.allocation_rule_code,
        AA.turnover_rule_code,
        AA.zone_type,
        AA.sku_quality,
        AA.version,
        AA.created_by,
        AA.created_time,
        AA.updated_by,
        AA.deleted,
        AA.updated_time FROM  dt_package_detail AA LEFT JOIN dt_package BB on AA.package_code = BB.package_code
        <where>
            <![CDATA[  BB.status <> '' ]]>
            <if test="packageCodeList!=null and packageCodeList.size()>0">
                AND BB.package_code IN
                <foreach collection="packageCodeList" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY BB.po_no ,AA.package_code
    </select>
</mapper>
