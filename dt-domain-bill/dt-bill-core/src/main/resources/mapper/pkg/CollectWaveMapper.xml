<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dt.domain.bill.pkg.mapper.CollectWaveMapper">

    <resultMap id="CollectWaveMap" type="com.dt.domain.bill.pkg.entity.CollectWave">
        <result column="warehouse_code" property="warehouseCode"/>
        <result column="cargo_code" property="cargoCode"/>
        <result column="carrier_code" property="carrierCode"/>
        <result column="sale_platform" property="salePlatform"/>
        <result column="sale_shop_id" property="saleShopId"/>
        <result column="business_type" property="businessType"/>
        <result column="package_struct" property="packageStruct"/>
        <result column="num" property="num"/>
    </resultMap>

    <select id="getCollectWaveList" resultMap="CollectWaveMap">
        SELECT warehouse_code,cargo_code,carrier_code,business_type,sale_platform,sale_shop_id,count(1) as num
        FROM dt_package
        <where>
            <if test="deleted !=null and deleted !=''">
                AND deleted = #{deleted}
            </if>
            <if test="isPre !=null and isPre !=''">
                AND is_pre = #{isPre}
            </if>
            <if test="warehouseCode !=null and warehouseCode !=''">
                AND warehouse_code = #{warehouseCode}
            </if>

            <if test="cargoCode !=null and cargoCode !=''">
                AND cargo_code = #{cargoCode}
            </if>
            <if test="cargoCodeList!=null and cargoCodeList.size()>0">
                AND cargo_code IN
                <foreach collection="cargoCodeList" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>

            <if test="packageCode !=null and packageCode !=''">
                AND package_code = #{packageCode}
            </if>
            <if test="packageCodeList!=null and packageCodeList.size()>0">
                AND package_code IN
                <foreach collection="packageCodeList" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>

            <if test="poNo !=null and poNo !=''">
                AND po_no = #{poNo}
            </if>
            <if test="poNoList!=null and poNoList.size()>0">
                AND po_no IN
                <foreach collection="poNoList" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>

            <if test="soNo !=null and soNo !=''">
                AND so_no = #{soNo}
            </if>
            <if test="soNoList!=null and soNoList.size()>0">
                AND so_no IN
                <foreach collection="soNoList" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>

            <if test="carrierCode !=null and carrierCode !=''">
                AND carrier_code = #{carrierCode}
            </if>
            <if test="carrierCodeList!=null and carrierCodeList.size()>0">
                AND carrier_code IN
                <foreach collection="carrierCodeList" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>

            <if test="carrierCode ==null and carrierCode =='' and carrierCodeList == null and carrierCodeList.size()==0 ">
                AND carrier_code != ""
            </if>

            <if test="expressNo !=null and expressNo !=''">
                AND express_no = #{expressNo}
            </if>
            <if test="expressNoList!=null and expressNoList.size()>0">
                AND express_no IN
                <foreach collection="expressNoList" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>

            <if test="expressNo ==null and expressNo =='' and expressNoList == null and expressNoList.size()==0 ">
                AND express_no != ""
            </if>

            <if test="shipmentOrderCode !=null and shipmentOrderCode !=''">
                AND shipment_order_code = #{shipmentOrderCode}
            </if>
            <if test="shipmentOrderCodeList!=null and shipmentOrderCodeList.size()>0">
                AND shipment_order_code IN
                <foreach collection="shipmentOrderCodeList" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>

            <if test="salePlatform !=null and salePlatform !=''">
                AND sale_platform = #{salePlatform}
            </if>
            <if test="salePlatformList!=null and salePlatformList.size()>0">
                AND sale_platform IN
                <foreach collection="salePlatformList" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>

            <if test="saleShopId !=null and saleShopId !=''">
                AND sale_shop_id = #{saleShopId}
            </if>
            <if test="saleShopIdList!=null and saleShopIdList.size()>0">
                AND sale_shop_id IN
                <foreach collection="saleShopIdList" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>

            <if test="status !=null and status !=''">
                AND status = #{status}
            </if>
            <if test="statusList!=null and statusList.size()>0">
                AND status IN
                <foreach collection="statusList" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>

            <if test="businessType !=null and businessType !=''">
                AND business_type = #{businessType}
            </if>

            <if test="packageStruct !=null and packageStruct !=''">
                AND package_struct = #{packageStruct}
            </if>

            <if test="collectStatus ==null and collectStatus ==''">
                AND collect_status = ""
            </if>

            <if test="collectStatus !=null and collectStatus !=''">
                AND collect_status = #{collectStatus}
            </if>

            <if test="packageStructList!=null and packageStructList.size()>0">
                AND package_struct IN
                <foreach collection="packageStructList" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>

        </where>
        group by warehouse_code,cargo_code,carrier_code,business_type,sale_platform,sale_shop_id
    </select>


    <select id="querySpikeCollectWave" resultMap="CollectWaveMap">
        SELECT warehouse_code,cargo_code,carrier_code,sale_platform,sku_quality,package_struct,analysis_sku,count(1) as num
        FROM dt_package
        <where>
            <if test="warehouseCode !=null and warehouseCode !=''">
                AND warehouse_code = #{warehouseCode}
            </if>
            <if test="isPre !=null and isPre !=''">
                AND is_pre = #{isPre}
            </if>
            <if test="cargoCode !=null and cargoCode !=''">
                AND cargo_code = #{cargoCode}
            </if>

            <if test="waveCode !=null and waveCode !=''">
                AND wave_code = #{waveCode}
            </if>

            <if test="cargoCodeList!=null and cargoCodeList.size()>0">
                AND cargo_code IN
                <foreach collection="cargoCodeList" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>

            <if test="packageCode !=null and packageCode !=''">
                AND package_code = #{packageCode}
            </if>
            <if test="packageCodeList!=null and packageCodeList.size()>0">
                AND package_code IN
                <foreach collection="packageCodeList" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>

            <if test="carrierCode !=null and carrierCode !=''">
                AND carrier_code = #{carrierCode}
            </if>
            <if test="carrierCodeList!=null and carrierCodeList.size()>0">
                AND carrier_code IN
                <foreach collection="carrierCodeList" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>

            <if test="carrierCode ==null and carrierCode =='' and carrierCodeList == null and carrierCodeList.size()==0 ">
                AND carrier_code != ""
            </if>


            <if test="expressNo ==null and expressNo =='' and expressNoList == null and expressNoList.size()==0 ">
                AND express_no != ""
            </if>

            <if test="salePlatform !=null">
                AND sale_platform = #{salePlatform}
            </if>
            <if test="salePlatformList!=null and salePlatformList.size()>0">
                AND sale_platform IN
                <foreach collection="salePlatformList" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>

            <if test="saleShopId !=null and saleShopId !=''">
                AND sale_shop_id = #{saleShopId}
            </if>
            <if test="saleShopIdList!=null and saleShopIdList.size()>0">
                AND sale_shop_id IN
                <foreach collection="saleShopIdList" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>

            <if test="status !=null and status !=''">
                AND status = #{status}
            </if>
            <if test="statusList!=null and statusList.size()>0">
                AND status IN
                <foreach collection="statusList" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>

            <if test="businessType !=null and businessType !=''">
                AND business_type = #{businessType}
            </if>

            <if test="packageStruct !=null and packageStruct !=''">
                AND package_struct = #{packageStruct}
            </if>
            <if test="packageStructList!=null and packageStructList.size()>0">
                AND package_struct IN
                <foreach collection="packageStructList" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>

        </where>
        group by warehouse_code,cargo_code,carrier_code,sale_platform,sku_quality,package_struct,analysis_sku
    </select>

</mapper>
