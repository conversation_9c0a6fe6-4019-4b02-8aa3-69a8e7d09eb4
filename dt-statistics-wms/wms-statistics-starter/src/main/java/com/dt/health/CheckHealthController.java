package com.dt.health;

import com.dt.component.common.result.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Api(value = "启动之后的健康检查", tags = "启动之后的健康检查")
public class CheckHealthController {

    /**
     * 启动之后的健康检查
     */
    @GetMapping("/health")
    @ApiOperation(value = "启动之后的健康检查", notes = "")
    public Result<Boolean> health() {
        return Result.success(true);
    }
}
