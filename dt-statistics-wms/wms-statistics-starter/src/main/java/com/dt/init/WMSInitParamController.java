package com.dt.init;

import com.dt.component.common.enums.SortEnum;
import com.dt.component.common.enums.bill.ShipmentOrderEnum;
import com.dt.component.common.enums.pkg.PackEnum;
import com.dt.component.common.filter.RpcContextUtil;
import com.dt.component.common.result.Result;
import com.dt.domain.bill.param.PackageParam;
import com.dt.domain.bill.param.ShipmentOrderParam;
import com.dt.domain.statistics.param.shipmentStatistics.ShipmentOrderStatisticsParam;
import com.dt.platform.wms.biz.config.DefaultWarehouseCodeConfig;
import com.dt.platform.wms.integration.IRemoteShipmentOrderClient;
import com.dt.statistics.wms.biz.dateSumary.IDateSummaryBiz;
import com.dt.statistics.wms.biz.shipmentStatistics.IShipmentStatisticsBiz;
import com.dt.statistics.wms.integration.shipmentStatistics.IRemoteShipmentOrderStatisticsClient;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.context.annotation.Profile;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/1/15 13:21
 */
@RestController
@RequestMapping("/dt-wms-portal")
@Profile({"dev", "test", "pre", "prod"})
public class WMSInitParamController {

    @Resource
    private DefaultWarehouseCodeConfig defaultWarehouseCodeConfig;

    @Resource
    IDateSummaryBiz dateSummaryBiz;

    @Resource
    IShipmentStatisticsBiz shipmentStatisticsBiz;

    @Resource
    RocketMQTemplate rocketMQTemplate;

    @Resource
    IRemoteShipmentOrderClient remoteShipmentOrderClient;


    /**
     * 功能描述: 订单大屏统计 创建时间: 2021/6/16 3:41 下午
     *
     * @param warehouseCode:
     * @param startTime:
     * @param endTime:
     * @return com.dt.component.common.result.Result<java.util.Map < java.lang.String, java.lang.Integer>>
     * <AUTHOR>
     */
    @GetMapping(value = "/getShipmentStatistic")
    public Result<Map<String, Integer>> getShipmentStatistic(String warehouseCode, Long startTime, Long endTime) {
        if (Objects.isNull(warehouseCode)) {
            throw new RuntimeException("仓库编码不能为空");
        }
        if (Objects.isNull(startTime)) {
            throw new RuntimeException("查询开始时间不能为空");
        }
        if (Objects.isNull(startTime)) {
            throw new RuntimeException("查询结束时间不能为空");
        }
        if (!defaultWarehouseCodeConfig.getWarehouseCodeList().contains(warehouseCode)) {
            throw new RuntimeException("不存在的的仓库编码，请确认仓库编码");
        }
        RpcContextUtil.setWarehouseCode(warehouseCode);
        Map<String, Integer> returnMap = new HashMap<>();

        ShipmentOrderParam param = new ShipmentOrderParam();
        param.setCreatedTimeStart(startTime);
        param.setCreatedTimeEnd(endTime);
        // 总接入，只查询时间范围，不根据状态来。
        // param.setStatus(ShipmentOrderEnum.STATUS.CREATE_STATUS.getCode());
        Integer totalCount = remoteShipmentOrderClient.count(param);
        returnMap.put("inTotalCount", totalCount);

        ShipmentOrderParam param1 = new ShipmentOrderParam();
        param1.setUpdatedTimeStart(startTime);
        param1.setUpdatedTimeEnd(endTime);
        param1.setStatus(ShipmentOrderEnum.STATUS.OUT_STOCK_STATUS.getCode());
        Integer outTotal = remoteShipmentOrderClient.count(param1);
        returnMap.put("outTotalCount", outTotal);
        return Result.success(returnMap);
    }

    /**
     * 功能描述: 初始化包裹日统计 创建时间: 2021/1/14 1:45 下午
     *
     * @param map:
     * @return com.dt.component.common.result.Result<java.lang.String>
     * <AUTHOR>
     */
    @RequestMapping(value = "/initPackStatusCount", method = RequestMethod.POST,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<String> initPackStatusCount(@RequestBody Map<String, String> map) {
        defaultWarehouseCodeConfig.getWarehouseCodeList().parallelStream().forEach(a -> {
            if (map.isEmpty()) {
                PackageParam packageParam = new PackageParam();
                Map<String, String> sortParamMap = new HashMap<>();
                sortParamMap.put("created_time", SortEnum.ASC.getSort());
                packageParam.setSortParamMap(sortParamMap);
                packageParam.setWarehouseCode(a);
                dateSummaryBiz.initStatusCount(packageParam);
            } else {
                if (a.equals(map.get("warehouseCode"))) {
                    PackageParam packageParam = new PackageParam();
                    Map<String, String> sortParamMap = new HashMap<>();
                    sortParamMap.put("created_time", SortEnum.ASC.getSort());
                    packageParam.setSortParamMap(sortParamMap);
                    packageParam.setWarehouseCode(a);
                    dateSummaryBiz.initStatusCount(packageParam);
                }
            }
        });
        return Result.success();
    }

    /**
     * 功能描述: 初始化出库单日统计 创建时间: 2021/1/14 1:45 下午
     *
     * @param map:
     * @return com.dt.component.common.result.Result<java.lang.String>
     * <AUTHOR>
     */
    @RequestMapping(value = "/initShipStatusCount", method = RequestMethod.POST,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<String> initShipStatusCount(@RequestBody Map<String, String> map) {
        defaultWarehouseCodeConfig.getWarehouseCodeList().parallelStream().forEach(a -> {
            if (map.isEmpty()) {
                ShipmentOrderParam packageParam = new ShipmentOrderParam();
                Map<String, String> sortParamMap = new HashMap<>();
                sortParamMap.put("created_time", SortEnum.ASC.getSort());
                packageParam.setSortParamMap(sortParamMap);
                packageParam.setWarehouseCode(a);
                dateSummaryBiz.initStatusCount(packageParam);
            } else {
                if (a.equals(map.get("warehouseCode"))) {
                    ShipmentOrderParam packageParam = new ShipmentOrderParam();
                    Map<String, String> sortParamMap = new HashMap<>();
                    sortParamMap.put("created_time", SortEnum.ASC.getSort());
                    packageParam.setSortParamMap(sortParamMap);
                    packageParam.setWarehouseCode(a);
                    dateSummaryBiz.initStatusCount(packageParam);
                }
            }
        });
        return Result.success("");
    }

    /**
     * 功能描述: 初始化出库单发货明细记录
     *
     * <AUTHOR>
     */
    @PostMapping("/initShipmentStatistics")
    public Result<Boolean> initShipmentStatistics(@RequestBody Map<String, String> map) {
        defaultWarehouseCodeConfig.getWarehouseCodeList().parallelStream().forEach(a -> {
            if (map.isEmpty()) {
                PackageParam packageParam = new PackageParam();
                if (map.get("startOutStockDate") != null) {
                    packageParam.setStartOutStockDate(Long.parseLong(map.get("startOutStockDate")));
                }
                if (map.get("endOutStockDate") != null) {
                    packageParam.setEndExpOutStockDate(Long.parseLong(map.get("endOutStockDate")));
                }
                Map<String, String> sortParamMap = new HashMap<>();
                sortParamMap.put("created_time", SortEnum.ASC.getSort());
                packageParam.setSortParamMap(sortParamMap);
                packageParam.setWarehouseCode(a);
                packageParam.setStatusList(Arrays.asList(PackEnum.STATUS.HAVE_COLLECT_STATUS.getCode(),
                        PackEnum.STATUS.PICK_BEGIN_STATUS.getCode(), PackEnum.STATUS.CHECK_BEGIN_STATUS.getCode(),
                        PackEnum.STATUS.UNENOUGH_STOCK_STATUS.getCode(), PackEnum.STATUS.PREPARE_HANDLER_STATUS.getCode(),
                        PackEnum.STATUS.PICK_COMPELETE_STATUS.getCode(), PackEnum.STATUS.CHECK_COMPELETE_STATUS.getCode()));
                shipmentStatisticsBiz.initShipmentStatistics(packageParam);
            } else {
                if (a.equals(map.get("warehouseCode"))) {
                    PackageParam packageParam = new PackageParam();
                    if (map.get("startOutStockDate") != null) {
                        packageParam.setStartOutStockDate(Long.parseLong(map.get("startOutStockDate")));
                    }
                    if (map.get("endOutStockDate") != null) {
                        packageParam.setEndExpOutStockDate(Long.parseLong(map.get("endOutStockDate")));
                    }
                    Map<String, String> sortParamMap = new HashMap<>();
                    sortParamMap.put("created_time", SortEnum.ASC.getSort());
                    packageParam.setSortParamMap(sortParamMap);
                    packageParam.setWarehouseCode(a);
                    packageParam.setStatusList(Arrays.asList(PackEnum.STATUS.HAVE_COLLECT_STATUS.getCode(),
                            PackEnum.STATUS.PICK_BEGIN_STATUS.getCode(), PackEnum.STATUS.CHECK_BEGIN_STATUS.getCode(),
                            PackEnum.STATUS.UNENOUGH_STOCK_STATUS.getCode(),
                            PackEnum.STATUS.PREPARE_HANDLER_STATUS.getCode(),
                            PackEnum.STATUS.PICK_COMPELETE_STATUS.getCode(),
                            PackEnum.STATUS.CHECK_COMPELETE_STATUS.getCode()));
                    shipmentStatisticsBiz.initShipmentStatistics(packageParam);
                }
            }
        });
        return Result.success();
    }

    @Resource
    private IRemoteShipmentOrderStatisticsClient remoteShipmentOrderStatisticsClient;

    @PostMapping("/remove")
    public Boolean remove(@RequestBody ShipmentOrderStatisticsParam param) {
        RpcContextUtil.setWarehouseCode("DT_HNWMS");
        remoteShipmentOrderStatisticsClient.remove(param);
        return true;
    }

}
