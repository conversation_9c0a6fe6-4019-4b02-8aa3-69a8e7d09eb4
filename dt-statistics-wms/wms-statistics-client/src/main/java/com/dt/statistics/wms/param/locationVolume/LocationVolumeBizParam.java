package com.dt.statistics.wms.param.locationVolume;

import java.math.BigDecimal;
import java.util.List;

import com.dt.component.common.param.BaseSearchParam;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class LocationVolumeBizParam extends BaseSearchParam  implements java.io.Serializable  {
    private static final long serialVersionUID = 1L;

    /**
     * 仓库编码
     */
    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    /**
     * 计算时间
     */
    @ApiModelProperty(value = "计算时间")
    private Long inDate;
    private Long inDateStart;
    private Long inDateEnd;

    /**
     * 库区编码
     */
    @ApiModelProperty(value = "库区编码")
    private String zoneCode;
    private List<String> zoneCodeList;
    /**
     * 库区类型
     */
    @ApiModelProperty(value = "库区类型")
    private String zoneType;
    private List<String> zoneTypeList;
    /**
     * 库位编码
     */
    @ApiModelProperty(value = "库位编码")
    private String locationCode;
    private List<String> locationCodeList;
    /**
     * 巷道编码
     */
    @ApiModelProperty(value = "巷道编码")
    private String tunnelCode;

    /**
     * 商品总体积
     */
    @ApiModelProperty(value = "商品总体积")
    private BigDecimal totalVolume;

    /**
     * 商品总品种数
     */
    @ApiModelProperty(value = "商品总品种数")
    private Integer totalSpecies;

    /**
     * 商品总件数
     */
    @ApiModelProperty(value = "商品总件数")
    private Integer totalQuantity;

    /**
     * 库位存储率
     */
    @ApiModelProperty(value = "库位存储率")
    private BigDecimal storageRate;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "修改人")
    private String updatedBy;

    @ApiModelProperty(value = "快照时间")
    private Long snapshotDate;
}