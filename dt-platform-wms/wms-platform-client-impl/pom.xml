<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.dt</groupId>
        <artifactId>dt-platform-wms</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <groupId>com.dt</groupId>
    <artifactId>wms-platform-client-impl</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <name>wms-platform-client-impl</name>
    <description>暴露接口具体实现</description>

    <dependencies>
        <!--业务处理-->

        <dependency>
            <groupId>com.dt</groupId>
            <artifactId>wms-platform-client</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.dt</groupId>
            <artifactId>wms-platform-rpc-client</artifactId>
            <version>3.2.7-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.dt</groupId>
            <artifactId>wms-oms-rpc-client</artifactId>
            <version>3.2.6-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.dt</groupId>
            <artifactId>wms-platform-integration</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>

        <!--    biz    -->
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>ares-order-rpc-client</artifactId>
            <version>${ares.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>dubbo</artifactId>
                    <groupId>org.apache.dubbo</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.danding.wms</groupId>
            <artifactId>wcs-client</artifactId>
            <version>1.0.4-RELEASE</version>
        </dependency>
        <!--服务配置-->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dt</groupId>
            <artifactId>wms-statistics-integration</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.dt</groupId>
            <artifactId>dt-component-utils</artifactId>
        </dependency>

        <dependency>
            <groupId>com.dt</groupId>
            <artifactId>dt-component-uid</artifactId>
        </dependency>

        <dependency>
            <groupId>com.dt</groupId>
            <artifactId>dt-component-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>2.2.7</version>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>park-client</artifactId>
            <version>1.0.4-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>park-client-autoconfigure</artifactId>
            <version>1.0.4-SNAPSHOT</version>
        </dependency>
        <!--test-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <!--测试数据库-->
        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.kafka</groupId>
            <artifactId>spring-kafka</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dt</groupId>
            <artifactId>dt-component-canal</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <!--    biz    -->
        <!--    transaction    -->
        <!--第三方集成-->
        <dependency>
            <groupId>io.seata</groupId>
            <artifactId>seata-all</artifactId>
        </dependency>

        <dependency>
            <groupId>io.seata</groupId>
            <artifactId>seata-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.danding.component</groupId>
            <artifactId>apolloclient-component</artifactId>
        </dependency>

        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson</artifactId>
        </dependency>
        <!--    transaction    -->
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
