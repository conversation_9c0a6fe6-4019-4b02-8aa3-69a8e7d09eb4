package com.dt.platform.wms.client;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.dto.WeChatMessageDTO;
import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.enums.SeqEnum;
import com.dt.component.common.enums.SystemEventEnum;
import com.dt.component.common.enums.base.CarrierStatusEnum;
import com.dt.component.common.enums.base.ContainerStatusEnum;
import com.dt.component.common.enums.base.WorkBenchStatusEnum;
import com.dt.component.common.enums.base.WorkBenchTypeEnum;
import com.dt.component.common.enums.bill.*;
import com.dt.component.common.enums.cargo.CargoConfigParamEnum;
import com.dt.component.common.enums.cargo.CargoConfigStatusEnum;
import com.dt.component.common.enums.pick.PickBatchCheckEnum;
import com.dt.component.common.enums.pick.PickEnum;
import com.dt.component.common.enums.pkg.PackEnum;
import com.dt.component.common.enums.pkg.PackInspectionEnum;
import com.dt.component.common.enums.pkg.PackIsPreEnum;
import com.dt.component.common.enums.pkg.PackReturnEnum;
import com.dt.component.common.enums.stock.OperationTypeEnum;
import com.dt.component.common.enums.wms.WmsHandoverBizEnum;
import com.dt.component.common.exceptions.BaseException;
import com.dt.component.common.holder.CurrentRouteHolder;
import com.dt.component.common.holder.CurrentUserHolder;
import com.dt.component.common.msg.StockOperationMessage;
import com.dt.component.common.result.Result;
import com.dt.domain.base.dto.*;
import com.dt.domain.base.dto.carrier.ExitConfigDTO;
import com.dt.domain.base.dto.contLog.ContainerLogDTO;
import com.dt.domain.base.param.PackageMaterialParam;
import com.dt.domain.base.param.SkuParam;
import com.dt.domain.base.param.carrier.ExitConfigParam;
import com.dt.domain.bill.bo.BatchOutBoundBillBO;
import com.dt.domain.bill.bo.WeighCommitBillBO;
import com.dt.domain.bill.dto.*;
import com.dt.domain.bill.dto.material.MaterialUseRecordDTO;
import com.dt.domain.bill.dto.message.MessageMqDTO;
import com.dt.domain.bill.dto.performance.SystemEventDTO;
import com.dt.domain.bill.dto.sourceCode.OutSourceCodeDTO;
import com.dt.domain.bill.param.*;
import com.dt.domain.bill.param.material.MaterialUseRecordParam;
import com.dt.domain.bill.param.sourceCode.OutSourceCodeParam;
import com.dt.platform.utils.CommonConstantUtil;
import com.dt.platform.utils.ConverterUtil;
import com.dt.platform.utils.LambdaHelpUtils;
import com.dt.platform.utils.WechatUtil;
import com.dt.platform.wms.biz.ICheckPackWeightAndVolumeBiz;
import com.dt.platform.wms.biz.IOutStockBiz;
import com.dt.platform.wms.biz.InterceptionManagerBiz;
import com.dt.platform.wms.biz.config.UrlConfig;
import com.dt.platform.wms.biz.config.WmsOtherConfig;
import com.dt.platform.wms.dto.base.ContainerBizDTO;
import com.dt.platform.wms.dto.base.WorkBenchBizDTO;
import com.dt.platform.wms.dto.handover.*;
import com.dt.platform.wms.integration.*;
import com.dt.platform.wms.integration.carrier.IRemoteExitConfigClient;
import com.dt.platform.wms.integration.material.IRemoteMaterialUseRecordClient;
import com.dt.platform.wms.integration.performance.IRemoteSystemEventClient;
import com.dt.platform.wms.integration.sourceCode.IRemoteOutSourceCodeClient;
import com.dt.platform.wms.param.CodeListParam;
import com.dt.platform.wms.param.CodeParam;
import com.dt.platform.wms.param.handover.*;
import com.dt.platform.wms.param.handover.pda.CommitHandoverParam;
import com.dt.platform.wms.param.handover.pda.ScanBillNoOrContainerParam;
import com.dt.platform.wms.transaction.IHandoverGtsService;
import com.dt.platform.wms.transaction.bo.ContainerHandoverBO;
import com.dt.platform.wms.transaction.bo.HandoverContainerCommitBO;
import com.dt.platform.wms.transaction.impl.PretreatmentMessageHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.redisson.RedissonMultiLock;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2020/10/18 16:29
 */
@DubboService(version = "${dubbo.service.version}")
@Slf4j
public class HandoverBizClientImpl implements IHandoverBizClient {

    @Resource
    private IRemoteHandoverClient iremoteHandoverClient;
    @Resource
    private IRemoteDecimalPlaceClient decimalPlaceClient;
    @Resource
    private IRemoteAllocationOrderClient remoteAllocationOrderClient;
    @Resource
    private IRemoteShipmentOrderClient remoteShipmentOrderClient;
    @Resource
    private IRemotePackageClient remotePackageClient;
    @Resource
    private IRemoteSkuClient remoteSkuClient;
    @Resource
    private IRemotePackageDetailClient remotePackageDetailClient;
    @Resource
    private IRemoteWorkBenchClient remoteWorkBenchClient;
    @Resource
    private IRemoteWarehouseClient remoteWarehouseClient;
    @Resource
    private IRemoteHandoverClient remoteHandoverClient;
    @Resource
    private IRemoteContainerClient remoteContainerClient;
    @Resource
    private IHandoverGtsService handoverGtsService;
    @Resource
    private IRemoteZoneClient iRemoteZoneClient;

    @Resource
    private IRemoteTunnelClient iRemoteTunnelClient;

    @Resource
    private IRemoteLocationClient remoteLocationClient;

    @Resource
    private InterceptionManagerBiz interceptionManagerBiz;

    @Resource
    private IRemoteSeqRuleClient iRemoteSeqRuleClient;

    @Resource
    private IRemotePickClient remotePickClient;

    @Resource
    private IRemotePickDetailClient remotePickDetailClient;

    @Resource
    private IRemoteOutSourceCodeClient remoteOutSourceCodeClient;

    @Resource
    private IRemoteCargoConfigClient remoteCargoConfigClient;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private IOutStockBiz outStockBiz;

    @Resource
    private ICheckPackWeightAndVolumeBiz iCheckPackWeightAndVolumeBiz;

    @Resource
    private IRemotePackageInspectionClient remotePackageInspectionClient;

    @Resource
    IRemotePackageMaterialClient remotePackageMaterialClient;

    @Resource
    private IRemoteSystemEventClient remoteSystemEventClient;

    @Resource
    private IRemoteSkuLotClient remoteSkuLotClient;

    @Resource
    private PretreatmentMessageHelper pretreatmentMessageHelper;

    @Resource
    IRemoteMessageClient remoteMessageClient;

    @Resource
    IRemoteBillContextClient remoteBillContextClient;

    @Resource
    private IRemoteMaterialUseRecordClient remoteMaterialUseRecordClient;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private IRemoteHandoverDetailClient remoteHandoverDetailClient;

    @Resource
    private IRemoteExitConfigClient remoteExitConfigClient;

    @Resource
    private UrlConfig urlConfig;

    @Resource
    WmsOtherConfig wmsOtherConfig;

    @Override
    public Result<Page<HandoverBizDTO>> queryPage(HandoverBizParam handoverBizParam) {
        HandoverParam param = ConverterUtil.convert(handoverBizParam, HandoverParam.class);
        Page<HandoverDTO> page = iremoteHandoverClient.getPage(param);
        Page<HandoverBizDTO> result = ConverterUtil.convertPage(page, HandoverBizDTO.class);
        return Result.success(result);
    }

    @Override
    public Result<HandoverBizDTO> getDetail(HandoverBizParam handoverBizParam) {
        HandoverParam param = ConverterUtil.convert(handoverBizParam, HandoverParam.class);
        HandoverDTO handoverDTO = remoteHandoverClient.get(param);
        HandoverParam detailParam = new HandoverParam();
        detailParam.setHandoverCode(handoverDTO.getHandoverCode());
        List<HandoverDetailDTO> listDetail = remoteHandoverClient.getAppointMultipleParam(detailParam, LambdaHelpUtils.convertToFieldNameList(HandoverDetailDTO::getPackageCode,
                HandoverDetailDTO::getCreatedTime,
                HandoverDetailDTO::getUpdatedTime,
                HandoverDetailDTO::getPackageWeight,
                HandoverDetailDTO::getHandoverCode,
                HandoverDetailDTO::getPickCode,
                HandoverDetailDTO::getExpressNo,
                HandoverDetailDTO::getPoNo,
                HandoverDetailDTO::getSoNo,
                HandoverDetailDTO::getCreatedBy,
                HandoverDetailDTO::getShipmentOrderCode));


        HandoverBizDTO oretDTO = ConverterUtil.convert(handoverDTO, HandoverBizDTO.class);
        oretDTO.setNumberFormat(3);
        List<HandoverDetailBizDTO> toListDetail = ConverterUtil.convertList(listDetail, HandoverDetailBizDTO.class);
        oretDTO.setListDetail(toListDetail);
        if (!CollectionUtils.isEmpty(toListDetail)) {
            PickParam pickParam = new PickParam();
            pickParam.setPackageCodeList(toListDetail.stream().map(s -> s.getPackageCode()).distinct().collect(Collectors.toList()));
            pickParam.setShipmentOrderCodeList(toListDetail.stream().map(s -> s.getShipmentOrderCode()).distinct().collect(Collectors.toList()));
            List<PickDetailDTO> pickDetailDTOList = remotePickClient.getPickDetailList(pickParam);
            /**
             * 过滤掉无效的拣选单明细
             */
            if (!CollectionUtils.isEmpty(pickDetailDTOList)) {
                pickParam = new PickParam();
                pickParam.setStatusList(Arrays.stream(PickEnum.PickStatusEnum.values()).filter(s -> !s.equals(PickEnum.PickStatusEnum.CANCEL_STATUS)).map(s -> s.getCode()).collect(Collectors.toList()));
                pickParam.setPickCodeList(pickDetailDTOList.stream().map(s -> s.getPickCode()).distinct().collect(Collectors.toList()));
                List<PickDTO> _listPickDTO = remotePickClient.getList(pickParam);
                if (!CollectionUtils.isEmpty(_listPickDTO)) {
                    List<String> pickCodeLists = _listPickDTO.stream().map(s -> s.getPickCode()).distinct().collect(Collectors.toList());
                    pickDetailDTOList = pickDetailDTOList.stream().filter(s -> pickCodeLists.contains(s.getPickCode())).collect(Collectors.toList());
                }
            }
            final List<PickDetailDTO> finalPickDetailDTOList = new ArrayList<>(pickDetailDTOList);
            oretDTO.getListDetail().forEach(handoverDetailBizDTO -> {
                //TODO 包裹出库，证明拣选单号肯定是最大的
                finalPickDetailDTOList.stream().filter(pickDetailDTO -> pickDetailDTO.getPackageCode().equals(handoverDetailBizDTO.getPackageCode())).max(Comparator.comparing(PickDetailDTO::getPickCode)).ifPresent(a -> handoverDetailBizDTO.setPickCode(a.getPickCode()));
            });
        }
        return Result.success(oretDTO);
    }

    @Override
    public Result<List<HandoverDTO>> getExportList(HandoverParam param) {
        return Result.success(iremoteHandoverClient.getExportList(param));
    }

    @Override
    public Result<WorkBenchBizDTO> getWorkBench(CodeParam param) {
        WorkBenchDTO workBench = remoteWorkBenchClient.queryByCode(param.getCode());
        WorkBenchBizDTO dto = ConverterUtil.convert(workBench, WorkBenchBizDTO.class);
        if (ObjectUtils.isEmpty(dto)) {
            throw new BaseException(WmsHandoverBizEnum.HANDOVER_WORKBENCH_NOT_EXITS, param.getCode());
        }
        if (!Objects.equals(dto.getStatus(), WorkBenchStatusEnum.ENABLE.value())) {
            throw new BaseException(BaseBizEnum.TIP, "质检台被禁用");
        }
        if (!WorkBenchTypeEnum.HANDOVER.getType().equals(workBench.getType())) {
            throw new BaseException(WmsHandoverBizEnum.HANDOVER_WORKBENCH_TYPE_ERROR, param.getCode());
        }
        WarehouseDTO warehouseDTO = remoteWarehouseClient.queryByCode(dto.getWarehouseCode());
        dto.setWarehouseName(ObjectUtils.isEmpty(warehouseDTO) ? "" : warehouseDTO.getName());
        return Result.success(dto);
    }

    private List<ScanWaybillDTO> findScanWayBill(HandoverDTO handoverDTO) {
        List<ScanWaybillDTO> list = new ArrayList<ScanWaybillDTO>();
        HandoverParam param = new HandoverParam();
        param.setHandoverCode(handoverDTO.getHandoverCode());
        List<HandoverDetailDTO> handoverDetailDTOList = remoteHandoverClient.getDetailList(param);
        if (!CollectionUtils.isEmpty(handoverDetailDTOList)) {
            PackageParam packageParam = new PackageParam();
            packageParam.setPackageCodeList(handoverDetailDTOList.stream().map(HandoverDetailDTO::getPackageCode).collect(Collectors.toList()));
            List<PackageDTO> packageDTOList = remotePackageClient.getList(packageParam);

            for (HandoverDetailDTO detailDTO : handoverDetailDTOList) {
                ScanWaybillDTO scanWaybill = new ScanWaybillDTO();
                scanWaybill.setHandoverCode(handoverDTO.getHandoverCode());
                scanWaybill.setPackageCode(detailDTO.getPackageCode());
                PackageDTO pack = packageDTOList.stream().filter(a -> a.getPackageCode().equalsIgnoreCase(detailDTO.getPackageCode())).findFirst().orElse(null);
                if (pack != null) {
                    scanWaybill.setBusinessType(pack.getBusinessType());
                    scanWaybill.setPackageStatus(pack.getStatus());
                    scanWaybill.setPackageStatusName(PackEnum.STATUS.findEnumDesc(pack.getStatus()).getDesc());
                    scanWaybill.setWaybillNo(pack.getExpressNo());
                    scanWaybill.setWeight(pack.getWeight().add(pack.getActualPackWeight()));
                    scanWaybill.setRealWeight(pack.getRealWeight());
                    scanWaybill.setRecPackUpc(pack.getRecPackUpc());
                    scanWaybill.setPackageQty(handoverDTO.getPackageQty());
                    list.add(scanWaybill);
                }
            }
        }
        return list;
    }

    @Override
    public Result<ScanContainerOrHandoverDTO> scanContainerOrHandover(ScanContainerOrHandoverBizParam param) {
        ContainerDTO container = remoteContainerClient.queryByCode(param.getCode());
        HandoverParam handoverParam = new HandoverParam();
        handoverParam.setHandoverCode(param.getCode());
        HandoverDTO handover = remoteHandoverClient.get(handoverParam);
        if (ObjectUtils.isEmpty(container) && ObjectUtils.isEmpty(handover)) {
            throw new BaseException(WmsHandoverBizEnum.HANDOVER_CONTAINER_OR_HANDOVER_ERROR, param.getCode());
        }
        if (handover != null && Objects.equals(handover.getAutomationType(), HandoverEnum.AutomationTypeEnum.AUTOMATION.getCode())) {
            throw new BaseException(BaseBizEnum.TIP, "自动化交接单不允许在web操作");
        }
        ScanContainerOrHandoverDTO scanContainerOrHandover = new ScanContainerOrHandoverDTO();
        //容器存在
        if (!ObjectUtils.isEmpty(container)) {
            ContainerStatusEnum containerStatus = ContainerStatusEnum.getEnum(container.getStatus());
            switch (containerStatus) {
                case ENABLE:
                    //交接单
                    HandoverDTO createHandover = new HandoverDTO();
                    createHandover.setWarehouseCode(container.getWarehouseCode());
                    createHandover.setContCode(container.getCode());
                    createHandover.setCarrierCode(container.getCode());
                    createHandover.setHandoverCode(iRemoteSeqRuleClient.findSequence(SeqEnum.HANDOVER_CODE_000001));
//                    createHandover.setHandoverCode(UidUtils.getCacheSerialNo("HD-"));
                    createHandover.setContCode(container.getCode());
                    createHandover.setCarrierCode(param.getCarrierCode());
                    createHandover.setPackageQty(0);
                    createHandover.setStatus(HandoverEnum.statusEnum.CREATE_STATUS.getCode());
                    createHandover.setPrintStatus(HandoverEnum.PrintStatusEnum.CREATE_STATUS.getCode());
                    //容器
                    container.setStatus(ContainerStatusEnum.OCCUPY.getValue());
                    container.setOccupyNo(createHandover.getHandoverCode());
                    container.setOccupyType(WorkBenchTypeEnum.HANDOVER.getType());
                    container.setRemark(String.format("%s:%s", WorkBenchTypeEnum.HANDOVER.getMessage(), container.getOccupyNo()));
                    //容器日志记录
                    ContainerLogDTO containerLogDTO = new ContainerLogDTO();
                    containerLogDTO.setWarehouseCode(container.getWarehouseCode());
                    containerLogDTO.setContCode(container.getCode());
                    containerLogDTO.setCreatedBy(CurrentUserHolder.getUserName());
                    containerLogDTO.setCreatedTime(System.currentTimeMillis());
                    containerLogDTO.setOpBy(CurrentUserHolder.getUserName());
                    containerLogDTO.setOpContent(String.format("交接单%s绑定容器:%s", createHandover.getHandoverCode(), container.getCode()));
                    containerLogDTO.setOpDate(System.currentTimeMillis());
                    containerLogDTO.setOccupyNo(container.getOccupyNo());
                    containerLogDTO.setOccupyType(container.getOccupyType());
                    //事务提交
                    ContainerHandoverBO containerHandover = new ContainerHandoverBO();
                    containerHandover.setContainer(container);
                    containerHandover.setContainerLogDTO(containerLogDTO);
                    containerHandover.setHandover(createHandover);
                    Boolean result = handoverGtsService.occupyContainerAndSaveHandover(containerHandover);
                    if (!result) {
                        throw new BaseException(WmsHandoverBizEnum.HANDOVER_HANDOVER_CREAT_ERROR, param.getCarrierCode());
                    }
                    scanContainerOrHandover.setPackageQty(0);
                    scanContainerOrHandover.setContainer(ConverterUtil.convert(container, ContainerBizDTO.class));
                    scanContainerOrHandover.setHandover(ConverterUtil.convert(createHandover, HandoverBizDTO.class));
                    scanContainerOrHandover.setHandoverDetailList(new ArrayList<>());
                    break;
                case OCCUPY:
                    if (!WorkBenchTypeEnum.HANDOVER.getType().equals(container.getOccupyType())) {
                        throw new BaseException(WmsHandoverBizEnum.HANDOVER_CONTAINER_OCCUPY_TYPE_ERROR, param.getCode(), WorkBenchTypeEnum.fromCode(container.getOccupyType()).getMessage());
                    }
                    HandoverParam occupyHandoverParam = new HandoverParam();
                    occupyHandoverParam.setHandoverCode(container.getOccupyNo());
                    HandoverDTO occupyHandover = remoteHandoverClient.get(occupyHandoverParam);
                    if (ObjectUtils.isEmpty(occupyHandover)) {
                        throw new BaseException(WmsHandoverBizEnum.HANDOVER_CONTAINER_OCCUPY_INFO_ERROR, container.getCode(), container.getOccupyNo());
                    }
                    if (!occupyHandover.getCarrierCode().equals(param.getCarrierCode())) {
                        throw new BaseException(BaseBizEnum.TIP, "交接单已存在、运输商不匹配！");
                    }
                    HandoverParam occupyHandoverDetailParam = new HandoverParam();
                    occupyHandoverDetailParam.setHandoverCode(container.getOccupyNo());
                    List<HandoverDetailDTO> handoverDetailList = remoteHandoverClient.getDetailList(occupyHandoverDetailParam);

                    scanContainerOrHandover.setPackageQty(occupyHandover.getPackageQty());
                    scanContainerOrHandover.setContainer(ConverterUtil.convert(container, ContainerBizDTO.class));
                    scanContainerOrHandover.setHandover(ConverterUtil.convert(occupyHandover, HandoverBizDTO.class));
                    scanContainerOrHandover.setHandoverDetailList(ConverterUtil.convertList(handoverDetailList, HandoverDetailBizDTO.class));
                    scanContainerOrHandover.setScanWaybillDTOList(findScanWayBill(occupyHandover));
                    break;
                default:
                    throw new BaseException(WmsHandoverBizEnum.HANDOVER_CONTAINER_DISABLE_ERROR, param.getCode());
            }
        }

        //批次存在
        if (!ObjectUtils.isEmpty(handover)) {
            //校验拣选单
            checkHandoverStatus(handover);
            //查询占用容器
            ContainerDTO occupyContainer = remoteContainerClient.queryByCode(handover.getContCode());
            if (ObjectUtils.isEmpty(occupyContainer) || !WorkBenchTypeEnum.HANDOVER.getType().equals(occupyContainer.getOccupyType())) {
                throw new BaseException(WmsHandoverBizEnum.HANDOVER_CONTAINER_OCCUPY_INFO_ERROR, handover.getHandoverCode(), handover.getContCode());
            }
            if (!handover.getCarrierCode().equals(param.getCarrierCode())) {
                throw new BaseException(BaseBizEnum.TIP, "交接单已存在、运输商不匹配！");
            }

            HandoverParam occupyHandoverDetailParam = new HandoverParam();
            occupyHandoverDetailParam.setHandoverCode(handover.getHandoverCode());
            List<HandoverDetailDTO> handoverDetailList = remoteHandoverClient.getDetailList(occupyHandoverDetailParam);

            scanContainerOrHandover.setPackageQty(handover.getPackageQty());
            scanContainerOrHandover.setContainer(ConverterUtil.convert(occupyContainer, ContainerBizDTO.class));
            scanContainerOrHandover.setHandover(ConverterUtil.convert(handover, HandoverBizDTO.class));
            scanContainerOrHandover.setHandoverDetailList(ConverterUtil.convertList(handoverDetailList, HandoverDetailBizDTO.class));
            scanContainerOrHandover.setScanWaybillDTOList(findScanWayBill(handover));
        }

        return Result.success(scanContainerOrHandover);
    }

    @Override
    public Result<ScanWaybillDTO> scanWaybill(ScanWaybillBizParam param) {
        HandoverParam handoverParam = new HandoverParam();
        handoverParam.setHandoverCode(param.getHandoverCode());
        HandoverDTO handover = remoteHandoverClient.get(handoverParam);
        if (ObjectUtils.isEmpty(handover)) {
            throw new BaseException(WmsHandoverBizEnum.HANDOVER_HANDOVER_NOT_EXITS, param.getHandoverCode());
        }
        //校验容器
        checkContOccupy(handover.getHandoverCode(), handover.getContCode());
        //获取快递公司
        String carrierCode = handover.getCarrierCode();
        //校验有效包裹
        PackageDTO pack = checkPackageEffective(carrierCode, param.getWaybillNo());

        List<String> interceptPackCodeList = interceptionManagerBiz.queryInterceptionPackTipOperation(Arrays.asList(pack.getPackageCode()));
        if (!CollectionUtils.isEmpty(interceptPackCodeList)) {
            Map<String, Object> map = new HashMap<>();
            map.put("num", interceptPackCodeList.size());
            map.put("interceptionExpressNoList", Arrays.asList(pack.getExpressNo()));
            map.put("packageCode", pack.getPackageCode());
            map.put("pickCode", param.getPickCode());
            throw new BaseException(JSONUtil.toJsonStr(map), BaseBizEnum.TIP_HANDLE, "包裹拦截明细");
        }
        ScanWaybillDTO scanWaybill = new ScanWaybillDTO();
        if (!PackEnum.STATUS.CHECK_COMPELETE_STATUS.getCode().equals(pack.getStatus())) {
            throw new BaseException(WmsHandoverBizEnum.HANDOVER_PACKAGE_STATUS_PROTECT, pack.getExpressNo(), pack.getPackageCode(), PackEnum.STATUS.findEnumDesc(pack.getStatus()).getDesc(), PackEnum.STATUS.CHECK_COMPELETE_STATUS.getCode());
        }
        if (!handover.getCarrierCode().equals(pack.getCarrierCode())) {
            throw new BaseException(WmsHandoverBizEnum.HANDOVER_PACKAGE_WAYBILL_NOT_MATCH, param.getWaybillNo());
        }
        //check交接单网点是否一致
        if (handover.getStatus().equalsIgnoreCase(HandoverEnum.statusEnum.DOING_STATUS.getCode())) {
            checkHandoverExpressBranch(handover, pack);
        }
        //校验拣选单号 --批量复核
        if (!StringUtils.isEmpty(param.getPickCode())) {
            PickParam pickParam = new PickParam();
            pickParam.setPickCode(param.getPickCode());
            List<PickDetailDTO> pickDetailList = remotePickClient.getPickDetailList(pickParam);
            if (CollectionUtils.isEmpty(pickDetailList)) {
                throw new BaseException(BaseBizEnum.TIP, String.format("拣选单号%s未找到包裹", param.getPickCode()));
            }
            PackageDTO finalPack = pack;
            if (pickDetailList.stream().noneMatch(a -> Objects.equals(a.getPackageCode(), finalPack.getPackageCode()))) {
                throw new BaseException(BaseBizEnum.TIP, String.format("拣选单号:%s未找到单号:%s", param.getPickCode(), param.getWaybillNo()));
            }
        }
        //批量复核秒杀单--抽检校验
        //TODO 增加散单抽检
        checkPackageInspection(pack, CommonConstantUtil.WMS_WEB_WEIGHT);

        //合流单校验必须所有包裹在同一个拣选单里面
        if (remoteWarehouseClient.getTaoTianWarehouse(CurrentRouteHolder.getWarehouseCode())
                && Objects.equals(pack.getBusinessType(), ShipmentOrderEnum.BUSSINESS_TYPE.B2C.toString())
                && isTaoTianMerge(pack)) {
            checkTaoTianMergeScanWaybill(pack, handover.getHandoverCode(), false);
        }
        scanWaybill.setHandoverCode(handover.getHandoverCode());
        scanWaybill.setPackageCode(pack.getPackageCode());
        scanWaybill.setBusinessType(pack.getBusinessType());
        scanWaybill.setPackageStatus(pack.getStatus());
        scanWaybill.setPackageStatusName(PackEnum.STATUS.findEnumDesc(pack.getStatus()).getDesc());
        scanWaybill.setWaybillNo(pack.getExpressNo());
        //重新计算理论重量
        List<PackageDetailDTO> packageDetailList = remotePackageClient.getPackageDetailListByCode(pack.getPackageCode());
        if (CollectionUtils.isEmpty(packageDetailList)) {
            throw new BaseException(BaseBizEnum.TIP, "包裹明细查询异常");
        }
        if (pack.getBusinessType().equals(ShipmentOrderEnum.BUSSINESS_TYPE.B2C.toString())) {
            BigDecimal weight = getWeight(pack, packageDetailList);
            scanWaybill.setWeight(weight);
        } else {
            scanWaybill.setWeight(pack.getWeight().add(pack.getActualPackWeight().multiply(new BigDecimal(pack.getActualPackNum() + ""))));
        }
        scanWaybill.setRealWeight(pack.getRealWeight());
        scanWaybill.setRecPackUpc(pack.getRecPackUpc());
        scanWaybill.setPackageQty(handover.getPackageQty());

        return Result.success(scanWaybill);
    }

    private boolean isTaoTianMerge(PackageDTO pack) {
        //合流单
        if (pack.getOrderTag() != null
                && pack.getOrderTag() > 0 && OrderTagEnum.NumToEnum(pack.getOrderTag()).contains(OrderTagEnum.ORDER_MERGE)) {
            return true;
        }
        return false;
    }

    private void checkTaoTianMergeScanWaybill(PackageDTO pack, String handoverCode, Boolean isWeight) {
        RLock lock = redissonClient.getLock("dt_handover_scanWaybill:" + pack.getWarehouseCode() + pack.getCargoCode() + pack.getCarrierCode() + pack.getTradeNo());
        Boolean tryLock = false;
        try {
            tryLock = lock.tryLock(0, 4, TimeUnit.SECONDS);
            if (!tryLock) {
                throw new BaseException(BaseBizEnum.TIP, "正在出库,请等待确认");
            }
            //非合流单
            if (pack.getOrderTag() == null
                    || pack.getOrderTag() == 0 || !OrderTagEnum.NumToEnum(pack.getOrderTag()).contains(OrderTagEnum.ORDER_MERGE)) {
                return;
            }
            PackageParam packageParam = new PackageParam();
            packageParam.setTradeNo(pack.getTradeNo());
            packageParam.setCargoCode(pack.getCargoCode());
            packageParam.setCarrierCode(pack.getCarrierCode());
            List<PackageDTO> packageDTOList = remotePackageClient.getAppointMultipleParam(packageParam,
                    LambdaHelpUtils.convertToFieldNameList(PackageDTO::getPackageCode, PackageDTO::getStatus, PackageDTO::getTradeNo
                            , PackageDTO::getOrderTag, PackageDTO::getExtraJson, PackageDTO::getCargoCode, PackageDTO::getCarrierCode));
            if (CollectionUtils.isEmpty(packageDTOList)) {
                return;
            }
            //获取合流单的单数
            Integer mergeNum = pack.getMergeNum();

            //过滤取消重推的单子
            List<String> cancelStatsList = Arrays.asList(PackEnum.STATUS.PART_ASSIGN_STATUS.getCode(), PackEnum.STATUS.PART_ASSIGN_CANCEL_STATUS.getCode(), PackEnum.STATUS.PART_ASSIGN_COMPLETE_STATUS.getCode());
            packageDTOList = packageDTOList.stream().filter(a -> !cancelStatsList.contains(a.getStatus())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(packageDTOList)) {
                return;
            }
            List<String> packStatusList = Arrays.asList(PackEnum.STATUS.CHECK_COMPELETE_STATUS.getCode(), PackEnum.STATUS.PREPARE_HANDLER_STATUS.getCode());
            if (packageDTOList.stream().anyMatch(a -> !packStatusList.contains(a.getStatus()))) {
                throw new BaseException(BaseBizEnum.TIP, String.format("淘天合流单,交易号:%s所有包裹必须全部复核完成以后,才允许称重出库", pack.getTradeNo()));
            }
            Long count = packageDTOList.stream().filter(a -> packStatusList.contains(a.getStatus())).count();
            if (count.intValue() != mergeNum) {
                throw new BaseException(BaseBizEnum.TIP, String.format("淘天合流单,交易号:%s所有包裹必须全部复核完成以后,才允许称重出库", pack.getTradeNo()));
            }
            HandoverDetailParam handoverDetailParam = new HandoverDetailParam();
            handoverDetailParam.setPackageCodeList(packageDTOList.stream().map(PackageDTO::getPackageCode).distinct().collect(Collectors.toList()));
            List<HandoverDetailDTO> handoverDetailDTOList = remoteHandoverDetailClient.getList(handoverDetailParam);
            if (CollectionUtils.isEmpty(handoverDetailDTOList)) {
                return;
            }
            List<String> handoverCodeList = handoverDetailDTOList.stream().map(HandoverDetailDTO::getHandoverCode).distinct().collect(Collectors.toList());
            if (!handoverCodeList.contains(handoverCode)) {
                throw new BaseException(BaseBizEnum.TIP, String.format("淘天合流单,交易号:%s有包裹存在其他交接单:%s,合流单需要同交接单出库", pack.getTradeNo(), handoverCodeList.get(0)));
            }
        } catch (Exception e) {
            log.error("{}", e);
            e.printStackTrace();
            throw new BaseException(BaseBizEnum.TIP, StringUtils.isEmpty(e.getMessage()) ? "系统异常" : e.getMessage());
        } finally {
            if (!isWeight && tryLock) {
                lock.unlock();
            }
        }
    }

    /**
     * @param carrierCode
     * @param waybillNo
     * @return com.dt.domain.bill.dto.PackageDTO
     * @author: WuXian
     * description:  出库校验单据有效性
     * create time: 2021/11/1 14:35
     */
    private PackageDTO checkPackageEffective(String carrierCode, String waybillNo) {
        PackageDTO pack;
        //包裹出库
        if (waybillNo.toUpperCase().startsWith(SeqEnum.PACK_CODE_000001.getPrefix())) {
            PackageParam packageCodeParam = new PackageParam();
            packageCodeParam.setPackageCode(waybillNo);
            packageCodeParam.setCarrierCode(carrierCode);
            pack = remotePackageClient.get(packageCodeParam);
            if (ObjectUtils.isEmpty(pack)) {
                throw new BaseException(BaseBizEnum.TIP, String.format("此单号%s未找到包裹，请确认后继续操作", waybillNo));
            }
        } else {
            //运单号
            PackageParam packageParam = new PackageParam();
            packageParam.setCarrierCode(carrierCode);
            packageParam.setExpressNo(waybillNo);
            List<PackageDTO> packageDTOList = remotePackageClient.getList(packageParam);
            if (CollectionUtils.isEmpty(packageDTOList)) {
                throw new BaseException(BaseBizEnum.TIP, String.format("单号:%s未找到需要称重的包裹", waybillNo));
            }
            //无效单据号状态码
            List<String> invalidStatusList = new ArrayList<>();
            invalidStatusList.add(PackEnum.STATUS.PART_ASSIGN_COMPLETE_STATUS.getCode());
            invalidStatusList.add(PackEnum.STATUS.PART_ASSIGN_CANCEL_STATUS.getCode());
            invalidStatusList.add(PackEnum.STATUS.PART_ASSIGN_STATUS.getCode());
            if (packageDTOList.stream().allMatch(a -> invalidStatusList.contains(a.getStatus()))) {
                throw new BaseException(BaseBizEnum.TIP, String.format("单号:%s已拦截或取消", waybillNo));
            }
            //移除拦截和取消的包裹
            packageDTOList = packageDTOList.stream().filter(a -> !invalidStatusList.contains(a.getStatus())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(packageDTOList)) {
                throw new BaseException(BaseBizEnum.TIP, String.format("单号:%s未找到需要称重的包裹", waybillNo));
            }
            if (packageDTOList.size() > 1) {
                throw new BaseException(BaseBizEnum.TIP, String.format("单号%s对应多个有效发货状态包裹，请确认后继续操作", waybillNo));
            }
            pack = packageDTOList.get(0);
        }
        if (pack == null) {
            throw new BaseException(BaseBizEnum.TIP, String.format("包裹%s不可操作出库，请检查确认包裹状态后继续操作", waybillNo));
        } else {
            if (pack.getStatus().equals(PackEnum.STATUS.PREPARE_HANDLER_STATUS.getCode())) {
                throw new BaseException(BaseBizEnum.TIP, String.format("单号:%s已出库", waybillNo));
            }
            if (pack.getStatus().equals(PackEnum.STATUS.PART_ASSIGN_STATUS.getCode()) || pack.getStatus().equals(PackEnum.STATUS.PART_ASSIGN_CANCEL_STATUS.getCode())) {
                throw new BaseException(BaseBizEnum.TIP, String.format("单号:%s已拦截", waybillNo));
            }
            if (pack.getStatus().equals(PackEnum.STATUS.PART_ASSIGN_COMPLETE_STATUS.getCode())) {
                throw new BaseException(BaseBizEnum.TIP, String.format("单号:%s已取消", waybillNo));
            }
            if (!pack.getStatus().equals(PackEnum.STATUS.CHECK_COMPELETE_STATUS.getCode())) {
                throw new BaseException(BaseBizEnum.TIP, String.format("包裹%s不可操作出库，请检查确认包裹状态后继续操作", waybillNo));
            }
        }
        return pack;
    }

    /**
     * @param handover
     * @param pack
     * @return void
     * @author: WuXian
     * description:
     * create time: 2022/2/16 13:28
     */
    private void checkHandoverExpressBranch(HandoverDTO handover, PackageDTO pack) {
        //有交接网点
        if (!StringUtils.isEmpty(handover.getExpressBranch())) {
            if (!handover.getExpressBranch().equalsIgnoreCase(pack.getExpressBranch())) {
                throw new BaseException(BaseBizEnum.TIP, String.format("运单号%s快递网点与交接单不一致", pack.getExpressNo()));
            }
        } else {
            //无交接网点
            HandoverParam handoverParam = new HandoverParam();
            handoverParam.setHandoverCode(handover.getHandoverCode());
            List<HandoverDetailDTO> handoverDetailDTOList = remoteHandoverClient.getDetailList(handoverParam);
            if (CollectionUtils.isEmpty(handoverDetailDTOList)) {
                return;
            }
            //找交接单明细包裹有无交接网点
            PackageParam packageParam = new PackageParam();
            packageParam.setPackageCode(handoverDetailDTOList.get(0).getPackageCode());
            PackageDTO packageDTO = remotePackageClient.get(packageParam);
            if (packageDTO == null) {
                throw new BaseException(BaseBizEnum.TIP, "交接单明细包裹异常");
            }
            if (!packageDTO.getExpressBranch().equalsIgnoreCase(pack.getExpressBranch())) {
                throw new BaseException(BaseBizEnum.TIP, String.format("运单号%s快递网点与交接单不一致", pack.getExpressNo()));
            }
        }
    }

    private BigDecimal getWeight(PackageDTO packageDTO, List<PackageDetailDTO> packageDetailList) {
        //包裹重量计算
        List<String> skuCodeList;
        if (Objects.equals(packageDTO.getIsPre(), PackEnum.TYPE.NORMAL.getCode())) {
            skuCodeList = packageDetailList.stream().filter(a -> a.getIsPre().equalsIgnoreCase(PackIsPreEnum.NORMAL.getCode())).flatMap(a -> Stream.of(a.getSkuCode())).distinct().collect(Collectors.toList());
        } else {
            skuCodeList = packageDetailList.stream().filter(a -> a.getIsPre().equalsIgnoreCase(PackIsPreEnum.PRE.getCode()) || a.getIsPre().equalsIgnoreCase(PackIsPreEnum.LAST.getCode())).flatMap(a -> Stream.of(a.getSkuCode())).distinct().collect(Collectors.toList());
        }
        SkuParam skuParam = new SkuParam();
        skuParam.setCargoCode(packageDTO.getCargoCode());
        skuParam.setCodeList(skuCodeList);
        List<SkuDTO> skuList = remoteSkuClient.getList(skuParam);
        //包裹理论重量信息
        BigDecimal weight;
        if (Objects.equals(packageDTO.getIsPre(), PackEnum.TYPE.NORMAL.getCode())) {
            weight = packageDetailList.stream().filter(a -> a.getIsPre().equalsIgnoreCase(PackIsPreEnum.NORMAL.getCode())).flatMap(a -> {
                SkuDTO sku = skuList.stream().filter(b -> b.getCode().equals(a.getSkuCode())).findAny().get();
                return Stream.of(a.getSkuQty().multiply(sku.getGrossWeight()));
            }).reduce(BigDecimal.ZERO, BigDecimal::add);
        } else {
            weight = packageDetailList.stream().filter(a -> a.getIsPre().equalsIgnoreCase(PackIsPreEnum.PRE.getCode()) || a.getIsPre().equalsIgnoreCase(PackIsPreEnum.LAST.getCode())).flatMap(a -> {
                SkuDTO sku = skuList.stream().filter(b -> b.getCode().equals(a.getSkuCode())).findAny().get();
                return Stream.of(a.getSkuQty().multiply(sku.getGrossWeight()));
            }).reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        PackageMaterialDTO packageMaterialDTO = remotePackageMaterialClient.queryByUpcCode(packageDTO.getCargoCode(), packageDTO.getActualPackUpc());
        if (packageMaterialDTO == null) {
            throw new BaseException(BaseBizEnum.TIP, "包裹未找到");
        }
        //TODO 2023-02-08 移除耗材计重
        //复核记录耗材重量计算
        MaterialUseRecordParam materialUseRecordParam = new MaterialUseRecordParam();
        materialUseRecordParam.setPackageCode(packageDTO.getPackageCode());
        List<MaterialUseRecordDTO> materialUseRecordDTOList = remoteMaterialUseRecordClient.getList(materialUseRecordParam);
        if (!CollectionUtils.isEmpty(materialUseRecordDTOList)) {
            //获取只计重的耗材
            PackageMaterialParam packageMaterialParam = new PackageMaterialParam();
            packageMaterialParam.setCargoCode(packageDTO.getCargoCode());
            packageMaterialParam.setBarCodeList(materialUseRecordDTOList.stream().map(MaterialUseRecordDTO::getBarCode).collect(Collectors.toList()));
            List<PackageMaterialDTO> packageMaterialDTOList = remotePackageMaterialClient.getList(packageMaterialParam);
            if (!CollectionUtils.isEmpty(packageMaterialDTOList)) {
                BigDecimal materialWeight = materialUseRecordDTOList.stream().flatMap(a -> {
                    PackageMaterialDTO materialDTO = packageMaterialDTOList.stream().filter(pc -> pc.getBarCode().equals(a.getBarCode())).findFirst().orElse(null);
                    BigDecimal grossWeight = materialDTO == null ? BigDecimal.ZERO : materialDTO.getGrossWeight();
                    return Stream.of(new BigDecimal(a.getMaterialNum() + "").multiply(grossWeight));
                }).reduce(BigDecimal.ZERO, BigDecimal::add);
                weight = weight.add(materialWeight);
            }
        }
        return weight.add(packageMaterialDTO.getGrossWeight().multiply(new BigDecimal(packageDTO.getActualPackNum() + "")));
    }


    private void checkPackageInspection(PackageDTO pack, String flag) {
        /**
         * @王响平
         * 如果是C单，判断拣选单是否是批量复核，如果是批量复核的C单包裹，判断包裹是否批量复核抽检
         * 如果抽检了，才允许出库
         */
        if (pack.getBusinessType().equals(ShipmentOrderEnum.BUSSINESS_TYPE.B2C.toString())) {
            List<PickDetailDTO> pickDetailDTOList = remotePickClient.getPickDetailList2(pack.getShipmentOrderCode(), pack.getPackageCode());
            pickDetailDTOList = pickDetailDTOList.stream().filter(s -> !s.getPackageStatus().equals(PackEnum.STATUS.PART_ASSIGN_COMPLETE_STATUS.getCode())).filter(s -> s.getFlag().equalsIgnoreCase(PickEnum.PickDetailFlagEnum.ORIGIN.getCode())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(pickDetailDTOList) || pickDetailDTOList.size() > 1) {
                throw new BaseException(BaseBizEnum.TIP, "当前包裹未找到拣选单,请核查");
            }
            PickDetailDTO pickDetailDTO = pickDetailDTOList.stream().findFirst().get();
            PickParam pickParam = new PickParam();
            pickParam.setPickCode(pickDetailDTO.getPickCode());
            PickDTO pickDTO = remotePickClient.get(pickParam);
            if (pickDTO == null) {
                throw new BaseException(BaseBizEnum.TIP, "拣选单不存在");
            }
            if (pickDTO.getType().equals(PickEnum.PickOrderTypeEnum.SPIKE.getCode()) && PickBatchCheckEnum.YES_BATCH.getValue().equals(pickDTO.getBatchCheckStatus())) {
                //获取抽检系数
                CargoConfigDTO cargoConfigDTO = remoteCargoConfigClient.queryByCargoCodeAndpropKey(pickDTO.getWarehouseCode(), pickDTO.getCargoCode(), CargoConfigParamEnum.SPOT_CHECK.getCode());
                if (cargoConfigDTO != null && cargoConfigDTO.getStatus().equals(CargoConfigStatusEnum.ENABLE.getValue()) && Integer.valueOf(cargoConfigDTO.getPropValue()) > 0) {
                } else {
                    throw new BaseException(BaseBizEnum.TIP, "当前货主抽检系数未配置");
                }
                //TODO 校验抽检系数是否通过  查抽检表，判定当前包裹是否抽检完成
                PackageInspectionParam packageInspectionParam = new PackageInspectionParam();
                packageInspectionParam.setPickCode(pickDTO.getPickCode());
                List<PackageInspectionDTO> inspectionClientList = remotePackageInspectionClient.getList(packageInspectionParam);
                String warmMsg = "";
                if (Objects.equals(flag, CommonConstantUtil.WMS_WCS_WEIGHT)) {
                    String executeDest = "";
                    try {
                        if (!StringUtils.isEmpty(pack.getExtraJson()) && JSONUtil.isJson(pack.getExtraJson()) && pack.getExtraJson().contains("executeDest")) {
                            JSONObject jsonObject = JSONUtil.parseObj(pack.getExtraJson());
                            if (jsonObject != null && jsonObject.containsKey("executeDest")) {
                                executeDest = jsonObject.getOrDefault("executeDest", "").toString();
                            }
                        }
                    } catch (Exception e) {

                    }
                    warmMsg = String.format("【仓库:%s,拣选单号:%s,运单号:%s,分拣口:%s】", pack.getWarehouseCode(), pickDTO.getPickCode(), pack.getExpressNo(), executeDest);
                }
                if (CollectionUtils.isEmpty(inspectionClientList)) {
                    if (Objects.equals(flag, CommonConstantUtil.WMS_WCS_WEIGHT)) {
                        WechatUtil.sendMessage("【批量】拣选单没有抽检,请核查！！！" + warmMsg, urlConfig.getWcsInterceptPackageOutUrls());
                    } else {
                        throw new BaseException(BaseBizEnum.TIP, "当前拣选单没有抽检,不允许出库!!");
                    }
                }
                //校验
                checkInspectionPass(cargoConfigDTO.getPropValue(), inspectionClientList, pickDTO, flag, warmMsg);
            } else {
                PackageInspectionParam packageInspectionParam = new PackageInspectionParam();
                packageInspectionParam.setPackageCode(pack.getPackageCode());
                List<PackageInspectionDTO> inspectionClientList = remotePackageInspectionClient.getList(packageInspectionParam);
                if (!CollectionUtils.isEmpty(inspectionClientList) && inspectionClientList.stream().allMatch(a -> a.getResult().equalsIgnoreCase(PackInspectionEnum.FAIL_RESULT.getValue()))) {
                    if (Objects.equals(flag, CommonConstantUtil.WMS_WCS_WEIGHT)) {
                        String executeDest = "";
                        try {
                            if (!StringUtils.isEmpty(pack.getExtraJson()) && JSONUtil.isJson(pack.getExtraJson()) && pack.getExtraJson().contains("executeDest")) {
                                JSONObject jsonObject = JSONUtil.parseObj(pack.getExtraJson());
                                if (jsonObject != null && jsonObject.containsKey("executeDest")) {
                                    executeDest = jsonObject.getOrDefault("executeDest", "").toString();
                                }
                            }
                        } catch (Exception e) {

                        }
                        String warmMsg = String.format("包裹抽检失败,请核查！！！【仓库:%s,拣选单号:%s,运单号:%s,分拣口:%s】", pack.getWarehouseCode(), pickDTO.getPickCode(), pack.getExpressNo(), executeDest);
                        WechatUtil.sendMessage(warmMsg, urlConfig.getWcsInterceptPackageOutUrls());
                    } else {
                        throw new BaseException(BaseBizEnum.TIP, "当前包裹抽检失败,不允许出库!!");
                    }
                }
            }
        }
    }

    @Override
    public Result<WeightResultDTO> weighCommit(WeighCommitBizParam param) throws Exception {
        log.info("weighCommit warehouseCode:{} param:{}", CurrentRouteHolder.getWarehouseCode(), JSONUtil.toJsonStr(param));
        HandoverParam handoverParam = new HandoverParam();
        handoverParam.setHandoverCode(param.getHandoverCode());
        HandoverDTO handover = remoteHandoverClient.get(handoverParam);
        if (ObjectUtils.isEmpty(handover)) {
            throw new BaseException(WmsHandoverBizEnum.HANDOVER_HANDOVER_NOT_EXITS, param.getHandoverCode());
        }
        //校验交接单状态
        checkHandoverStatus(handover);

        PackageParam packageParam = new PackageParam();
        packageParam.setPackageCode(param.getPackageCode());
        packageParam.setExpressNo(param.getWaybillNo());
        packageParam.setStatus(PackEnum.STATUS.CHECK_COMPELETE_STATUS.getCode());
        PackageDTO pack = remotePackageClient.get(packageParam);
        if (ObjectUtils.isEmpty(pack)) {
            throw new BaseException(WmsHandoverBizEnum.HANDOVER_WAYBILL_NOT_EXITS, param.getWaybillNo());
        }
        if (!PackEnum.STATUS.CHECK_COMPELETE_STATUS.getCode().equals(pack.getStatus())) {
            throw new BaseException(WmsHandoverBizEnum.HANDOVER_PACKAGE_STATUS_PROTECT, pack.getExpressNo(), pack.getPackageCode(), PackEnum.STATUS.findEnumDesc(pack.getStatus()).getDesc(), PackEnum.STATUS.CHECK_COMPELETE_STATUS.getCode());
        }
        //过滤拦截单
        RLock lock = redissonClient.getLock(CommonConstantUtil.INTERCEPT_PREFIX + pack.getWarehouseCode() + pack.getShipmentOrderCode());
        Boolean tryLock = false;
        WeightResultDTO weightResultDTO = new WeightResultDTO();
        try {
            tryLock = lock.tryLock(1, 15, TimeUnit.SECONDS);
            if (!tryLock) {
                throw new BaseException(BaseBizEnum.TIP, "正在出库,请等待确认");
            }
            //拦截出库单
            List<String> interceptPackCodeList = interceptionManagerBiz.queryInterceptionPackTipOperation(Arrays.asList(pack.getPackageCode()));
            if (!CollectionUtils.isEmpty(interceptPackCodeList)) {
                Map<String, Object> map = new HashMap<>();
                map.put("num", interceptPackCodeList.size());
                map.put("interceptionExpressNoList", Arrays.asList(pack.getExpressNo()));
                map.put("packageCode", pack.getPackageCode());
                map.put("pickCode", param.getPickCode());
                throw new BaseException(JSONUtil.toJsonStr(map), BaseBizEnum.TIP_HANDLE, "包裹拦截明细");
            }
            //校验包裹是否是批量复核
            checkPackageInspection(pack, CommonConstantUtil.WMS_WEB_WEIGHT);
            //查询包裹明细
            PackageDetailParam packageDetailParam = new PackageDetailParam();
            packageDetailParam.setPackageCode(pack.getPackageCode());
            List<PackageDetailDTO> packageDetailList = remotePackageDetailClient.getList(packageDetailParam);

            if (pack.getStatus().equalsIgnoreCase(PackEnum.STATUS.PREPARE_HANDLER_STATUS.getCode())) {
                throw new BaseException(BaseBizEnum.TIP, "包裹已出库");
            }
            //check交接单网点是否一致
            if (handover.getStatus().equalsIgnoreCase(HandoverEnum.statusEnum.DOING_STATUS.getCode())) {
                checkHandoverExpressBranch(handover, pack);
            }
            //包裹
            pack.setStatus(PackEnum.STATUS.PREPARE_HANDLER_STATUS.getCode());
            pack.setOutStockDate(System.currentTimeMillis());
            if (pack.getIsPre().equalsIgnoreCase(PackEnum.TYPE.NORMAL.getCode())) {
                pack.setOutSkuQty(packageDetailList.stream().filter(a -> a.getIsPre().equalsIgnoreCase(PackIsPreEnum.NORMAL.getCode())).map(PackageDetailDTO::getCheckQty).reduce(BigDecimal.ZERO, BigDecimal::add));
            } else {
                pack.setOutSkuQty(packageDetailList.stream().filter(a -> a.getIsPre().equalsIgnoreCase(PackIsPreEnum.PRE.getCode()) || a.getIsPre().equalsIgnoreCase(PackIsPreEnum.LAST.getCode())).map(PackageDetailDTO::getCheckQty).reduce(BigDecimal.ZERO, BigDecimal::add));
            }
            packageDetailList.stream().forEach(entity -> {
                entity.setOutStockQty(entity.getCheckQty());
                entity.setStatus(PackEnum.STATUS.PREPARE_HANDLER_STATUS.getCode());
            });
            //查询出库单明细
            ShipmentOrderParam shipmentOrderParam = new ShipmentOrderParam();
            shipmentOrderParam.setShipmentOrderCode(pack.getShipmentOrderCode());
            ShipmentOrderDTO shipmentOrder = remoteShipmentOrderClient.get(shipmentOrderParam);
            if (ObjectUtils.isEmpty(shipmentOrder)) {
                throw new BaseException(WmsHandoverBizEnum.HANDOVER_SHIPMENT_NOT_EXITS, shipmentOrder.getShipmentOrderCode());
            }
            if (Objects.equals(shipmentOrder.getStatus(), ShipmentOrderEnum.STATUS.OUT_STOCK_STATUS.getCode())) {
                throw new BaseException(BaseBizEnum.TIP, String.format("出库单:%s已出库,请核查！！！", shipmentOrder.getShipmentOrderCode()));
            }
            //10 记录部分包裹出库  30 最后包裹出库
            String saveShipmentOrderLog = "00";
            if (Objects.equals(shipmentOrder.getStatus(), ShipmentOrderEnum.STATUS.CHECK_COMPLETE_STATUS.getCode())) {
                saveShipmentOrderLog = "10";
            }
            List<ShipmentOrderDetailDTO> shipmentOrderDetailList = remoteShipmentOrderClient.getDetailList(shipmentOrder.getShipmentOrderCode());

            shipmentOrder.setOutPackageQty(shipmentOrder.getOutPackageQty() + 1);
            shipmentOrder.setStatus(ShipmentOrderEnum.STATUS.PART_OUT_STOCK_STATUS.getCode());
            if (shipmentOrder.getFirstPackOutStockDate().equals(0L)) {
                shipmentOrder.setFirstPackOutStockDate(System.currentTimeMillis());
            }
            //回写出库单明细
            shipmentOrderDetailList.stream().forEach(entity -> {
                BigDecimal writQty = packageDetailList.stream().filter(a -> a.getIsPre().equalsIgnoreCase(PackIsPreEnum.NORMAL.getCode())).filter(a -> a.getPUid().equals(entity.getId())).map(PackageDetailDTO::getCheckQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                if (!ObjectUtils.isEmpty(writQty)) {
                    entity.setOutStockQty(entity.getOutStockQty().add(writQty));

                    //TODO add 2021-04-12 出库数量不能大于计划数量
                    if (entity.getOutStockQty().compareTo(entity.getExpSkuQty()) > 0) {
                        throw new BaseException(BaseBizEnum.TIP, String.format("出库单:%s商品:%s出库数量不能大于计划数量", entity.getShipmentOrderCode(), entity.getSkuCode()));
                    }
                    entity.setStatus(ShipmentOrderEnum.STATUS.PART_OUT_STOCK_STATUS.getCode());
                    if (entity.getOutStockQty().compareTo(entity.getExpSkuQty()) == 0) {
                        entity.setStatus(ShipmentOrderEnum.STATUS.OUT_STOCK_STATUS.getCode());
                    }
                }
            });
            shipmentOrder.setOutSkuQty(shipmentOrderDetailList.stream().map(ShipmentOrderDetailDTO::getOutStockQty).reduce(BigDecimal.ZERO, BigDecimal::add));
            //全部出库
            List<ShipmentOrderDetailDTO> shipmentOrderDetailCheckCompleteList = shipmentOrderDetailList.stream().filter(a -> a.getStatus().equals(ShipmentOrderEnum.STATUS.OUT_STOCK_STATUS.getCode())).collect(Collectors.toList());
            if (shipmentOrderDetailCheckCompleteList.size() == shipmentOrderDetailList.size()) {
                shipmentOrder.setStatus(ShipmentOrderEnum.STATUS.OUT_STOCK_STATUS.getCode());
                shipmentOrder.setOutStockDate(System.currentTimeMillis());
                saveShipmentOrderLog = "30";
            }

            handover.setPackageQty(handover.getPackageQty() + 1);
            //创建状态，交接单改成交接中 加上网点编码和名称
            if (handover.getStatus().equalsIgnoreCase(HandoverEnum.statusEnum.CREATE_STATUS.getCode())) {
                handover.setStatus(HandoverEnum.statusEnum.DOING_STATUS.getCode());
                handover.setExpressBranch(pack.getExpressBranch());
                handover.setExpressBranchName(pack.getExpressBranchName());
            }
            //交接明细
            HandoverDetailDTO handoverDetail = new HandoverDetailDTO();
            handoverDetail.setWarehouseCode(pack.getWarehouseCode());
            handoverDetail.setCargoCode(pack.getCargoCode());
            handoverDetail.setPoNo(pack.getPoNo());
            handoverDetail.setSoNo(pack.getSoNo());
            handoverDetail.setExpressNo(pack.getExpressNo());
            handoverDetail.setLineSeq(remoteHandoverClient.getMaxLineSeq(handover.getHandoverCode()) + 1);
            handoverDetail.setShipmentOrderCode(pack.getShipmentOrderCode());
            handoverDetail.setPackageCode(pack.getPackageCode());
            handoverDetail.setHandoverCode(handover.getHandoverCode());
            handoverDetail.setCarrierCode(handover.getCarrierCode());

            if (!ObjectUtils.isEmpty(param.getRealWeight()) && param.getRealWeight().compareTo(BigDecimal.ZERO) > 0) {
                pack.setRealWeight(param.getRealWeight());
                handoverDetail.setPackageWeight(param.getRealWeight());
            } else {
                handoverDetail.setPackageWeight(pack.getRealWeight());
            }
            //校验出库单重量
            if (ObjectUtils.isEmpty(handoverDetail.getPackageWeight()) || handoverDetail.getPackageWeight().compareTo(BigDecimal.ZERO) <= 0) {
                throw new BaseException(BaseBizEnum.TIP, String.format("包裹：%s重量不正确！", pack.getPackageCode()));
            }
            //包裹理论重量、实际重量误差校验
            if (!Objects.equals(shipmentOrder.getBusinessType(), ShipmentOrderEnum.BUSSINESS_TYPE.B2B.toString())) {
                iCheckPackWeightAndVolumeBiz.checkPackageWeightAndVolumeNew(pack, packageDetailList, CommonConstantUtil.WMS_WEB_WEIGHT);
            }
            //
            String weightAutomation = param.getWeightAutomation();
            String weightAutomationMessage = "电子秤称重";
            if (!StringUtils.isEmpty(weightAutomation) && weightAutomation.equalsIgnoreCase(HandoverEnum.weightAutomationEnum.ARTIFICIAL.getCode())) {
                weightAutomationMessage = "手动输入重量";
                noticeWeigh(pack.getWarehouseCode(), CurrentUserHolder.getUserName(), pack.getPackageCode());
            }

            PackageLogDTO packageLog = new PackageLogDTO();
            packageLog.setCargoCode(pack.getCargoCode());
            packageLog.setPackageCode(pack.getPackageCode());
            packageLog.setWarehouseCode(pack.getWarehouseCode());
            packageLog.setOpBy(CurrentUserHolder.getUserName());
            packageLog.setOpDate(System.currentTimeMillis());
            if (Objects.equals(shipmentOrder.getBusinessType(), ShipmentOrderEnum.BUSSINESS_TYPE.B2B.toString())) {
                packageLog.setOpContent(String.format("称重:%s 包裹,出库成功", pack.getPackageCode()));
            } else {
                packageLog.setOpRemark(weightAutomationMessage);
                packageLog.setOpContent(String.format("【%s】称重:%s 包裹,出库成功", weightAutomationMessage, pack.getPackageCode()));
            }

            ShipmentOrderLogDTO shipmentOrderLog = new ShipmentOrderLogDTO();
            shipmentOrderLog.setCargoCode(pack.getCargoCode());
            shipmentOrderLog.setShipmentOrderCode(pack.getShipmentOrderCode());
            shipmentOrderLog.setWarehouseCode(pack.getWarehouseCode());
            shipmentOrderLog.setOpBy(CurrentUserHolder.getUserName());
            shipmentOrderLog.setOpDate(System.currentTimeMillis());
            if (saveShipmentOrderLog.equals("10")) {
                shipmentOrderLog.setOpContent(String.format("出库单部分出库成功,包裹单号:%s", pack.getPackageCode()));
            }
            if (saveShipmentOrderLog.equals("30")) {
                shipmentOrderLog.setOpContent(String.format("出库单出库成功,包裹单号:%s", pack.getPackageCode()));
            }
            //事务信息提交
            WeighCommitBillBO weighCommit = new WeighCommitBillBO();
            weighCommit.setShipmentOrder(shipmentOrder);
            weighCommit.setShipmentOrderDetailList(shipmentOrderDetailList);
            weighCommit.setPack(pack);
            weighCommit.setPackageDetailList(packageDetailList);
            weighCommit.setHandover(handover);
            weighCommit.setHandoverDetail(handoverDetail);
            weighCommit.setPackageLog(packageLog);
            if (saveShipmentOrderLog.equals("10") || saveShipmentOrderLog.equals("30")) {
                weighCommit.setShipmentOrderLog(shipmentOrderLog);
            }
            if (shipmentOrder.getBusinessType().equals(ShipmentOrderEnum.BUSSINESS_TYPE.B2C.toString())) {
                OutSourceCodeParam outSourceCodeParam = new OutSourceCodeParam();
                outSourceCodeParam.setPackageCode(pack.getPackageCode());
                outSourceCodeParam.setShipmentOrderCode(pack.getShipmentOrderCode());
                List<OutSourceCodeDTO> outSourceCodeDTOList = remoteOutSourceCodeClient.getList(outSourceCodeParam);
                if (!CollectionUtils.isEmpty(outSourceCodeDTOList)) {
                    outSourceCodeDTOList.forEach(a -> a.setOutStockDate(System.currentTimeMillis()));
                    weighCommit.setOutSourceCodeDTOList(outSourceCodeDTOList);
                }
            }
            //合流单校验必须所有包裹在同一个拣选单里面
            if (remoteWarehouseClient.getTaoTianWarehouse(CurrentRouteHolder.getWarehouseCode())
                    && Objects.equals(pack.getBusinessType(), ShipmentOrderEnum.BUSSINESS_TYPE.B2C.toString())
                    && isTaoTianMerge(pack)) {
                checkTaoTianMergeScanWaybill(pack, handover.getHandoverCode(), true);
            }
            //人效消息
            SystemEventDTO systemEventDTO = buildSystemEventDTO(pack);
            weighCommit.setSystemEventDTO(systemEventDTO);
            //mq发送消息记录
            OperationTypeEnum operationTypeEnum;
            if (shipmentOrder.getOrderType().equalsIgnoreCase(ShipmentOrderEnum.ORDER_TYPE.CONSIGNMENT_OUT_STOCK_TYPE.getCode())) {
                operationTypeEnum = OperationTypeEnum.OPERATION_CIRCLE_GOODS_STOCK;
            } else if (shipmentOrder.getOrderType().equalsIgnoreCase(ShipmentOrderEnum.ORDER_TYPE.SALE_OUT_STOCK_TYPE.getCode())) {
                operationTypeEnum = OperationTypeEnum.OPERATION_CIRCLE_GOODS_SALE_STOCK;
            } else {
                operationTypeEnum = OperationTypeEnum.OPERATION_STOCK;
            }
            MessageMqDTO messageMqDTO = buildMessageMqDTO(pack, operationTypeEnum, BillTypeEnum.BILL_TYPE_PACKAGE);
            weighCommit.setMessageMqDTO(messageMqDTO);
            //提交数据
            Boolean result = remoteBillContextClient.handoverWeightCommit(weighCommit);
            if (!result) {
                throw new BaseException(WmsHandoverBizEnum.HANDOVER_PACKAGE_WEIGHT_ERROR);
            }
            //发送消息-扣减库存
            try {
                StockOperationMessage stockOperationMessage = new StockOperationMessage();
                stockOperationMessage.setWarehouseCode(pack.getWarehouseCode());
                stockOperationMessage.setBillNo(pack.getPackageCode());
                stockOperationMessage.setOperationType(operationTypeEnum.getType());
                stockOperationMessage.setCargoCode(pack.getCargoCode());
                stockOperationMessage.setBillType(BillTypeEnum.BILL_TYPE_PACKAGE.getType());
                remoteMessageClient.sendStockOperationMessageWithNoTX(stockOperationMessage);
            } catch (Exception e) {
                e.printStackTrace();
            }
            weightResultDTO.setIsComplete("10");
            if (!StringUtils.isEmpty(param.getPickCode())) {
                weightResultDTO.setIsComplete(batchOutPickEnd(param.getPickCode()));
            }
            //返回结果集
            weightResultDTO.setCarrierCode(pack.getCarrierCode());
            weightResultDTO.setExpressBranch(pack.getExpressBranch());
            weightResultDTO.setExpressBranchName(pack.getExpressBranchName());
        } catch (Exception e) {
            log.error("out bound error:{}", e.getMessage());
            e.printStackTrace();
            throw e;
        } finally {
            if (tryLock) {
                lock.unlock();
            }
        }
        weightResultDTO.setHandoverCode(handover.getHandoverCode());
        weightResultDTO.setPackageQty(handover.getPackageQty());
        log.info("wmsWeightPackSuccess:{}", pack.getWarehouseCode() + "#" + pack.getPackageCode() + "");
        return Result.success(weightResultDTO);
    }

    private void noticeWeigh(String warehouseCode, String opBy, String packageCode) {
        try {
            List<String> addCargoWeChatWarnUrls = wmsOtherConfig.getWeighWeChatWarnUrls();
            WeChatMessageDTO messageDTO = new WeChatMessageDTO();
            WeChatMessageDTO.Content markdown = new WeChatMessageDTO.Content();
            markdown.setContent(String.format("**仓库编码:%s  **\n 手动输入重量操作人:%s \n 包裹号:%s  \n 操作时间:%s", warehouseCode, opBy, packageCode, DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss")));
            messageDTO.setMarkdown(markdown);
            log.info("noticeWeigh:{}", warehouseCode + ":" + JSONUtil.toJsonStr(messageDTO));
            if (!CollectionUtils.isEmpty(addCargoWeChatWarnUrls)) {
                addCargoWeChatWarnUrls.forEach(it -> {
                    HttpUtil.post(it, JSONUtil.toJsonStr(messageDTO));
                });
            }
        } catch (Exception e) {
        }
    }

    /**
     * @param pickCode
     * @return java.lang.String
     * <AUTHOR>
     * @describe:
     * @date 2022/10/17 10:20
     */
    private String batchOutPickEnd(String pickCode) {
        PickDetailParam pickDetailParam = new PickDetailParam();
        pickDetailParam.setPickCode(pickCode);
        List<PickDetailDTO> pickDetailDTOList = remotePickDetailClient.getList(pickDetailParam);
        if (CollectionUtils.isEmpty(pickDetailDTOList)) {
            throw new BaseException(BaseBizEnum.TIP, "拣选单未找到包裹");
        }
        PackageParam packageParam = new PackageParam();
        packageParam.setPackageCodeList(pickDetailDTOList.stream().map(PickDetailDTO::getPackageCode).collect(Collectors.toList()));
        List<PackageDTO> packageDTOList = remotePackageClient.getList(packageParam);
        if (CollectionUtils.isEmpty(packageDTOList)) {
            throw new BaseException(BaseBizEnum.TIP, "拣选单未找到包裹");
        }
        if (packageDTOList.stream().allMatch(a -> Arrays.asList(PackEnum.STATUS.PART_ASSIGN_STATUS.getCode(), PackEnum.STATUS.PART_ASSIGN_CANCEL_STATUS.getCode(), PackEnum.STATUS.PREPARE_HANDLER_STATUS.getCode(), PackEnum.STATUS.PART_ASSIGN_COMPLETE_STATUS.getCode()).contains(a.getStatus()))) {
            return "30";
        }
        return "10";
    }

    /**
     * @param pack
     * @param operationTypeEnum
     * @param billTypePackage
     * @return com.dt.domain.bill.dto.message.MessageMqDTO
     * @author: WuXian
     * description:
     * create time: 2021/12/3 10:55
     */
    private MessageMqDTO buildMessageMqDTO(PackageDTO pack, OperationTypeEnum operationTypeEnum, BillTypeEnum billTypePackage) {
        MessageMqDTO messageMqDTO = new MessageMqDTO();
        messageMqDTO.setWarehouseCode(pack.getWarehouseCode());
        messageMqDTO.setCargoCode(pack.getCargoCode());
        messageMqDTO.setBillNo(pack.getPackageCode());
        messageMqDTO.setOperationType(operationTypeEnum.getType());
        messageMqDTO.setBillType(billTypePackage.getType());
        messageMqDTO.setCreatedTime(System.currentTimeMillis());
        messageMqDTO.setStatus(MessageMqStatusEnum.CREATE_STATUS.getCode());
        return messageMqDTO;
    }


    private void checkHandoverStatus(HandoverDTO handover) {
        if (HandoverEnum.statusEnum.COMPLETE_STATUS.getCode().equals(handover.getStatus()) || HandoverEnum.statusEnum.CANCEL_STATUS.getCode().equals(handover.getStatus())) {
            throw new BaseException(BaseBizEnum.TIP, String.format("交接单%s状态不正确,当前状态:%s", handover.getHandoverCode(), HandoverEnum.statusEnum.getEnum(handover.getStatus()).get().getMessage()));
        }
    }

    @Override
    public Result<Boolean> completeHandover(CodeListParam param) {
        HandoverParam handoverParam = new HandoverParam();
        handoverParam.setHandoverCodeList(param.getCodeList());
        List<HandoverDTO> handoverList = remoteHandoverClient.getList(handoverParam);
        if (CollectionUtils.isEmpty(handoverList) || handoverList.size() != param.getCodeList().size()) {
            throw new BaseException(WmsHandoverBizEnum.HANDOVER_HANDOVER_NOT_EXITS, String.join(",", param.getCodeList()));
        }
        List<ContainerDTO> containerList = new ArrayList<>();
        List<ContainerLogDTO> containerLogDTOList = new ArrayList<>();
        for (HandoverDTO handover : handoverList) {
            if (!HandoverEnum.statusEnum.DOING_STATUS.getCode().equals(handover.getStatus())) {
                throw new BaseException(BaseBizEnum.TIP, "出库交接单不是交接中状态、不允许交接");
            }
            //合流单校验必须所有包裹在同一个拣选单里面
            if (remoteWarehouseClient.getTaoTianWarehouse(CurrentRouteHolder.getWarehouseCode())) {
                checkTaoTianMerge(handover.getHandoverCode());
            }
            //交接单状态修改
            handover.setHandoverTime(System.currentTimeMillis());
            handover.setStatus(HandoverEnum.statusEnum.COMPLETE_STATUS.getCode());
            //非自动化需要解绑容器
            if (Objects.equals(handover.getAutomationType(), HandoverEnum.AutomationTypeEnum.NO_AUTOMATION.getCode())) {
                ContainerDTO occupyContainer = remoteContainerClient.queryByCode(handover.getContCode());
                if (ObjectUtils.isEmpty(occupyContainer)) {
                    throw new BaseException(BaseBizEnum.TIP, String.format("出库交接单容器：%s异常", handover.getContCode()));
                }
                //容器日志记录
                ContainerLogDTO containerLogDTO = new ContainerLogDTO();
                containerLogDTO.setWarehouseCode(occupyContainer.getWarehouseCode());
                containerLogDTO.setContCode(occupyContainer.getCode());
                containerLogDTO.setCreatedBy(CurrentUserHolder.getUserName());
                containerLogDTO.setCreatedTime(System.currentTimeMillis());
                containerLogDTO.setOpBy(CurrentUserHolder.getUserName());
                containerLogDTO.setOpContent(String.format("交接单%s释放容器:%s", handover.getHandoverCode(), occupyContainer.getCode()));
                containerLogDTO.setOpDate(System.currentTimeMillis());
                containerLogDTO.setOccupyNo(occupyContainer.getOccupyNo());
                containerLogDTO.setOccupyType(occupyContainer.getOccupyType());
                containerLogDTOList.add(containerLogDTO);
                //释放容器
                occupyContainer.setStatus(ContainerStatusEnum.ENABLE.getValue());
                occupyContainer.setOccupyType("");
                occupyContainer.setOccupyNo("");
                occupyContainer.setRemark("");
                containerList.add(occupyContainer);
            }
        }
        HandoverContainerCommitBO handoverContainer = new HandoverContainerCommitBO();
        handoverContainer.setContainerList(containerList);
        handoverContainer.setHandoverList(handoverList);
        handoverContainer.setContainerLogDTOList(containerLogDTOList);
        Boolean result = handoverGtsService.handoverContainerCommit(handoverContainer);
        if (!result) {
            throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
        }
        return Result.success(true);
    }

    /**
     * @param handoverCode
     * @return void
     * <AUTHOR>
     * @describe: 完成交接单，校验合流单
     * @date 2024/4/13 19:17
     */
    private void checkTaoTianMerge(String handoverCode) {
        HandoverDetailParam handoverDetailParam = new HandoverDetailParam();
        handoverDetailParam.setHandoverCode(handoverCode);
        List<HandoverDetailDTO> handoverDetailDTOList = remoteHandoverDetailClient.getList(handoverDetailParam);
        if (CollectionUtils.isEmpty(handoverDetailDTOList)) {
            return;
        }
        List<String> packCodeList = handoverDetailDTOList.stream().map(HandoverDetailDTO::getPackageCode).collect(Collectors.toList());
        //只查询合流单
        PackageParam packageParam = new PackageParam();
        packageParam.setPackageCodeList(packCodeList);
        packageParam.setOrderTag(OrderTagEnum.enumToNum(OrderTagEnum.ORDER_MERGE));
        List<PackageDTO> packageDTOList = remotePackageClient.getList(packageParam);
        if (CollectionUtils.isEmpty(packageDTOList)) {
            return;
        }
        Map<String, List<PackageDTO>> packMap = packageDTOList.stream().collect(Collectors.groupingBy(it -> StrUtil.join("#", it.getCargoCode(), it.getTradeNo())));
        for (Map.Entry<String, List<PackageDTO>> packTradeNo : packMap.entrySet()) {
            List<PackageDTO> packageDTOTemp = packTradeNo.getValue();
            Integer mergeNum = packageDTOTemp.get(0).getMergeNum();
            String tradeNo = packageDTOTemp.get(0).getTradeNo();
            if (mergeNum != packageDTOTemp.size()) {
                throw new BaseException(BaseBizEnum.TIP, String.format("淘天合流单,交易号:%s所有包裹必须在同一个交接单,总包裹数:%s,当前包裹数:%s", tradeNo, mergeNum, packageDTOTemp.size()));
            }
        }
    }

    @Override
    public Result<Boolean> cancelHandover(CodeListParam param) {
        HandoverParam handoverParam = new HandoverParam();
        handoverParam.setHandoverCodeList(param.getCodeList());
        List<HandoverDTO> handoverList = remoteHandoverClient.getList(handoverParam);
        if (CollectionUtils.isEmpty(handoverList) || handoverList.size() != param.getCodeList().size()) {
            throw new BaseException(WmsHandoverBizEnum.HANDOVER_HANDOVER_NOT_EXITS, String.join(",", param.getCodeList()));
        }
        List<ContainerDTO> containerList = new ArrayList<>();
        List<ContainerLogDTO> containerLogDTOList = new ArrayList<>();
        for (HandoverDTO handover : handoverList) {
            if (!HandoverEnum.statusEnum.CREATE_STATUS.getCode().equals(handover.getStatus())) {
                throw new BaseException(WmsHandoverBizEnum.HANDOVER_STATUS_IS_NOT_CREATED);
            }
            if (ObjectUtils.isEmpty(handover)) {
                throw new BaseException(WmsHandoverBizEnum.HANDOVER_STATUS_IS_NOT_CREATED);
            }
            handover.setStatus(HandoverEnum.statusEnum.CANCEL_STATUS.getCode());

            if (Objects.equals(handover.getAutomationType(), HandoverEnum.AutomationTypeEnum.NO_AUTOMATION.getCode())) {
                ContainerDTO occupyContainer = remoteContainerClient.queryByCode(handover.getContCode());
                //容器日志记录
                ContainerLogDTO containerLogDTO = new ContainerLogDTO();
                containerLogDTO.setWarehouseCode(occupyContainer.getWarehouseCode());
                containerLogDTO.setContCode(occupyContainer.getCode());
                containerLogDTO.setCreatedBy(CurrentUserHolder.getUserName());
                containerLogDTO.setCreatedTime(System.currentTimeMillis());
                containerLogDTO.setOpBy(CurrentUserHolder.getUserName());
                containerLogDTO.setOpContent(String.format("交接单%s释放容器:%s", handover.getHandoverCode(), occupyContainer.getCode()));
                containerLogDTO.setOpDate(System.currentTimeMillis());
                containerLogDTO.setOccupyNo(occupyContainer.getOccupyNo());
                containerLogDTO.setOccupyType(occupyContainer.getOccupyType());
                containerLogDTOList.add(containerLogDTO);
                //释放容器
                occupyContainer.setStatus(ContainerStatusEnum.ENABLE.getValue());
                occupyContainer.setOccupyType("");
                occupyContainer.setOccupyNo("");
                occupyContainer.setRemark("");
                containerList.add(occupyContainer);
            }
        }
        HandoverContainerCommitBO handoverContainer = new HandoverContainerCommitBO();
        handoverContainer.setContainerList(containerList);
        handoverContainer.setHandoverList(handoverList);
        handoverContainer.setContainerLogDTOList(containerLogDTOList);
        Boolean result = handoverGtsService.handoverContainerCommit(handoverContainer);
        if (!result) {
            throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
        }
        return Result.success(true);
    }

    /**
     * 暂不支持箱号
     *
     * @param param
     * @return
     */
    @Override
    public Result<Boolean> sacnPDABillNoAndOutBound(CommitHandoverParam param) {
        log.info("sacnPDABillNoAndOutBound warehouseCode:{} param:{}", CurrentRouteHolder.getWarehouseCode(), JSONUtil.toJsonStr(param));
        HandoverParam handoverParam = new HandoverParam();
        handoverParam.setHandoverCode(param.getHandoverCode());
        HandoverDTO handover = remoteHandoverClient.get(handoverParam);
        if (ObjectUtils.isEmpty(handover)) {
            throw new BaseException(WmsHandoverBizEnum.HANDOVER_HANDOVER_NOT_EXITS, param.getHandoverCode());
        }
        //校验容器
        checkContOccupy(handover.getHandoverCode(), handover.getContCode());
        //获取快递公司
        String carrierCode = handover.getCarrierCode();
        //校验有效包裹
        PackageDTO pack = checkPackageEffective(carrierCode, param.getBillNo());

        if (!Objects.equals(pack.getCarrierCode(), handover.getCarrierCode())) {
            throw new BaseException(BaseBizEnum.TIP, "当前运单号快递公司与交接单不一致");
        }
        //check交接单网点是否一致
        if (handover.getStatus().equalsIgnoreCase(HandoverEnum.statusEnum.DOING_STATUS.getCode())) {
            checkHandoverExpressBranch(handover, pack);
        }
        if (!pack.getBusinessType().equals(ShipmentOrderEnum.BUSSINESS_TYPE.B2B.toString())) {
            throw new BaseException(BaseBizEnum.TIP, "交接包裹不属于B2B");
        }
        //拦截出库单
        interceptionManagerBiz.interception(PackReturnEnum.INTERCEPT_STATUS.WEIGH_HAND_POINT.getCode(), pack.getPackageCode(), "", null, "");

        if (!PackEnum.STATUS.CHECK_COMPELETE_STATUS.getCode().equals(pack.getStatus())) {
            throw new BaseException(WmsHandoverBizEnum.HANDOVER_PACKAGE_STATUS_PROTECT, pack.getExpressNo(), pack.getPackageCode(), PackEnum.STATUS.findEnumDesc(pack.getStatus()).getDesc(), PackEnum.STATUS.CHECK_COMPELETE_STATUS.getDesc());
        }
        if (!handover.getCarrierCode().equals(pack.getCarrierCode())) {
            throw new BaseException(WmsHandoverBizEnum.HANDOVER_PACKAGE_WAYBILL_NOT_MATCH, param.getBillNo());
        }
//        //获取暂存位
//        LocationDTO minPickTempLocation = getLocationBySkuQuality(pack.getSkuQuality());

        PackageDetailParam packageDetailParam = new PackageDetailParam();
        packageDetailParam.setPackageCode(pack.getPackageCode());
        List<PackageDetailDTO> packageDetailList = remotePackageDetailClient.getList(packageDetailParam);

        //包裹
        pack.setStatus(PackEnum.STATUS.PREPARE_HANDLER_STATUS.getCode());
        pack.setOutStockDate(System.currentTimeMillis());
        pack.setOutSkuQty(packageDetailList.stream().map(PackageDetailDTO::getCheckQty).reduce(BigDecimal.ZERO, BigDecimal::add));
        packageDetailList.stream().forEach(entity -> {
            entity.setOutStockQty(entity.getCheckQty());
            entity.setStatus(PackEnum.STATUS.PREPARE_HANDLER_STATUS.getCode());
        });

        ShipmentOrderParam shipmentOrderParam = new ShipmentOrderParam();
        shipmentOrderParam.setShipmentOrderCode(pack.getShipmentOrderCode());
        ShipmentOrderDTO shipmentOrder = remoteShipmentOrderClient.get(shipmentOrderParam);
        if (ObjectUtils.isEmpty(pack)) {
            throw new BaseException(WmsHandoverBizEnum.HANDOVER_SHIPMENT_NOT_EXITS, shipmentOrder.getShipmentOrderCode());
        }
        if (Objects.equals(shipmentOrder.getStatus(), ShipmentOrderEnum.STATUS.OUT_STOCK_STATUS.getCode())) {
            throw new BaseException(BaseBizEnum.TIP, String.format("出库单:%s已出库,请核查！！！", shipmentOrder.getShipmentOrderCode()));
        }
        //10 记录部分包裹出库  30 最后包裹出库
        String saveShipmentOrderLog = "00";
        if (Objects.equals(shipmentOrder.getStatus(), ShipmentOrderEnum.STATUS.CHECK_COMPLETE_STATUS.getCode())) {
            saveShipmentOrderLog = "10";
        }
        List<ShipmentOrderDetailDTO> shipmentOrderDetailList = remoteShipmentOrderClient.getDetailList(shipmentOrder.getShipmentOrderCode());

        //兼容后置拆包逻辑
        List<AllocationOrderDTO> allocationOrderList = remoteAllocationOrderClient.getPackList(Arrays.asList(pack.getPackageCode()), pack.getWaveCode());

        //出库单状态变更
        shipmentOrder.setOutSkuQty(shipmentOrder.getOutSkuQty().add(pack.getOutSkuQty()));
        shipmentOrder.setOutPackageQty(shipmentOrder.getOutPackageQty() + 1);
        shipmentOrder.setStatus(ShipmentOrderEnum.STATUS.PART_OUT_STOCK_STATUS.getCode());
        if (shipmentOrder.getFirstPackOutStockDate().equals(0L)) {
            shipmentOrder.setFirstPackOutStockDate(System.currentTimeMillis());
        }

        shipmentOrderDetailList.stream().forEach(entity -> {
            BigDecimal writeQty = packageDetailList.stream().filter(a -> a.getIsPre().equalsIgnoreCase(PackIsPreEnum.NORMAL.getCode())).filter(a -> a.getPUid().equals(entity.getId())).map(PackageDetailDTO::getCheckQty).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (!ObjectUtils.isEmpty(writeQty)) {
                entity.setOutStockQty(entity.getOutStockQty().add(writeQty));

                //TODO add 2021-04-12 出库数量不能大于计划数量
                if (entity.getOutStockQty().compareTo(entity.getExpSkuQty()) > 0) {
                    throw new BaseException(BaseBizEnum.TIP, String.format("出库单:%s商品:%s出库数量不能大于计划数量", entity.getShipmentOrderCode(), entity.getSkuCode()));
                }
                entity.setStatus(ShipmentOrderEnum.STATUS.PART_OUT_STOCK_STATUS.getCode());
                if (entity.getOutStockQty().compareTo(entity.getExpSkuQty()) == 0) {
                    entity.setStatus(ShipmentOrderEnum.STATUS.OUT_STOCK_STATUS.getCode());
                }
            }
        });

        //全部出库
        List<ShipmentOrderDetailDTO> shipmentOrderDetailCheckCompleteList = shipmentOrderDetailList.stream().filter(a -> a.getStatus().equals(ShipmentOrderEnum.STATUS.OUT_STOCK_STATUS.getCode())).collect(Collectors.toList());
        if (shipmentOrderDetailCheckCompleteList.size() == shipmentOrderDetailList.size()) {
            shipmentOrder.setStatus(ShipmentOrderEnum.STATUS.OUT_STOCK_STATUS.getCode());
            shipmentOrder.setOutStockDate(System.currentTimeMillis());
            saveShipmentOrderLog = "30";
        }

        handover.setPackageQty(handover.getPackageQty() + 1);
        //创建状态，交接单改成交接中 加上网点编码和名称
        if (handover.getStatus().equalsIgnoreCase(HandoverEnum.statusEnum.CREATE_STATUS.getCode())) {
            handover.setStatus(HandoverEnum.statusEnum.DOING_STATUS.getCode());
            handover.setExpressBranch(pack.getExpressBranch());
            handover.setExpressBranchName(pack.getExpressBranchName());
        }
        //交接明细
        HandoverDetailDTO handoverDetail = new HandoverDetailDTO();
        handoverDetail.setWarehouseCode(pack.getWarehouseCode());
        handoverDetail.setCargoCode(pack.getCargoCode());
        handoverDetail.setPoNo(pack.getPoNo());
        handoverDetail.setSoNo(pack.getSoNo());
        handoverDetail.setExpressNo(pack.getExpressNo());
        handoverDetail.setLineSeq(remoteHandoverClient.getMaxLineSeq(handover.getHandoverCode()) + 1);
        handoverDetail.setShipmentOrderCode(pack.getShipmentOrderCode());
        handoverDetail.setPackageCode(pack.getPackageCode());
        handoverDetail.setHandoverCode(handover.getHandoverCode());
        handoverDetail.setCarrierCode(handover.getCarrierCode());
        handoverDetail.setPackageWeight(pack.getRealWeight());

        //校验出库单重量
        if (ObjectUtils.isEmpty(handoverDetail.getPackageWeight()) || handoverDetail.getPackageWeight().compareTo(BigDecimal.ZERO) <= 0) {
            throw new BaseException(BaseBizEnum.TIP, String.format("包裹：%s重量不正确！", pack.getPackageCode()));
        }

        //包裹理论重量、实际重量误差校验 B单是在复核称重校验 不进行二次校验
        if (shipmentOrder.getBusinessType().equals(ShipmentOrderEnum.BUSSINESS_TYPE.B2C.toString())) {
            checkPackageWeightAndVolume(pack, packageDetailList);
        }

        PackageLogDTO packageLog = new PackageLogDTO();
        packageLog.setCargoCode(pack.getCargoCode());
        packageLog.setPackageCode(pack.getPackageCode());
        packageLog.setWarehouseCode(pack.getWarehouseCode());
        packageLog.setOpBy(CurrentUserHolder.getUserName());
        packageLog.setOpDate(System.currentTimeMillis());
        packageLog.setOpContent(String.format("包裹出库成功,单号:%s", pack.getPackageCode()));

        ShipmentOrderLogDTO shipmentOrderLog = new ShipmentOrderLogDTO();
        shipmentOrderLog.setCargoCode(pack.getCargoCode());
        shipmentOrderLog.setShipmentOrderCode(pack.getShipmentOrderCode());
        shipmentOrderLog.setWarehouseCode(pack.getWarehouseCode());
        shipmentOrderLog.setOpBy(CurrentUserHolder.getUserName());
        shipmentOrderLog.setOpDate(System.currentTimeMillis());
        if (saveShipmentOrderLog.equals("10")) {
            shipmentOrderLog.setOpContent(String.format("出库单部分出库成功,包裹单号:%s", pack.getPackageCode()));
        }
        if (saveShipmentOrderLog.equals("30")) {
            shipmentOrderLog.setOpContent(String.format("出库单出库成功,包裹单号:%s", pack.getPackageCode()));
        }

        //事务信息提交
        WeighCommitBillBO weighCommit = new WeighCommitBillBO();
        weighCommit.setShipmentOrder(shipmentOrder);
        weighCommit.setShipmentOrderDetailList(shipmentOrderDetailList);
        weighCommit.setPack(pack);
        weighCommit.setPackageDetailList(packageDetailList);
//        weighCommit.setAllocationOrderList(allocationOrderList);
        weighCommit.setHandover(handover);
        weighCommit.setHandoverDetail(handoverDetail);

        weighCommit.setPackageLog(packageLog);
        if (saveShipmentOrderLog.equals("10") || saveShipmentOrderLog.equals("30")) {
            weighCommit.setShipmentOrderLog(shipmentOrderLog);
        }
        //mq发送消息记录
        OperationTypeEnum operationTypeEnum;
        if (shipmentOrder.getOrderType().equalsIgnoreCase(ShipmentOrderEnum.ORDER_TYPE.CONSIGNMENT_OUT_STOCK_TYPE.getCode())) {
            operationTypeEnum = OperationTypeEnum.OPERATION_CIRCLE_GOODS_STOCK;
        } else if (shipmentOrder.getOrderType().equalsIgnoreCase(ShipmentOrderEnum.ORDER_TYPE.SALE_OUT_STOCK_TYPE.getCode())) {
            operationTypeEnum = OperationTypeEnum.OPERATION_CIRCLE_GOODS_SALE_STOCK;
        } else {
            operationTypeEnum = OperationTypeEnum.OPERATION_STOCK;
        }
        MessageMqDTO messageMqDTO = buildMessageMqDTO(pack, operationTypeEnum, BillTypeEnum.BILL_TYPE_PACKAGE);
        weighCommit.setMessageMqDTO(messageMqDTO);
        //库存数据组装 TODO 吴方兵(核销库存) 三级
//        List<StockTransactionDTO> stockTransactionDTOList = outStockBiz.buildStockToOutOrder(allocationOrderList);
//        weighCommit.setStockTransactionDTOList(stockTransactionDTOList);
        //人效消息
        SystemEventDTO systemEventDTO = buildSystemEventDTO(pack);
        weighCommit.setSystemEventDTO(systemEventDTO);
        //提交数据
        Boolean result = remoteBillContextClient.handoverWeightCommit(weighCommit);
        if (!result) {
            throw new BaseException(WmsHandoverBizEnum.HANDOVER_PACKAGE_WEIGHT_ERROR);
        }
        //发送消息-扣减库存
        StockOperationMessage stockOperationMessage = new StockOperationMessage();
        stockOperationMessage.setWarehouseCode(pack.getWarehouseCode());
        stockOperationMessage.setBillNo(pack.getPackageCode());
        stockOperationMessage.setOperationType(operationTypeEnum.getType());
        stockOperationMessage.setCargoCode(pack.getCargoCode());
        stockOperationMessage.setBillType(BillTypeEnum.BILL_TYPE_PACKAGE.getType());
        remoteMessageClient.sendStockOperationMessageWithNoTX(stockOperationMessage);
        return Result.success(true);
    }

    /**
     * 检查容器
     *
     * @param handoverCode
     * @param contCode
     */
    private ContainerDTO checkContOccupy(String handoverCode, String contCode) {
        ContainerDTO containerDTO = remoteContainerClient.queryByCode(contCode);
        if (containerDTO == null) {
            throw new BaseException(BaseBizEnum.TIP, String.format("容器号:%s不存在", contCode));
        }
        if (Objects.equals(containerDTO.getStatus(), ContainerStatusEnum.DISABLE.getValue())) {
            throw new BaseException(BaseBizEnum.TIP, String.format("容器号:%s不能使用", contCode));
        }
        if (!Objects.equals(containerDTO.getStatus(), ContainerStatusEnum.OCCUPY.getValue())) {
            throw new BaseException(BaseBizEnum.TIP, String.format("容器号:%s与交接单%s,未绑定", contCode, handoverCode));
        }
        if (!Objects.equals(handoverCode, containerDTO.getOccupyNo())) {
            throw new BaseException(BaseBizEnum.TIP, String.format("容器号:%s与交接单%s,未绑定", contCode, handoverCode));
        }
        return containerDTO;
    }

    @Override
    public Result<String> checkPDAHandoverContainer(ScanBillNoOrContainerParam param) {
        ContainerDTO container = remoteContainerClient.queryByCode(param.getCode());
        if (ObjectUtils.isEmpty(container)) {
            throw new BaseException(BaseBizEnum.TIP, String.format("编码：%s 容器扫描有误，请重新扫描", param.getCode()));
        }
        if (Objects.equals(container.getStatus(), ContainerStatusEnum.DISABLE.getValue())) {
            throw new BaseException(BaseBizEnum.TIP, "当前容器禁用");
        }
        //容器存在
        String handoverCode = "";
        if (!ObjectUtils.isEmpty(container)) {
            ContainerStatusEnum containerStatus = ContainerStatusEnum.getEnum(container.getStatus());
            switch (containerStatus) {
                case ENABLE:
                    //交接单
                    HandoverDTO createHandover = new HandoverDTO();
                    createHandover.setWarehouseCode(container.getWarehouseCode());
                    createHandover.setHandoverCode(iRemoteSeqRuleClient.findSequence(SeqEnum.HANDOVER_CODE_000001));
                    createHandover.setContCode(container.getCode());
                    createHandover.setCarrierCode(param.getCarrierCode());
                    createHandover.setPackageQty(0);
                    createHandover.setStatus(HandoverEnum.statusEnum.CREATE_STATUS.getCode());
                    createHandover.setPrintStatus(HandoverEnum.PrintStatusEnum.CREATE_STATUS.getCode());
                    //容器
                    container.setStatus(ContainerStatusEnum.OCCUPY.getValue());
                    container.setOccupyNo(createHandover.getHandoverCode());
                    container.setOccupyType(WorkBenchTypeEnum.HANDOVER.getType());
                    container.setRemark(String.format("%s:%s", WorkBenchTypeEnum.HANDOVER.getMessage(), container.getOccupyNo()));
                    //容器日志记录
                    ContainerLogDTO containerLogDTO = new ContainerLogDTO();
                    containerLogDTO.setWarehouseCode(container.getWarehouseCode());
                    containerLogDTO.setContCode(container.getCode());
                    containerLogDTO.setCreatedBy(CurrentUserHolder.getUserName());
                    containerLogDTO.setCreatedTime(System.currentTimeMillis());
                    containerLogDTO.setOpBy(CurrentUserHolder.getUserName());
                    containerLogDTO.setOpContent(String.format("交接单%s绑定容器:%s", createHandover.getHandoverCode(), container.getCode()));
                    containerLogDTO.setOpDate(System.currentTimeMillis());
                    containerLogDTO.setOccupyNo(container.getOccupyNo());
                    containerLogDTO.setOccupyType(container.getOccupyType());
                    //事务提交
                    ContainerHandoverBO containerHandover = new ContainerHandoverBO();
                    containerHandover.setContainer(container);
                    containerHandover.setHandover(createHandover);
                    containerHandover.setContainerLogDTO(containerLogDTO);
                    Boolean result = handoverGtsService.occupyContainerAndSaveHandover(containerHandover);
                    if (!result) {
                        throw new BaseException(BaseBizEnum.TIP, "交接作业批次创建异常，请稍后再试");
                    }
                    handoverCode = createHandover.getHandoverCode();
                    break;
                case OCCUPY:
                    if (!WorkBenchTypeEnum.HANDOVER.getType().equals(container.getOccupyType())) {
                        throw new BaseException(BaseBizEnum.TIP, String.format("容器%s已被其他业务%s占用，请重新扫描", param.getCode(), WorkBenchTypeEnum.fromCode(container.getOccupyType()).getMessage()));
                    }
                    HandoverParam occupyHandoverParam = new HandoverParam();
                    occupyHandoverParam.setHandoverCode(container.getOccupyNo());
                    HandoverDTO occupyHandover = remoteHandoverClient.get(occupyHandoverParam);
                    if (ObjectUtils.isEmpty(occupyHandover)) {
                        throw new BaseException(BaseBizEnum.TIP, String.format("作业批次%s容器%s占用信息异常，请重新扫描", container.getCode(), container.getOccupyNo()));
                    }
                    if (!Objects.equals(occupyHandover.getCarrierCode(), param.getCarrierCode())) {
                        throw new BaseException(BaseBizEnum.TIP, String.format("容器%s已绑定的快递公司与当前快递公司不匹配", container.getCode(), container.getOccupyNo()));
                    }
                    handoverCode = container.getOccupyNo();
                    break;
                default:
                    throw new BaseException(BaseBizEnum.TIP, String.format("容器%s已停用，请重新扫描", param.getCode()));
            }
        }
        return Result.success(handoverCode);
    }

    @Override
    public Result<Boolean> commitPDAHandover(CodeParam param) {
        HandoverParam handoverParam = new HandoverParam();
        handoverParam.setHandoverCode(param.getCode());
        HandoverDTO handover = remoteHandoverClient.get(handoverParam);
        if (StringUtils.isEmpty(handover)) {
            throw new BaseException(WmsHandoverBizEnum.HANDOVER_HANDOVER_NOT_EXITS, param.getCode());
        }
        ContainerDTO occupyContainer = remoteContainerClient.queryByCode(handover.getContCode());
        if (!HandoverEnum.statusEnum.DOING_STATUS.getCode().equals(handover.getStatus())) {
            throw new BaseException(WmsHandoverBizEnum.HANDOVER_STATUS_IS_NOT_CREATED);
        }
        if (ObjectUtils.isEmpty(handover)) {
            throw new BaseException(WmsHandoverBizEnum.HANDOVER_STATUS_IS_NOT_CREATED);
        }
        //交接单状态修改
        handover.setHandoverTime(System.currentTimeMillis());
        handover.setStatus(HandoverEnum.statusEnum.COMPLETE_STATUS.getCode());
        //容器日志记录
        ContainerLogDTO containerLogDTO = new ContainerLogDTO();
        containerLogDTO.setWarehouseCode(occupyContainer.getWarehouseCode());
        containerLogDTO.setContCode(occupyContainer.getCode());
        containerLogDTO.setCreatedBy(CurrentUserHolder.getUserName());
        containerLogDTO.setCreatedTime(System.currentTimeMillis());
        containerLogDTO.setOpBy(CurrentUserHolder.getUserName());
        containerLogDTO.setOpContent(String.format("交接单%s释放容器:%s", handover.getHandoverCode(), occupyContainer.getCode()));
        containerLogDTO.setOpDate(System.currentTimeMillis());
        containerLogDTO.setOccupyNo(occupyContainer.getOccupyNo());
        containerLogDTO.setOccupyType(occupyContainer.getOccupyType());
        //释放容器
        occupyContainer.setStatus(ContainerStatusEnum.ENABLE.getValue());
        occupyContainer.setOccupyType("");
        occupyContainer.setOccupyNo("");
        occupyContainer.setRemark("");
        HandoverContainerCommitBO handoverContainer = new HandoverContainerCommitBO();
        handoverContainer.setContainerList(Arrays.asList(occupyContainer));
        handoverContainer.setHandoverList(Arrays.asList(handover));
        handoverContainer.setContainerLogDTOList(Arrays.asList(containerLogDTO));
        Boolean result = handoverGtsService.handoverContainerCommit(handoverContainer);
        if (!result) {
            throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
        }
        return Result.success(true);
    }

    @Override
    public Result<Boolean> scanPickCode(CodeParam param) {
        PickDTO pickDTO = checkPickDTO(param.getCode());
        PickParam pickParam = new PickParam();
        pickParam.setPickCode(param.getCode());
        List<PickDetailDTO> pickDetailList = remotePickClient.getPickDetailList(pickParam);
        if (CollectionUtils.isEmpty(pickDetailList)) {
            throw new BaseException(BaseBizEnum.TIP, "拣选单未找到包裹记录");
        }
        if (pickDetailList.stream().anyMatch(a -> Objects.equals(a.getPackageStatus(), PackEnum.STATUS.PICK_COMPELETE_STATUS.getCode()) && Objects.equals(a.getPackageStatus(), PackEnum.STATUS.CHECK_BEGIN_STATUS.getCode()))) {
            throw new BaseException(BaseBizEnum.TIP, "拣选单存在未复核的包裹");
        }
        if (pickDetailList.stream().allMatch(a -> Objects.equals(a.getPackageStatus(), PackEnum.STATUS.PART_ASSIGN_STATUS.getCode()))) {
            throw new BaseException(BaseBizEnum.TIP, "拣选单所有包裹被拦截");
        }
        CargoConfigDTO cargoConfigDTO = remoteCargoConfigClient.queryByCargoCodeAndpropKey(pickDTO.getWarehouseCode(), pickDTO.getCargoCode(), CargoConfigParamEnum.SPOT_CHECK.getCode());
        Boolean isSpotCheck = Boolean.FALSE;
        if (cargoConfigDTO != null && cargoConfigDTO.getStatus().equals(CargoConfigStatusEnum.ENABLE.getValue()) && Integer.valueOf(cargoConfigDTO.getPropValue()) > 0) {
            isSpotCheck = Boolean.TRUE;
        } else {
            throw new BaseException(BaseBizEnum.TIP, "当前货主抽检系数未配置");
        }
        //TODO 校验抽检系数是否通过  查抽检表，判定当前包裹是否抽检完成
        PackageInspectionParam packageInspectionParam = new PackageInspectionParam();
        packageInspectionParam.setPickCode(pickDTO.getPickCode());
        List<PackageInspectionDTO> inspectionClientList = remotePackageInspectionClient.getList(packageInspectionParam);
        if (CollectionUtils.isEmpty(inspectionClientList)) {
            throw new BaseException(BaseBizEnum.TIP, "当前拣选单没有抽检,不允许出库!!");
        }
        checkInspectionPass(cargoConfigDTO.getPropValue(), inspectionClientList, pickDTO, CommonConstantUtil.WMS_WEB_WEIGHT, "");
        return Result.success(isSpotCheck);
    }

    /**
     * 抽检包裹数是否达标
     *
     * @param propValue
     * @param inspectionClientList
     */
    private void checkInspectionPass(String propValue, List<PackageInspectionDTO> inspectionClientList, PickDTO pickDTO, String flag, String warmMsg) {
        List<String> packCodeList = inspectionClientList.stream().map(PackageInspectionDTO::getPackageCode).distinct().collect(Collectors.toList());
        List<String> passPackCodeList = new ArrayList<>();
        for (String packCode : packCodeList) {
            List<PackageInspectionDTO> inspectionDTOS = inspectionClientList.stream().filter(a -> a.getPackageCode().equals(packCode)).collect(Collectors.toList());
            if (inspectionDTOS.stream().allMatch(a -> a.getResult().equals(PackInspectionEnum.FAIL_RESULT.getValue()))) {
                if (Objects.equals(flag, CommonConstantUtil.WMS_WCS_WEIGHT)) {
                    WechatUtil.sendMessage(String.format("拣选单内包裹号:%s,抽检不成功-", packCode) + warmMsg, urlConfig.getWcsInterceptPackageOutUrls());
                    return;
                } else {
                    throw new BaseException(BaseBizEnum.TIP, String.format("拣选单包裹%s,抽检不成功", packCode));
                }
            }
            if (inspectionDTOS.stream().anyMatch(a -> a.getResult().equals(PackInspectionEnum.SUCCESS_RESULT.getValue()))) {
                passPackCodeList.add(packCode);
            }
        }
        if (passPackCodeList.size() < Integer.valueOf(propValue)) {
            if (Objects.equals(flag, CommonConstantUtil.WMS_WCS_WEIGHT)) {
                WechatUtil.sendMessage("拣选单抽检未达到系数,请核查！！！" + warmMsg, urlConfig.getWcsInterceptPackageOutUrls());
            } else {
                throw new BaseException(BaseBizEnum.TIP, "当前拣选单有抽检未达到系数,不允许出库!!");
            }
        }
    }

    private PickDTO checkPickDTO(String pickCode) {
        PickParam pickParam = new PickParam();
        pickParam.setPickCode(pickCode);
        PickDTO pickDTO = remotePickClient.get(pickParam);
        if (pickDTO == null) {
            throw new BaseException(BaseBizEnum.TIP, "拣选单不存在");
        }
        if (!pickDTO.getType().equals(PickEnum.PickOrderTypeEnum.SPIKE.getCode())) {
            throw new BaseException(BaseBizEnum.TIP, "拣选单非秒杀单,不允许出库");
        }
        if (Integer.valueOf(pickDTO.getStatus()) < Integer.valueOf(PickEnum.PickStatusEnum.ZJ_END_STATUS.getCode())) {
            throw new BaseException(BaseBizEnum.TIP, "拣选单状态不对");
        }
        if (!pickDTO.getBatchCheckStatus().equals(PickBatchCheckEnum.YES_BATCH.getValue())) {
            throw new BaseException(BaseBizEnum.TIP, "拣选单非批量复核的单据");
        }
        return pickDTO;
    }

    @Override
    public Result<Boolean> batchOutBound(BatchOutBoundParam param) throws Exception {
        log.info("batchOutBound warehouseCode:{} param:{}", CurrentRouteHolder.getWarehouseCode(), JSONUtil.toJsonStr(param));
        PickDTO pickDTO = checkPickDTO(param.getPickCode());
        PickParam pickParam = new PickParam();
        pickParam.setPickCode(pickDTO.getPickCode());
        List<PickDetailDTO> pickDetailDTOList = remotePickClient.getPickDetailList(pickParam);
        if (CollectionUtils.isEmpty(pickDetailDTOList)) {
            throw new BaseException(BaseBizEnum.TIP, "拣选单明细为空");
        }
        if (!pickDTO.getStatus().equalsIgnoreCase(PickEnum.PickStatusEnum.ZJ_END_STATUS.getCode())) {
            throw new BaseException(BaseBizEnum.TIP, "拣选单非复核结束，不允许批量出库");
        }
        if (pickDetailDTOList.stream().noneMatch(a -> Objects.equals(a.getPackageCode(), param.getPackageCode()))) {
            throw new BaseException(BaseBizEnum.TIP, String.format("当前包裹%s不属于拣选单%s", param.getPackageCode(), param.getPickCode()));
        }
        //过滤拦截单
        RedissonMultiLock lock = multiLock(pickDTO.getWarehouseCode(), pickDetailDTOList.stream().map(PickDetailDTO::getShipmentOrderCode).distinct().collect(Collectors.toList()));
        Boolean tryLock = false;
        List<PackageDTO> packageDTOList = new ArrayList<>();
        try {
            tryLock = lock.tryLock(1, 300, TimeUnit.SECONDS);
            if (!tryLock) {
                throw new BaseException(BaseBizEnum.TIP, "操作太快了,请稍后刷新确认单据!!!");
            }
            PackageDTO packageDTO = remotePackageClient.getPackageByCode(param.getPackageCode());
            if (packageDTO == null) {
                throw new BaseException(BaseBizEnum.TIP, String.format("包裹号：%s未找到", param.getPackageCode()));
            }
            if (!packageDTO.getStatus().equals(PackEnum.STATUS.PREPARE_HANDLER_STATUS.getCode())) {
                throw new BaseException(BaseBizEnum.TIP, String.format("包裹号：%s未出库成功", param.getPackageCode()));
            }
            //包裹信息
            PackageParam packageParam = new PackageParam();
            packageParam.setStatus(PackEnum.STATUS.CHECK_COMPELETE_STATUS.getCode());
            packageParam.setPackageCodeList(pickDetailDTOList.stream().map(PickDetailDTO::getPackageCode).distinct().collect(Collectors.toList()));
            packageDTOList = remotePackageClient.getList(packageParam);
            if (CollectionUtils.isEmpty(packageDTOList)) {
                throw new BaseException(BaseBizEnum.TIP, "批量包裹未找到");
            }

            packageDTOList = packageDTOList.stream().filter(a -> !a.getPackageCode().equals(param.getPackageCode())).collect(Collectors.toList());

            List<String> waitOutBoundPack = packageDTOList.stream().map(PackageDTO::getPackageCode).distinct().collect(Collectors.toList());
            //复核完成的包裹,不包含已拦截和已出库的包裹
            pickDetailDTOList = pickDetailDTOList.stream().filter(a -> !Objects.equals(a.getPackageCode(), param.getPackageCode())).filter(a -> waitOutBoundPack.contains(a.getPackageCode())).filter(a -> Objects.equals(a.getPackageStatus(), PackEnum.STATUS.CHECK_COMPELETE_STATUS.getCode())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(pickDetailDTOList)) {
                throw new BaseException(BaseBizEnum.TIP, "拣选单未找到需要出库的数据");
            }
            //包裹明细
            PackageDetailParam packageDetailParam = new PackageDetailParam();
            packageDetailParam.setPackageCodeList(waitOutBoundPack);
            List<PackageDetailDTO> packageDetailDTOList = remotePackageClient.getPackageDetailListByListCode(packageDetailParam);
            if (CollectionUtils.isEmpty(packageDetailDTOList)) {
                throw new BaseException(BaseBizEnum.TIP, "批量包裹未找到");
            }
            //出库单主表
            ShipmentOrderParam shipmentOrderParam = new ShipmentOrderParam();
            shipmentOrderParam.setShipmentOrderCodeList(packageDTOList.stream().map(PackageDTO::getShipmentOrderCode).distinct().collect(Collectors.toList()));
            List<ShipmentOrderDTO> shipmentOrderDTOList = remoteShipmentOrderClient.getList(shipmentOrderParam);
            if (CollectionUtils.isEmpty(shipmentOrderDTOList)) {
                throw new BaseException(BaseBizEnum.TIP, "出库单未找到");
            }
            if (shipmentOrderDTOList.stream().anyMatch(a -> Objects.equals(a.getStatus(), ShipmentOrderEnum.STATUS.OUT_STOCK_STATUS.getCode()))) {
                throw new BaseException(BaseBizEnum.TIP, "批量出库的出库单存在已出库单据,请核查！！！");
            }
            //出库单明细
            ShipmentOrderDetailParam shipmentOrderDetailParam = new ShipmentOrderDetailParam();
            shipmentOrderDetailParam.setShipmentOrderCodeList(pickDetailDTOList.stream().map(PickDetailDTO::getShipmentOrderCode).distinct().collect(Collectors.toList()));
            List<ShipmentOrderDetailDTO> shipmentOrderDetailDTOList = remoteShipmentOrderClient.getDetailList(shipmentOrderDetailParam);
            if (CollectionUtils.isEmpty(shipmentOrderDetailDTOList)) {
                throw new BaseException(BaseBizEnum.TIP, "出库单未找到");
            }
            //出库单日志
            List<ShipmentOrderLogDTO> shipmentOrderLogDTOS = new ArrayList<>();
            //交接单号
            HandoverParam handoverParam = new HandoverParam();
            handoverParam.setHandoverCode(param.getHandoverCode());
            HandoverDTO handover = remoteHandoverClient.get(handoverParam);
            if (ObjectUtils.isEmpty(handover)) {
                throw new BaseException(WmsHandoverBizEnum.HANDOVER_HANDOVER_NOT_EXITS, param.getHandoverCode());
            }
            checkHandoverStatus(handover);
            //校验容器
            checkContOccupy(handover.getHandoverCode(), handover.getContCode());
            //包裹日志
            List<PackageLogDTO> packageLogDTOS = new ArrayList<>();
            List<HandoverDetailDTO> handoverDetailDTOList = new ArrayList<>();
            buildPackLogAndHandOverDetail(packageLogDTOS, handoverDetailDTOList, packageDTOList, handover, packageDTO.getRealWeight());


            //拦截单触发-不拦截（返回拦截的出库单号）
            List<String> interceptPackCodeList = interceptionManagerBiz.queryInterceptionPackTipOperation(pickDetailDTOList.stream().map(PickDetailDTO::getPackageCode).collect(Collectors.toList()));
            if (!CollectionUtils.isEmpty(interceptPackCodeList)) {
                Map<String, Object> map = new HashMap<>();
                map.put("num", interceptPackCodeList.size());
                List<String> interceptExpressNoList = pickDetailDTOList.stream().filter(a -> interceptPackCodeList.contains(a.getPackageCode())).map(PickDetailDTO::getExpressNo).collect(Collectors.toList());
                map.put("interceptionExpressNoList", interceptExpressNoList);
                throw new BaseException(JSONUtil.toJsonStr(map), BaseBizEnum.TIP_HANDLE_TWO, "包裹拦截明细");
            }
            //修改包裹信息
            packageDTOList.forEach(a -> {
                if (a.getIsPre().equalsIgnoreCase(PackEnum.TYPE.PRE.getCode())) {
                    BigDecimal qty = packageDetailDTOList.stream().filter(a1 -> a1.getIsPre().equalsIgnoreCase(PackIsPreEnum.PRE.getCode()) || a1.getIsPre().equalsIgnoreCase(PackIsPreEnum.LAST.getCode())).filter(a1 -> a.getPackageCode().equals(a1.getPackageCode())).map(PackageDetailDTO::getCheckQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                    a.setOutSkuQty(qty);
                } else {
                    BigDecimal qty = packageDetailDTOList.stream().filter(a1 -> a1.getIsPre().equalsIgnoreCase(PackIsPreEnum.NORMAL.getCode())).filter(a1 -> a.getPackageCode().equals(a1.getPackageCode())).map(PackageDetailDTO::getCheckQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                    a.setOutSkuQty(qty);
                }
                a.setRealWeight(packageDTO.getRealWeight());
                a.setActualPackNum(packageDTO.getActualPackNum());
                a.setActualPackUpc(packageDTO.getActualPackUpc());
                a.setWeight(packageDTO.getWeight());
                a.setVolume(packageDTO.getVolume());
                remotePackageClient.handlePackageVolume(a);

                a.setStatus(PackEnum.STATUS.PREPARE_HANDLER_STATUS.getCode());
                a.setOutStockDate(System.currentTimeMillis());
            });
            packageDetailDTOList.forEach(a -> {
                a.setStatus(PackEnum.STATUS.PREPARE_HANDLER_STATUS.getCode());
                a.setOutStockQty(a.getCheckQty());
            });
            buildModifyShipmentAndSaveLog(shipmentOrderDTOList, shipmentOrderDetailDTOList, shipmentOrderLogDTOS, packageDTOList, packageDetailDTOList);
            //组装参数
            BatchOutBoundBillBO batchOutBoundBO = new BatchOutBoundBillBO();
            handover.setPackageQty(handover.getPackageQty() + packageDTOList.size());
            batchOutBoundBO.setHandover(handover);
            batchOutBoundBO.setHandoverDetailList(handoverDetailDTOList);
            batchOutBoundBO.setPackList(packageDTOList);
            batchOutBoundBO.setPackageDetailList(packageDetailDTOList);
            batchOutBoundBO.setShipmentOrderDetailList(shipmentOrderDetailDTOList);
            batchOutBoundBO.setShipmentOrderList(shipmentOrderDTOList);
            batchOutBoundBO.setShipmentOrderLogList(shipmentOrderLogDTOS);
            batchOutBoundBO.setPackageLogList(packageLogDTOS);

            //提交数据
            List<SystemEventDTO> systemEventDTOS = batchOutBoundBO.getPackList().stream().map(it -> {
                SystemEventDTO systemEventDTO = buildSystemEventDTO(it);
                return systemEventDTO;
            }).collect(Collectors.toList());
            batchOutBoundBO.setSystemEventDTOList(systemEventDTOS);
            //mq发送消息记录
            List<MessageMqDTO> messageMqDTOList = new ArrayList<>();
            for (PackageDTO pack : packageDTOList) {
                ShipmentOrderDTO shipmentOrder = shipmentOrderDTOList.stream().filter(a -> a.getShipmentOrderCode().equalsIgnoreCase(pack.getShipmentOrderCode())).findFirst().orElse(null);
                OperationTypeEnum operationTypeEnum;
                if (shipmentOrder.getOrderType().equalsIgnoreCase(ShipmentOrderEnum.ORDER_TYPE.CONSIGNMENT_OUT_STOCK_TYPE.getCode())) {
                    operationTypeEnum = OperationTypeEnum.OPERATION_CIRCLE_GOODS_STOCK;
                } else if (shipmentOrder.getOrderType().equalsIgnoreCase(ShipmentOrderEnum.ORDER_TYPE.SALE_OUT_STOCK_TYPE.getCode())) {
                    operationTypeEnum = OperationTypeEnum.OPERATION_CIRCLE_GOODS_SALE_STOCK;
                } else {
                    operationTypeEnum = OperationTypeEnum.OPERATION_STOCK;
                }
                MessageMqDTO messageMqDTO = buildMessageMqDTO(pack, operationTypeEnum, BillTypeEnum.BILL_TYPE_PACKAGE);
                messageMqDTOList.add(messageMqDTO);
            }
            batchOutBoundBO.setMessageMqDTOList(messageMqDTOList);

            Boolean result = remoteBillContextClient.handoverBatchOutBound(batchOutBoundBO);
            if (!result) {
                throw new BaseException(WmsHandoverBizEnum.HANDOVER_PACKAGE_WEIGHT_ERROR);
            }
            //发送消息--扣减库存
            try {
                for (PackageDTO pack : packageDTOList) {
                    StockOperationMessage stockOperationMessage = new StockOperationMessage();
                    stockOperationMessage.setWarehouseCode(pack.getWarehouseCode());
                    stockOperationMessage.setBillNo(pack.getPackageCode());
                    ShipmentOrderDTO shipmentOrder = shipmentOrderDTOList.stream().filter(a -> a.getShipmentOrderCode().equalsIgnoreCase(pack.getShipmentOrderCode())).findFirst().orElse(null);
                    if (shipmentOrder.getOrderType().equalsIgnoreCase(ShipmentOrderEnum.ORDER_TYPE.CONSIGNMENT_OUT_STOCK_TYPE.getCode())) {
                        stockOperationMessage.setOperationType(OperationTypeEnum.OPERATION_CIRCLE_GOODS_STOCK.getType());
                    } else if (shipmentOrder.getOrderType().equalsIgnoreCase(ShipmentOrderEnum.ORDER_TYPE.SALE_OUT_STOCK_TYPE.getCode())) {
                        stockOperationMessage.setOperationType(OperationTypeEnum.OPERATION_CIRCLE_GOODS_SALE_STOCK.getType());
                    } else {
                        stockOperationMessage.setOperationType(OperationTypeEnum.OPERATION_STOCK.getType());
                    }
                    stockOperationMessage.setCargoCode(pack.getCargoCode());
                    stockOperationMessage.setBillType(BillTypeEnum.BILL_TYPE_PACKAGE.getType());
                    remoteMessageClient.sendStockOperationMessageWithNoTX(stockOperationMessage);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        } catch (Exception e) {
            log.error("批量出库异常", e);
            if (!CollectionUtils.isEmpty(packageDTOList)) {
                List<PackageLogDTO> list = new ArrayList<>();
                packageDTOList.forEach(it -> {
                    PackageLogDTO packageLogDTO = new PackageLogDTO();
                    packageLogDTO.setOpDate(System.currentTimeMillis());
                    packageLogDTO.setCargoCode(it.getCargoCode());
                    packageLogDTO.setPackageCode(it.getPackageCode());
                    packageLogDTO.setWarehouseCode(it.getWarehouseCode());
                    packageLogDTO.setOpBy(CurrentUserHolder.getUserName());
                    packageLogDTO.setOpContent(String.format("包裹:%s批量出库异常,异常原因: %s", it.getPackageCode(), e.getMessage()));
                    list.add(packageLogDTO);
                });
                remotePackageClient.savePackLogList(list);
            }
            throw e;
        } finally {
            if (tryLock) {
                lock.unlock();
            }
        }
        return Result.success(true);
    }

    @Override
    public Result<String> wcsOutBound(WcsHandoverBizParam param) throws Exception {
        log.info("wcsOutBound param:{} userName:{}", JSONUtil.toJsonStr(param), CurrentUserHolder.getUserName());
        if (StringUtils.isEmpty(param) || StringUtils.isEmpty(param.getCarrierCode()) || StringUtils.isEmpty(param.getPackageCode()) || StringUtils.isEmpty(param.getRealWeight()) ) {
            throw new BaseException(BaseBizEnum.TIP, "参数不能为空");
        }
        if (param.getRealWeight().compareTo(BigDecimal.ZERO) <= 0) {
            throw new BaseException(BaseBizEnum.TIP, "重量必须大于0");
        }
        // 处理重量单位
        param.setRealWeight(param.getRealWeight().divide(BigDecimal.valueOf(1000), 3, RoundingMode.FLOOR));
        //过滤拦截单
        RLock lock = redissonClient.getLock("dt_wms_out_bound_lock:" + param.getPackageCode());
        Boolean tryLock = false;

        try {
            tryLock = lock.tryLock(1, 15, TimeUnit.SECONDS);
            if (!tryLock) {
                throw new BaseException(BaseBizEnum.TIP, "正在出库,请等待确认");
            }
            //检查包裹
            PackageDTO pack = checkWcsPack(param);
            String executeDest = String.valueOf(param.getExecuteDest());
            // 保存分拣口信息
            addExtraJson(pack, "dest", String.valueOf(param.getDest()));
            addExtraJson(pack, "executeDest", String.valueOf(param.getExecuteDest()));

            //已出库
            if (pack.getStatus().equalsIgnoreCase(PackEnum.STATUS.PREPARE_HANDLER_STATUS.getCode())) {
                return Result.success("包裹-已出库");
            }
            List<String> cancelStatusList = Arrays.asList(PackEnum.STATUS.PART_ASSIGN_STATUS.getCode(), PackEnum.STATUS.PART_ASSIGN_CANCEL_STATUS.getCode());
            if (cancelStatusList.contains(pack.getStatus())) {
                String warmMsg = String.format("包裹【拦截】需要检查是否被快递拉走【仓库:%s,运单号:%s,分拣口:%s】", pack.getWarehouseCode(), pack.getExpressNo(), param.getExecuteDest());
                WechatUtil.sendMessage(warmMsg, urlConfig.getWcsInterceptPackageOutUrls());
//                throw new BaseException(BaseBizEnum.TIP, String.format("运单号%s已拦截", param.getExpressNo()));
                return Result.fail(-222, String.format("运单号%s已拦截,端口", param.getExpressNo()), null);
            }
            if (!pack.getStatus().equalsIgnoreCase(PackEnum.STATUS.CHECK_COMPELETE_STATUS.getCode())) {
                throw new BaseException(BaseBizEnum.TIP, String.format("运单号%s未找到复核完成的包裹", param.getExpressNo()));
            }
            //查找交接单
            HandoverDTO handover = checkWcsHandover(pack, executeDest);
            //校验包裹是否是批量复核
            checkPackageInspection(pack, CommonConstantUtil.WMS_WCS_WEIGHT);
            //查询包裹明细
            PackageDetailParam packageDetailParam = new PackageDetailParam();
            packageDetailParam.setPackageCode(pack.getPackageCode());
            List<PackageDetailDTO> packageDetailList = remotePackageDetailClient.getList(packageDetailParam);
            //包裹
            pack.setStatus(PackEnum.STATUS.PREPARE_HANDLER_STATUS.getCode());
            pack.setOutStockDate(System.currentTimeMillis());
            if (pack.getIsPre().equalsIgnoreCase(PackEnum.TYPE.NORMAL.getCode())) {
                pack.setOutSkuQty(packageDetailList.stream().filter(a -> a.getIsPre().equalsIgnoreCase(PackIsPreEnum.NORMAL.getCode())).map(PackageDetailDTO::getCheckQty).reduce(BigDecimal.ZERO, BigDecimal::add));
            } else {
                pack.setOutSkuQty(packageDetailList.stream().filter(a -> a.getIsPre().equalsIgnoreCase(PackIsPreEnum.PRE.getCode()) || a.getIsPre().equalsIgnoreCase(PackIsPreEnum.LAST.getCode())).map(PackageDetailDTO::getCheckQty).reduce(BigDecimal.ZERO, BigDecimal::add));
            }
            packageDetailList.stream().forEach(entity -> {
                entity.setOutStockQty(entity.getCheckQty());
                entity.setStatus(PackEnum.STATUS.PREPARE_HANDLER_STATUS.getCode());
            });
            //查询出库单明细
            ShipmentOrderParam shipmentOrderParam = new ShipmentOrderParam();
            shipmentOrderParam.setShipmentOrderCode(pack.getShipmentOrderCode());
            ShipmentOrderDTO shipmentOrder = remoteShipmentOrderClient.get(shipmentOrderParam);
            if (ObjectUtils.isEmpty(shipmentOrder)) {
                throw new BaseException(WmsHandoverBizEnum.HANDOVER_SHIPMENT_NOT_EXITS, shipmentOrder.getShipmentOrderCode());
            }
            if (Objects.equals(shipmentOrder.getStatus(), ShipmentOrderEnum.STATUS.OUT_STOCK_STATUS.getCode())) {
//                throw new BaseException(BaseBizEnum.TIP, String.format("出库单:%s已出库,请核查！！！", shipmentOrder.getShipmentOrderCode()));
                return Result.success("出库单-已出库");
            }
            //10 记录部分包裹出库  30 最后包裹出库
            String saveShipmentOrderLog = "00";
            if (Objects.equals(shipmentOrder.getStatus(), ShipmentOrderEnum.STATUS.CHECK_COMPLETE_STATUS.getCode())) {
                saveShipmentOrderLog = "10";
            }
            List<ShipmentOrderDetailDTO> shipmentOrderDetailList = remoteShipmentOrderClient.getDetailList(shipmentOrder.getShipmentOrderCode());

            shipmentOrder.setOutPackageQty(shipmentOrder.getOutPackageQty() + 1);
            shipmentOrder.setStatus(ShipmentOrderEnum.STATUS.PART_OUT_STOCK_STATUS.getCode());
            if (shipmentOrder.getFirstPackOutStockDate().equals(0L)) {
                shipmentOrder.setFirstPackOutStockDate(System.currentTimeMillis());
            }
            //回写出库单明细
            shipmentOrderDetailList.stream().forEach(entity -> {
                BigDecimal packageDetailQty = packageDetailList.stream().filter(a -> a.getIsPre().equalsIgnoreCase(PackIsPreEnum.NORMAL.getCode())).filter(a -> a.getPUid().equals(entity.getId())).map(PackageDetailDTO::getCheckQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                if (!ObjectUtils.isEmpty(packageDetailQty)) {
                    entity.setOutStockQty(entity.getOutStockQty().add(packageDetailQty));
                    //TODO add 2021-04-12 出库数量不能大于计划数量
                    if (entity.getOutStockQty().compareTo(entity.getExpSkuQty()) > 0) {
                        throw new BaseException(BaseBizEnum.TIP, String.format("出库单:%s商品:%s出库数量不能大于计划数量", entity.getShipmentOrderCode(), entity.getSkuCode()));
                    }
                    entity.setStatus(ShipmentOrderEnum.STATUS.PART_OUT_STOCK_STATUS.getCode());
                    if (entity.getOutStockQty().compareTo(entity.getExpSkuQty()) == 0) {
                        entity.setStatus(ShipmentOrderEnum.STATUS.OUT_STOCK_STATUS.getCode());
                    }
                }
            });
            shipmentOrder.setOutSkuQty(shipmentOrderDetailList.stream().map(ShipmentOrderDetailDTO::getOutStockQty).reduce(BigDecimal.ZERO, BigDecimal::add));
            //全部出库
            List<ShipmentOrderDetailDTO> shipmentOrderDetailCheckCompleteList = shipmentOrderDetailList.stream().filter(a -> a.getStatus().equals(ShipmentOrderEnum.STATUS.OUT_STOCK_STATUS.getCode())).collect(Collectors.toList());
            if (shipmentOrderDetailCheckCompleteList.size() == shipmentOrderDetailList.size()) {
                shipmentOrder.setStatus(ShipmentOrderEnum.STATUS.OUT_STOCK_STATUS.getCode());
                shipmentOrder.setOutStockDate(System.currentTimeMillis());
                saveShipmentOrderLog = "30";
            }
            //交接明细
            HandoverDetailDTO handoverDetail = new HandoverDetailDTO();
            handoverDetail.setWarehouseCode(pack.getWarehouseCode());
            handoverDetail.setCargoCode(pack.getCargoCode());
            handoverDetail.setPoNo(pack.getPoNo());
            handoverDetail.setSoNo(pack.getSoNo());
            handoverDetail.setExpressNo(pack.getExpressNo());
            handoverDetail.setLineSeq(remoteHandoverClient.getMaxLineSeq(handover.getHandoverCode()) + 1);
            handoverDetail.setShipmentOrderCode(pack.getShipmentOrderCode());
            handoverDetail.setPackageCode(pack.getPackageCode());
            handoverDetail.setHandoverCode(handover.getHandoverCode());
            handoverDetail.setCarrierCode(handover.getCarrierCode());

            if (!ObjectUtils.isEmpty(param.getRealWeight()) && param.getRealWeight().compareTo(BigDecimal.ZERO) > 0) {
                pack.setRealWeight(param.getRealWeight());
                handoverDetail.setPackageWeight(param.getRealWeight());
            } else {
                handoverDetail.setPackageWeight(pack.getRealWeight());
            }
            //校验出库单重量
            if (ObjectUtils.isEmpty(handoverDetail.getPackageWeight()) || handoverDetail.getPackageWeight().compareTo(BigDecimal.ZERO) <= 0) {
                throw new BaseException(BaseBizEnum.TIP, String.format("包裹：%s重量不正确！", pack.getPackageCode()));
            }
            //包裹理论重量、实际重量误差校验 自动化流水不校验重量
            if (!Objects.equals(shipmentOrder.getBusinessType(), ShipmentOrderEnum.BUSSINESS_TYPE.B2B.toString())) {
                try {
                    iCheckPackWeightAndVolumeBiz.checkPackageWeightAndVolumeNew(pack, packageDetailList, CommonConstantUtil.WMS_WCS_WEIGHT);
                } catch (Exception e) {
                    e.printStackTrace();
                    return Result.fail(-222, String.format("运单号%s称重异常", param.getExpressNo()), null);
                }
            }
            PackageLogDTO packageLog = new PackageLogDTO();
            packageLog.setCargoCode(pack.getCargoCode());
            packageLog.setPackageCode(pack.getPackageCode());
            packageLog.setWarehouseCode(pack.getWarehouseCode());
            packageLog.setOpBy(CurrentUserHolder.getUserName());
            packageLog.setOpDate(System.currentTimeMillis());
            packageLog.setOpContent(String.format("(wcs)称重:%s 包裹,出库成功", pack.getPackageCode()));

            ShipmentOrderLogDTO shipmentOrderLog = new ShipmentOrderLogDTO();
            shipmentOrderLog.setCargoCode(pack.getCargoCode());
            shipmentOrderLog.setShipmentOrderCode(pack.getShipmentOrderCode());
            shipmentOrderLog.setWarehouseCode(pack.getWarehouseCode());
            shipmentOrderLog.setOpBy(CurrentUserHolder.getUserName());
            shipmentOrderLog.setOpDate(System.currentTimeMillis());
            if (saveShipmentOrderLog.equals("10")) {
                shipmentOrderLog.setOpContent(String.format("(wcs)出库单部分出库成功,包裹单号:%s", pack.getPackageCode()));
            }
            if (saveShipmentOrderLog.equals("30")) {
                shipmentOrderLog.setOpContent(String.format("(wcs)出库单出库成功,包裹单号:%s", pack.getPackageCode()));
            }
            //事务信息提交
            WeighCommitBillBO weighCommit = new WeighCommitBillBO();
            weighCommit.setShipmentOrder(shipmentOrder);
            weighCommit.setShipmentOrderDetailList(shipmentOrderDetailList);
            weighCommit.setPack(pack);
            weighCommit.setPackageDetailList(packageDetailList);
            if (Objects.equals(handover.getStatus(), HandoverEnum.statusEnum.CREATE_STATUS.getCode())) {
                handover.setStatus(HandoverEnum.statusEnum.DOING_STATUS.getCode());
            }
            weighCommit.setHandover(handover);
            weighCommit.setHandoverDetail(handoverDetail);
            weighCommit.setPackageLog(packageLog);
            if (saveShipmentOrderLog.equals("10") || saveShipmentOrderLog.equals("30")) {
                weighCommit.setShipmentOrderLog(shipmentOrderLog);
            }
            if (shipmentOrder.getBusinessType().equals(ShipmentOrderEnum.BUSSINESS_TYPE.B2C.toString())) {
                OutSourceCodeParam outSourceCodeParam = new OutSourceCodeParam();
                outSourceCodeParam.setPackageCode(pack.getPackageCode());
                outSourceCodeParam.setShipmentOrderCode(pack.getShipmentOrderCode());
                List<OutSourceCodeDTO> outSourceCodeDTOList = remoteOutSourceCodeClient.getList(outSourceCodeParam);
                if (!CollectionUtils.isEmpty(outSourceCodeDTOList)) {
                    outSourceCodeDTOList.forEach(a -> a.setOutStockDate(System.currentTimeMillis()));
                    weighCommit.setOutSourceCodeDTOList(outSourceCodeDTOList);
                }
            }
            //拦截出库单
//            try {
//                interceptionManagerBiz.interception(PackReturnEnum.INTERCEPT_STATUS.WEIGH_HAND_POINT.getCode(), pack.getPackageCode(), "", null, null);
//            } catch (Exception e) {
//                e.printStackTrace();
//                WechatUtil.sendMessage(StrUtil.join(StrUtil.COLON, pack.getWarehouseCode(), pack.getPackageCode(), "包裹拦截需要检查是否被快递拉走"), urlConfig.getWcsInterceptPackageOutUrls());
////                throw new BaseException(BaseBizEnum.TIP, String.format("运单号%s已拦截", param.getExpressNo()));
//                return Result.fail(-222, String.format("运单号%s已拦截", param.getExpressNo()), null);
//            }
            List<String> tipOperation = interceptionManagerBiz.queryInterceptionPackTipOperation(Arrays.asList(pack.getPackageCode()));
            if (!CollectionUtils.isEmpty(tipOperation)) {
                String warmMsg = String.format("包裹【拦截】需要检查是否被快递拉走【仓库:%s,运单号:%s,分拣口:%s】", pack.getWarehouseCode(), pack.getExpressNo(), param.getExecuteDest());
                WechatUtil.sendMessage(warmMsg, urlConfig.getWcsInterceptPackageOutUrls());
                return Result.fail(-222, String.format("运单号%s已拦截", param.getExpressNo()), null);
            }
//            //人效消息
//            SystemEventDTO systemEventDTO = buildSystemEventDTO(pack);
//            weighCommit.setSystemEventDTO(systemEventDTO);
            //mq发送消息记录
            OperationTypeEnum operationTypeEnum;
            if (shipmentOrder.getOrderType().equalsIgnoreCase(ShipmentOrderEnum.ORDER_TYPE.CONSIGNMENT_OUT_STOCK_TYPE.getCode())) {
                operationTypeEnum = OperationTypeEnum.OPERATION_CIRCLE_GOODS_STOCK;
            } else if (shipmentOrder.getOrderType().equalsIgnoreCase(ShipmentOrderEnum.ORDER_TYPE.SALE_OUT_STOCK_TYPE.getCode())) {
                operationTypeEnum = OperationTypeEnum.OPERATION_CIRCLE_GOODS_SALE_STOCK;
            } else {
                operationTypeEnum = OperationTypeEnum.OPERATION_STOCK;
            }
            MessageMqDTO messageMqDTO = buildMessageMqDTO(pack, operationTypeEnum, BillTypeEnum.BILL_TYPE_PACKAGE);
            weighCommit.setMessageMqDTO(messageMqDTO);
            //提交数据
            Boolean result = remoteBillContextClient.handoverWeightCommit(weighCommit);
            if (!result) {
                throw new BaseException(WmsHandoverBizEnum.HANDOVER_PACKAGE_WEIGHT_ERROR);
            }
            //人效消息
            //发送消息-扣减库存
            StockOperationMessage stockOperationMessage = new StockOperationMessage();
            stockOperationMessage.setWarehouseCode(pack.getWarehouseCode());
            stockOperationMessage.setBillNo(pack.getPackageCode());
            stockOperationMessage.setOperationType(operationTypeEnum.getType());
            stockOperationMessage.setCargoCode(pack.getCargoCode());
            stockOperationMessage.setBillType(BillTypeEnum.BILL_TYPE_PACKAGE.getType());
            remoteMessageClient.sendStockOperationMessageWithNoTX(stockOperationMessage);
            String cacheKey = CurrentRouteHolder.getWarehouseCode() + ":" + handover.getHandoverCode();
            //交接单计数
            if (stringRedisTemplate.hasKey(cacheKey)) {
                stringRedisTemplate.opsForValue().increment(cacheKey, 1);
            } else {
                if (!StringUtils.isEmpty(handover.getId())) {
                    Integer count = remoteHandoverDetailClient.getCount(handover.getHandoverCode());
                    stringRedisTemplate.opsForValue().increment(cacheKey, count);
                } else {
                    stringRedisTemplate.opsForValue().increment(cacheKey, 1);
                }
                stringRedisTemplate.expire(cacheKey, 1, TimeUnit.DAYS);
            }
        } catch (Exception e) {
            throw e;
        } finally {
            if (tryLock) {
                lock.unlock();
            }
        }

        return Result.success("出库成功");
    }

    /**
     * @param pack
     * @return com.dt.domain.bill.dto.HandoverDTO
     * @author: WuXian
     * description:
     * create time: 2022/4/18 11:16
     */
    private HandoverDTO checkWcsHandover(PackageDTO pack, String executeDest) throws Exception {
        //查询交接中和创建的自动化交接单
        HandoverParam handoverParam = new HandoverParam();
        handoverParam.setCarrierCode(pack.getCarrierCode());
        handoverParam.setExitCode(executeDest);
        handoverParam.setAutomationType(HandoverEnum.AutomationTypeEnum.AUTOMATION.getCode());
        handoverParam.setStatusList(Arrays.asList(HandoverEnum.statusEnum.CREATE_STATUS.getCode(), HandoverEnum.statusEnum.DOING_STATUS.getCode()));
        List<HandoverDTO> handoverDTOList = remoteHandoverClient.getList(handoverParam);
        if (CollectionUtils.isEmpty(handoverDTOList) || handoverDTOList.stream().noneMatch(a -> a.getExpressBranch().equalsIgnoreCase(pack.getExpressBranch()))) {
            HandoverDTO createHandover = buildWcsHandoverDTO(pack, executeDest);
            return createHandover;
        }
        HandoverDTO handoverDTO = handoverDTOList.stream().filter(a -> a.getExpressBranch().equalsIgnoreCase(pack.getExpressBranch())).findFirst().orElse(null);
        if (handoverDTO == null) {
            HandoverDTO createHandover = buildWcsHandoverDTO(pack, executeDest);
            return createHandover;
        }
        if (StringUtils.isEmpty(handoverDTO.getId())) {
            return handoverDTO;
        }
        String cacheKey = CurrentRouteHolder.getWarehouseCode() + ":" + handoverDTO.getHandoverCode();
        Integer packCount = 0;
        if (stringRedisTemplate.hasKey(cacheKey)) {
            String count = stringRedisTemplate.opsForValue().get(CurrentRouteHolder.getWarehouseCode() + ":" + handoverDTO.getHandoverCode());
            packCount = Integer.valueOf(count);
        } else {
            packCount = remoteHandoverDetailClient.getCount(handoverDTO.getHandoverCode());
        }
        //获取自动化设备最大的交接上限
        Integer maxNum = getWcsMaxNum(pack, executeDest);
        if (packCount >= maxNum) {
            //结束上一单
            //交接单状态修改
            handoverDTO.setHandoverTime(System.currentTimeMillis());
            handoverDTO.setStatus(HandoverEnum.statusEnum.COMPLETE_STATUS.getCode());
            handoverDTO.setPackageQty(packCount);
            remoteHandoverClient.modify(handoverDTO);
            //创建新的单据
            HandoverDTO createHandover = buildWcsHandoverDTO(pack, executeDest);
            return createHandover;
        }
        handoverDTO.setPackageQty(handoverDTO.getPackageQty() + 1);
        return handoverDTO;
    }

    /**
     * @param pack
     * @return java.lang.Integer
     * @author: WuXian
     * description:
     * create time: 2022/4/29 10:30
     */
    private Integer getWcsMaxNum(PackageDTO pack, String executeDest) {
        ExitConfigParam exitConfigParam = new ExitConfigParam();
        exitConfigParam.setCarrierCode(pack.getCarrierCode());
        List<ExitConfigDTO> exitConfigDTOList = remoteExitConfigClient.getList(exitConfigParam);
        if (CollectionUtils.isEmpty(exitConfigDTOList)) {
//            throw new BaseException(BaseBizEnum.TIP, String.format("%s快递未找到自动化配置", pack.getCarrierCode()));
            return 200;
        }
        exitConfigDTOList = exitConfigDTOList.stream().filter(a -> ObjectUtil.equal(a.getExpressBranch(), pack.getExpressBranch())).filter(a -> ObjectUtil.equal(a.getExitCode() + "", executeDest)).collect(Collectors.toList());
        //找不到,默认上限200
        if (CollectionUtils.isEmpty(exitConfigDTOList)) {
//            throw new BaseException(BaseBizEnum.TIP, String.format("%s网点未找到自动化配置", pack.getExpressBranch()));
            return 200;
        }
        Integer maxNum = exitConfigDTOList.get(0).getMaxPackage();
        return maxNum;
    }

    /**
     * @param pack
     * @return com.dt.domain.bill.dto.HandoverDTO
     * @author: WuXian
     * description:  创建交接单
     * create time: 2022/4/18 13:43
     */
    private HandoverDTO buildWcsHandoverDTO(PackageDTO pack, String executeDest) throws Exception {
        //过滤拦截单
        RLock lock = redissonClient.getLock("dt_wms_build_handover_lock:" + String.format("%s%s%s", pack.getWarehouseCode(), pack.getCarrierCode(), pack.getExpressBranch()));
        Boolean tryLock = false;
        try {
            tryLock = lock.tryLock(0, 15, TimeUnit.SECONDS);
            if (!tryLock) {
                throw new BaseException(BaseBizEnum.TIP, "正在创建交接单,请重试");
            }
            HandoverDTO createHandover = new HandoverDTO();
            createHandover.setWarehouseCode(pack.getWarehouseCode());
            createHandover.setContCode("");
            createHandover.setHandoverCode(iRemoteSeqRuleClient.findSequence(SeqEnum.HANDOVER_CODE_000001));
            createHandover.setCarrierCode(pack.getCarrierCode());
            createHandover.setPackageQty(1);
            createHandover.setExitCode(executeDest);
            createHandover.setAutomationType(HandoverEnum.AutomationTypeEnum.AUTOMATION.getCode());
            createHandover.setStatus(HandoverEnum.statusEnum.DOING_STATUS.getCode());
            createHandover.setPrintStatus(HandoverEnum.PrintStatusEnum.CREATE_STATUS.getCode());
            createHandover.setExpressBranchName(pack.getExpressBranchName());
            createHandover.setExpressBranch(pack.getExpressBranch());
            return createHandover;
        } catch (Exception e) {
            throw e;
        } finally {
            if (tryLock) {
                lock.unlock();
            }
        }
    }

    /**
     * @param param
     * @return void
     * @author: WuXian
     * description:
     * create time: 2022/4/18 11:10
     */
    private PackageDTO checkWcsPack(WcsHandoverBizParam param) {
        PackageParam packageParam = new PackageParam();
        packageParam.setPackageCode(param.getPackageCode());
        packageParam.setExpressNo(param.getExpressNo());
        PackageDTO pack = remotePackageClient.get(packageParam);
        if (ObjectUtils.isEmpty(pack)) {
            throw new BaseException(BaseBizEnum.TIP, String.format("运单号%s未找到包裹", param.getExpressNo()));
        }
        if (!Objects.equals(pack.getBusinessType(), ShipmentOrderEnum.BUSSINESS_TYPE.B2C.toString())) {
            throw new BaseException(BaseBizEnum.TIP, String.format("运单号%s非C单,不允许使用自动化流水线", param.getExpressNo()));
        }
        return pack;
    }

    /**
     * 组装出库单
     *
     * @param shipmentOrderDTOList
     * @param shipmentOrderDetailDTOList
     * @param shipmentOrderLogDTOS
     * @param packageDTOList
     * @param packageDetailDTOList
     */
    private void buildModifyShipmentAndSaveLog(List<ShipmentOrderDTO> shipmentOrderDTOList, List<ShipmentOrderDetailDTO> shipmentOrderDetailDTOList, List<ShipmentOrderLogDTO> shipmentOrderLogDTOS, List<PackageDTO> packageDTOList, List<PackageDetailDTO> packageDetailDTOList) {
        for (ShipmentOrderDTO entity : shipmentOrderDTOList) {
            List<PackageDTO> packageDTOS = packageDTOList.stream().filter(a -> Objects.equals(a.getShipmentOrderCode(), entity.getShipmentOrderCode())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(packageDTOS)) {
                throw new BaseException(BaseBizEnum.TIP, String.format("出库单%s找不到包裹", entity.getShipmentOrderCode()));
            }
            List<String> packList = packageDTOS.stream().map(PackageDTO::getPackageCode).distinct().collect(Collectors.toList());
            List<PackageDetailDTO> packageDetailDTOS = packageDetailDTOList.stream().filter(a -> packList.contains(a.getPackageCode())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(packageDetailDTOS)) {
                throw new BaseException(BaseBizEnum.TIP, String.format("出库单%s找不到包裹", entity.getShipmentOrderCode()));
            }
            if (CollectionUtils.isEmpty(shipmentOrderDetailDTOList.stream().filter(a -> Objects.equals(a.getShipmentOrderCode(), entity.getShipmentOrderCode())).collect(Collectors.toList()))) {
                throw new BaseException(BaseBizEnum.TIP, String.format("出库单%s找不到明细", entity.getShipmentOrderCode()));
            }
            String saveLog = "10";

            //修改出库单明细
            shipmentOrderDetailDTOList.stream().forEach(sto -> {
                BigDecimal qty = packageDetailDTOS.stream().filter(a -> a.getIsPre().equalsIgnoreCase(PackIsPreEnum.NORMAL.getCode())).filter(a -> packList.contains(a.getPackageCode())).filter(a -> a.getPUid().equals(sto.getId())).map(PackageDetailDTO::getCheckQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                if (qty.compareTo(BigDecimal.ZERO) > 0) {
                    sto.setOutStockQty(sto.getOutStockQty().add(qty));
                    //TODO add 2021-04-12 出库数量不能大于计划数量
                    if (sto.getOutStockQty().compareTo(sto.getExpSkuQty()) > 0) {
                        throw new BaseException(BaseBizEnum.TIP, String.format("出库单:%s商品:%s出库数量不能大于计划数量", sto.getShipmentOrderCode(), sto.getSkuCode()));
                    }
                    sto.setStatus(ShipmentOrderEnum.STATUS.PART_OUT_STOCK_STATUS.getCode());
                    if (sto.getOutStockQty().compareTo(sto.getExpSkuQty()) == 0) {
                        sto.setStatus(ShipmentOrderEnum.STATUS.OUT_STOCK_STATUS.getCode());
                    }
                }
            });
            if (entity.getStatus().equals(ShipmentOrderEnum.STATUS.CHECK_COMPLETE_STATUS.getCode())) {
                entity.setFirstPackOutStockDate(System.currentTimeMillis());
                entity.setStatus(ShipmentOrderEnum.STATUS.PART_OUT_STOCK_STATUS.getCode());
            }
            if (shipmentOrderDetailDTOList.stream().filter(a -> Objects.equals(a.getShipmentOrderCode(), entity.getShipmentOrderCode())).allMatch(a -> a.getStatus().equals(ShipmentOrderEnum.STATUS.OUT_STOCK_STATUS.getCode()))) {
                entity.setStatus(ShipmentOrderEnum.STATUS.OUT_STOCK_STATUS.getCode());
                entity.setOutStockDate(System.currentTimeMillis());
                saveLog = "30";
            }
            BigDecimal qty = shipmentOrderDetailDTOList.stream().filter(a -> Objects.equals(a.getShipmentOrderCode(), entity.getShipmentOrderCode())).map(ShipmentOrderDetailDTO::getOutStockQty).reduce(BigDecimal.ZERO, BigDecimal::add);
            entity.setOutSkuQty(entity.getOutSkuQty().add(qty));
            entity.setOutPackageQty(entity.getOutPackageQty() + packageDTOS.size());

            ShipmentOrderLogDTO shipmentOrderLogDTO = new ShipmentOrderLogDTO();
            shipmentOrderLogDTO.setOpDate(System.currentTimeMillis());
            shipmentOrderLogDTO.setCargoCode(entity.getCargoCode());
            shipmentOrderLogDTO.setWarehouseCode(entity.getWarehouseCode());
            shipmentOrderLogDTO.setShipmentOrderCode(entity.getShipmentOrderCode());
            shipmentOrderLogDTO.setOpBy(CurrentUserHolder.getUserName());
            if (saveLog.equals("30")) {
                shipmentOrderLogDTO.setOpContent(String.format("订单已出库,单号:%s", entity.getShipmentOrderCode()));
            } else {
                shipmentOrderLogDTO.setOpContent(String.format("订单部分出库,单号:%s", entity.getShipmentOrderCode()));
            }
            shipmentOrderLogDTOS.add(shipmentOrderLogDTO);
        }
    }

    /**
     * 组装包裹日志和交接单明细
     *
     * @param packageLogDTOS
     * @param handoverDetailDTOList
     * @param packageDTOList
     * @param handover
     */
    private void buildPackLogAndHandOverDetail(List<PackageLogDTO> packageLogDTOS, List<HandoverDetailDTO> handoverDetailDTOList, List<PackageDTO> packageDTOList, HandoverDTO handover, BigDecimal realWeight) {
        for (PackageDTO pack : packageDTOList) {
            //交接明细
            HandoverDetailDTO handoverDetail = new HandoverDetailDTO();
            handoverDetail.setWarehouseCode(pack.getWarehouseCode());
            handoverDetail.setCargoCode(pack.getCargoCode());
            handoverDetail.setPoNo(pack.getPoNo());
            handoverDetail.setSoNo(pack.getSoNo());
            handoverDetail.setExpressNo(pack.getExpressNo());
            handoverDetail.setLineSeq(remoteHandoverClient.getMaxLineSeq(handover.getHandoverCode()) + 1);
            handoverDetail.setShipmentOrderCode(pack.getShipmentOrderCode());
            handoverDetail.setPackageCode(pack.getPackageCode());
            handoverDetail.setHandoverCode(handover.getHandoverCode());
            handoverDetail.setCarrierCode(handover.getCarrierCode());
            handoverDetail.setPackageWeight(realWeight);
            handoverDetailDTOList.add(handoverDetail);

            PackageLogDTO packageLogDTO = new PackageLogDTO();
            packageLogDTO.setOpDate(System.currentTimeMillis());
            packageLogDTO.setCargoCode(pack.getCargoCode());
            packageLogDTO.setPackageCode(pack.getPackageCode());
            packageLogDTO.setWarehouseCode(pack.getWarehouseCode());
            packageLogDTO.setOpBy(CurrentUserHolder.getUserName());
            packageLogDTO.setOpContent(String.format("称重:%s 包裹,出库成功", pack.getPackageCode()));
            packageLogDTOS.add(packageLogDTO);
        }
    }


    /**
     * 批量加锁
     * dt_wms_intercept_lock
     *
     * @param warehouseCode
     * @param shipmentCodeList
     */
    private RedissonMultiLock multiLock(String warehouseCode, List<String> shipmentCodeList) {
        List<RLock> lockList = shipmentCodeList.stream().sorted(Comparator.comparing(String::new)).flatMap(a -> {
            String shipmentOrderLockKey = String.format(CommonConstantUtil.INTERCEPT_PREFIX + "%s%s", warehouseCode, a);
            RLock rLock = redissonClient.getLock(shipmentOrderLockKey);
            return Stream.of(rLock);
        }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(lockList)) {
            throw new BaseException(BaseBizEnum.TIP, "未获取到汇单锁");
        }
        RLock[] locks = new RLock[lockList.size()];
        return new RedissonMultiLock(lockList.toArray(locks));
    }

    private void checkPackageWeightAndVolume(PackageDTO pack, List<PackageDetailDTO> packageDetailList) {
        //包裹理论重量、实际重量误差校验
        CargoConfigDTO cargoConfig = remoteCargoConfigClient.queryByCargoCodeAndpropKey(pack.getWarehouseCode(), pack.getCargoCode(), CargoConfigParamEnum.ALLOW_CHECK_WEIGHT.getCode());
        CargoConfigDTO cargoConfigLimit = remoteCargoConfigClient.queryByCargoCodeAndpropKey(pack.getWarehouseCode(), pack.getCargoCode(), CargoConfigParamEnum.ALLOW_CHECK_WEIGHT_PERCENT.getCode());
        Boolean checkFlag = ObjectUtils.isEmpty(cargoConfig) || ObjectUtils.isEmpty(cargoConfigLimit) || CarrierStatusEnum.DISABLE.getValue().equals(cargoConfig.getStatus()) || CarrierStatusEnum.DISABLE.getValue().equals(cargoConfigLimit.getStatus());
        BigDecimal mixLimit = checkFlag ? new BigDecimal("0.9") : new BigDecimal("100").subtract(new BigDecimal(cargoConfigLimit.getPropValue())).divide(new BigDecimal("100"));
        BigDecimal maxLimit = checkFlag ? new BigDecimal("1.1") : new BigDecimal("100").add(new BigDecimal(cargoConfigLimit.getPropValue())).divide(new BigDecimal("100"));
        if (ObjectUtils.isEmpty(cargoConfig) || cargoConfig.getPropValue().equals("1")) {
            BigDecimal totalActualPackWeight = pack.getActualPackWeight().multiply(new BigDecimal(pack.getActualPackNum()));

            if (pack.getRealWeight().subtract(totalActualPackWeight).compareTo(pack.getWeight().multiply(mixLimit)) <= 0 || pack.getRealWeight().subtract(totalActualPackWeight).compareTo(pack.getWeight().multiply(maxLimit)) >= 0) {
                //校验不通过重新计算包裹重量
                calculatePackageWeightAndVolume(pack, packageDetailList);
            }
            //重新判断是否重量满足出库误差判断
            if (pack.getRealWeight().subtract(totalActualPackWeight).compareTo(pack.getWeight().multiply(mixLimit)) <= 0 || pack.getRealWeight().subtract(totalActualPackWeight).compareTo(pack.getWeight().multiply(maxLimit)) >= 0) {
                throw new BaseException(BaseBizEnum.TIP, String.format("包裹：%s重量误差检验不通过！", pack.getPackageCode()));
            }
        }
    }

    private void calculatePackageWeightAndVolume(PackageDTO pack, List<PackageDetailDTO> packageDetailList) {
        //包裹重量计算
        List<String> skuCodeList = packageDetailList.stream().flatMap(a -> Stream.of(a.getSkuCode())).distinct().collect(Collectors.toList());
        SkuParam skuParam = new SkuParam();
        skuParam.setCodeList(skuCodeList);
        List<SkuDTO> skuList = remoteSkuClient.getList(skuParam);

        //包裹理论重量信息
        pack.setWeight(packageDetailList.stream().flatMap(a -> {
            SkuDTO sku = skuList.stream().filter(b -> b.getCode().equals(a.getSkuCode())).findAny().get();
            return Stream.of(a.getSkuQty().multiply(sku.getGrossWeight()));
        }).reduce(BigDecimal.ZERO, BigDecimal::add));

        //包裹理论体积信息
        pack.setVolume(packageDetailList.stream().flatMap(a -> {
            SkuDTO sku = skuList.stream().filter(b -> b.getCode().equals(a.getSkuCode())).findAny().get();
            BigDecimal _bigDecimal = a.getSkuQty().multiply(sku.getLength().multiply(sku.getWidth()).multiply(sku.getHeight()));
            if (_bigDecimal.compareTo(BigDecimal.ZERO) <= 0) {
                _bigDecimal = sku.getVolume();
            }
            return Stream.of(_bigDecimal == null ? BigDecimal.ZERO : _bigDecimal);
        }).reduce(BigDecimal.ZERO, BigDecimal::add));
        remotePackageClient.handlePackageVolume(pack);

    }

    private SystemEventDTO buildSystemEventDTO(PackageDTO packageDTO) {
        SystemEventDTO systemEventDTO = new SystemEventDTO();
        systemEventDTO.setType(SystemEventEnum.WEIGHING.getCode());
        systemEventDTO.setWarehouseCode(packageDTO.getWarehouseCode());
        systemEventDTO.setCargoCode(packageDTO.getCargoCode());
        systemEventDTO.setWorker(CurrentUserHolder.getUserName());
        systemEventDTO.setWorkTime(System.currentTimeMillis());
        systemEventDTO.setWorkDate(DateUtil.parse(DateUtil.date(System.currentTimeMillis()).toDateStr()).getTime());
        systemEventDTO.setBillNo(packageDTO.getPackageCode());
        return systemEventDTO;
    }

    public static void addExtraJson(PackageDTO packageDTO, String key, String value) {
        if (StrUtil.isBlank(packageDTO.getExtraJson())) {
            packageDTO.setExtraJson("{}");
        }
        JSONObject jsonObject = JSONUtil.parseObj(packageDTO.getExtraJson());
        jsonObject.set(key, value);
        packageDTO.setExtraJson(jsonObject.toString());
    }
}
