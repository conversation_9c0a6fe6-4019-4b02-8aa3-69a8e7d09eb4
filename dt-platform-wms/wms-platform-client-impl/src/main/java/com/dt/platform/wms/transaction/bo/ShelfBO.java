package com.dt.platform.wms.transaction.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="上架完成容器明细DTO", description="上架完成容器")
public class ShelfBO extends AbsWarehouseBO  implements java.io.Serializable  {

    @ApiModelProperty("交易日期")
    private Long tradeDate;

    @ApiModelProperty(value = "上架单号")
    private String shelfCode;

    @ApiModelProperty("上架类型")
    private String shelfType;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    @ApiModelProperty(value = "商品属性")
    private String skuQuality;

    @ApiModelProperty(value = "上架操作明细")
    private List<ShelfDetailBO> detailList;

}