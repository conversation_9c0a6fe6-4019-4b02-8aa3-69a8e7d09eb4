package com.dt.platform.wms.client;

import com.alibaba.excel.EasyExcelFactory;
import com.danding.park.client.ParkClient;
import com.danding.park.client.core.load.dto.LoadTaskDTO;
import com.danding.park.client.core.load.dto.LoadTaskInfoDTO;
import com.danding.park.client.core.load.form.LoadTaskCreateForm;
import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.enums.excel.ExcelImportEnum;
import com.dt.component.common.exceptions.BaseException;
import com.dt.component.common.holder.CurrentUserHolder;
import com.dt.component.common.result.Result;
import com.dt.platform.utils.SpringUtil;
import com.dt.platform.wms.biz.excel.DefaultReadEventListener;
import com.dt.platform.wms.integration.IRemoteOssClient;
import com.dt.platform.wms.param.excel.ExcelImportParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.InputStream;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Slf4j
@DubboService(version = "${dubbo.service.version}")
public class ExcelImportBizClient implements IExcelImportBizClient {

    @Resource
    private IRemoteOssClient remoteOssClient;

    @Override
    public Result<LoadTaskDTO> syncExcelImport(ExcelImportParam param) {
        try {

            ExcelImportEnum excelImportEnum = ExcelImportEnum.getEnum(param.getFuncCode());

            LoadTaskCreateForm loadTaskCreateForm = new LoadTaskCreateForm();
            loadTaskCreateForm.setFuncCode(excelImportEnum.getFuncCode());
            loadTaskCreateForm.setName(excelImportEnum.getFuncName());
            loadTaskCreateForm.setMasterUserId(CurrentUserHolder.getMasterUserId());
            loadTaskCreateForm.setUserId(CurrentUserHolder.getUserId());

            LoadTaskInfoDTO loadTaskInfoDTO = new LoadTaskInfoDTO();
            loadTaskInfoDTO.setUrl(param.getUrl());
            // 表头开始行: 默认0
            loadTaskInfoDTO.setHeaderRow(1);
            // 数据开始行：默认 表头行+1
            loadTaskInfoDTO.setDataStartRow(2);
            // 记录总数量
            loadTaskInfoDTO.setTotal(0);
            DefaultReadEventListener eventListener = SpringUtil.getBean(excelImportEnum.getListenerBeanName());
            // 添加表头信息
            loadTaskInfoDTO.setHeader(eventListener.getParkHeader());

            // 任务详情xwj123
            loadTaskCreateForm.setTaskInfo(loadTaskInfoDTO);
            LoadTaskDTO loadTaskDTO = ParkClient.loadClient().create(loadTaskCreateForm);

            if (ObjectUtils.isEmpty(eventListener) || ObjectUtils.isEmpty(loadTaskDTO)) {
                throw new BaseException(BaseBizEnum.TIP, "获取实例异常");
            }

            ExecutorService executor = Executors.newFixedThreadPool(1);
            CompletableFuture
                    .supplyAsync(() -> {
                        //执行导入逻辑
                        try (InputStream inputStream = remoteOssClient.downLoadExcel(param.getUrl())) {
                            //设置任务ID
                            eventListener.setUid(loadTaskDTO.getUid());
                            //设置文件流
                            eventListener.setExcel(inputStream);
                            //设置当前主账号下所有用户名
                            if(!CollectionUtils.isEmpty(param.getUserNames())) {
                                eventListener.setUserNames(param.getUserNames());
                            }
                            eventListener.setExportUserId(param.getExportUserId());
                            eventListener.setExportUserName(param.getExportUserName());
                            //设置url
                            if(!StringUtils.isEmpty(param.getUrl())){
                                eventListener.setUrl(param.getUrl());
                            }
                            //设置模板  code
                            eventListener.setFuncCode(param.getFuncCode());
                            EasyExcelFactory
                                    .read(inputStream, eventListener.supportClass(), eventListener)
                                    .autoTrim(true)
                                    .autoCloseStream(true)
//                                .excelType(ExcelTypeEnum.XLS)
                                    .headRowNumber(2)
                                    .sheet(0).doReadSync();

                        } catch (Exception e) {
                            log.error("导入文件解析异常", e);
                            eventListener.callBackPublicService(false, loadTaskDTO.getUid(), 0, null, "文件解析异常");
                        } finally {
                            log.info("关闭导入任务开始：{}", System.currentTimeMillis());
                            //更新任务完成
                            ParkClient.loadClient().finishByUid(loadTaskDTO.getUid());
                            log.info("关闭导入任务结束：{}", System.currentTimeMillis());
                        }
                        return eventListener;
                    }, executor);
            executor.shutdown();
            return Result.success(loadTaskDTO);
        } catch (Exception e) {
            throw new BaseException(BaseBizEnum.TIP, e.getMessage());
        }
    }
}
