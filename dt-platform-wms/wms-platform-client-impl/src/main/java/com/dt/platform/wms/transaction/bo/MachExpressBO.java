package com.dt.platform.wms.transaction.bo;

import com.dt.domain.bill.dto.AbnormalOrderDTO;
import com.dt.domain.bill.dto.PackageDTO;
import com.dt.domain.bill.dto.ShipmentOrderDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "MachExpressBO", description = "快递面单")
public class MachExpressBO extends AbsWarehouseBO  implements java.io.Serializable  {

    @ApiModelProperty("出库单")
    private ShipmentOrderDTO shipmentOrder;

    @ApiModelProperty("异常单")
    private List<AbnormalOrderDTO> abnormalOrderDTOList;

    @ApiModelProperty("包裹")
    private List<PackageDTO> packageDTOList;
}