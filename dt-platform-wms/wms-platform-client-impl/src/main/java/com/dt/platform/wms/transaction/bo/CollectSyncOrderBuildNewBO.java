package com.dt.platform.wms.transaction.bo;

import com.dt.domain.bill.dto.*;
import com.dt.platform.wms.biz.stock.biz.bo.AllocationStockBO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/12/1 17:05
 */
@Data
public class CollectSyncOrderBuildNewBO implements Serializable {

    @ApiModelProperty("需要提交的包裹数据")
    List<PackageDTO> commitPackage = new ArrayList<>();

    @ApiModelProperty("需要提交的拣选单数据")
    PickDTO commitPick;

    @ApiModelProperty("需要提交的出库单数据")
    List<ShipmentOrderDTO> commitShipment = new ArrayList<>();

    @ApiModelProperty("需要提交的分配单数据")
    List<AllocationOrderDTO> commitAllocation = new ArrayList<>();

    @ApiModelProperty("三级库存扣减")
    List<AllocationStockBO> allocationStockBOList = new ArrayList<>();

    @ApiModelProperty("库区")
    List<String> zoneList = new ArrayList<>();


}
