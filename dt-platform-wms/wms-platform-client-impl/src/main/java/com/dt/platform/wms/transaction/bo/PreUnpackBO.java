package com.dt.platform.wms.transaction.bo;

import com.dt.domain.bill.dto.*;
import lombok.Data;

import java.util.List;

/**
 * 前置拆单BO
 * Created by nobody on 2021/6/9 9:44
 */
@Data
public class PreUnpackBO extends AbsWarehouseBO  implements java.io.Serializable  {
    private ShipmentOrderDTO shipmentOrderDTO;
    private AbnormalOrderDTO abnormalOrderDTO;
    private List<PackageDTO> packageDTOList;
    private List<PackageDetailDTO> packageDetailDTOList;
    private List<PackageLogDTO> packageLogDTOList;
}