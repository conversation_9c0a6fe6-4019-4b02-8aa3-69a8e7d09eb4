package com.dt.platform.wms.client;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.enums.PrintTypeEnum;
import com.dt.component.common.enums.bill.PrintAgainTypeEnum;
import com.dt.component.common.enums.bill.ShipmentOrderEnum;
import com.dt.component.common.enums.pick.PickEnum;
import com.dt.component.common.enums.pkg.PackEnum;
import com.dt.component.common.exceptions.BaseException;
import com.dt.component.common.result.Result;
import com.dt.domain.base.dto.CargoOwnerDTO;
import com.dt.domain.base.dto.CarrierDTO;
import com.dt.domain.base.dto.SalePlatformDTO;
import com.dt.domain.base.param.CargoOwnerParam;
import com.dt.domain.base.param.CarrierParam;
import com.dt.domain.bill.dto.*;
import com.dt.domain.bill.param.*;
import com.dt.domain.base.param.SalePlatformParam;
import com.dt.domain.bill.dto.BillPrintAgainDTO;
import com.dt.domain.bill.dto.PackageCheckDTO;
import com.dt.domain.bill.dto.PickDTO;
import com.dt.domain.bill.dto.PickDetailDTO;
import com.dt.platform.utils.ConverterUtil;
import com.dt.platform.utils.LambdaHelpUtils;
import com.dt.platform.wms.dto.print.AllBoxAgainBizBillDTO;
import com.dt.platform.wms.dto.print.BillPrintAgainBizDTO;
import com.dt.platform.wms.integration.*;
import com.dt.platform.wms.param.pick.PickOrderBizParam;
import com.dt.platform.wms.param.shipment.AllBoxBizParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2021/1/7 17:12
 */
@DubboService(version = "${dubbo.service.version}")
@Slf4j
public class BillPrintAgainClientImpl implements IBillPrintAgainClient {

    @Resource
    IRemotePickClient iRemotePickClient;

    @Resource
    IRemotePickDetailClient remotePickDetailClient;

    @Resource
    IRemotePackageCheckClient iRemotePackageCheckClient;

    @Resource
    IRemoteCargoOwnerClient iRemoteCargoOwnerClient;

    @Resource
    IRemoteCarrierClient iRemoteCarrierClient;

    @Resource
    IRemoteShipmentOrderClient iRemoteShipmentOrderClient;

    @Resource
    IRemotePackageClient iRemotePackageClient;

    @Resource
    IRemoteSalePlatform remoteSalePlatform;

    @Override
    public Result<IPage<BillPrintAgainBizDTO>> getBillPrintAgainPage(PickOrderBizParam param) {
        //快递参数拿掉
        param.setCarrierCodeList(new ArrayList<String>());
        param.setCarrierCode(null);
        if (PrintAgainTypeEnum.getEnum(param.getPrintAgainType()) == null) {
            throw new BaseException(BaseBizEnum.TIP, "补打类型参数异常");
        }
        //处理查询参数 pickCode  packageCode expressNo 只支持单个查询
        buildParam(param);
        //B单查询箱号
        List<String> tempCodeList = new ArrayList<>();
        if (!StringUtils.isEmpty(param.getBoxNo())) {
            PackageCheckParam packageCheckParam = ConverterUtil.convert(param, PackageCheckParam.class);
            packageCheckParam.setBusinessType(ShipmentOrderEnum.BUSSINESS_TYPE.B2B.toString());
            List<PackageCheckDTO> packageCheckDTOList = iRemotePackageCheckClient.getPackageCheckListAppointColumn(packageCheckParam, Arrays.asList("pick_code", "package_code"));
            if (CollectionUtils.isEmpty(packageCheckDTOList)) {
                return Result.success(new Page<>());
            }

            tempCodeList.addAll(packageCheckDTOList.stream().map(PackageCheckDTO::getPickCode).distinct().collect(Collectors.toList()));
            if (!CollectionUtils.isEmpty(param.getPickCodeList())) {
                param.getPickCodeList().retainAll(tempCodeList);
                if (CollectionUtils.isEmpty(param.getPickCodeList())) {
                    return Result.success(new Page<>());
                }
            } else {
                param.setPickCodeList(tempCodeList);
            }
            tempCodeList = new ArrayList<>();
            tempCodeList.addAll(packageCheckDTOList.stream().map(PackageCheckDTO::getPackageCode).distinct().collect(Collectors.toList()));
            if (!CollectionUtils.isEmpty(param.getPackageCodeList())) {
                tempCodeList.retainAll(param.getPackageCodeList());
                if (CollectionUtils.isEmpty(param.getPackageCodeList())) {
                    return Result.success(new Page<>());
                }
            } else {
                param.setPackageCodeList(tempCodeList);
            }
            param.setExpressNoList(null);
        }

        //查询包裹明细
        if (!CollectionUtils.isEmpty(param.getPackageCodeList()) || !CollectionUtils.isEmpty(param.getExpressNoList()) || !CollectionUtils.isEmpty(param.getCarrierCodeList()) || !CollectionUtils.isEmpty(param.getPickCodeList())) {
            PickDetailParam pickDetailParam = new PickDetailParam();
            if (!CollectionUtils.isEmpty(param.getExpressNoList())) {
                pickDetailParam.setExpressNoList(param.getExpressNoList());
            }
            if (!CollectionUtils.isEmpty(param.getPackageCodeList())) {
                pickDetailParam.setPackageCodeList(param.getPackageCodeList());
            }
            if (!CollectionUtils.isEmpty(param.getPickCodeList())) {
                pickDetailParam.setPickCodeList(param.getPickCodeList());
            }
            //过滤掉拦截，取消，拦截取消
            pickDetailParam.setPackageStatusList(Stream.of(PackEnum.STATUS.values())
                    .filter(s -> !s.equals(PackEnum.STATUS.PART_ASSIGN_COMPLETE_STATUS))
                    .filter(s -> !s.equals(PackEnum.STATUS.PART_ASSIGN_STATUS))
                    .filter(s -> !s.equals(PackEnum.STATUS.PART_ASSIGN_CANCEL_STATUS)).map(s -> s.getCode()).collect(Collectors.toList()));
            List<PickDetailDTO> pickDetailDTOList = remotePickDetailClient.getPickDetailListAppointColumn(pickDetailParam, Arrays.asList("pick_code", "package_code"));
            if (CollectionUtils.isEmpty(pickDetailDTOList)) {
                return Result.success(new Page<>());
            }
            //过滤聚合子单据号
            PickParam pickParam = new PickParam();
            pickParam.setPickCodeList(pickDetailDTOList.stream().map(PickDetailDTO::getPickCode).distinct().collect(Collectors.toList()));
            pickParam.setPickFlagList(Arrays.asList(PickEnum.PickFlagEnum.MERGE.getCode(), PickEnum.PickFlagEnum.ORIGIN.getCode()));
            List<PickDTO> pickDTOList = iRemotePickClient.getPickListAppointColumn(pickParam, LambdaHelpUtils.convertToFieldNameList(PickDTO::getPickCode));
            if (CollectionUtils.isEmpty(pickDTOList)) {
                return Result.success(new Page<>());
            }
            tempCodeList = new ArrayList<>();
            tempCodeList.addAll(pickDTOList.stream().map(PickDTO::getPickCode).distinct().collect(Collectors.toList()));
            if (!CollectionUtils.isEmpty(param.getPickCodeList())) {
                tempCodeList.retainAll(param.getPickCodeList());
                if (CollectionUtils.isEmpty(tempCodeList)) {
                    return Result.success(new Page<>());
                } else {
                    param.setPickCodeList(tempCodeList);
                }
            }else{
                param.setPickCodeList(tempCodeList);
            }
        }
        //查询拣选单
        PickParam pickParam = ConverterUtil.convert(param, PickParam.class);

        //过滤掉拦截，取消，拦截取消
        pickParam.setPackageStatusList(Stream.of(PackEnum.STATUS.values())
                .filter(s -> !s.equals(PackEnum.STATUS.PART_ASSIGN_COMPLETE_STATUS))
                .filter(s -> !s.equals(PackEnum.STATUS.PART_ASSIGN_STATUS))
                .filter(s -> !s.equals(PackEnum.STATUS.PART_ASSIGN_CANCEL_STATUS)).map(s -> s.getCode()).collect(Collectors.toList()));
        Page<BillPrintAgainDTO> dtoPage = iRemotePickClient.getBillPrintAgainPage(pickParam);
        IPage<BillPrintAgainBizDTO> page = ConverterUtil.convertPage(dtoPage, BillPrintAgainBizDTO.class);
        List<BillPrintAgainDTO> againDTOList = dtoPage.getRecords();
        if (!CollectionUtils.isEmpty(againDTOList)) {
            List<BillPrintAgainBizDTO> list = new ArrayList<>();
            pickParam.setPickCodeList(againDTOList.stream().map(BillPrintAgainDTO::getPickCode).distinct().collect(Collectors.toList()));
            List<PickDTO> pickDTOList = iRemotePickClient.getAgainList(pickParam);
            if (CollectionUtils.isEmpty(pickDTOList)) {
                return Result.success(new Page<>());
            }
            //取所有货主
            CargoOwnerParam cargoOwnerParam = new CargoOwnerParam();
            cargoOwnerParam.setCodeList(againDTOList.stream().map(BillPrintAgainDTO::getCargoCode).distinct().collect(Collectors.toList()));
            List<CargoOwnerDTO> cargoOwnerDTOList = iRemoteCargoOwnerClient.queryList(cargoOwnerParam);

            CarrierParam carrierParam = new CarrierParam();
            carrierParam.setCodeList(pickDTOList.stream().map(PickDTO::getCarrierCode).distinct().collect(Collectors.toList()));
            List<CarrierDTO> carrierDTOList = iRemoteCarrierClient.getList(carrierParam);

            PickParam pickParam1 = new PickParam();
            pickParam1.setPickCodeList(againDTOList.stream().map(BillPrintAgainDTO::getPickCode).distinct().collect(Collectors.toList()));
            List<PickDTO> pickClientAgainList = iRemotePickClient.getList(pickParam1);
            if (CollectionUtils.isEmpty(pickClientAgainList)) {
                pickClientAgainList = new ArrayList<>();
            }
            SalePlatformParam salePlatformParam = new SalePlatformParam();
            salePlatformParam.setCodeList(pickClientAgainList.stream().map(PickDTO::getSalePlatform).distinct().collect(Collectors.toList()));
            List<SalePlatformDTO> salePlatformDTOList = remoteSalePlatform.getList(salePlatformParam);
            if (CollectionUtils.isEmpty(salePlatformDTOList)) {
                salePlatformDTOList = new ArrayList<>();
            }

            for (BillPrintAgainDTO entity : againDTOList) {
                BillPrintAgainBizDTO bizDTO = ConverterUtil.convert(entity, BillPrintAgainBizDTO.class);
                bizDTO.setCargoName(cargoOwnerDTOList.stream().filter(a -> a.getCode().equals(entity.getCargoCode())).findFirst().get().getName());
                bizDTO.setCarrierCode(pickDTOList.stream().filter(a -> a.getPickCode().equals(entity.getPickCode())).findFirst().get().getCarrierCode());
                bizDTO.setCarrierName(carrierDTOList.stream().filter(a -> a.getCode().equals(bizDTO.getCarrierCode())).findFirst().get().getName());
                bizDTO.setCreatedTime(ConverterUtil.convertVoTime(entity.getCreatedTime()));
                if (!CollectionUtils.isEmpty(pickClientAgainList)) {
                    String salePlatform = pickClientAgainList.stream()
                            .filter(a -> a.getPickCode().equals(entity.getPickCode())).map(PickDTO::getSalePlatform)
                            .findFirst().orElse("");
                    if (StringUtils.isEmpty(salePlatform)) {
                        bizDTO.setSalePlatform("");
                        bizDTO.setSalePlatformName("");
                    } else {
                        bizDTO.setSalePlatform(salePlatform);
                        String salePlatformName = salePlatformDTOList.stream().filter(a -> a.getCode().equals(salePlatform))
                                .map(SalePlatformDTO::getName).findFirst().orElse("");
                        bizDTO.setSalePlatformName(salePlatformName);
                    }
                }
                PackageParam packageParam = new PackageParam();
                packageParam.setPackageCode(entity.getPackageCode());
                PackageDTO packageDTO = iRemotePackageClient.get(packageParam);
                if (packageDTO != null) {
                    bizDTO.setBoxNo(packageDTO.getBoxNo());
                }
                bizDTO.setPackageStatusName(PackEnum.STATUS.findEnumDesc(packageDTO.getStatus()).getDesc());
                list.add(bizDTO);
            }
            page.setRecords(list);
        }
        return Result.success(page);
    }

    @Override
    public Result<IPage<AllBoxAgainBizBillDTO>> getAllBoxBillPrintAgainPage(AllBoxBizParam param) {

        AllBoxParam boxParam = ConverterUtil.convert(param, AllBoxParam.class);
        PickParam pickParam = new PickParam();
        //过滤掉取消的拣选单
        pickParam.setStatusList(Stream.of(PickEnum.PickStatusEnum.values()).filter(s -> !s.equals(PickEnum.PickStatusEnum.CANCEL_STATUS)).map(s -> s.getCode()).collect(Collectors.toList()));
        pickParam.setPrintAgainType(PrintAgainTypeEnum.ALL_BOX_BILL_TYPE.getType());
        List<PickDTO> pickDTOList = iRemotePickClient.getAgainList(pickParam);
        if (CollectionUtils.isEmpty(pickDTOList)) {
            return Result.success(new Page<>());
        }
        pickParam = new PickParam();
        pickParam.setPickCodeList(pickDTOList.stream().map(s -> s.getPickCode()).distinct().collect(Collectors.toList()));
        List<PickDetailDTO> detailDTOList = iRemotePickClient.getPickDetailList(pickParam);
        if (CollectionUtils.isEmpty(detailDTOList)) {
            return Result.success(new Page<>());
        }
        if (CollectionUtils.isEmpty(boxParam.getShipmentOrderCodeList())) {
            boxParam.setShipmentOrderCodeList(detailDTOList.stream().map(s -> s.getShipmentOrderCode()).distinct().collect(Collectors.toList()));
        } else if (!CollectionUtils.isEmpty(boxParam.getShipmentOrderCodeList())) {
            ArrayList tempCodeList = new ArrayList<>();
            tempCodeList.addAll(detailDTOList.stream().map(s -> s.getShipmentOrderCode()).distinct().collect(Collectors.toList()));
            tempCodeList.retainAll(param.getShipmentOrderCodeList());
            if (CollectionUtils.isEmpty(tempCodeList)) {
                return Result.success(new Page<>());
            } else {
                boxParam.setShipmentOrderCodeList(tempCodeList);
            }
        }

        Page<ShipmentOrderDTO> dtoPage = iRemoteShipmentOrderClient.getAgainPage(boxParam);
        IPage<AllBoxAgainBizBillDTO> page = ConverterUtil.convertPage(dtoPage, AllBoxAgainBizBillDTO.class);
        List<ShipmentOrderDTO> againDTOList = dtoPage.getRecords();
        if (!CollectionUtils.isEmpty(againDTOList)) {
            List<AllBoxAgainBizBillDTO> list = new ArrayList<>();
            CargoOwnerParam cargoOwnerParam = new CargoOwnerParam();
            cargoOwnerParam.setCodeList(againDTOList.stream().map(ShipmentOrderDTO::getCargoCode).distinct().collect(Collectors.toList()));
            List<CargoOwnerDTO> cargoOwnerDTOList = iRemoteCargoOwnerClient.queryList(cargoOwnerParam);
            CarrierParam carrierParam = new CarrierParam();
            carrierParam.setCodeList(againDTOList.stream().map(ShipmentOrderDTO::getCarrierCode).distinct().collect(Collectors.toList()));
            List<CarrierDTO> carrierDTOList = iRemoteCarrierClient.getList(carrierParam);
            for (ShipmentOrderDTO entity : againDTOList) {
                AllBoxAgainBizBillDTO bizDTO = ConverterUtil.convert(entity, AllBoxAgainBizBillDTO.class);
                Optional<CargoOwnerDTO> optional = cargoOwnerDTOList.stream().filter(a -> a.getCode().equals(entity.getCargoCode())).findFirst();
                if (optional.isPresent()) {
                    bizDTO.setCargoName(optional.get().getName());
                } else {
                    bizDTO.setCargoName("");
                }
                Optional<CarrierDTO> optionalCarrierDTO = carrierDTOList.stream().filter(a -> a.getCode().equals(entity.getCarrierCode())).findFirst();
                if (optionalCarrierDTO.isPresent()) {
                    bizDTO.setCarrierName(optionalCarrierDTO.get().getName());
                } else {
                    bizDTO.setCarrierName("");
                }
                bizDTO.setCreatedTime(ConverterUtil.convertVoTime(entity.getCreatedTime()));
                bizDTO.setStatusName(ShipmentOrderEnum.STATUS.findOrderStatus(entity.getStatus()).getDesc());
                list.add(bizDTO);
            }
            page.setRecords(list);
        }
        return Result.success(page);
    }

    /**
     * 将单个改成List pickCode  packageCode expressNo,carrierCode
     *
     * @param param
     */
    private void buildParam(PickOrderBizParam param) {
        if (!StringUtils.isEmpty(param.getPickCode())) {
            param.setPickCodeList(Arrays.asList(param.getPickCode()));
            param.setPickCode(null);
        }

        if (!StringUtils.isEmpty(param.getPackageCode())) {
            param.setPackageCodeList(Arrays.asList(param.getPackageCode()));
            param.setPackageCode(null);
        }
        if (!StringUtils.isEmpty(param.getExpressNo())) {
            param.setExpressNoList(Arrays.asList(param.getExpressNo()));
            param.setExpressNo(null);
        }
        if (!StringUtils.isEmpty(param.getCarrierCode())) {
            param.setCarrierCodeList(Arrays.asList(param.getCarrierCode()));
            param.setCarrierCode(null);
        }
    }
}
