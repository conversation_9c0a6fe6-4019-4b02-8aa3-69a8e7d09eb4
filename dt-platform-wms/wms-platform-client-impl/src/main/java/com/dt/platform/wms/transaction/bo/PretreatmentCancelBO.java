package com.dt.platform.wms.transaction.bo;

import com.dt.domain.bill.dto.*;
import com.dt.domain.core.stock.dto.StockTransactionDTO;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by nobody on 2021/6/8 17:55
 */
@Data
public class PretreatmentCancelBO extends AbsWarehouseBO  implements java.io.Serializable  {
    private ShipmentOrderDTO shipmentOrderDTO;
    private List<ShipmentOrderDetailDTO> shipmentOrderDetailDTOList =  new ArrayList<>();
    private ShipmentOrderLogDTO shipmentOrderLogDTO;
    private List<PackageDTO> packageDTOList = new ArrayList<>();
    private List<PackageDetailDTO> packageDetailDTOList = new ArrayList<>();
    private List<PackageLogDTO> packageLogDTOList = new ArrayList<>();
    private List<AbnormalOrderDTO> abnormalOrderDTOList = new ArrayList<>();
    private List<ReplenishTaskDTO> replenishTaskDTOList = new ArrayList<>();
    private List<StockTransactionDTO> stockTransactionDTOList = new ArrayList<>();
}