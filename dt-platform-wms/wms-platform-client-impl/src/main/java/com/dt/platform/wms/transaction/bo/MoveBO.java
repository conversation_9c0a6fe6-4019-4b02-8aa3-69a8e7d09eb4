package com.dt.platform.wms.transaction.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="移位单操作DTO", description="移位操作")
public class MoveBO extends AbsWarehouseBO  implements java.io.Serializable  {

    @ApiModelProperty("交易日期")
    private Long tradeDate;

    @ApiModelProperty(value = "移库单号")
    private String moveCode;

    @ApiModelProperty(value = "操作方式")
    private String opType;

    @ApiModelProperty(value = "商品属性")
    private String skuQuality;

    @ApiModelProperty(value = "移位单明细")
    private List<MoveDetailBO> moveDetailList;

}