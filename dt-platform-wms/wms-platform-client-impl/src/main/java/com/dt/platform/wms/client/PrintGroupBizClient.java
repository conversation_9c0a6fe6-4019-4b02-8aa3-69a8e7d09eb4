package com.dt.platform.wms.client;

import cn.hutool.core.util.StrUtil;
import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.enums.PrintDesensitizeEnum;
import com.dt.component.common.enums.PrintTypeEnum;
import com.dt.component.common.enums.bill.ShipmentOrderEnum;
import com.dt.component.common.enums.cargo.CargoConfigParamEnum;
import com.dt.component.common.enums.cargo.CargoConfigStatusEnum;
import com.dt.component.common.enums.pick.PickEnum;
import com.dt.component.common.enums.pick.PickRulePrintGroupEnum;
import com.dt.component.common.enums.pkg.PackEnum;
import com.dt.component.common.enums.pkg.PackIsPreEnum;
import com.dt.component.common.enums.wms.WmsPrintBizEnum;
import com.dt.component.common.exceptions.BaseException;
import com.dt.component.common.exceptions.WmsBizException;
import com.dt.component.common.holder.CurrentRouteHolder;
import com.dt.component.common.holder.CurrentUserHolder;
import com.dt.component.common.result.Result;
import com.dt.component.uid.utils.UidUtils;
import com.dt.domain.base.client.ISalePlatformClient;
import com.dt.domain.base.dto.*;
import com.dt.domain.base.param.*;
import com.dt.domain.bill.dto.*;
import com.dt.domain.bill.param.*;
import com.dt.platform.utils.ConverterUtil;
import com.dt.platform.wms.biz.IBusinessLogBiz;
import com.dt.platform.wms.biz.IPickGuideBiz;
import com.dt.platform.wms.biz.IShipmentOrderDecryptBiz;
import com.dt.platform.wms.biz.config.WmsOtherConfig;
import com.dt.platform.wms.dto.pick.PickGuideBizDTO;
import com.dt.platform.wms.dto.print.PrintBoxBillDTO;
import com.dt.platform.wms.dto.print.PrintPickDTO;
import com.dt.platform.wms.dto.print.PrintWaybillDTO;
import com.dt.platform.wms.dto.print.PrintWrapInfo;
import com.dt.platform.wms.integration.*;
import com.dt.platform.wms.param.CodeListParam;
import com.dt.platform.wms.param.pkg.PrintWaybillBizParam;
import com.google.common.collect.Lists;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@DubboService(version = "${dubbo.service.version}", timeout = 60000)
@Deprecated
public class PrintGroupBizClient implements IPrintGroupBizClient {

    @Resource
    private IRemoteSkuClient remoteSkuClient;

    @Resource
    IRemoteCargoConfigClient remoteCargoConfigClient;

    @Resource
    private IRemoteCargoOwnerClient remoteCargoOwnerClient;

    @Resource
    private IRemoteWarehouseClient remoteWarehouseClient;

    @Resource
    private IRemoteShipmentOrderClient remoteShipmentOrderClient;

    @Resource
    private IRemotePackageClient remotePackageClient;

    @Resource
    private IRemotePackageWaybillClient remotePackageWaybillClient;

    @Resource
    private IRemotePackageDetailClient remotePackageDetailClient;

    @Resource
    private IRemoteCarrierClient remoteCarrierClient;

    @Resource
    private IRemoteAllocationOrderClient remoteAllocationOrderClient;

    @Resource
    private IRemoteDecimalPlaceClient remoteDecimalPlaceClient;

    @Resource
    private IRemotePickClient remotePickClient;

    @Resource
    private IRemotePackageCheckClient remotePackageCheckClient;

    @Resource
    IBusinessLogBiz iBusinessLogBiz;

    @Resource
    private WmsOtherConfig wmsOtherConfig;

    @Resource
    private IRemoteAreaClient remoteAreaClient;

    @Resource
    private ISalePlatformClient salePlatformClient;

    @Resource
    private IPickGuideBiz pickGuideBiz;

    @Resource
    private IShipmentOrderDecryptBiz shipmentOrderDecryptBiz;


    @Deprecated
    private PrintBoxBillDTO getPrintGroupBoxBillDTO(List<String> packageCodeList) {
        PackageParam packageParam = new PackageParam();
        packageParam.setPackageCodeList(packageCodeList);
        List<PackageDTO> list = remotePackageClient.getList(packageParam);
        if (CollectionUtils.isEmpty(list)) {
            throw new BaseException(BaseBizEnum.TIP, "查询包裹记录为空");
        }
        PrintBoxBillDTO.RequestParam requestParam = new PrintBoxBillDTO.RequestParam();
        requestParam.setPrint_type(PrintTypeEnum.PRINT_CHECKPACKAGE.getPrintType());
        requestParam.setStock_code(CurrentRouteHolder.getWarehouseCode());
        requestParam.setTemplate_name(PrintTypeEnum.PRINT_CHECKPACKAGE.getTemplateName());
        List<PrintBoxBillDTO.Content> contentList = new ArrayList<>();
        Map<String, DecimalPlaceDTO> decimalPlaceDTOMap = new HashMap<>();

        //分组掉，防止一次拉dubbo 接口报错
        List<List<PackageDTO>> groupPackageDTOList = new ArrayList<List<PackageDTO>>();

        groupPackageDTOList = Lists.partition(list, 400);
        Map<String, String> cargoNameMap = new HashMap<>();

        //
        for (List<PackageDTO> groupItemPackageDTO : groupPackageDTOList) {
            ShipmentOrderParam shipmentOrderParam = new ShipmentOrderParam();
            shipmentOrderParam.setShipmentOrderCodeList(groupItemPackageDTO.stream().map(s -> s.getShipmentOrderCode()).distinct().collect(Collectors.toList()));
            List<ShipmentOrderDTO> shipmentOrderDTOList = remoteShipmentOrderClient.getList(shipmentOrderParam);
            PackageDetailParam detailParam = new PackageDetailParam();
            detailParam.setPackageCodeList(groupItemPackageDTO.stream().map(s -> s.getPackageCode()).distinct().collect(Collectors.toList()));
            List<PackageDetailDTO> _packageDetailDTOList = remotePackageClient.getPackageDetailListByListCode(detailParam);
            _packageDetailDTOList = _packageDetailDTOList.stream().filter(a -> a.getIsPre().equalsIgnoreCase(PackIsPreEnum.NORMAL.getCode())).collect(Collectors.toList());
            SkuParam skuParam = new SkuParam();
            skuParam.setCodeList(_packageDetailDTOList.stream().map(s -> s.getSkuCode()).distinct().collect(Collectors.toList()));
            skuParam.setCargoCodeList(groupItemPackageDTO.stream().map(s -> s.getCargoCode()).distinct().collect(Collectors.toList()));
            List<SkuDTO> skuDTOList = remoteSkuClient.getList(skuParam);


            PickParam pickParam = new PickParam();
            pickParam.setPackageCodeList(groupItemPackageDTO.stream().map(s -> s.getPackageCode()).distinct().collect(Collectors.toList()));
            List<PickDetailDTO> _pickDetailDTOList = remotePickClient.getPickDetailList(pickParam).stream().filter(s -> !s.getPackageStatus().equalsIgnoreCase(PackEnum.STATUS.PART_ASSIGN_COMPLETE_STATUS.getCode())).collect(Collectors.toList());

            //一次性拉出来
            PickParam pickParam2 = new PickParam();
            pickParam2.setWaveCodeList(groupItemPackageDTO.stream().map(s -> s.getWaveCode()).distinct().collect(Collectors.toList()));
            List<PickDTO> pickGroupClientList = remotePickClient.getList(pickParam2);


            List<String> pickCodeList = pickGroupClientList.stream().map(s -> s.getPickCode()).collect(Collectors.toList());
            List<PickDetailDTO> pickDetailGroupClientList = _pickDetailDTOList.stream().filter(s -> pickCodeList.contains(s.getPickCode())).collect(Collectors.toList());

            for (PackageDTO packageDTO : groupItemPackageDTO) {
                //优化下，避免多次查询
                Integer numberFormat = 0, weightNumberFormat = 0, qtyNumberFormat = 0;
                String key = StrUtil.join(StrUtil.COLON, packageDTO.getWarehouseCode(), packageDTO.getCargoCode());
                DecimalPlaceDTO decimalPlaceDTO = decimalPlaceDTOMap.get(key);
                if (decimalPlaceDTO == null) {
                    decimalPlaceDTO = remoteDecimalPlaceClient.getAllDecimalPlaceFormat(packageDTO.getWarehouseCode(), packageDTO.getCargoCode());
                    if (decimalPlaceDTO != null) {
                        decimalPlaceDTOMap.put(key, decimalPlaceDTO);
                    }
                }
                if (decimalPlaceDTO != null) {
                    numberFormat = decimalPlaceDTO.getNumber();
                    weightNumberFormat = decimalPlaceDTO.getWeight();
                    qtyNumberFormat = decimalPlaceDTO.getNumber();
                }

                packageDTO.setListDetail(_packageDetailDTOList.stream().filter(s -> s.getPackageCode().equalsIgnoreCase(packageDTO.getPackageCode())).collect(Collectors.toList()));
                PrintBoxBillDTO.Content content = new PrintBoxBillDTO.Content();
                PrintBoxBillDTO.OutHead boxBillHead = new PrintBoxBillDTO.OutHead();
                boxBillHead.setBox_no(packageDTO.getBoxNo());

                PickDetailDTO pickDetail = _pickDetailDTOList.stream().filter(s -> s.getPackageCode().equalsIgnoreCase(packageDTO.getPackageCode())).findFirst().orElse(null);
                boxBillHead.setPick_code(findPickCodeGroup(pickGroupClientList, pickDetailGroupClientList, packageDTO));
                if (pickDetail != null) {
                    boxBillHead.setPrintSort(StringUtils.isEmpty(pickDetail.getBasketNo()) ? 0 : Integer.parseInt(pickDetail.getBasketNo()));
                } else {
                    boxBillHead.setPrintSort(0);
                }

                boxBillHead.setShipping_code(packageDTO.getShipmentOrderCode());
                boxBillHead.setPackage_code(packageDTO.getPackageCode());

                String cargoName = "";
                if (!StringUtils.isEmpty(packageDTO.getCargoCode())) {
                    cargoName = cargoNameMap.get(packageDTO.getCargoCode());
                    if (StringUtils.isEmpty(cargoName)) {
                        CargoOwnerDTO cargoOwnerDTO = remoteCargoOwnerClient.queryByCode(packageDTO.getCargoCode());
                        if (cargoOwnerDTO != null) {
                            cargoName = cargoOwnerDTO.getName();
                            cargoNameMap.put(packageDTO.getCargoCode(), cargoName);
                        }
                    }
                }
                boxBillHead.setCargo_name(cargoName);

                Optional<ShipmentOrderDTO> optionShipmentOrderDTO = shipmentOrderDTOList.stream().filter(s -> s.getShipmentOrderCode().equalsIgnoreCase(packageDTO.getShipmentOrderCode())).findFirst();
                ShipmentOrderDTO shipmentOrderDTO = null;
                if (optionShipmentOrderDTO.isPresent()) {
                    shipmentOrderDTO = optionShipmentOrderDTO.get();
                }
                if (shipmentOrderDTO != null) {
                    //解密奇门过来的数据，看是否需要解密
                    shipmentOrderDecryptBiz.getDecryptResult(shipmentOrderDTO, PrintDesensitizeEnum.PACKINGLIST.toString());
                    boxBillHead.setCustomer_name(shipmentOrderDTO.getReceiverMan());
                    boxBillHead.setReceive_address(String.join("", shipmentOrderDTO.getReceiverProvName(), shipmentOrderDTO.getReceiverCityName(),
                            shipmentOrderDTO.getReceiverAreaName(), shipmentOrderDTO.getReceiverAddress()));
                    boxBillHead.setReceive_name(shipmentOrderDTO.getReceiverMan());
                    boxBillHead.setReceive_phone(shipmentOrderDTO.getReceiverTel());
                    boxBillHead.setShipping_date(ConverterUtil.convertVoTime(shipmentOrderDTO.getOutStockDate()));
                    boxBillHead.setSo_no(shipmentOrderDTO.getPoNo());
                    boxBillHead.setRemark("");
                }
                boxBillHead.setPrint_by(CurrentUserHolder.getUserName());
                boxBillHead.setCheck_by("");
                List<PackageDetailDTO> listDetail = packageDTO.getListDetail().stream().filter(a -> a.getIsPre().equalsIgnoreCase(PackIsPreEnum.NORMAL.getCode())).collect(Collectors.toList());

                List<PrintBoxBillDTO.OutDetails> listPackMarkDetails = new ArrayList<PrintBoxBillDTO.OutDetails>();
                BigDecimal total = BigDecimal.ZERO;
                for (PackageDetailDTO detailDTO : listDetail) {
                    PrintBoxBillDTO.OutDetails markDetail = new PrintBoxBillDTO.OutDetails();
                    total = total.add(detailDTO.getSkuQty());
                    markDetail.setQty(detailDTO.getSkuQty().setScale(qtyNumberFormat, BigDecimal.ROUND_FLOOR).toString());
                    markDetail.setSku_code(detailDTO.getSkuCode());
                    markDetail.setSku_name(detailDTO.getSkuName());
                    markDetail.setUpc_code(detailDTO.getUpcCode());
                    if (!StringUtils.isEmpty(detailDTO.getUpcCode())) {
                        SkuDTO skuDTO = skuDTOList.stream().filter(s -> s.getCode().equalsIgnoreCase(detailDTO.getSkuCode()) && s.getCargoCode().equalsIgnoreCase(detailDTO.getCargoCode())).findFirst().orElse(null);
                        if (skuDTO != null) {
                            markDetail.setSku_size(skuDTO.getSkuSize());
                            markDetail.setSku_color(skuDTO.getColour());
                        }
                    }
                    listPackMarkDetails.add(markDetail);
                }
                boxBillHead.setTotal_qty(total.setScale(qtyNumberFormat, BigDecimal.ROUND_FLOOR).toString());
                BigDecimal weight = packageDTO.getRealWeight() == null ? BigDecimal.ZERO : packageDTO.getRealWeight();
                boxBillHead.setTotal_weight(weight.setScale(weightNumberFormat, BigDecimal.ROUND_FLOOR).toString());
                content.setOut_head(boxBillHead);
                content.setOut_details(listPackMarkDetails);
                contentList.add(content);
            }
        }

        Comparator<PrintBoxBillDTO.Content> pickCodeOrderAsc = Comparator.comparing(s -> s.getOut_head().getPick_code(), Comparator.reverseOrder());
        Comparator<PrintBoxBillDTO.Content> finalComparator = pickCodeOrderAsc.thenComparing(Comparator.comparing(s -> s.getOut_head().getPrintSort()));
        contentList = contentList.stream().sorted(finalComparator).collect(Collectors.toList());
        requestParam.setContents(contentList);
        PrintBoxBillDTO printBoxBillDTO = new PrintBoxBillDTO();
        printBoxBillDTO.setAction(PrintTypeEnum.PRINT_CHECKPACKAGE.getAction());
        printBoxBillDTO.setMessage_id(String.valueOf(UidUtils.getCacheUid()));
        printBoxBillDTO.setRequest_params(requestParam);
        return printBoxBillDTO;
    }

    @Override
    public Result<PrintWrapInfo> getPrintGroupBoxBillInfo(CodeListParam param) {
        List<String> pickCodeList = param.getCodeList();
        PickParam pickParam = new PickParam();
        pickParam.setPickCodeList(pickCodeList);
        List<PickDTO> pickList = remotePickClient.getList(pickParam);
        pickList = pickList.stream().filter(s -> PickRulePrintGroupEnum.GOODSLIST_PRINT_GROUP.getCode().equalsIgnoreCase(s.getPrintGroup())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(pickList)) {
            return Result.success(PrintWrapInfo.createPrintWrapInfo());
        }
        List<String> salePlatfrom = pickList.stream().map(a -> {
            String salePlatform = "";
            if (!StringUtils.isEmpty(a.getSalePlatform())) {
                salePlatform = a.getSalePlatform().toLowerCase();
            } else {
                salePlatform = a.getSalePlatform();
            }
            return salePlatform;
        }).distinct().collect(Collectors.toList());
        if (salePlatfrom.size() > 1) {
            if (CollectionUtils.contains(salePlatfrom.iterator(), "PDD") || CollectionUtils.contains(salePlatfrom.iterator(), "pdd")) {
                throw new BaseException(BaseBizEnum.TIP, "批量打印面单存在多个平台,且含有拼多多平台");
            }
        }
        pickParam.setPickCodeList(pickList.stream().map(s -> s.getPickCode()).collect(Collectors.toList()));
        List<String> packageCodeList = remotePickClient.getPickDetailList(pickParam).stream().map(s -> s.getPackageCode()).distinct().collect(Collectors.toList());
        PrintBoxBillDTO printBoxBillDTO = getPrintGroupBoxBillDTO(packageCodeList);
        PrintWrapInfo wrapInfo = new PrintWrapInfo();
        wrapInfo.setRefId(pickList.stream().map(s -> s.getPickCode()).collect(Collectors.toList()));
        wrapInfo.setPrintObj(printBoxBillDTO);
        return Result.success(wrapInfo);

    }

    @Override
    public Result<Boolean> modifyPickPrintStatus(List<String> pickList_0, List<String> pickList_1, List<String> pickList_2) {
        PickParam param1 = new PickParam();
        param1.setPickCodeList(pickList_0);
        List<PickDTO> listPick = remotePickClient.getList(param1);
        if (!CollectionUtils.isEmpty(listPick)) {
            for (PickDTO pickDTO : listPick) {
                if (CollectionUtils.contains(pickList_0.iterator(), pickDTO.getPickCode())) {
                    pickDTO.setPrintStatus(PickEnum.PrintEnum.PICK_PRINT_1.getCode());
                    pickDTO.setPrintNum((pickDTO.getPrintNum() == null ? 0 : pickDTO.getPrintNum()) + 1);
                }
                if (CollectionUtils.contains(pickList_1.iterator(), pickDTO.getPickCode())) {
                    pickDTO.setExpressBillStatus(PickEnum.PrintEnum.PICK_PRINT_1.getCode());
                }
                if (CollectionUtils.contains(pickList_2.iterator(), pickDTO.getPickCode())) {
                    pickDTO.setGoodsListStatus(PickEnum.PrintEnum.PICK_PRINT_1.getCode());
                }
            }
            PickBatchParam pickBatchParam = new PickBatchParam();
            pickBatchParam.setPickList(listPick);
            Boolean oretResult = remotePickClient.modifyBatch(pickBatchParam);
            if (!oretResult) {
                throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
            }
        }
        return Result.success(true);
    }

    /**
     * 打印组[面单打印组]
     *
     * @param param
     * @return
     */
    @Override
    public Result<PrintWrapInfo> getPrintGroupPickWaybillInfo(CodeListParam param) {
        PickParam pickParam = new PickParam();
        pickParam.setPickCodeList(param.getCodeList());
        List<PickDTO> pickList = remotePickClient.getList(pickParam);
        pickList = pickList.stream().filter(s -> PickRulePrintGroupEnum.EXPRESS_PRINT_GROUP.getCode().equalsIgnoreCase(s.getPrintGroup())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(pickList)) {
            return Result.success(PrintWrapInfo.createPrintWrapInfo());
        }
        List<String> salePlatfrom = pickList.stream().map(a -> {
            String salePlatform = "";
            if (!StringUtils.isEmpty(a.getSalePlatform())) {
                salePlatform = a.getSalePlatform().toLowerCase();
            } else {
                salePlatform = a.getSalePlatform();
            }
            return salePlatform;
        }).distinct().collect(Collectors.toList());
        if (salePlatfrom.size() > 1) {
            if (CollectionUtils.contains(salePlatfrom.iterator(), "PDD") || CollectionUtils.contains(salePlatfrom.iterator(), "pdd")) {
                throw new BaseException(BaseBizEnum.TIP, "批量打印面单存在多个平台,且含有拼多多平台");
            }
        }
        //按照拣选单号排序，降序,
        pickList = pickList.stream().sorted(Comparator.comparing(PickDTO::getPickCode, Comparator.reverseOrder())).collect(Collectors.toList());
        pickParam.setPickCodeList(pickList.stream().map(s -> s.getPickCode()).collect(Collectors.toList()));
        List<PickDetailDTO> pickDetailList = remotePickClient.getPickDetailList(pickParam);
        if (CollectionUtils.isEmpty(pickDetailList)) {
            throw new BaseException(BaseBizEnum.TIP, "拣选单编号查询包裹信息异常");
        }
        List<String> carrierCodeList = pickList.stream()
                .flatMap(a -> Stream.of(a.getCarrierCode()))
                .distinct()
                .collect(Collectors.toList());
        if (carrierCodeList.size() > 1) {
            throw new BaseException(BaseBizEnum.TIP, "批量打印面单存在多个不同快递公司");
        }
        List<String> packageCodeList = pickDetailList
                .stream()
                .flatMap(a -> Stream.of(a.getPackageCode()))
                .distinct()
                .collect(Collectors.toList());
        PrintWaybillBizParam printWaybillBizParam = new PrintWaybillBizParam();
        printWaybillBizParam.setPackageCodeList(packageCodeList);
        printWaybillBizParam.setPrintType(PickEnum.PrintExpressTypeEnum.EXPRESS_PRINT_2.getCode());
        PrintWaybillDTO printWaybill = getPrintWaybillDTO(printWaybillBizParam);
        printWaybill.getRequest_params().setOwner_code("");

        PrintWrapInfo wrapInfo = new PrintWrapInfo();
        wrapInfo.setPrintObj(printWaybill);
        wrapInfo.setRefId(pickList.stream().map(s -> s.getPickCode()).collect(Collectors.toList()));
        return Result.success(wrapInfo);
    }

    @Deprecated
    private PrintWaybillDTO getPrintWaybillDTO(PrintWaybillBizParam param) {
        String warehouseCode = CurrentRouteHolder.getWarehouseCode();
        WarehouseDTO warehouseDTO = remoteWarehouseClient.queryByCode(warehouseCode);

        PackageParam packageParam = new PackageParam();
        packageParam.setPackageCodeList(param.getPackageCodeList());
        List<PackageDTO> packageList = remotePackageClient.getList(packageParam);
        List<String> shipmentOrderCodeList = packageList
                .stream()
                .flatMap(a -> Stream.of(a.getShipmentOrderCode()))
                .distinct()
                .collect(Collectors.toList());
        ShipmentOrderParam shipmentOrderParam = new ShipmentOrderParam();
        shipmentOrderParam.setShipmentOrderCodeList(shipmentOrderCodeList);
        List<ShipmentOrderDTO> shipmentOrderList = remoteShipmentOrderClient.getList(shipmentOrderParam);
        PackageDetailParam packageDetailParam = new PackageDetailParam();
        packageDetailParam.setPackageCodeList(param.getPackageCodeList());
        List<PackageDetailDTO> packageDetailList = remotePackageDetailClient.getList(packageDetailParam);
        if (CollectionUtils.isEmpty(packageList) ||
                CollectionUtils.isEmpty(packageDetailList) ||
                CollectionUtils.isEmpty(shipmentOrderList)) {
            throw new WmsBizException(BaseBizEnum.DATA_ERROR);
        }
        packageDetailList = packageDetailList.stream().filter(a -> a.getIsPre().equalsIgnoreCase(PackIsPreEnum.NORMAL.getCode())).collect(Collectors.toList());

        List<String> areaCodeList = new ArrayList<>();
        for (ShipmentOrderDTO shipmentOrder : shipmentOrderList) {
            if (!areaCodeList.contains(shipmentOrder.getSenderProv())) {
                areaCodeList.add(shipmentOrder.getSenderProv());
            }
            if (!areaCodeList.contains(shipmentOrder.getSenderCity())) {
                areaCodeList.add(shipmentOrder.getSenderCity());
            }
            if (!areaCodeList.contains(shipmentOrder.getSenderArea())) {
                areaCodeList.add(shipmentOrder.getSenderArea());
            }
            if (!areaCodeList.contains(shipmentOrder.getReceiverProv())) {
                areaCodeList.add(shipmentOrder.getReceiverProv());
            }
            if (!areaCodeList.contains(shipmentOrder.getReceiverCity())) {
                areaCodeList.add(shipmentOrder.getReceiverCity());
            }
            if (!areaCodeList.contains(shipmentOrder.getReceiverArea())) {
                areaCodeList.add(shipmentOrder.getReceiverArea());
            }
            //解密奇门过来的数据，看是否需要解密
            shipmentOrderDecryptBiz.getDecryptResult(shipmentOrder, PrintDesensitizeEnum.EXPRESS.toString());
        }
        areaCodeList.add(warehouseDTO.getProvinceCode());
        areaCodeList.add(warehouseDTO.getCityCode());
        areaCodeList.add(warehouseDTO.getDistrictCode());

        List<String> carrierCodes = shipmentOrderList.stream().map(ShipmentOrderDTO::getCarrierCode).distinct().collect(Collectors.toList());
        CarrierParam carrierParam = new CarrierParam();
        carrierParam.setCodeList(carrierCodes);
        List<CarrierDTO> carrierDTOS = remoteCarrierClient.getList(carrierParam);

        AreaParam areaParam = new AreaParam();
        areaParam.setAreaCodeList(areaCodeList);
        List<AreaDTO> areaList = remoteAreaClient.getList(areaParam);
        PackageDTO packageCheck = packageList.get(0);
        List<PrintWaybillDTO.Content> contentList = new ArrayList<>();

        List<String> interceptShipmentOrderList = new ArrayList<>();
        interceptShipmentOrderList.add(ShipmentOrderEnum.STATUS.INTERCEPT_STATUS.getCode());

        List<String> interceptPackageList = new ArrayList<>();
        interceptPackageList.add(PackEnum.STATUS.PART_ASSIGN_STATUS.getCode());
        interceptPackageList.add(PackEnum.STATUS.PART_ASSIGN_CANCEL_STATUS.getCode());

        String warehouseProvinceName = areaList.stream()
                .filter(areaDTO -> areaDTO.getAreaCode().equals(warehouseDTO.getProvinceCode()))
                .map(AreaDTO::getAreaName)
                .findAny().orElse("");
        String warehouseCityName = areaList.stream()
                .filter(areaDTO -> areaDTO.getAreaCode().equals(warehouseDTO.getCityCode()))
                .map(AreaDTO::getAreaName)
                .findAny().orElse("");
        String warehouseDistrictName = areaList.stream()
                .filter(areaDTO -> areaDTO.getAreaCode().equals(warehouseDTO.getDistrictCode()))
                .map(AreaDTO::getAreaName)
                .findAny().orElse("");

        PackageWaybillParam packageWaybillParam = new PackageWaybillParam();
        packageWaybillParam.setShipmentCodeList(packageList.stream().map(s -> s.getShipmentOrderCode()).distinct().collect(Collectors.toList()));
        packageWaybillParam.setWaybillNoList(packageList.stream().map(s -> s.getExpressNo()).collect(Collectors.toList()));
        List<PackageWaybillDTO> clientPackageWaybillList = remotePackageWaybillClient.getList(packageWaybillParam);

        Map<String, DecimalPlaceDTO> decimalPlaceDTOMap = new HashMap<>();

        PickParam pickParam = new PickParam();
        pickParam.setPackageCodeList(packageList.stream().map(s -> s.getPackageCode()).distinct().collect(Collectors.toList()));

        List<PickDetailDTO> _pickDetailDTOList = remotePickClient.getPickDetailList(pickParam).stream().filter(s -> !s.getPackageStatus().equalsIgnoreCase(PackEnum.STATUS.PART_ASSIGN_COMPLETE_STATUS.getCode())).collect(Collectors.toList());


        PickParam pickParam2 = new PickParam();
        pickParam2.setWaveCodeList(packageList.stream().map(s -> s.getWaveCode()).distinct().collect(Collectors.toList()));
        List<PickDTO> pickGroupClientList = remotePickClient.getList(pickParam2);

        List<String> pickCodeList = pickGroupClientList.stream().map(s -> s.getPickCode()).distinct().collect(Collectors.toList());
        List<PickDetailDTO> pickDetailGroupClientList = _pickDetailDTOList.stream().filter(s -> pickCodeList.contains(s.getPickCode())).collect(Collectors.toList());

        //更新面单打印次数
        List<PickDetailDTO> pickDetailList = new ArrayList<>();
        for (PackageDTO pack : packageList) {
            //优化下，避免多次查询
            Integer numberFormat = 0, weightFormat = 0;
            String key = StrUtil.join(StrUtil.COLON, pack.getWarehouseCode(), pack.getCargoCode());
            DecimalPlaceDTO decimalPlaceDTO = decimalPlaceDTOMap.get(key);
            if (decimalPlaceDTO == null) {
                decimalPlaceDTO = remoteDecimalPlaceClient.getAllDecimalPlaceFormat(pack.getWarehouseCode(), pack.getCargoCode());
                if (decimalPlaceDTO != null) {
                    decimalPlaceDTOMap.put(key, decimalPlaceDTO);
                }
            }
            if (decimalPlaceDTO != null) {
                numberFormat = decimalPlaceDTO.getNumber();
                weightFormat = decimalPlaceDTO.getWeight();
            }

            if (StringUtils.isEmpty(pack.getExpressNo())) {
                throw new WmsBizException(WmsPrintBizEnum.PRINT_WAY_BILL_PRINT_STATUS_PROTECT);
            }
            if (!packageCheck.getCarrierCode().equals(pack.getCarrierCode())) {
                throw new WmsBizException(WmsPrintBizEnum.PRINT_WAY_BILL_CARRIER_ERROR);
            }

            List<PackageWaybillDTO> packageWaybillList = clientPackageWaybillList.stream().filter(s -> s.getShipmentCode().equalsIgnoreCase(pack.getShipmentOrderCode()))
                    .filter(s -> s.getWaybillNo().equalsIgnoreCase(pack.getExpressNo())).collect(Collectors.toList());
            PackageWaybillDTO packageWaybill = packageWaybillList.stream()
                    .filter(a -> a.getShipmentCode().equals(pack.getShipmentOrderCode()))
                    .filter(a -> a.getPackageCode().equals(pack.getPackageCode()))
                    .findAny().orElse(null);
            if (ObjectUtils.isEmpty(packageWaybill)) {
                packageWaybill = packageWaybillList.stream()
                        .filter(a -> a.getShipmentCode().equals(pack.getShipmentOrderCode()))
                        .filter(a -> a.getPackageCode().equals(pack.getShipmentOrderCode()))
                        .findAny().orElse(null);
            }

            List<PackageDetailDTO> rowPackageDetailList = packageDetailList
                    .stream()
                    .filter(a -> a.getIsPre().equalsIgnoreCase(PackIsPreEnum.NORMAL.getCode()))
                    .filter(a -> a.getPackageCode().equals(pack.getPackageCode()))
                    .collect(Collectors.toList());

            ShipmentOrderDTO shipmentOrder = shipmentOrderList
                    .stream()
                    .filter(a -> a.getShipmentOrderCode().equals(pack.getShipmentOrderCode()))
                    .findAny().get();

            AreaDTO senderProv = areaList.stream()
                    .filter(a -> a.getAreaCode().equals(shipmentOrder.getSenderProv())).findAny().orElse(null);
            AreaDTO senderCity = areaList.stream()
                    .filter(a -> a.getAreaCode().equals(shipmentOrder.getSenderCity())).findAny().orElse(null);
            AreaDTO senderArea = areaList.stream()
                    .filter(a -> a.getAreaCode().equals(shipmentOrder.getSenderArea())).findAny().orElse(null);
            AreaDTO receiverProv = areaList.stream()
                    .filter(a -> a.getAreaCode().equals(shipmentOrder.getReceiverProv())).findAny().orElse(null);
            AreaDTO receiverCity = areaList.stream()
                    .filter(a -> a.getAreaCode().equals(shipmentOrder.getReceiverCity())).findAny().orElse(null);
            AreaDTO receiverArea = areaList.stream()
                    .filter(a -> a.getAreaCode().equals(shipmentOrder.getReceiverArea())).findAny().orElse(null);
            //避免lamba表达式语法错误
            Integer _numberFormat = numberFormat;
            List<PrintWaybillDTO.OrderItem> orderItemList = rowPackageDetailList.stream().flatMap(a -> {
                PrintWaybillDTO.OrderItem orderItem = new PrintWaybillDTO.OrderItem();
                orderItem.setItem_code(a.getSkuCode());
                orderItem.setItem_name(a.getSkuName());
                orderItem.setItem_qty((a.getSkuQty() == null ? BigDecimal.ZERO : a.getSkuQty()).setScale(_numberFormat, BigDecimal.ROUND_FLOOR).toString());
                return Stream.of(orderItem);
            }).collect(Collectors.toList());

            PrintWaybillDTO.TradeOrderInfo tradeOrderInfo = new PrintWaybillDTO.TradeOrderInfo();
            tradeOrderInfo.setOrder_no(pack.getPackageCode());
            tradeOrderInfo.setWaybill_no(pack.getExpressNo());
            tradeOrderInfo.setTotal_fees(BigDecimal.ZERO.setScale(weightFormat, BigDecimal.ROUND_FLOOR).toString());
            tradeOrderInfo.setInsured_fees(BigDecimal.ZERO.setScale(weightFormat, BigDecimal.ROUND_FLOOR).toString());
            tradeOrderInfo.setFreight(BigDecimal.ZERO.setScale(weightFormat, BigDecimal.ROUND_FLOOR).toString());
            tradeOrderInfo.setTotal_weight(((pack.getWeight() == null) ? BigDecimal.ZERO : pack.getWeight()).setScale(weightFormat, BigDecimal.ROUND_FLOOR).toString());
            tradeOrderInfo.setPay_mothod("");
            tradeOrderInfo.setMonthly_account("");
            carrierDTOS.stream()
                    .filter(carrierDTO -> carrierDTO.getCode().equals(shipmentOrder.getCarrierCode()))
                    .findAny()
                    .ifPresent(carrierDTO -> tradeOrderInfo.setMonthly_account(carrierDTO.getSettlementAccount()));
            tradeOrderInfo.setShort_address(ObjectUtils.isEmpty(packageWaybill) ? "" : packageWaybill.getShortAddress());
            tradeOrderInfo.setPackage_center_code(ObjectUtils.isEmpty(packageWaybill) ? "" : packageWaybill.getPackageCenterCode());
            tradeOrderInfo.setPackage_center_name(ObjectUtils.isEmpty(packageWaybill) ? "" : packageWaybill.getPackageCenterName());
            tradeOrderInfo.setOne_section_code(ObjectUtils.isEmpty(packageWaybill) ? "" : packageWaybill.getOneSectionCode());
            tradeOrderInfo.setTwo_section_code(ObjectUtils.isEmpty(packageWaybill) ? "" : packageWaybill.getTwoSectionCode());
            tradeOrderInfo.setThree_section_code(ObjectUtils.isEmpty(packageWaybill) ? "" : packageWaybill.getThreeSectionCode());

            if (interceptShipmentOrderList.contains(shipmentOrder.getStatus()) || interceptPackageList.contains(pack.getStatus())) {
                tradeOrderInfo.setIs_cancel("1");
            } else {
                tradeOrderInfo.setIs_cancel("-1");
            }

            tradeOrderInfo.setOrder_items(orderItemList);

            //发件人
            PrintWaybillDTO.SendInfo sendInfo = new PrintWaybillDTO.SendInfo();
            String senderMan = shipmentOrder.getSenderMan();
            String senderTel = shipmentOrder.getSenderTel();
            if (StrUtil.isBlank(senderMan)) {
                senderMan = warehouseDTO.getContact();
            }
            if (StrUtil.isBlank(senderTel)) {
                senderTel = warehouseDTO.getContactNumber();
            }
            sendInfo.setSend_name(senderMan);
            sendInfo.setSend_phone(senderTel);

            String sendProv = ObjectUtils.isEmpty(shipmentOrder.getSenderProvName()) ? (ObjectUtils.isEmpty(senderProv) ? "" : senderProv.getAreaName()) : shipmentOrder.getSenderProvName();
            if (StrUtil.isBlank(sendProv)) {
                sendProv = warehouseProvinceName;
            }
            sendInfo.setSend_province(sendProv);

            String sendCity = ObjectUtils.isEmpty(shipmentOrder.getSenderCityName()) ? (ObjectUtils.isEmpty(senderCity) ? "" : senderCity.getAreaName()) : shipmentOrder.getSenderCityName();
            if (StrUtil.isBlank(sendCity)) {
                sendCity = warehouseCityName;
            }
            sendInfo.setSend_city(sendCity);

            String sendArea = ObjectUtils.isEmpty(shipmentOrder.getSenderAreaName()) ? (ObjectUtils.isEmpty(senderArea) ? "" : senderArea.getAreaName()) : shipmentOrder.getSenderAreaName();
            if (StrUtil.isBlank(sendArea)) {
                sendArea = warehouseDistrictName;
            }
            sendInfo.setSend_area(sendArea);

            sendInfo.setSend_town("");
            String senderAddress = shipmentOrder.getSenderAddress();
            if (StrUtil.isBlank(senderAddress)) {
                senderAddress = warehouseDTO.getAddress();
            }
            sendInfo.setSend_address_detail(senderAddress);

            //收件人
            PrintWaybillDTO.ReceiveInfo receiveInfo = new PrintWaybillDTO.ReceiveInfo();
            receiveInfo.setReceive_name(shipmentOrder.getReceiverMan());
            receiveInfo.setReceive_phone(shipmentOrder.getReceiverTel());

            String receiveProv = ObjectUtils.isEmpty(shipmentOrder.getReceiverProvName()) ? (ObjectUtils.isEmpty(receiverProv) ? "" : receiverProv.getAreaName()) :
                    shipmentOrder.getReceiverProvName();
            receiveInfo.setReceive_province(receiveProv);

            String receiveCity = ObjectUtils.isEmpty(shipmentOrder.getReceiverCityName()) ? (ObjectUtils.isEmpty(receiverCity) ? "" : receiverCity.getAreaName())
                    : shipmentOrder.getReceiverCityName();
            receiveInfo.setReceive_city(receiveCity);


            String receiveArea = ObjectUtils.isEmpty(shipmentOrder.getReceiverAreaName()) ? (ObjectUtils.isEmpty(receiverArea) ? "" : receiverArea.getAreaName())
                    : shipmentOrder.getReceiverAreaName();

            receiveInfo.setReceive_area(receiveArea);

            receiveInfo.setReceive_town("");
            receiveInfo.setReceive_address_detail(shipmentOrder.getReceiverAddress());

            PickDetailDTO pickDetail = _pickDetailDTOList.stream().filter(s -> s.getPackageCode().equalsIgnoreCase(pack.getPackageCode())).findFirst().orElse(null);

            PrintWaybillDTO.CustomerArea customerArea = new PrintWaybillDTO.CustomerArea();
            customerArea.setPackage_code(pack.getPackageCode());

            String itemCodeAndQty = rowPackageDetailList.stream().map(aa -> {
                return aa.getUpcCode() + "*" + aa.getSkuQty().stripTrailingZeros().toPlainString();
            }).collect(Collectors.joining(";"));

            customerArea.setItem_code_and_qty(itemCodeAndQty);

            customerArea.setShipment_code(pack.getShipmentOrderCode());
            customerArea.setWave_code(findPickCodeGroup(pickGroupClientList, pickDetailGroupClientList, pack));
            customerArea.setBasket_no(ObjectUtils.isEmpty(pickDetail) ? "" : String.format("%02d", Integer.parseInt(pickDetail.getBasketNo())));
            customerArea.setTotal_sku_qty((pack.getPackageSkuQty() == null ? BigDecimal.ZERO : pack.getPackageSkuQty())
                    .setScale(numberFormat, BigDecimal.ROUND_FLOOR).toString());

            PrintWaybillDTO.Content content = new PrintWaybillDTO.Content();
            content.setSend_info(sendInfo);
            content.setReceive_info(receiveInfo);
            content.setCustomer_area(customerArea);
            content.setPrintSort(StringUtils.isEmpty(customerArea.getBasket_no()) ? 0 : Integer.parseInt(customerArea.getBasket_no()));
            content.setTrade_order_info(tradeOrderInfo);
            contentList.add(content);
            pickDetail.setExpressPrintNum((pickDetail.getExpressPrintNum() == null ? 0 : pickDetail.getExpressPrintNum()) + 1);
            pickDetailList.add(pickDetail);
        }
        //包裹按照蓝号打印@王响平
        Comparator<PrintWaybillDTO.Content> pickCodeOrderAsc = Comparator.comparing(s -> s.getCustomer_area().getWave_code(), Comparator.reverseOrder());
        Comparator<PrintWaybillDTO.Content> finalComparator = pickCodeOrderAsc.thenComparing(Comparator.comparing(PrintWaybillDTO.Content::getPrintSort));
        contentList = contentList.stream().sorted(finalComparator).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(contentList)) {
            throw new BaseException(BaseBizEnum.TIP, "没有可打印面单的包裹数据");
        }
        PrintWaybillDTO.RequestParam requestParam = new PrintWaybillDTO.RequestParam();
        //TODO PDD和JD传 平台编码 其他传快递编码
//        if (packageCheck.getSalePlatform().equalsIgnoreCase("CAINIAO")
//                || packageCheck.getSalePlatform().equalsIgnoreCase("JD")
//                || packageCheck.getSalePlatform().equalsIgnoreCase("PDD")
//                || packageCheck.getSalePlatform().equalsIgnoreCase("VIP")
//                || packageCheck.getSalePlatform().equalsIgnoreCase("SN")
//                || packageCheck.getSalePlatform().equalsIgnoreCase("TBTOP")
//                || packageCheck.getSalePlatform().equalsIgnoreCase("DY")) {
        //忽略大小写
        if (wmsOtherConfig != null
                && !CollectionUtils.isEmpty(wmsOtherConfig.getPrintSalePlatformList())
                && wmsOtherConfig.getPrintSalePlatformList().stream().anyMatch(a -> a.equalsIgnoreCase(packageCheck.getSalePlatform()))) {
            requestParam.setPlatform_code(packageCheck.getSalePlatform().toUpperCase());
        } else {
            requestParam.setPlatform_code(packageCheck.getCarrierCode());
        }
//        requestParam.setPlatform_code("PDD".equals(packageCheck.getSalePlatform()) ? packageCheck.getSalePlatform() : packageCheck.getCarrierCode());
        requestParam.setStock_code(packageCheck.getWarehouseCode());
        requestParam.setOwner_code(packageCheck.getCargoCode());
        requestParam.setCp_code(packageCheck.getCarrierCode());
        requestParam.setTemplate_name(packageCheck.getCarrierCode());

        requestParam.setContents(contentList);

        PrintWaybillDTO printWaybill = new PrintWaybillDTO();
        printWaybill.setAction(PrintTypeEnum.PRINT_WAYBILL.getAction());
        printWaybill.setMessage_id(String.valueOf(UidUtils.getCacheUid()));
        printWaybill.setRequest_params(requestParam);

        List<PackageLogDTO> packageLogDTOS = new ArrayList<>();
        if (param.getPrintType() == null) {
            param.setPrintType(PickEnum.PrintExpressTypeEnum.EXPRESS_PRINT_0.getCode());
        }
        for (PackageDTO packageDTO : packageList) {
            PackageLogDTO packageLogDTO = new PackageLogDTO();
            packageLogDTO.setOpDate(System.currentTimeMillis());
            packageLogDTO.setCargoCode(packageDTO.getCargoCode());
            packageLogDTO.setPackageCode(packageDTO.getPackageCode());
            packageLogDTO.setWarehouseCode(packageDTO.getWarehouseCode());
            packageLogDTO.setOpBy(CurrentUserHolder.getUserName());
            if (PickEnum.PrintExpressTypeEnum.EXPRESS_PRINT_0.getCode().equals(param.getPrintType())) {
                packageLogDTO.setOpContent(String.format("包裹打印面单,单号:%s", packageDTO.getPackageCode()));
            } else if (PickEnum.PrintExpressTypeEnum.EXPRESS_PRINT_1.getCode().equals(param.getPrintType())) {
                packageLogDTO.setOpContent(String.format("包裹补打面单,单号:%s", packageDTO.getPackageCode()));
            } else if (PickEnum.PrintExpressTypeEnum.EXPRESS_PRINT_2.getCode().equals(param.getPrintType())) {
                packageLogDTO.setOpContent(String.format("包裹补打面单,单号:%s", packageDTO.getPackageCode()));
            } else {
                packageLogDTO.setOpContent(String.format("打印包裹,单号:%s", packageDTO.getPackageCode()));
            }
            packageLogDTOS.add(packageLogDTO);
        }
        iBusinessLogBiz.savePackLogList(packageLogDTOS);
        //面单打印记录打印次数
        if (!CollectionUtils.isEmpty(pickDetailList)) {
            PickBatchParam pickBatchParam = new PickBatchParam();
            pickBatchParam.setPickDetailList(pickDetailList);
            remotePickClient.modifyPickDetailBatch(pickBatchParam);
        }
        return printWaybill;
    }

    private String findPickCodeGroup(List<PickDTO> pickDTOList, List<PickDetailDTO> pickDetailDTOList, PackageDTO pack) {
        if (StringUtils.isEmpty(pack.getWaveCode())) {
            return "";
        }
        if (CollectionUtils.isEmpty(pickDTOList)) {
            return "";
        }
        List<PickDTO> pickClientList = pickDTOList.stream().filter(s -> s.getWaveCode().equalsIgnoreCase(pack.getWaveCode())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(pickClientList)) {
            return "";
        }
        List<String> pickCodeList = pickClientList.stream().map(s -> s.getPickCode()).collect(Collectors.toList());
        List<PickDetailDTO> clientList = pickDetailDTOList.stream().filter(s -> s.getPackageCode().equalsIgnoreCase(pack.getPackageCode()))
                .filter(s -> pickCodeList.contains(s.getPickCode())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(clientList)) {
            return "";
        }
        return clientList.get(0).getPickCode();
    }

    private Boolean isPickConcatWeight(String cargoCode) {
        CargoConfigDTO cargoConfigDTO = remoteCargoConfigClient.queryByCargoCodeAndpropKey("", cargoCode, CargoConfigParamEnum.PICK_CONCAT_WEIGHT.getCode());
        if (cargoConfigDTO != null &&
                cargoConfigDTO.getStatus().equals(CargoConfigStatusEnum.ENABLE.getValue())
                && Objects.equals(cargoConfigDTO.getPropValue(), "1")) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    @Override
    public Result<PrintWrapInfo> getPrintGroupPickInfo(CodeListParam param) {
        PickParam pickParam = new PickParam();
        pickParam.setPickCodeList(param.getCodeList());
        List<PickDTO> pickList = remotePickClient.getList(pickParam);

        List<String> salePlatfrom = pickList.stream().map(a -> {
            String salePlatform = "";
            if (!StringUtils.isEmpty(a.getSalePlatform())) {
                salePlatform = a.getSalePlatform().toLowerCase();
            } else {
                salePlatform = a.getSalePlatform();
            }
            return salePlatform;
        }).distinct().collect(Collectors.toList());
        if (salePlatfrom.size() > 1) {
            if (CollectionUtils.contains(salePlatfrom.iterator(), "PDD") || CollectionUtils.contains(salePlatfrom.iterator(), "pdd")) {
                throw new BaseException(BaseBizEnum.TIP, "批量打印面单存在多个平台,且含有拼多多平台");
            }
        }

        PickParam pickDetailParam = new PickParam();
        pickDetailParam.setPickCodeList(param.getCodeList());
        List<PickDetailDTO> pickDetailList = remotePickClient.getPickDetailList(pickDetailParam);
        if (ObjectUtils.isEmpty(pickList) || CollectionUtils.isEmpty(pickDetailList)) {
            throw new WmsBizException(BaseBizEnum.DATA_ERROR);
        }
        String businessType = pickList.get(0).getBusinessType();
        List<PickEnum.PickStatusEnum> pickStatusList = new ArrayList<>();
        pickStatusList.add(PickEnum.PickStatusEnum.CANCEL_STATUS);
        pickStatusList.add(PickEnum.PickStatusEnum.CREATE_STATUS);
        List<String> cargoCodeList = pickList
                .stream()
                .flatMap(a -> Stream.of(a.getCargoCode()))
                .distinct()
                .collect(Collectors.toList());
        CargoOwnerParam cargoOwnerParam = new CargoOwnerParam();
        cargoOwnerParam.setCodeList(cargoCodeList);
        List<CargoOwnerDTO> cargoOwnerList = remoteCargoOwnerClient.queryList(cargoOwnerParam);

        List<String> carrierCodeList = pickList
                .stream()
                .flatMap(a -> Stream.of(a.getCarrierCode()))
                .distinct()
                .collect(Collectors.toList());

        CarrierParam carrierParam = new CarrierParam();
        carrierParam.setCodeList(carrierCodeList);
        List<CarrierDTO> carrierList = remoteCarrierClient.getList(carrierParam);

        List<PrintPickDTO.Content> contentList = new ArrayList<>();
        //统一按照拣选单降序排序
        pickList = pickList.stream().sorted(Comparator.comparing(PickDTO::getPickCode, Comparator.reverseOrder())).collect(Collectors.toList());
        //获取大相关出库单信息
        List<ShipmentOrderDTO> shipmentOrderDTOList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(pickDetailList)) {
            ShipmentOrderParam shipmentOrderParam = new ShipmentOrderParam();
            shipmentOrderParam.setShipmentOrderCodeList(pickDetailList.stream().map(s -> s.getShipmentOrderCode()).distinct().collect(Collectors.toList()));
            List<ShipmentOrderDTO> tempShipmentOrderDTOList = remoteShipmentOrderClient.getList(shipmentOrderParam);
            if (!CollectionUtils.isEmpty(tempShipmentOrderDTOList)) {
                shipmentOrderDTOList.addAll(tempShipmentOrderDTOList);
            }
        }
        Map<String, DecimalPlaceDTO> decimalPlaceDTOMap = new HashMap<>();
        Map<String, String> salePlatformDTOMap = new HashMap<>();
        for (PickDTO pick : pickList) {
            boolean isPickConcatWeight = isPickConcatWeight(pick.getCargoCode());
            Integer numberFormat = 0;
            String key = StrUtil.join(StrUtil.COLON, pick.getWarehouseCode(), pick.getCargoCode());
            DecimalPlaceDTO decimalPlaceDTO = decimalPlaceDTOMap.get(key);
            if (decimalPlaceDTO == null) {
                decimalPlaceDTO = remoteDecimalPlaceClient.getAllDecimalPlaceFormat(pick.getWarehouseCode(), pick.getCargoCode());
                if (decimalPlaceDTO != null) {
                    decimalPlaceDTOMap.put(key, decimalPlaceDTO);
                }
            }
            if (decimalPlaceDTO != null) {
                numberFormat = decimalPlaceDTO.getNumber();
            }
            PickEnum.PickStatusEnum pickStatusEnum = PickEnum.PickStatusEnum.getEnum(pick.getStatus()).get();
            if (pickStatusList.contains(pickStatusEnum)) {
                throw new WmsBizException(WmsPrintBizEnum.PRINT_PICK_PRINT_STATUS_PROTECT);
            }
            CargoOwnerDTO cargoOwner = cargoOwnerList
                    .stream()
                    .filter(b -> b.getCode().equals(pick.getCargoCode()))
                    .findAny().orElse(null);

            CarrierDTO carrier = carrierList
                    .stream()
                    .filter(b -> b.getCode().equals(pick.getCarrierCode()))
                    .findAny().orElse(null);
            List<PickDetailDTO> rowPickDetailList = pickDetailList.stream()
                    .filter(a -> a.getPickCode().equals(pick.getPickCode()))
                    .collect(Collectors.toList());
            //获取拣选单明细信息
            pick.setDetailList(rowPickDetailList);

            Integer formatNum = decimalPlaceDTO == null ? 0 : decimalPlaceDTO.getNumber();
            //修复拣选单打印，数据精度的问题
            pick.setQty(pick.getQty().setScale(formatNum, RoundingMode.FLOOR));
            //指引
            List<PickGuideBizDTO> pickGuideList = pickGuideBiz.getValidityPeriodPickGuide(pick);

            PrintPickDTO.OutHead outHead = new PrintPickDTO.OutHead();
            outHead.setPickinglist_no(pick.getPickCode());
            outHead.setPickinglist_type(PickEnum.PickOrderTypeEnum.matchOpCode(pick.getType()).getMessage());
            outHead.setSubstr_pickinglist_no(org.apache.commons.lang3.StringUtils.substring(
                    outHead.getPickinglist_no(), outHead.getPickinglist_no().length() - 5 < 0 ? 0 : outHead.getPickinglist_no().length() - 5));
            if (pick.getBusinessType().equals(ShipmentOrderEnum.BUSSINESS_TYPE.B2B.toString())) {
                //B2B的拣选单打印时， 如果拣选单中，只有一个出库单， 需要打印出出库单的备注
                outHead.setRemark("");
                if (rowPickDetailList.stream().map(s -> s.getShipmentOrderCode()).distinct().count() == 1) {
                    String shipmentOrderCode = rowPickDetailList.stream().map(s -> s.getShipmentOrderCode()).findFirst().orElse(null);
                    Optional<ShipmentOrderDTO> optional = shipmentOrderDTOList.stream().filter(s -> s.getShipmentOrderCode().equalsIgnoreCase(shipmentOrderCode)).findFirst();
                    if (optional.isPresent()) {
                        outHead.setRemark(optional.get().getRemark());
                    }
                }
                outHead.setShipping_code_list(rowPickDetailList.stream().map(PickDetailDTO::getShipmentOrderCode).distinct().collect(Collectors.toList()));
            }
            outHead.setCargo_name(ObjectUtils.isEmpty(cargoOwner) ? "" : cargoOwner.getName());
            outHead.setCarrier_name(ObjectUtils.isEmpty(carrier) ? "" : carrier.getName());
            outHead.setOrder_qty(new BigDecimal(pick.getPackageQty()).setScale(numberFormat, BigDecimal.ROUND_FLOOR).toString());
            outHead.setProduct_qty((pick.getQty() == null ? BigDecimal.ZERO : pick.getQty()).setScale(numberFormat, BigDecimal.ROUND_FLOOR).toString());
            outHead.setSku_qty(new BigDecimal(pickGuideList.stream().map(a -> a.getSkuCode()).distinct().count()).setScale(numberFormat, BigDecimal.ROUND_FLOOR).toString());
            outHead.setShop_name("");
            if (!StringUtils.isEmpty(pick.getSalePlatform())) {
                String platformName = salePlatformDTOMap.get(pick.getSalePlatform());
                if (platformName == null) {
                    SalePlatformParam param1 = new SalePlatformParam();
                    param1.setCode(pick.getSalePlatform());
                    SalePlatformDTO salePlatformDTO = salePlatformClient.get(param1).getData();
                    if (salePlatformDTO != null) {
                        platformName = salePlatformDTO.getName();
                        salePlatformDTOMap.put(pick.getSalePlatform(), platformName);
                    } else if ("other".equalsIgnoreCase(pick.getSalePlatform())) {
                        platformName = "其它";
                        salePlatformDTOMap.put(pick.getSalePlatform(), platformName);
                    }
                }
                outHead.setPlatform_code(platformName == null ? "" : platformName);
            } else {
                outHead.setPlatform_code("");
            }
            outHead.setPrint_name(CurrentUserHolder.getUserName());
            outHead.setPicking_name(pick.getPickBy());
            Integer _numberFormat = numberFormat;
            if (PickEnum.PickOrderTypeEnum.SPIKE.getCode().equals(pick.getType())) {
                PackageDetailParam packageDetailParam = new PackageDetailParam();
                packageDetailParam.setPackageCode(rowPickDetailList.get(0).getPackageCode());
                List<PackageDetailDTO> packageDetailList = remotePackageDetailClient.getList(packageDetailParam);
                packageDetailList = packageDetailList.stream().filter(a -> a.getIsPre().equalsIgnoreCase(PackIsPreEnum.NORMAL.getCode())).collect(Collectors.toList());
                outHead.setGoods_information(String.join(";  ", packageDetailList.stream()
                        .flatMap(a -> Stream.of(String.format("%s*%s", a.getUpcCode(), a.getSkuQty().setScale(_numberFormat, BigDecimal.ROUND_FLOOR))))
                        .distinct().collect(Collectors.toList())));
            } else {
                outHead.setGoods_information("");
            }
            List<PrintPickDTO.OutDetails> orderItemList = pickGuideList.stream().flatMap(a -> {
                PrintPickDTO.OutDetails outDetails = new PrintPickDTO.OutDetails();
                outDetails.setBasket_no(a.getGuide());
                outDetails.setLocation_code(a.getLocationCode());
                outDetails.setSku_code(a.getSkuCode());
                String skuName = a.getSkuName();
                if (isPickConcatWeight) {
                    skuName = org.apache.commons.lang3.StringUtils.join(skuName, "(", a.getGrossWeight().toPlainString(), " ", "KG", ")");
                }
                outDetails.setSku_name(skuName);
                String upcCode = a.getUpcCode();
                outDetails.setSubstr_upc_code(org.apache.commons.lang3.StringUtils.substring(upcCode, upcCode.length() - 4 < 0 ? 0 : upcCode.length() - 4));
                outDetails.setUpc_code(org.apache.commons.lang3.StringUtils.removeEnd(upcCode, outDetails.getSubstr_upc_code()));
                outDetails.setItem_qty(a.getQty());
                outDetails.setExpire_date(a.getExpireDateDesc());
                return Stream.of(outDetails);
            }).collect(Collectors.toList());

            PrintPickDTO.Content content = new PrintPickDTO.Content();
            content.setOut_head(outHead);
            content.setOut_details(orderItemList);
            pick.setPrintStatus(PickEnum.PrintEnum.PICK_PRINT_1.getCode());
            pick.setPrintNum((pick.getPrintNum() == null ? 0 : pick.getPrintNum()) + 1);
            contentList.add(content);
        }
        PrintPickDTO.RequestParam requestParam = new PrintPickDTO.RequestParam();
        requestParam.setStock_code(CurrentRouteHolder.getWarehouseCode());
        requestParam.setPrint_type(PrintTypeEnum.PRINT_PICK.getPrintType());
        if (businessType.equals(ShipmentOrderEnum.BUSSINESS_TYPE.B2B.toString())) {
            requestParam.setTemplate_name(PrintTypeEnum.PRINT_PICK_B2B.getTemplateName());
        } else {
            requestParam.setTemplate_name(PrintTypeEnum.PRINT_PICK.getTemplateName());
        }
        requestParam.setContents(contentList);

        PrintPickDTO printPick = new PrintPickDTO();
        printPick.setAction(PrintTypeEnum.PRINT_PICK.getAction());
        printPick.setMessage_id(String.valueOf(UidUtils.getCacheUid()));
        printPick.setRequest_params(requestParam);
        PrintWrapInfo wrapInfo = new PrintWrapInfo();
        wrapInfo.setRefId(pickList.stream().map(s -> s.getPickCode()).collect(Collectors.toList()));
        wrapInfo.setPrintObj(printPick);
        return Result.success(wrapInfo);
    }


}
