package com.dt.platform.wms.client;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.enums.base.ContainerStatusEnum;
import com.dt.component.common.enums.ret.ReturnOrderEnum;
import com.dt.component.common.enums.shelf.ShelfStatusEnum;
import com.dt.component.common.exceptions.BaseException;
import com.dt.component.common.result.Result;
import com.dt.domain.base.dto.ContainerDTO;
import com.dt.domain.base.param.ContainerParam;
import com.dt.domain.bill.dto.ReturnOrderDTO;
import com.dt.domain.bill.dto.ReturnOrderDetailDTO;
import com.dt.domain.bill.dto.ShelfDTO;
import com.dt.domain.bill.param.ReturnOrderParam;
import com.dt.domain.bill.param.ShelfParam;
import com.dt.platform.utils.ConverterUtil;
import com.dt.platform.wms.dto.ret.ReturnOrderBizDTO;
import com.dt.platform.wms.dto.ret.ReturnOrderDetailBizDTO;
import com.dt.platform.wms.integration.IRemoteContainerClient;
import com.dt.platform.wms.integration.IRemoteDecimalPlaceClient;
import com.dt.platform.wms.integration.IRemoteReturnOrderClient;
import com.dt.platform.wms.integration.IRemoteShelfClient;
import com.dt.platform.wms.param.CodeParam;
import com.dt.platform.wms.param.ret.ReturnOrderBizParam;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@DubboService(version = "${dubbo.service.version}")
public class ReturnOrderBizClient implements IReturnOrderBizClient {
    @Resource
    private IRemoteReturnOrderClient remoteReturnOrderClient;
    @Resource
    IRemoteDecimalPlaceClient decimalPlaceClient;

    @Resource
    private IRemoteContainerClient remoteContainerClient;

    @Resource
    private IRemoteShelfClient remoteShelfClient;

    @Override
    public Result<Boolean> save(ReturnOrderBizParam param) {
        ReturnOrderParam returnOrderParam = ConverterUtil.convert(param, ReturnOrderParam.class);
        return Result.success(remoteReturnOrderClient.save(returnOrderParam));
    }

    @Override
    public Result<Boolean> modify(ReturnOrderBizParam param) {
        ReturnOrderParam returnOrderParam = ConverterUtil.convert(param, ReturnOrderParam.class);
        return Result.success(remoteReturnOrderClient.modify(returnOrderParam));
    }

    @Override
    public Result<ReturnOrderBizDTO> get(ReturnOrderBizParam param) {
        ReturnOrderParam returnOrderParam = ConverterUtil.convert(param, ReturnOrderParam.class);
        ReturnOrderDTO returnOrderDTO = remoteReturnOrderClient.get(returnOrderParam);
        if (returnOrderDTO == null) {
            return Result.success(null);
        }
        List<ReturnOrderDetailDTO> listDetail = returnOrderDTO.getListDetail();
        ReturnOrderBizDTO returnOrderBizDTO = ConverterUtil.convert(returnOrderDTO, ReturnOrderBizDTO.class);
        returnOrderBizDTO.setNumberFormat(decimalPlaceClient.getNumberFormat(returnOrderDTO.getWarehouseCode(), null));
        List<ReturnOrderDetailBizDTO> listBizDto = ConverterUtil.convertList(listDetail, ReturnOrderDetailBizDTO.class);
        returnOrderBizDTO.setListDetail(listBizDto);
        return Result.success(returnOrderBizDTO);
    }

    @Override
    public Result<Page<ReturnOrderBizDTO>> queryPage(ReturnOrderBizParam param) {
        ReturnOrderParam returnOrderParam = ConverterUtil.convert(param, ReturnOrderParam.class);
        Page<ReturnOrderDTO> page = remoteReturnOrderClient.queryPage(returnOrderParam);
        List<ReturnOrderBizDTO> recordsList = page.getRecords().stream().map(item -> {
            ReturnOrderBizDTO dto = new ReturnOrderBizDTO();
            BeanUtils.copyProperties(item, dto);
            dto.setNumberFormat(decimalPlaceClient.getNumberFormat(item.getWarehouseCode(), null));
            return dto;
        }).collect(Collectors.toList());
        Page<ReturnOrderBizDTO> result = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        result.setRecords(recordsList);
        return Result.success(result);
    }

    @Override
    public Result<Boolean> completeReturnOrder(CodeParam param) {
        ReturnOrderParam returnOrderParam = new ReturnOrderParam();
        returnOrderParam.setRetOrderCode(param.getCode());
        ReturnOrderDTO returnOrderDTO = remoteReturnOrderClient.get(returnOrderParam);
        if (returnOrderDTO == null) {
            throw new BaseException(BaseBizEnum.TIP, String.format("归位单号:%s未找到", param.getCode()));
        }
        if (CollectionUtils.isEmpty(returnOrderDTO.getListDetail())) {
            throw new BaseException(BaseBizEnum.TIP, String.format("归位单号:%s未找到明细", param.getCode()));
        }
        if (Objects.equals(returnOrderDTO.getStatus(), ReturnOrderEnum.RETURN_ORDER_COMPLETED.getStatus())) {
            throw new BaseException(BaseBizEnum.TIP, String.format("归位单号:%s已完成", param.getCode()));
        }
        ShelfParam shelfParam = new ShelfParam();
        shelfParam.setBillNo(returnOrderDTO.getRetOrderCode());
        List<ShelfDTO> shelfDTOList = remoteShelfClient.getList(shelfParam);
        if (!CollectionUtils.isEmpty(returnOrderDTO.getListDetail()) && CollectionUtils.isEmpty(shelfDTOList)) {
            throw new BaseException(BaseBizEnum.TIP, String.format("归位单号:%s还没有生成上架单,不允许完成", param.getCode()));
        }
        long count = shelfDTOList.stream().filter(shelfDTO -> ShelfStatusEnum.STATUS_WAIT_SHELF.getStatus().equals(shelfDTO.getStatus()) || ShelfStatusEnum.STATUS_DOING.getStatus().equals(shelfDTO.getStatus())).count();
        if (count > 0) {
            throw new BaseException(BaseBizEnum.TIP, String.format("归位单号:%s存在对应上架单没有上架完成", param.getCode()));
        }
        //获取商品数量
        BigDecimal shelfQty = shelfDTOList.stream().map(ShelfDTO::getSkuQty).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal retQty = returnOrderDTO.getListDetail().stream().map(ReturnOrderDetailDTO::getExpReturnQty).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (shelfQty.compareTo(retQty) != 0) {
            throw new BaseException(BaseBizEnum.TIP, String.format("归位单号:%s的总商品数量与对应上架单的总数量不一致,不允许完成", param.getCode()));
        }
        // 防止完成归位单没有释放容器
        ContainerParam containerParam = new ContainerParam();
        containerParam.setOccupyNo(returnOrderDTO.getRetOrderCode());
        List<ContainerDTO> containerDTOS = remoteContainerClient.getList(containerParam);
        containerDTOS.forEach(it -> {
            if (it.getStatus().equals(ContainerStatusEnum.OCCUPY.getValue()) && it.getOccupyNo().equalsIgnoreCase(returnOrderDTO.getRetOrderCode())) {
                it.setStatus(ContainerStatusEnum.ENABLE.getValue());
                it.setOccupyType("");
                it.setOccupyNo("");
                it.setRemark("");
                remoteContainerClient.update(ConverterUtil.convert(it, ContainerParam.class));
            }
        });

        // 数量和状态
        for (ReturnOrderDetailDTO returnOrderDetailDTO : returnOrderDTO.getListDetail()) {
            returnOrderDetailDTO.setCompleteOnShelfQty(returnOrderDetailDTO.getExpReturnQty());
            returnOrderDetailDTO.setStatus(ReturnOrderEnum.RETURN_ORDER_COMPLETED.getStatus());
        }
        remoteReturnOrderClient.updateDetails(returnOrderDTO.getListDetail());

        // 主单据状态
        returnOrderDTO.setStatus(ReturnOrderEnum.RETURN_ORDER_COMPLETED.getStatus());
        returnOrderDTO.setCompleteOnShelfQty(returnOrderDTO.getExpReturnQty());
        returnOrderDTO.setCompleteShelfTime(System.currentTimeMillis());
        remoteReturnOrderClient.update(returnOrderDTO);
        return Result.success(true);
    }
}
