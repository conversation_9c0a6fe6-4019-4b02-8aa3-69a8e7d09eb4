package com.dt.platform.wms.client;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.dt.component.common.enums.cargo.CargoConfigGroupEnum;
import com.dt.component.common.enums.cargo.CargoConfigParamEnum;
import com.dt.component.common.enums.cargo.CargoConfigStatusEnum;
import com.dt.component.common.result.Result;
import com.dt.component.common.vo.IdNameVO;
import com.dt.platform.wms.biz.ICargoConfigBiz;
import com.dt.platform.wms.dto.cargo.CargoConfigBizDTO;
import com.dt.platform.wms.dto.cargo.CargoConfigIdNameDescBizDTO;
import com.dt.platform.wms.dto.cargo.CargoConfigParamBizDTO;
import com.dt.platform.wms.form.CargoConfigAddBizForm;
import com.dt.platform.wms.form.CargoConfigUpdateBizForm;
import com.dt.platform.wms.param.cargo.CargoConfigBizParam;
import com.dt.platform.wms.param.cargo.CargoConfigEnableBizParam;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/9/15 15:31
 */
@DubboService(version = "${dubbo.service.version}")
public class CargoConfigBizClient implements ICargoConfigBizClient {

    @Resource
    ICargoConfigBiz iCargoConfigBiz;

    @Override
    public Result<IPage<CargoConfigBizDTO>> queryPage(CargoConfigBizParam param) {
        return Result.success(iCargoConfigBiz.queryPage(param));
    }

    @Override
    public Result<Boolean> enable(CargoConfigEnableBizParam param) {
        return Result.success(iCargoConfigBiz.enable(param));
    }

    @Override
    public Result<List<IdNameVO>> queryStatusAll() {
        return Result.success(IdNameVO.buildAll(CargoConfigStatusEnum.class, "value", "message"));
    }

    @Override
    public Result<List<IdNameVO>> queryStatus() {
        return Result.success(IdNameVO.build(CargoConfigStatusEnum.class, "value", "message"));
    }

    @Override
    public Result<CargoConfigBizDTO> queryById(Long id) {
        return Result.success(iCargoConfigBiz.queryById(id));
    }

    @Override
    public Result<List<IdNameVO>> codeGroup() {
        return Result.success(IdNameVO.build(CargoConfigGroupEnum.class, "code", "message"));
    }

    @Override
    public Result<List<IdNameVO>> codeGroupAll() {
        return Result.success(IdNameVO.buildAll(CargoConfigGroupEnum.class, "code", "message"));
    }

    @Override
    public Result<List<CargoConfigParamBizDTO>> paramList() {
        List<CargoConfigParamBizDTO> cargoConfigParamBizDTOS = new ArrayList<>();
        CargoConfigGroupEnum[] cargoConfigGroupEnum = CargoConfigGroupEnum.values();
        for (CargoConfigGroupEnum entity : cargoConfigGroupEnum) {
            CargoConfigParamBizDTO cargoConfigParamBizDTO = new CargoConfigParamBizDTO();
            cargoConfigParamBizDTO.setId(entity.getCode());
            cargoConfigParamBizDTO.setName(entity.getMessage());
            cargoConfigParamBizDTO.setGroup(buildIdNameDesc(entity.getCode()));
            cargoConfigParamBizDTOS.add(cargoConfigParamBizDTO);
        }
        return Result.success(cargoConfigParamBizDTOS);
    }

    @Override
    public Result<Boolean> add(CargoConfigAddBizForm param) {
        return Result.success(iCargoConfigBiz.add(param));
    }

    @Override
    public Result<Boolean> update(CargoConfigUpdateBizForm param) {
        return Result.success(iCargoConfigBiz.update(param));
    }

    /**
     * 构造货主参数配置某一组
     *
     * @param code
     * @return
     */
    private List<CargoConfigIdNameDescBizDTO> buildIdNameDesc(String code) {
        List<CargoConfigIdNameDescBizDTO> cargoConfigIdNameDescBizDTOS = new ArrayList<>();
        List<CargoConfigParamEnum> configParamEnums = CargoConfigParamEnum.getList(code);
        if (configParamEnums.isEmpty()) {
            return cargoConfigIdNameDescBizDTOS;
        }
        for (CargoConfigParamEnum entity : configParamEnums) {
            CargoConfigIdNameDescBizDTO descBizDTO = new CargoConfigIdNameDescBizDTO();
            descBizDTO.setId(entity.getCode());
            descBizDTO.setName(entity.getName());
            descBizDTO.setDesc(entity.getMessage());
            cargoConfigIdNameDescBizDTOS.add(descBizDTO);
        }
        return cargoConfigIdNameDescBizDTOS;
    }
}
