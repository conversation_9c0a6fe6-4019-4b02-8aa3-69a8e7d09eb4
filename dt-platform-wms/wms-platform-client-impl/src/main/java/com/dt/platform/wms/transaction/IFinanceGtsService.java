package com.dt.platform.wms.transaction;

import com.dt.platform.wms.transaction.bo.*;

/**
 * <AUTHOR>
 * @date 2022/5/17 17:44
 */
public interface IFinanceGtsService {
    /**
     * @param financeSupervisionLockBO
     * @return java.lang.Boolean
     * @author: WuXian
     * description: 监管单锁定库存
     * create time: 2022/5/17 17:52
     */
    void supervisionCommitLockStock(FinanceSupervisionLockGtsBO financeSupervisionLockBO);

    /**
     * @param financeSupervisionReleaseGtsBO
     * @return void
     * @author: WuXian
     * description: 监管单取消锁定库存
     * create time: 2022/5/18 10:04
     */
    void supervisionCommitReleaseStock(FinanceSupervisionReleaseGtsBO financeSupervisionReleaseGtsBO);

    /**
     * 监管移位
     */
    void supervisionCommitMoveStock(FinanceSupervisionMoveGtsBO financeSupervisionMoveGtsBO);

    /**
     * @param financeRedeemReleaseGtsBO
     * @return void
     * @author: WuXian
     * description: 赎回单释放库存
     * create time: 2022/5/18 10:36
     */
    void redeemCommitReleaseStock(FinanceRedeemReleaseGtsBO financeRedeemReleaseGtsBO);

    /**
     * 赎回移位
     */
    void redeemCommitMoveStock(FinanceRedeemMoveGtsBO financeRedeemMoveGtsBO);

    /**
     * @param financeDisposalReleaseGtsBO
     * @return void
     * @author: WuXian
     * description: 处置单处理库存
     * create time: 2022/5/18 12:10
     */
    void disposalCommitReleaseStock(FinanceDisposalReleaseGtsBO financeDisposalReleaseGtsBO);

    /**
     * 处置移位
     */
    void disposalCommitMoveStock(FinanceDisposalMoveGtsBO financeDisposalMoveGtsBO);
}
