package com.dt.platform.wms.transaction.bo;

import com.dt.domain.bill.dto.finance.FinanceBillLogDTO;
import com.dt.domain.bill.dto.finance.FinanceDisposalDTO;
import com.dt.domain.bill.dto.finance.FinanceDisposalDetailDTO;
import com.dt.domain.bill.dto.finance.FinanceDisposalMoveDetailDTO;
import com.dt.platform.wms.biz.stock.biz.bo.FinanceDisposalMoveStockBO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 监管单移位事务层BO
 */
@Data
public class FinanceDisposalMoveGtsBO implements Serializable {

    private FinanceDisposalDTO financeDisposalDTO;

    private List<FinanceDisposalDetailDTO> financeDisposalDetailDTOList;

    private List<FinanceDisposalMoveDetailDTO> financeDisposalMoveDetailDTOList;

    private List<FinanceDisposalMoveStockBO> financeDisposalMoveStockBOList;

    private FinanceBillLogDTO financeBillLogDTO;

}
