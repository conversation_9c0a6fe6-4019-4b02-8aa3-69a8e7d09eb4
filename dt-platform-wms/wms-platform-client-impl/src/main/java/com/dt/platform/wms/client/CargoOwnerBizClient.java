package com.dt.platform.wms.client;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.enums.FromSourceEnum;
import com.dt.component.common.enums.TaxTypeEnum;
import com.dt.component.common.enums.cargo.CargoOpenEffectEnum;
import com.dt.component.common.enums.cargo.CargoOwnerStatusEnum;
import com.dt.component.common.enums.cargo.CargoSpecialTypeEnum;
import com.dt.component.common.enums.cargo.CargoTagEnum;
import com.dt.component.common.exceptions.BaseException;
import com.dt.component.common.holder.CurrentRouteHolder;
import com.dt.component.common.holder.CurrentUserHolder;
import com.dt.component.common.result.Result;
import com.dt.component.common.vo.IdNameVO;
import com.dt.domain.base.dto.CargoOwnerDTO;
import com.dt.domain.base.dto.SkuUpcDTO;
import com.dt.domain.base.dto.WarehouseDTO;
import com.dt.domain.base.dto.cargoOwnerLog.CargoOwnerLogDTO;
import com.dt.domain.base.param.CargoOwnerParam;
import com.dt.domain.base.param.InitCargoConfigParam;
import com.dt.domain.base.param.SkuUpcParam;
import com.dt.platform.utils.ConverterUtil;
import com.dt.platform.utils.ExceptionUtil;
import com.dt.platform.wms.biz.ICargoOwnerBiz;
import com.dt.platform.wms.biz.config.WmsOtherConfig;
import com.dt.platform.wms.dto.cargo.*;
import com.dt.platform.wms.integration.IRemoteCargoOwnerClient;
import com.dt.platform.wms.integration.IRemoteSkuClient;
import com.dt.platform.wms.integration.IRemoteWarehouseClient;
import com.dt.platform.wms.integration.IRemoteWmsInitParamClient;
import com.dt.platform.wms.integration.cargoOwnerLog.IRemoteCargoOwnerLogClient;
import com.dt.platform.wms.param.CodeParam;
import com.dt.platform.wms.param.cargo.CargoOwnerBizParam;
import com.dt.platform.wms.param.cargo.CargoOwnerExtraJsonParam;
import com.dt.platform.wms.param.cargo.CargoOwnerQueryBizParam;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/9/15 15:31
 */
@Slf4j
@DubboService(version = "${dubbo.service.version}")
public class CargoOwnerBizClient implements ICargoOwnerBizClient {

    @Resource
    ICargoOwnerBiz iCargoOwnerBiz;

    @Resource
    IRemoteCargoOwnerClient remoteCargoOwnerClient;

    @Resource
    IRemoteCargoOwnerLogClient remoteCargoOwnerLogClient;

    @Resource
    private IRemoteWarehouseClient remoteWarehouseClient;

    @Resource
    IRemoteWmsInitParamClient remoteWmsInitParamClient;

    @Resource
    WmsOtherConfig wmsOtherConfig;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private IRemoteSkuClient remoteSkuClient;

    @Override
    public Result<List<IdNameVO>> queryStatusAll() {
        return Result.success(IdNameVO.buildAll(CargoOwnerStatusEnum.class, "value", "message"));
    }

    @Override
    public Result<List<IdNameVO>> queryStatus() {
        return Result.success(IdNameVO.build(CargoOwnerStatusEnum.class, "value", "message"));
    }

    @Override
    public Result<IPage<CargoOwnerBizDTO>> queryPage(CargoOwnerBizParam param) {
        return Result.success(iCargoOwnerBiz.queryPage(param));
    }

    @Override
    public Result<List<IdNameVO>> getAllCargoOwner() {
        return Result.success(iCargoOwnerBiz.getAllCargoOwner());
    }

    @Override
    public Result<Boolean> enable(CargoOwnerBizParam param) {
        return Result.success(iCargoOwnerBiz.enable(param));
    }

    @Override
    public Result<CargoOwnerBizDTO> queryByCode(String code) {
        return Result.success(iCargoOwnerBiz.queryByCode(code));
    }

    @Override
    public Result<List<CargoOwnerBizDTO>> queryList(CargoOwnerBizParam cargoOwnerBizParam) {
        if (null != cargoOwnerBizParam && StrUtil.isNotBlank(cargoOwnerBizParam.getUpcCode())) {
            SkuUpcParam skuUpcParam = new SkuUpcParam();
            skuUpcParam.setUpcCode(cargoOwnerBizParam.getUpcCode());
            List<SkuUpcDTO> skuUpcList = remoteSkuClient.getSkuUpcList(skuUpcParam);
            List<String> cargoCodeList = skuUpcList.stream().map(SkuUpcDTO::getCargoCode).distinct().collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(cargoCodeList)) {
                cargoOwnerBizParam.setCodeList(cargoCodeList);
            }
        }
        CargoOwnerParam cargoOwnerParam = ConverterUtil.convert(cargoOwnerBizParam, CargoOwnerParam.class);
        return Result.success(ConverterUtil.convertList(remoteCargoOwnerClient.queryList(cargoOwnerParam), CargoOwnerBizDTO.class));
    }

    @Override
    public Result<List<CargoOwnerBizDTO>> queryListByUpc(CargoOwnerBizParam cargoOwnerBizParam) {
        if (null != cargoOwnerBizParam && StrUtil.isBlank(cargoOwnerBizParam.getUpcCode())) {
            return Result.success(ListUtil.empty());
        }
        return queryList(cargoOwnerBizParam);
    }

    @Override
//    @GlobalTransactional(timeoutMills = 300000, rollbackFor = Exception.class, name = "cargo-owner-create-sync-erp")
    public Result<Boolean> createCargoOwner(CargoOwnerBizParam cargoOwnerBizParam, String source) {
        WarehouseDTO warehouseDTO = remoteWarehouseClient.queryByCode(CurrentRouteHolder.getWarehouseCode());
        if (TaxTypeEnum.TYPE_BONDED_TAX.getCode().equals(warehouseDTO.getType())) {
            throw new BaseException(BaseBizEnum.TIP, "跨境仓不允许新建货主");
        }
        CargoOwnerParam cargoOwnerParam = ConverterUtil.convert(cargoOwnerBizParam, CargoOwnerParam.class);
        cargoOwnerParam.setOutCode(cargoOwnerParam.getCode());
        cargoOwnerParam.setFromSource(FromSourceEnum.WMS.value());
        // 效期强校验默认关闭
        cargoOwnerParam.setOpenEffect(CargoOpenEffectEnum.CLOSE.value());
        remoteCargoOwnerClient.save(cargoOwnerParam);

        // 初始化货主参数
        InitCargoConfigParam param = new InitCargoConfigParam();
        param.setCargoCodeList(Arrays.asList(cargoOwnerParam.getCode()));
        param.setWarehouseCode(CurrentRouteHolder.getWarehouseCode());
        remoteWmsInitParamClient.initCargoConfigParam(param);
        // 添加日志
        CargoOwnerLogDTO cargoOwnerLogDTO = new CargoOwnerLogDTO();
        cargoOwnerLogDTO.setCargoCode(cargoOwnerParam.getCode());
        cargoOwnerLogDTO.setOpBy(CurrentUserHolder.getUserName());
        cargoOwnerLogDTO.setOpContent("创建货主:" + cargoOwnerBizParam.getCode());
        cargoOwnerLogDTO.setOpDate(System.currentTimeMillis());
        cargoOwnerLogDTO.setOpRemark("原始报文:" + source);
        remoteCargoOwnerLogClient.save(cargoOwnerLogDTO);
        return Result.success();
    }

    @Override
    public Result<Boolean> checkExist(CargoOwnerBizParam cargoOwnerBizParam) {
        CargoOwnerParam cargoOwnerParam = ConverterUtil.convert(cargoOwnerBizParam, CargoOwnerParam.class);
        return Result.success(remoteCargoOwnerClient.checkExist(cargoOwnerParam));
    }

    @Override
//    @GlobalTransactional(timeoutMills = 300000, rollbackFor = Exception.class, name = "cargo-update-create-sync-erp")
    public Result<Boolean> updateCargoOwner(CargoOwnerBizParam cargoOwnerBizParam, String source) {
        CargoOwnerParam cargoOwnerParam = ConverterUtil.convert(cargoOwnerBizParam, CargoOwnerParam.class);
        CargoOwnerDTO cargoOwnerDTO = remoteCargoOwnerClient.queryByCode(cargoOwnerParam.getCode());
        if (cargoOwnerDTO.getFromSource().equals(FromSourceEnum.ERP.value())) {
            throw new BaseException(BaseBizEnum.TIP, "ERP下发货主档案不允许修改");
        }
        cargoOwnerParam.setId(cargoOwnerDTO.getId());
        cargoOwnerParam.setOpenEffect(CargoOpenEffectEnum.CLOSE.value());
        CargoOwnerLogDTO cargoOwnerLogDTO = new CargoOwnerLogDTO();
        cargoOwnerLogDTO.setCargoCode(cargoOwnerParam.getCode());
        cargoOwnerLogDTO.setOpBy(CurrentUserHolder.getUserName());
        cargoOwnerLogDTO.setOpContent("修改货主:" + cargoOwnerDTO.getCode());
        cargoOwnerLogDTO.setOpDate(System.currentTimeMillis());
        cargoOwnerLogDTO.setOpRemark("原始报文:" + source);
        remoteCargoOwnerLogClient.save(cargoOwnerLogDTO);
        return Result.success(remoteCargoOwnerClient.modify(cargoOwnerParam));
    }

    @Override
    public Result<Map<String, CargoOwnerDTO>> cargoMap(List<String> cargoCodeList) {
        return Result.success(remoteCargoOwnerClient.cargoMap(cargoCodeList));
    }

    @Override
    public Result<List<IdNameVO>> queryCargoOwnerByTag(CargoOwnerQueryBizParam param) {
        CargoOwnerParam cargoOwnerParam = new CargoOwnerParam();
        //订单标记
        if (param != null && !StringUtils.isEmpty(param.getCargoTagList())) {
            cargoOwnerParam.setCargoTag(CargoTagEnum.queryParamListToInteger(param.getCargoTagList()));
        }
        List<CargoOwnerDTO> cargoOwnerDTOList = remoteCargoOwnerClient.getAllCargoOwner(cargoOwnerParam);
        if (StringUtils.isEmpty(cargoOwnerDTOList)) {
            return Result.success(new ArrayList<>());
        }
        return Result.success(IdNameVO.build(cargoOwnerDTOList, "code", "name"));
    }

    @Override
    public Result<List<IdNameVO>> queryCargoOwnerCargoTag() {
        return Result.success(IdNameVO.build(CargoTagEnum.class, "code", "desc"));
    }

    @Override
    public Result<CargoQueryShow> queryCargoOwnerCargoAllTag() {
        CargoOwnerParam cargoOwnerParam = new CargoOwnerParam();
        List<CargoOwnerDTO> cargoOwnerDTOList = remoteCargoOwnerClient.getAllCargoOwner(cargoOwnerParam);
        CargoQueryShow cargoQueryShow = new CargoQueryShow();
        List<CargoTagShow> cargoTagList = new ArrayList<>();
        List<CargoCodeShow> cargoList = new ArrayList<>();
        if (StringUtils.isEmpty(cargoOwnerDTOList)) {
            cargoQueryShow.setCargoList(cargoList);
            cargoQueryShow.setCargoTagList(cargoTagList);
            return Result.success(cargoQueryShow);
        }
        cargoOwnerDTOList.stream().forEach(cargoOwnerDTO -> {
            CargoCodeShow cargoCodeShow = new CargoCodeShow();
            cargoCodeShow.setId(cargoOwnerDTO.getCode());
            cargoCodeShow.setName(cargoOwnerDTO.getName());

            Set<CargoTagEnum> cargoTagEnumSet = CargoTagEnum.NumToEnum(cargoOwnerDTO.getCargoTag());
            if (CollectionUtils.isEmpty(cargoTagEnumSet)) {
                cargoCodeShow.setTagList(new ArrayList<>());
            } else {
                cargoCodeShow.setTagList(cargoTagEnumSet.stream().map(CargoTagEnum::getCode).collect(Collectors.toList()));
            }
            cargoList.add(cargoCodeShow);
        });
        cargoQueryShow.setCargoList(cargoList);
        for (CargoTagEnum entity : CargoTagEnum.values()) {
            CargoTagShow cargoTagShow = new CargoTagShow();
            cargoTagShow.setId(entity.getCode());
            cargoTagShow.setName(entity.getDesc());
            cargoTagList.add(cargoTagShow);
        }
        cargoQueryShow.setCargoTagList(cargoTagList);
        return Result.success(cargoQueryShow);
    }

    @Override
    public Result<List<IdNameVO>> queryToOperationOccupy(CodeParam param) {
        if (param == null || StringUtils.isEmpty(param.getCode())) {
            throw new BaseException(BaseBizEnum.TIP, "参数不能为空");
        }
        CargoOwnerDTO cargoOwnerDTO = remoteCargoOwnerClient.queryByCode(param.getCode());
        if (cargoOwnerDTO == null) {
            throw new BaseException(BaseBizEnum.TIP, "货主不存在");
        }
        Set<CargoTagEnum> cargoTagEnumSet = CargoTagEnum.NumToEnum(cargoOwnerDTO.getCargoTag());
        return Result.success(IdNameVO.build(cargoTagEnumSet.stream().collect(Collectors.toList()), "code", "desc"));
    }

    @Override
    public Result<Boolean> modifyCargoTag(CargoOwnerParam param) {
        if (param == null || StringUtils.isEmpty(param.getCode())) {
            throw new BaseException(BaseBizEnum.TIP, "参数不能为空");
        }
        String syncKey = CurrentRouteHolder.getWarehouseCode() + param.getCode();
        RLock lock = redissonClient.getLock("dt_wms_cargo_lock:" + syncKey);
        Boolean isLock = false;
        try {
            isLock = lock.tryLock(1, 150, TimeUnit.SECONDS);
            if (!isLock) {
                throw new BaseException(BaseBizEnum.TIP, "操作太快了,请稍后重试");
            }
            //获取 所有货主
            List<CargoOwnerDTO> allCargoOwner = remoteCargoOwnerClient.getAllCargoOwner(new CargoOwnerParam());
            if (CollectionUtils.isEmpty(allCargoOwner)) {
                throw new BaseException(BaseBizEnum.TIP, "货主不存在");
            }
            if (allCargoOwner.stream().noneMatch(a -> a.getCode().equalsIgnoreCase(param.getCode()))) {
                throw new BaseException(BaseBizEnum.TIP, "货主不存在");
            }
            if (!CollectionUtils.isEmpty(param.getCargoTagList()) &&
                    CargoTagEnum.NumToEnum(CargoTagEnum.queryParamListToInteger(param.getCargoTagList())).contains(CargoTagEnum.CW_CARGO)) {
                //CW标记只允许有一个货主
                List<CargoOwnerDTO> cargoOwnerDTOList = allCargoOwner.stream().filter(a -> !a.getCode().equalsIgnoreCase(param.getCode())).collect(Collectors.toList());
                if (cargoOwnerDTOList.stream().anyMatch(a -> CargoTagEnum.NumToEnum(a.getCargoTag()).contains(CargoTagEnum.CW_CARGO))) {
                    throw new BaseException(BaseBizEnum.TIP, "已有货主存在【cw货主】标记,不允许多个");
                }
            }
            CargoOwnerDTO cargoOwnerDTO = allCargoOwner.stream().filter(a -> a.getCode().equalsIgnoreCase(param.getCode())).findFirst().orElse(null);
            if (null == cargoOwnerDTO) return Result.success();

            Integer i = CargoTagEnum.queryParamListToInteger(param.getCargoTagList());
            boolean contains = CargoTagEnum.NumToEnum(i).contains(CargoTagEnum.TT_CARGO);
            boolean contains1 = CargoTagEnum.NumToEnum(cargoOwnerDTO.getCargoTag()).contains(CargoTagEnum.TT_CARGO);
            if (contains1 ^ contains) {
                throw ExceptionUtil.exceptionWithMessage("淘天货主标记不允许修改");
            }

            //获取
            if (CollectionUtils.isEmpty(param.getCargoTagList())) {
                cargoOwnerDTO.setCargoTag(0);
            } else {
                cargoOwnerDTO.setCargoTag(CargoTagEnum.queryParamListToInteger(param.getCargoTagList()));
            }
            return Result.success(remoteCargoOwnerClient.modify(cargoOwnerDTO));
        } catch (Exception e) {
            e.printStackTrace();
            String errorMsg = StringUtils.isEmpty(e.getMessage()) ? "操作太快了,请稍后重试!!!" : e.getMessage();
            throw new BaseException(BaseBizEnum.TIP, errorMsg);
        } finally {
            if (isLock) {
                lock.unlock();
            }
        }
    }

    @Override
    public Result<List<IdNameVO>> cwCustom() {
        List<String> cwCustomList = wmsOtherConfig.getCwCustomList();
        if (CollectionUtils.isEmpty(cwCustomList)) {
            cwCustomList = Lists.newArrayList();
        }
        List<IdNameVO> idNameVOList = new ArrayList<>();
        cwCustomList.forEach(it -> {
            IdNameVO idNameVO = new IdNameVO();
            idNameVO.setId(it);
            idNameVO.setName(it);
            idNameVOList.add(idNameVO);
        });
        return Result.success(idNameVOList);
    }

    @Override
    public Result<List<CargoOwnerExtraJsonDataBizDTO>> queryCargoExtraJson(CargoOwnerBizParam cargoOwnerBizParam) {
        CargoOwnerDTO cargoOwnerDTO = remoteCargoOwnerClient.queryByCode(cargoOwnerBizParam.getCode());
        if (cargoOwnerDTO == null) {
            throw new BaseException(BaseBizEnum.TIP, "货主不存在");
        }
        if (!StringUtils.isEmpty(cargoOwnerDTO.getExtraJson())) {
//            CargoOwnerExtraJsonBizDTO cargoOwnerExtraJsonBizDTO = BeanUtil.toBean(cargoOwnerDTO.getExtraJson(), CargoOwnerExtraJsonBizDTO.class);
            JSONObject jsonObject = JSONUtil.parseObj(cargoOwnerDTO.getExtraJson());
            List<CargoOwnerExtraJsonDataBizDTO> dataDTOList = JSONUtil.toList(JSONUtil.parseArray(jsonObject.getStr("dataDTOList")), CargoOwnerExtraJsonDataBizDTO.class);
            return Result.success(dataDTOList);
        }
        return Result.success(null);
    }

//    public static void main(String[] args) {
//        String str = "{\n" +
//                "    \"dataDTOList\": [\n" +
//                "        {\n" +
//                "            \"toCode\": \"9999\",\n" +
//                "            \"type\": \"10\",\n" +
//                "            \"toName\": \"xxxx\"\n" +
//                "        }\n" +
//                "    ]\n" +
//                "}";
////        CargoOwnerExtraJsonBizDTO cargoOwnerExtraJsonBizDTO = BeanUtil.toBean(str, CargoOwnerExtraJsonBizDTO.class);
//        JSONObject jsonObject = JSONUtil.parseObj(str);
//        List<CargoOwnerExtraJsonDataBizDTO> dataDTOList = JSONUtil.toList(JSONUtil.parseArray(jsonObject.getStr("dataDTOList")), CargoOwnerExtraJsonDataBizDTO.class);
//        System.out.println(JSONUtil.toJsonStr(dataDTOList));
//
//    }

    @Override
    public Result<Boolean> modifyCargoExtra(CargoOwnerExtraJsonParam cargoOwnerExtraJsonFrom) {
        CargoOwnerDTO cargoOwnerDTO = remoteCargoOwnerClient.queryByCode(cargoOwnerExtraJsonFrom.getCargoCode());
        if (cargoOwnerDTO == null) {
            throw new BaseException(BaseBizEnum.TIP, "货主不存在");
        }
        CargoOwnerExtraJsonBizDTO cargoOwnerExtraJsonBizDTO = new CargoOwnerExtraJsonBizDTO();
        ;
        if (cargoOwnerDTO.getExtraJson() != "" && JSONUtil.isJson(cargoOwnerDTO.getExtraJson())) {
            JSONObject jsonObject = JSONUtil.parseObj(cargoOwnerDTO.getExtraJson());
            List<CargoOwnerExtraJsonDataBizDTO> dataDTOList = JSONUtil.toList(JSONUtil.parseArray(jsonObject.getStr("dataDTOList")), CargoOwnerExtraJsonDataBizDTO.class);
            if (CollectionUtils.isEmpty(dataDTOList)) {
                dataDTOList = new ArrayList<>();
            }
            CargoOwnerExtraJsonDataBizDTO cargoOwnerExtraJsonDataBizDTO = dataDTOList.stream()
                    .filter(a -> Objects.equals(a.getType(), CargoSpecialTypeEnum.ZI_WI_SOURCE.getCode())).findFirst().orElse(null);
            if (cargoOwnerExtraJsonDataBizDTO != null) {
                cargoOwnerExtraJsonDataBizDTO.setType(cargoOwnerExtraJsonFrom.getExtraType());
                cargoOwnerExtraJsonDataBizDTO.setToCode(cargoOwnerExtraJsonFrom.getToCode());
                cargoOwnerExtraJsonDataBizDTO.setToName(cargoOwnerExtraJsonFrom.getToName());
            } else {
                cargoOwnerExtraJsonDataBizDTO = new CargoOwnerExtraJsonDataBizDTO();
                cargoOwnerExtraJsonDataBizDTO.setType(cargoOwnerExtraJsonFrom.getExtraType());
                cargoOwnerExtraJsonDataBizDTO.setToCode(cargoOwnerExtraJsonFrom.getToCode());
                cargoOwnerExtraJsonDataBizDTO.setToName(cargoOwnerExtraJsonFrom.getToName());
                dataDTOList.add(cargoOwnerExtraJsonDataBizDTO);
            }
            cargoOwnerExtraJsonBizDTO.setDataDTOList(dataDTOList);
            cargoOwnerDTO.setExtraJson(JSONUtil.toJsonStr(cargoOwnerExtraJsonBizDTO));
        } else {
            List<CargoOwnerExtraJsonDataBizDTO> dataDTOList = new ArrayList<>();

            CargoOwnerExtraJsonDataBizDTO cargoOwnerExtraJsonDataBizDTO = new CargoOwnerExtraJsonDataBizDTO();
            cargoOwnerExtraJsonDataBizDTO.setType(cargoOwnerExtraJsonFrom.getExtraType());
            cargoOwnerExtraJsonDataBizDTO.setToCode(cargoOwnerExtraJsonFrom.getToCode());
            cargoOwnerExtraJsonDataBizDTO.setToName(cargoOwnerExtraJsonFrom.getToName());
            dataDTOList.add(cargoOwnerExtraJsonDataBizDTO);

            cargoOwnerExtraJsonBizDTO.setDataDTOList(dataDTOList);

            cargoOwnerDTO.setExtraJson(JSONUtil.toJsonStr(cargoOwnerExtraJsonBizDTO));
        }
        remoteCargoOwnerClient.modify(cargoOwnerDTO);

        CargoOwnerLogDTO cargoOwnerLogDTO = new CargoOwnerLogDTO();
        cargoOwnerLogDTO.setCargoCode(cargoOwnerDTO.getCode());
        cargoOwnerLogDTO.setOpBy(CurrentUserHolder.getUserName());
        cargoOwnerLogDTO.setOpContent("修改拓展信息:" + cargoOwnerDTO.getCode());
        cargoOwnerLogDTO.setOpDate(System.currentTimeMillis());
        cargoOwnerLogDTO.setOpRemark("原始报文:" + cargoOwnerExtraJsonFrom);
        remoteCargoOwnerLogClient.save(cargoOwnerLogDTO);
        return Result.success(true);
    }

}
