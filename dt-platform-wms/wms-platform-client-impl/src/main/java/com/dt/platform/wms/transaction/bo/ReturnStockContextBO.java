package com.dt.platform.wms.transaction.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="上架单完成容器DTO", description="上架完成容器")
public class ReturnStockContextBO extends AbsWarehouseBO  implements java.io.Serializable  {

    @ApiModelProperty(value = "单据编码")
    private String billNo;

    @ApiModelProperty("交易日期")
    private Long tradeDate;

    @ApiModelProperty(value = "商品属性")
    private String skuQuality;

    @ApiModelProperty(value = "来源库区类型")
    private String originZoneType;

    @ApiModelProperty(value = "来源库位编码")
    private String originLocationCode;

    @ApiModelProperty(value = "来源库位类型")
    private String originLocationType;

    @ApiModelProperty(value = "目标库区编码")
    private String targetZoneCode;

    @ApiModelProperty(value = "目标库区类型")
    private String targetZoneType;

    @ApiModelProperty(value = "目标库位编码")
    private String targetLocationCode;

    @ApiModelProperty(value = "目标库位类型")
    private String targetLocationType;

    @ApiModelProperty(value = "目标库位编码")
    private String targetLocationUseMode;

    @ApiModelProperty(value = "上架单信息")
    private List<ReceiptDetailBO> detailList;

}