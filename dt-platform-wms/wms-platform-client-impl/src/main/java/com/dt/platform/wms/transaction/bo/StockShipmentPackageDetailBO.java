package com.dt.platform.wms.transaction.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
@ApiModel(value="商品库存占用BO", description="商品库存占用")
public class StockShipmentPackageDetailBO extends AbsWarehouseBO  implements java.io.Serializable  {

    @ApiModelProperty(value = "包裹编码")
    private String packageCode;

    @ApiModelProperty(value = "包裹明细序号")
    private String packageLineSeq;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    @ApiModelProperty(value = "商品编码")
    private String skuCode;

    @ApiModelProperty(value = "商品批次")
    private String skuLotNo;

    @ApiModelProperty(value = "质量等级")
    private String skuQuality;

    @ApiModelProperty(value = "出库数量")
    private BigDecimal shipQty;

    @ApiModelProperty(value = "库区编码")
    private String zoneCode;

    @ApiModelProperty(value = "库位编码")
    private String locationCode;

    @ApiModelProperty("库位用途")
    private String locationUseMode;
}