package com.dt.platform.wms.transaction;

import com.dt.platform.wms.transaction.bo.HomingCompleteExpressBO;
import com.dt.platform.wms.transaction.bo.HomingCompleteShelfBO;
import com.dt.platform.wms.transaction.bo.HomingContainerBO;

public interface IHomingGtsService {


    /**
     * 提交归位数据--容器占用
     * @param homingContainerBO
     */
    void submitHomingContainerOccupy(HomingContainerBO homingContainerBO);

    /**
     *完成容器
     * @param completeShelfBO
     */
    void submitHomingContainer(HomingCompleteShelfBO completeShelfBO);

    /**
     * 归位完成运单
     * @param homingCompleteExpressBO
     */
    void homingCompleteExpress(HomingCompleteExpressBO homingCompleteExpressBO);
}
