package com.dt.platform.wms.transaction.bo;

import com.dt.domain.bill.dto.*;
import com.dt.domain.core.stock.dto.StockTransactionDTO;
import com.dt.platform.wms.transaction.context.StockCommitContext;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "LockStockZoneBO", description = "锁定二级库存")
public class LockStockZoneBO extends AbsWarehouseBO  implements java.io.Serializable  {

    @ApiModelProperty("出库单")
    private ShipmentOrderDTO shipmentOrder;

    @ApiModelProperty("包裹")
    private PackageDTO packageDTO;

    @ApiModelProperty("出库单明细列表")
    private List<ShipmentOrderDetailDTO> shipmentOrderDetailList;

    @ApiModelProperty("异常单")
    private List<AbnormalOrderDTO> abnormalOrderDTOList;

    @ApiModelProperty("包裹")
    private List<PackageDTO> packageDTOList;

    @ApiModelProperty("包裹")
    private List<PackageDetailDTO> packageDetailDTOList;

    @ApiModelProperty("补货指引信息保存")
    private List<ReplenishTaskDTO> replenishTaskList;

    @ApiModelProperty("一级库存操作明细")
    private StockCommitContext stockCommitContext;

    @ApiModelProperty("库存交易数据")
    private List<StockTransactionDTO> stockTransactionDTOList;

    @ApiModelProperty("库存交易数据")
    private PackageLogDTO packageLogDTO;

    @ApiModelProperty("库存交易数据")
    private ShipmentOrderLogDTO shipmentOrderLogDTO;


}