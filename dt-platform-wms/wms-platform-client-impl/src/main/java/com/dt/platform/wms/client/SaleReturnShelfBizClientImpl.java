package com.dt.platform.wms.client;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import com.dt.component.common.enums.base.OpTypeEnum;
import com.dt.component.common.enums.rs.RSSecondEntryEnum;
import com.dt.component.common.enums.shelf.ShelfMarkEnum;
import com.dt.component.common.enums.shelf.ShelfStatusEnum;
import com.dt.component.common.enums.shelf.ShelfTypeEnum;
import com.dt.component.common.enums.sku.InventoryTypeEnum;
import com.dt.component.common.enums.sku.SkuQualityEnum;
import com.dt.component.common.result.Result;
import com.dt.domain.base.dto.SkuLotDTO;
import com.dt.domain.bill.dto.ShelfDTO;
import com.dt.domain.bill.dto.ShelfDetailDTO;
import com.dt.domain.bill.param.ShelfDetailParam;
import com.dt.domain.bill.param.ShelfParam;
import com.dt.platform.utils.ExceptionUtil;
import com.dt.platform.wms.dto.shelf.SaleReturnBatchShelfTipDTO;
import com.dt.platform.wms.dto.shelf.ShelfCompleteDetailBizDTO;
import com.dt.platform.wms.integration.IRemoteShelfClient;
import com.dt.platform.wms.integration.IRemoteSkuLotClient;
import com.dt.platform.wms.param.shelf.ShelfBizParam;
import com.dt.platform.wms.param.shelf.ShelfCompleteParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@DubboService(version = "${dubbo.service.version}", timeout = 60000)
public class SaleReturnShelfBizClientImpl implements ISaleReturnShelfBizClient {

    @Resource
    IShelfBizClient shelfBizClient;

    @Resource
    private IRemoteShelfClient remoteShelfClient;

    @Resource
    private IRemoteSkuLotClient remoteSkuLotClient;

    @Override
    public Result<List<SaleReturnBatchShelfTipDTO>> saleReturnShelfTip(ShelfBizParam param) {
        if (null == param) throw ExceptionUtil.ARG_ERROR;
        if (CollectionUtil.isEmpty(param.getCodeList())) throw ExceptionUtil.exceptionWithMessage("请选择要上架的单据");
        ShelfParam shelfParam = new ShelfParam();
        shelfParam.setCodeList(param.getCodeList());
        List<ShelfDTO> shelfDTOList = remoteShelfClient.getList(shelfParam);
        if (CollectionUtil.isEmpty(shelfDTOList)) return Result.success(ListUtil.empty());
        ShelfDetailParam shelfDetailParam = new ShelfDetailParam();
        shelfDetailParam.setShelfCodeList(shelfDTOList.stream().map(ShelfDTO::getCode).collect(Collectors.toList()));
        List<ShelfDetailDTO> shelfDetailList = remoteShelfClient.getShelfDetailList(shelfDetailParam);
        if (CollectionUtil.isEmpty(shelfDetailList)) return Result.success(ListUtil.empty());
        Map<String, SkuLotDTO> skuLotDTOMap = remoteSkuLotClient.skuLotMap(shelfDetailList.stream().map(ShelfDetailDTO::getSkuLotNo).distinct().collect(Collectors.toList()));
        return Result.success(calculateTips(shelfDTOList, shelfDetailList, skuLotDTOMap));
    }

    @Override
    public Result<Boolean> saleReturnShelf(ShelfBizParam param) {
        if (null == param) throw ExceptionUtil.ARG_ERROR;
        if (CollectionUtil.isEmpty(param.getCodeList())) throw ExceptionUtil.exceptionWithMessage("请选择要上架的单据");
        ShelfParam shelfParam = new ShelfParam();
        shelfParam.setCodeList(param.getCodeList());
        List<ShelfDTO> shelfDTOList = remoteShelfClient.getList(shelfParam);
        if (CollectionUtil.isEmpty(shelfDTOList)) throw ExceptionUtil.SYSTEM_BUSY;
        ShelfDetailParam shelfDetailParam = new ShelfDetailParam();
        shelfDetailParam.setShelfCodeList(shelfDTOList.stream().map(ShelfDTO::getCode).collect(Collectors.toList()));
        List<ShelfDetailDTO> shelfDetailList = remoteShelfClient.getShelfDetailList(shelfDetailParam);
        if (CollectionUtil.isEmpty(shelfDetailList)) throw ExceptionUtil.SYSTEM_BUSY;
        Map<String, SkuLotDTO> skuLotDTOMap = remoteSkuLotClient.skuLotMap(shelfDetailList.stream().map(ShelfDetailDTO::getSkuLotNo).distinct().collect(Collectors.toList()));

        List<SaleReturnBatchShelfTipDTO> calculateTips = calculateTips(shelfDTOList, shelfDetailList, skuLotDTOMap);
        List<SaleReturnBatchShelfTipDTO> tipsFromParam = param.getSaleReturnBatchShelfTipDTOList();
        if (CollectionUtil.isEmpty(tipsFromParam)) throw ExceptionUtil.ARG_ERROR;
        if (tipsFromParam.stream().anyMatch(saleReturnBatchShelfTipDTO -> StrUtil.isBlank(saleReturnBatchShelfTipDTO.getLocationCode()))) {
            throw ExceptionUtil.exceptionWithMessage("库位必填");
        }

        tipsCheck(calculateTips, tipsFromParam);

        for (ShelfDTO shelfDTO : shelfDTOList) {
            shelfParam = new ShelfParam();
            shelfParam.setCode(shelfDTO.getCode());
            ShelfDTO detail = remoteShelfClient.getDetail(shelfParam);
            if (null == detail) continue;
            ShelfCompleteParam shelfCompleteParam = new ShelfCompleteParam();
            shelfCompleteParam.setCode(shelfDTO.getCode());
            shelfCompleteParam.setOpType(shelfDTO.getOpType());
            shelfCompleteParam.setDetailList(buildCompleteDetail(detail, tipsFromParam, skuLotDTOMap));
            shelfBizClient.completeWholeShelf(shelfCompleteParam);
        }

        return Result.success(true);
    }

    public List<SaleReturnBatchShelfTipDTO> calculateTips(List<ShelfDTO> shelfDTOList, List<ShelfDetailDTO> shelfDetailList, Map<String, SkuLotDTO> skuLotDTOMap) {
        if (CollectionUtil.isEmpty(shelfDTOList)) return ListUtil.empty();
        if (CollectionUtil.isEmpty(shelfDetailList)) return ListUtil.empty();

        long holdCount = shelfDTOList.stream().map(shelfDTO -> ShelfMarkEnum.NumToEnum(shelfDTO.getMark()).contains(ShelfMarkEnum.HOLD)).distinct().count();
        if (holdCount > 1) throw ExceptionUtil.exceptionWithMessage("合流与非合流上架单，不可同时操作！");


        if (shelfDTOList.stream().anyMatch(shelfDTO -> !shelfDTO.getType().equalsIgnoreCase(ShelfTypeEnum.SHELF_TYPE_SALE_RETURN.getType()))) {
            throw ExceptionUtil.exceptionWithMessage("仅销退上架可进行此操作！");
        }
        if (shelfDTOList.stream().anyMatch(shelfDTO -> !shelfDTO.getOpType().equalsIgnoreCase(OpTypeEnum.OP_TYPE_PAPER.getType()))) {
            throw ExceptionUtil.exceptionWithMessage("仅上架方式为纸质时可进行此操作！");
        }
        if (shelfDTOList.stream().anyMatch(shelfDTO -> !shelfDTO.getStatus().equalsIgnoreCase(ShelfStatusEnum.STATUS_WAIT_SHELF.getStatus()))) {
            throw ExceptionUtil.exceptionWithMessage("仅单据状态为待上架时可进行此操作！");
        }
        if (shelfDTOList.stream().map(ShelfDTO::getSkuQuality).distinct().count() > 1) {
            throw ExceptionUtil.exceptionWithMessage("商品属性或是否可入区不同，不可同时操作！");
        }
        if (shelfDTOList.stream().anyMatch(shelfDTO -> ShelfMarkEnum.NumToEnum(shelfDTO.getMark()).contains(ShelfMarkEnum.ENTRY_ALLOW))
                && shelfDTOList.stream().anyMatch(shelfDTO -> ShelfMarkEnum.NumToEnum(shelfDTO.getMark()).contains(ShelfMarkEnum.ENTRY_DENY))) {
            throw ExceptionUtil.exceptionWithMessage("商品属性或是否可入区不同，不可同时操作！");
        }
        for (ShelfDetailDTO shelfDetailDTO : shelfDetailList) {
            SkuLotDTO skuLotDTO = skuLotDTOMap.get(shelfDetailDTO.getSkuLotNo());
            if (null == skuLotDTO) throw ExceptionUtil.exceptionWithMessage("批次信息丢失");
        }
        Map<String, List<ShelfDetailDTO>> collect = shelfDetailList.stream().collect(Collectors.groupingBy(shelfDetailDTO -> skuLotDTOMap.get(shelfDetailDTO.getSkuLotNo()).getInventoryType()));
        ShelfDTO firstShelfDTO = shelfDTOList.get(0);
        List<SaleReturnBatchShelfTipDTO> list = new ArrayList<>();
        for (String inventory : collect.keySet()) {
            List<ShelfDetailDTO> shelfDetailDTOList = collect.get(inventory);
            SaleReturnBatchShelfTipDTO saleReturnBatchShelfTipDTO = new SaleReturnBatchShelfTipDTO();
            saleReturnBatchShelfTipDTO.setSkuQuality(firstShelfDTO.getSkuQuality());
            saleReturnBatchShelfTipDTO.setSkuQualityDesc(SkuQualityEnum.desc(firstShelfDTO.getSkuQuality()));
            saleReturnBatchShelfTipDTO.setInventoryType(inventory);
            saleReturnBatchShelfTipDTO.setInventoryTypeDesc(InventoryTypeEnum.desc(inventory));
            if (ShelfMarkEnum.NumToEnum(firstShelfDTO.getMark()).contains(ShelfMarkEnum.ENTRY_ALLOW)) {
                saleReturnBatchShelfTipDTO.setSecondEntry(RSSecondEntryEnum.YES.getCode());
            } else if (ShelfMarkEnum.NumToEnum(firstShelfDTO.getMark()).contains(ShelfMarkEnum.ENTRY_DENY)) {
                saleReturnBatchShelfTipDTO.setSecondEntry(RSSecondEntryEnum.NO.getCode());
            } else {
                throw ExceptionUtil.exceptionWithMessage("是否可入区标记缺失，不允许批量上架");
            }
            saleReturnBatchShelfTipDTO.setSecondEntryDesc(RSSecondEntryEnum.desc(saleReturnBatchShelfTipDTO.getSecondEntry()));
            long count = shelfDetailDTOList.stream().map(shelfDetailDTO -> StrUtil.join(StrUtil.COLON, shelfDetailDTO.getCargoCode(), shelfDetailDTO.getSkuCode())).distinct().count();
            saleReturnBatchShelfTipDTO.setSkuTypeQty(((int) count));
            saleReturnBatchShelfTipDTO.setSkuQty(shelfDetailDTOList.stream().map(ShelfDetailDTO::getSkuQty).reduce(BigDecimal.ZERO, BigDecimal::add));
            list.add(saleReturnBatchShelfTipDTO);
        }

        shelfDTOList.stream().findFirst().ifPresent(shelfDTO -> {
            for (SaleReturnBatchShelfTipDTO saleReturnBatchShelfTipDTO : list) {
                saleReturnBatchShelfTipDTO.setHold(ShelfMarkEnum.NumToEnum(shelfDTO.getMark()).contains(ShelfMarkEnum.HOLD));
            }
        });
        return list;
    }

    private List<ShelfCompleteDetailBizDTO> buildCompleteDetail(ShelfDTO shelfBiz, List<SaleReturnBatchShelfTipDTO> tipDTOList, Map<String, SkuLotDTO> skuLotDTOMap) {
        List<ShelfCompleteDetailBizDTO> shelfCompleteDetailBizDTOS = new ArrayList<>();
        for (ShelfDetailDTO entity : shelfBiz.getDetailList()) {
            ShelfCompleteDetailBizDTO shelfCompleteDetailBizDTO = new ShelfCompleteDetailBizDTO();
            BeanUtil.copyProperties(entity, shelfCompleteDetailBizDTO);
            shelfCompleteDetailBizDTO.setShelfSkuQty(entity.getSkuQty());
            SkuLotDTO skuLotDTO = skuLotDTOMap.get(entity.getSkuLotNo());
            if (null == skuLotDTO) throw ExceptionUtil.exceptionWithMessage("批次信息丢失");
            String location = tipDTOList.stream()
                    .filter(saleReturnBatchShelfTipDTO -> saleReturnBatchShelfTipDTO.getInventoryType().equalsIgnoreCase(skuLotDTO.getInventoryType()))
                    .map(SaleReturnBatchShelfTipDTO::getLocationCode)
                    .filter(StrUtil::isNotBlank)
                    .findFirst().orElseThrow(ExceptionUtil::dataError);
            shelfCompleteDetailBizDTO.setTargetLocationCode(location);
            shelfCompleteDetailBizDTOS.add(shelfCompleteDetailBizDTO);
        }
        return shelfCompleteDetailBizDTOS;
    }

    private void tipsCheck(List<SaleReturnBatchShelfTipDTO> first, List<SaleReturnBatchShelfTipDTO> second) {
        String signFirst = first.stream()
                .map(it -> StrUtil.join(StrUtil.COLON, it.getSkuQuality(), it.getInventoryType(), it.getSecondEntry(), it.getSkuTypeQty(), it.getSkuQty().stripTrailingZeros()))
                .sorted()
                .collect(Collectors.joining(StrUtil.COMMA));
        String signSecond = second.stream()
                .map(it -> StrUtil.join(StrUtil.COLON, it.getSkuQuality(), it.getInventoryType(), it.getSecondEntry(), it.getSkuTypeQty(), it.getSkuQty().stripTrailingZeros()))
                .sorted()
                .collect(Collectors.joining(StrUtil.COMMA));
        if (!signFirst.equalsIgnoreCase(signSecond)) {
            throw ExceptionUtil.DATA_ERROR;
        }
    }
}
