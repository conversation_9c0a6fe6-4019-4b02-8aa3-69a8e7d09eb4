package com.dt.platform.wms.transaction.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.enums.SeqEnum;
import com.dt.component.common.enums.SystemEventEnum;
import com.dt.component.common.enums.asn.AsnTypeEnum;
import com.dt.component.common.enums.bill.BillTypeEnum;
import com.dt.component.common.enums.bill.MessageMqStatusEnum;
import com.dt.component.common.enums.sku.SkuLifeCtrlEnum;
import com.dt.component.common.enums.sku.SkuTagEnum;
import com.dt.component.common.enums.stock.OperationTypeEnum;
import com.dt.component.common.exceptions.BaseException;
import com.dt.component.common.holder.CurrentUserHolder;
import com.dt.component.common.msg.StockOperationMessage;
import com.dt.domain.base.dto.ContainerDTO;
import com.dt.domain.base.dto.SkuDTO;
import com.dt.domain.base.dto.SkuLotDTO;
import com.dt.domain.base.dto.box.BoxSkuDTO;
import com.dt.domain.base.dto.box.BoxSkuDetailDTO;
import com.dt.domain.base.param.ContainerBatchParam;
import com.dt.domain.base.param.SkuParam;
import com.dt.domain.base.param.box.BoxSkuParam;
import com.dt.domain.bill.bo.ReceiptAndContCancelReceiptBillBO;
import com.dt.domain.bill.bo.ReceiptCompleteContainerCommitBillBO;
import com.dt.domain.bill.bo.receipt.ReceiptCommitBillBO;
import com.dt.domain.bill.dto.ReceiptDTO;
import com.dt.domain.bill.dto.message.MessageMqDTO;
import com.dt.domain.bill.dto.performance.SystemEventDTO;
import com.dt.platform.wms.biz.stock.biz.StockOperationHandler;
import com.dt.platform.wms.integration.*;
import com.dt.platform.wms.integration.box.IRemoteBoxSkuClient;
import com.dt.platform.wms.integration.contLog.IRemoteContainerLogClient;
import com.dt.platform.wms.integration.performance.IRemoteSystemEventClient;
import com.dt.platform.wms.integration.rec.IRemoteReceiptExtraClient;
import com.dt.platform.wms.integration.rec.IRemoteReceiptExtraDetailClient;
import com.dt.platform.wms.integration.tally.IRemoteTallyDetailClient;
import com.dt.platform.wms.transaction.IReceiptGtsService;
import com.dt.platform.wms.transaction.IStockSerialService;
import com.dt.platform.wms.transaction.bo.*;
import io.seata.spring.annotation.GlobalTransactional;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/9/27 16:06
 */
@Service
public class ReceiptGtsService implements IReceiptGtsService {

    @Resource
    private IRemoteSkuLotClient iRemoteSkuLotClient;

    @Resource
    private IRemoteAsnClient iRemoteAsnClient;

    @Resource
    private IRemoteAsnDetailClient remoteAsnDetailClient;

    @Resource
    private IRemoteReceiptClient iRemoteReceiptClient;

    @Resource
    IRemoteReceiptDetailClient remoteReceiptDetailClient;

    @Resource
    private IRemoteReceiptExtraClient remoteReceiptExtraClient;

    @Resource
    private IRemoteReceiptExtraDetailClient remoteReceiptExtraDetailClient;

    @Resource
    private IRemoteShelfClient iRemoteShelfClient;

    @Resource
    private IRemoteShelfDetailClient remoteShelfDetailClient;

    @Resource
    private IRemoteStockClient remoteStockClient;

    @Resource
    private IRemoteStockZoneClient remoteStockZoneClient;

    @Resource
    private IRemoteStockLocationClient remoteStockLocationClient;

    @Resource
    private IRemoteContainerClient remoteContainerClient;

    @Resource
    private IRemoteContainerLogClient remoteContainerLogClient;

    @Resource
    private IStockSerialService stockSerialService;

    @Resource
    private IRemoteTallyDetailClient remoteTallyDetailClient;

    @Resource
    private IRemoteSystemEventClient remoteSystemEventClient;

    @Resource
    StockOperationHandler stockOperationHandler;

    @Resource
    IRemoteBoxSkuClient remoteBoxSkuClient;

    @Resource
    IRemoteSkuClient remoteSkuClient;

    @Resource
    private IRemoteSeqRuleClient remoteSeqRuleClient;

    @Resource
    IRemoteMessageClient remoteMessageClient;

    @Override
    public void receiptCommitContext(ReceiptCommitBO receiptCommitBO) throws Exception {
        // 人效记录事件
        SystemEventDTO systemEventDTO = buildSystemEventDTO(receiptCommitBO.getReceiptDTO());
        //组装事件
        MessageMqDTO messageMqDTO = new MessageMqDTO();
        messageMqDTO.setWarehouseCode(receiptCommitBO.getReceiptDTO().getWarehouseCode());
        messageMqDTO.setCargoCode(receiptCommitBO.getReceiptDTO().getCargoCode());
        messageMqDTO.setBillNo(receiptCommitBO.getReceiptDTO().getRecId());
        if (receiptCommitBO.getAsnDTO().getType().equalsIgnoreCase(AsnTypeEnum.DISTRIBUTE_RETURN.getCode())) {
            messageMqDTO.setOperationType(OperationTypeEnum.OPERATION_CIRCLE_GOODS_COMPLETE_CONTAINER.getType());
        } else {
            messageMqDTO.setOperationType(OperationTypeEnum.OPERATION_COMPLETE_CONTAINER.getType());
        }
        messageMqDTO.setBillType(BillTypeEnum.BILL_TYPE_RECEIPT_LOT.getType());
        messageMqDTO.setCreatedTime(System.currentTimeMillis());
        messageMqDTO.setStatus(MessageMqStatusEnum.CREATE_STATUS.getCode());

        ReceiptCommitBillBO receiptCommitBillBO = new ReceiptCommitBillBO();
        receiptCommitBillBO.setReceiptDTO(receiptCommitBO.getReceiptDTO());
        receiptCommitBillBO.setShelfDTO(receiptCommitBO.getShelfDTO());
        receiptCommitBillBO.setAsnDTO(receiptCommitBO.getAsnDTO());
        receiptCommitBillBO.setSystemEventDTO(systemEventDTO);
        receiptCommitBillBO.setMessageMqDTO(messageMqDTO);
        Boolean isCommit = iRemoteReceiptClient.commitReceiptBill(receiptCommitBillBO);
        if (!isCommit) {
            throw new BaseException(BaseBizEnum.TIP, "提交数据异常");
        }
        //发送消息处理库存
        try {
            StockOperationMessage stockOperationMessage = new StockOperationMessage();
            stockOperationMessage.setBillNo(receiptCommitBO.getReceiptDTO().getRecId());
            stockOperationMessage.setWarehouseCode(receiptCommitBO.getReceiptDTO().getWarehouseCode());
            if (receiptCommitBO.getAsnDTO().getType().equalsIgnoreCase(AsnTypeEnum.DISTRIBUTE_RETURN.getCode())) {
                stockOperationMessage.setOperationType(OperationTypeEnum.OPERATION_CIRCLE_GOODS_COMPLETE_CONTAINER.getType());
            } else {
                stockOperationMessage.setOperationType(OperationTypeEnum.OPERATION_COMPLETE_CONTAINER.getType());
            }
            stockOperationMessage.setBillNoList(Arrays.asList(receiptCommitBO.getReceiptDTO().getRecId()));
            stockOperationMessage.setCargoCode(receiptCommitBO.getReceiptDTO().getCargoCode());
            remoteMessageClient.sendStockOperationMessageWithNoTX(stockOperationMessage);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private SystemEventDTO buildSystemEventDTO(ReceiptDTO receiptDTO) {
        SystemEventDTO systemEventDTO = new SystemEventDTO();
        systemEventDTO.setType(SystemEventEnum.IN_STOCK.getCode());
        systemEventDTO.setWarehouseCode(receiptDTO.getWarehouseCode());
        systemEventDTO.setCargoCode(receiptDTO.getCargoCode());
        systemEventDTO.setWorker(CurrentUserHolder.getUserName());
        systemEventDTO.setWorkTime(System.currentTimeMillis());
        systemEventDTO.setWorkDate(DateUtil.parse(DateUtil.date(System.currentTimeMillis()).toDateStr()).getTime());
        systemEventDTO.setBillNo(receiptDTO.getRecId());
        return systemEventDTO;
    }

    @Override
    public void receiptSkuCommitContext(ReceiptSkuCommitBO receiptSkuCommitBO) {
        //提交Sku批次 TODO 不走事务，批次可以多,不影响数据
        if (receiptSkuCommitBO.getSkuLotDTO() != null && StringUtils.isEmpty(receiptSkuCommitBO.getSkuLotDTO().getId())) {
            //处理套盒批次
            SkuDTO skuDTO = remoteSkuClient.querySkuByCode(receiptSkuCommitBO.getSkuLotDTO().getCargoCode(), receiptSkuCommitBO.getSkuLotDTO().getSkuCode());
            if (SkuTagEnum.NumToEnum(skuDTO.getSkuTag()).contains(SkuTagEnum.BOX_SKU)) {
                List<SkuLotDTO> commitSkyLotList = buildSkuLotNoList(receiptSkuCommitBO.getSkuLotDTO());
                iRemoteSkuLotClient.commitSkuLot(commitSkyLotList);
            } else {
                iRemoteSkuLotClient.commitSkuLot(Arrays.asList(receiptSkuCommitBO.getSkuLotDTO()));
            }
        }
        //提交收货作业批次
        if (receiptSkuCommitBO.getScanSnReceiveDTO() != null) {
            receiptSkuCommitBO.getReceiptDTO().setScanSnReceiveDTO(receiptSkuCommitBO.getScanSnReceiveDTO());
        }
        iRemoteReceiptClient.commitReceipt(receiptSkuCommitBO.getReceiptDTO());
    }

    /**
     * @param skuLotDTO
     * @return java.util.List<com.dt.domain.base.dto.SkuLotDTO>
     * <AUTHOR>
     * @describe:
     * @date 2022/12/8 13:31
     */
    private List<SkuLotDTO> buildSkuLotNoList(SkuLotDTO skuLotDTO) {
        BoxSkuParam boxSkuParam = new BoxSkuParam();
        boxSkuParam.setCargoCode(skuLotDTO.getCargoCode());
        boxSkuParam.setBoxSkuCode(skuLotDTO.getSkuCode());
        List<BoxSkuDTO> boxSkuDTOList = remoteBoxSkuClient.getBoxSkuByBoxSkuEffective(boxSkuParam);
        if (CollectionUtils.isEmpty(boxSkuDTOList)) {
            throw new BaseException(BaseBizEnum.TIP, "套盒商品数据异常");
        }
        BoxSkuDTO boxSkuDTO = boxSkuDTOList.get(0);
        if (CollectionUtils.isEmpty(boxSkuDTO.getDetailList())) {
            throw new BaseException(BaseBizEnum.TIP, "套盒商品数据异常");
        }
        SkuParam skuParam = new SkuParam();
        skuParam.setCargoCode(skuLotDTO.getCargoCode());
        skuParam.setCodeList(boxSkuDTO.getDetailList().stream().map(BoxSkuDetailDTO::getChildSkuCode).collect(Collectors.toList()));
        List<SkuDTO> skuDTOList = remoteSkuClient.getList(skuParam);
        if (CollectionUtils.isEmpty(skuDTOList)) {
            throw new BaseException(BaseBizEnum.TIP, "套盒子商品数据异常");
        }
        List<SkuLotDTO> newSkuLotList = new ArrayList<>();
        newSkuLotList.add(skuLotDTO);
        //处理套盒数据
        boxSkuDTO.getDetailList().forEach(boxSkuDetailDTO -> {
            SkuLotDTO copySkuLotDTO = ObjectUtil.cloneByStream(skuLotDTO);
            //重置ID和商品编码,批次编码
            copySkuLotDTO.setId(null);
            copySkuLotDTO.setSkuCode(boxSkuDetailDTO.getChildSkuCode());
            copySkuLotDTO.setCode(remoteSeqRuleClient.findSequence(SeqEnum.SKU_LOT_CODE_000001));
            SkuDTO childSkuDTO = skuDTOList.stream()
                    .filter(a -> a.getCode().equalsIgnoreCase(boxSkuDetailDTO.getChildSkuCode()))
                    .filter(a -> a.getCargoCode().equalsIgnoreCase(boxSkuDetailDTO.getCargoCode())).findFirst().orElse(null);
            if (childSkuDTO.getIsLifeMgt().equals(SkuLifeCtrlEnum.SKU_LIFE_CTRL_NO.getCode())) {
                copySkuLotDTO.setExpireDate(0L);
                copySkuLotDTO.setManufDate(0L);
            }
            copySkuLotDTO.setLinkSkuLotNo(skuLotDTO.getCode());
            newSkuLotList.add(copySkuLotDTO);
        });
        return newSkuLotList;
    }


    @Override
    public void receiptCancelCommitContext(ReceiptCancelBO receiptCancelBO) {
        //更新收货作业批次状态
        Boolean commitReceipt = iRemoteReceiptClient.commitReceipt(receiptCancelBO.getReceiptDTO());
        if (!commitReceipt) {
            throw new BaseException(BaseBizEnum.TIP, "收货作业批次释放异常");
        }
        //取消容器
        ContainerBatchParam containerBatchParam = new ContainerBatchParam();
        List<ContainerDTO> containerList = new ArrayList<>();
        containerList.add(receiptCancelBO.getContainer());
        containerBatchParam.setContainerList(containerList);
        Boolean modifyBatch = remoteContainerClient.modifyBatch(containerBatchParam);
        if (!modifyBatch) {
            throw new BaseException(BaseBizEnum.TIP, "容器释放异常");
        }
        //容器日志记录
        remoteContainerLogClient.save(receiptCancelBO.getContainerLogDTO());
    }

    @Override
    public void receiptExtraCancelCommitContext(ReceiptExtraCancelBO receiptCancelBO) {
        receiptCancelBO.getReceiptExtraDTO().setDetailDTOList(receiptCancelBO.getReceiptExtraDetailDTOList());
        //更新收货作业批次状态
        Boolean modifyCommitContext = remoteReceiptExtraClient.modifyCommitContext(receiptCancelBO.getReceiptExtraDTO());
        if (!modifyCommitContext) {
            throw new BaseException(BaseBizEnum.TIP, "多货收货作业批次释放异常");
        }
        ContainerBatchParam containerBatchParam = new ContainerBatchParam();
        List<ContainerDTO> containerList = new ArrayList<>();
        containerList.add(receiptCancelBO.getContainer());
        containerBatchParam.setContainerList(containerList);
        remoteContainerClient.modifyBatch(containerBatchParam);

        //容器日志记录
        remoteContainerLogClient.save(receiptCancelBO.getContainerLogDTO());
    }

    @Override
    public void receiptAndContAndStockCancel(ReceiptAndContCancelReceiptBO receiptAndContCancelReceiptBO) {
        ReceiptAndContCancelReceiptBillBO receiptAndContCancelReceiptBillBO = new ReceiptAndContCancelReceiptBillBO();
        receiptAndContCancelReceiptBillBO.setReceiptDTO(receiptAndContCancelReceiptBO.getReceiptDTO());
        receiptAndContCancelReceiptBillBO.setReceiptDetailDTOList(receiptAndContCancelReceiptBO.getReceiptDetailDTOList());
        receiptAndContCancelReceiptBillBO.setAsnDTOList(Arrays.asList(receiptAndContCancelReceiptBO.getAsnDTO()));
        receiptAndContCancelReceiptBillBO.setAsnLogDTOList(Arrays.asList(receiptAndContCancelReceiptBO.getAsnLogDTO()));
        receiptAndContCancelReceiptBillBO.setAsnDetailDTOList(receiptAndContCancelReceiptBO.getAsnDetailDTOList());
        receiptAndContCancelReceiptBillBO.setShelfDTOList(receiptAndContCancelReceiptBO.getShelfDTOList());
        receiptAndContCancelReceiptBillBO.setShelfDetailDTOList(receiptAndContCancelReceiptBO.getShelfDetailDTOList());
        //组装事件
        MessageMqDTO messageMqDTO = new MessageMqDTO();
        messageMqDTO.setWarehouseCode(receiptAndContCancelReceiptBO.getReceiptDTO().getWarehouseCode());
        messageMqDTO.setCargoCode(receiptAndContCancelReceiptBO.getReceiptDTO().getCargoCode());
        messageMqDTO.setBillNo(receiptAndContCancelReceiptBO.getReceiptDTO().getRecId());
        if (receiptAndContCancelReceiptBO.getAsnDTO().getType().equalsIgnoreCase(AsnTypeEnum.DISTRIBUTE_RETURN.getCode())) {
            messageMqDTO.setOperationType(OperationTypeEnum.OPERATION_CANCEL_CIRCLE_GOODS_COMPLETE_CONTAINER.getType());
        } else {
            messageMqDTO.setOperationType(OperationTypeEnum.OPERATION_RECEIPT_CANCEL.getType());
        }
        messageMqDTO.setBillType(BillTypeEnum.BILL_TYPE_RECEIPT_LOT.getType());
        messageMqDTO.setCreatedTime(System.currentTimeMillis());
        messageMqDTO.setStatus(MessageMqStatusEnum.CREATE_STATUS.getCode());
        receiptAndContCancelReceiptBillBO.setMessageMqDTO(messageMqDTO);

        Boolean isCommit = iRemoteReceiptClient.receiptAndContAndCancel(receiptAndContCancelReceiptBillBO);
        //处理库存

        //发送消息处理库存
        try {
            StockOperationMessage stockOperationMessage = new StockOperationMessage();
            stockOperationMessage.setWarehouseCode(receiptAndContCancelReceiptBO.getReceiptDTO().getWarehouseCode());

            OperationTypeEnum operationTypeEnum = OperationTypeEnum.OPERATION_RECEIPT_CANCEL;
            if (AsnTypeEnum.DISTRIBUTE_RETURN.getCode().equalsIgnoreCase(receiptAndContCancelReceiptBO.getAsnDTO().getType())) {
                operationTypeEnum = OperationTypeEnum.OPERATION_CANCEL_CIRCLE_GOODS_COMPLETE_CONTAINER;
            }
            stockOperationMessage.setBillNoList(Arrays.asList(receiptAndContCancelReceiptBO.getReceiptDTO().getRecId()));
            stockOperationMessage.setOperationType(operationTypeEnum.getType());
            stockOperationMessage.setCargoCode(receiptAndContCancelReceiptBO.getReceiptDTO().getCargoCode());
            remoteMessageClient.sendStockOperationMessageWithNoTX(stockOperationMessage);
        } catch (Exception e) {
            e.printStackTrace();
        }
        //容器释放和日志
        remoteContainerLogClient.saveBatch(receiptAndContCancelReceiptBO.getContainerLogDTOList());
        remoteContainerClient.modifyBatch(receiptAndContCancelReceiptBO.getContainerDTOList());
    }

    @Override
    public void commitReceiptByContainer(ReceiptCompleteContainerCommitBO receiptCompleteContainerCommitBO) {
        ReceiptCompleteContainerCommitBillBO receiptCompleteContainerCommitBillBO = new ReceiptCompleteContainerCommitBillBO();
        receiptCompleteContainerCommitBillBO.setReceiptDTO(receiptCompleteContainerCommitBO.getReceiptDTO());
        receiptCompleteContainerCommitBillBO.setReceiptDetailDTOList(receiptCompleteContainerCommitBO.getReceiptDetailDTOList());
        receiptCompleteContainerCommitBillBO.setAsnDetailDTOList(receiptCompleteContainerCommitBO.getAsnDetailDTOList());
        receiptCompleteContainerCommitBillBO.setAsnDTOList(receiptCompleteContainerCommitBO.getAsnDTOList());
        receiptCompleteContainerCommitBillBO.setAsnLogDTOList(receiptCompleteContainerCommitBO.getAsnLogDTOList());
        receiptCompleteContainerCommitBillBO.setShelfDTO(receiptCompleteContainerCommitBO.getShelfDTO());
        receiptCompleteContainerCommitBillBO.setShelfDetailDTOList(receiptCompleteContainerCommitBO.getShelfDetailDTOList());
        // 人效记录事件
        SystemEventDTO systemEventDTO = buildSystemEventDTO(receiptCompleteContainerCommitBO.getReceiptDTO());
        receiptCompleteContainerCommitBillBO.setSystemEventDTO(systemEventDTO);

        //组装事件
        MessageMqDTO messageMqDTO = new MessageMqDTO();
        messageMqDTO.setWarehouseCode(receiptCompleteContainerCommitBO.getReceiptDTO().getWarehouseCode());
        messageMqDTO.setCargoCode(receiptCompleteContainerCommitBO.getReceiptDTO().getCargoCode());
        messageMqDTO.setBillNo(receiptCompleteContainerCommitBO.getReceiptDTO().getRecId());
        messageMqDTO.setOperationType(OperationTypeEnum.OPERATION_COMPLETE_CONTAINER.getType());
        messageMqDTO.setBillType(BillTypeEnum.BILL_TYPE_RECEIPT_LOT.getType());
        messageMqDTO.setCreatedTime(System.currentTimeMillis());
        messageMqDTO.setStatus(MessageMqStatusEnum.CREATE_STATUS.getCode());
        receiptCompleteContainerCommitBillBO.setMessageMqDTO(messageMqDTO);

        Boolean isCommit = iRemoteReceiptClient.commitReceiptByContainer(receiptCompleteContainerCommitBillBO);
        //发送消息处理库存
        try {
            StockOperationMessage stockOperationMessage = new StockOperationMessage();
            stockOperationMessage.setBillNo(receiptCompleteContainerCommitBO.getReceiptDTO().getRecId());
            stockOperationMessage.setWarehouseCode(receiptCompleteContainerCommitBO.getReceiptDTO().getWarehouseCode());
            OperationTypeEnum operationTypeEnum = OperationTypeEnum.OPERATION_COMPLETE_CONTAINER;
            stockOperationMessage.setBillNoList(Arrays.asList(receiptCompleteContainerCommitBO.getReceiptDTO().getRecId()));
            stockOperationMessage.setOperationType(operationTypeEnum.getType());
            stockOperationMessage.setCargoCode(receiptCompleteContainerCommitBO.getReceiptDTO().getCargoCode());
            remoteMessageClient.sendStockOperationMessageWithNoTX(stockOperationMessage);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
//    @GlobalTransactional(timeoutMills = 300000, rollbackFor = Exception.class, name = "gts-receipt-by-cont-return-context-cancel-Commit")
    public void receiptAndContAndStockCancelToReturn(ReceiptAndContCancelReturnReceiptBO receiptAndContCancelReturnReceiptBO) {
        ReceiptAndContCancelReceiptBillBO receiptAndContCancelReceiptBillBO = new ReceiptAndContCancelReceiptBillBO();
        receiptAndContCancelReceiptBillBO.setReceiptDTO(receiptAndContCancelReturnReceiptBO.getReceiptDTO());
        receiptAndContCancelReceiptBillBO.setReceiptDetailDTOList(receiptAndContCancelReturnReceiptBO.getReceiptDetailDTOList());
        receiptAndContCancelReceiptBillBO.setAsnDTOList(receiptAndContCancelReturnReceiptBO.getAsnDTOList());
        receiptAndContCancelReceiptBillBO.setAsnLogDTOList(receiptAndContCancelReturnReceiptBO.getAsnLogDTOList());
        receiptAndContCancelReceiptBillBO.setAsnDetailDTOList(receiptAndContCancelReturnReceiptBO.getAsnDetailDTOList());
        receiptAndContCancelReceiptBillBO.setShelfDTOList(receiptAndContCancelReturnReceiptBO.getShelfDTOList());
        receiptAndContCancelReceiptBillBO.setShelfDetailDTOList(receiptAndContCancelReturnReceiptBO.getShelfDetailDTOList());
        //组装事件
        MessageMqDTO messageMqDTO = new MessageMqDTO();
        messageMqDTO.setWarehouseCode(receiptAndContCancelReturnReceiptBO.getReceiptDTO().getWarehouseCode());
        messageMqDTO.setCargoCode(receiptAndContCancelReturnReceiptBO.getReceiptDTO().getCargoCode());
        messageMqDTO.setBillNo(receiptAndContCancelReturnReceiptBO.getReceiptDTO().getRecId());

        messageMqDTO.setOperationType(OperationTypeEnum.OPERATION_RECEIPT_CANCEL.getType());
        messageMqDTO.setBillType(BillTypeEnum.BILL_TYPE_RECEIPT_LOT.getType());
        messageMqDTO.setCreatedTime(System.currentTimeMillis());
        messageMqDTO.setStatus(MessageMqStatusEnum.CREATE_STATUS.getCode());
        receiptAndContCancelReceiptBillBO.setMessageMqDTO(messageMqDTO);
        Boolean isCommit = iRemoteReceiptClient.receiptAndContAndCancel(receiptAndContCancelReceiptBillBO);
        //发送消息处理库存
        try {
            StockOperationMessage stockOperationMessage = new StockOperationMessage();
            stockOperationMessage.setWarehouseCode(receiptAndContCancelReturnReceiptBO.getReceiptDTO().getWarehouseCode());
            OperationTypeEnum operationTypeEnum = OperationTypeEnum.OPERATION_RECEIPT_CANCEL;
            stockOperationMessage.setBillNoList(Arrays.asList(receiptAndContCancelReturnReceiptBO.getReceiptDTO().getRecId()));
            stockOperationMessage.setOperationType(operationTypeEnum.getType());
            stockOperationMessage.setCargoCode(receiptAndContCancelReturnReceiptBO.getReceiptDTO().getCargoCode());
            remoteMessageClient.sendStockOperationMessageWithNoTX(stockOperationMessage);
        } catch (Exception e) {
            e.printStackTrace();
        }
        //容器释放和日志
        remoteContainerLogClient.saveBatch(receiptAndContCancelReturnReceiptBO.getContainerLogDTOList());
        remoteContainerClient.modifyBatch(receiptAndContCancelReturnReceiptBO.getContainerDTOList());

    }

}
