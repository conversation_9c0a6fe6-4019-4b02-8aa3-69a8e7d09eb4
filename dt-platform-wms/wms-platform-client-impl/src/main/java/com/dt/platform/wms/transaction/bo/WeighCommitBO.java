package com.dt.platform.wms.transaction.bo;

import com.dt.domain.bill.dto.*;
import com.dt.domain.bill.dto.sourceCode.OutSourceCodeDTO;
import com.dt.domain.core.stock.dto.StockTransactionDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="WeighCommitBO", description="交接单提交称重操作")
public class WeighCommitBO extends AbsWarehouseBO  implements java.io.Serializable  {

    @ApiModelProperty("出库单")
    private ShipmentOrderDTO shipmentOrder;

    @ApiModelProperty("明细")
    private List<ShipmentOrderDetailDTO> shipmentOrderDetailList;

    @ApiModelProperty("包裹信息")
    private PackageDTO pack;

    @ApiModelProperty("包裹明细信息")
    private List<PackageDetailDTO> packageDetailList;

    @ApiModelProperty("出库交接单")
    private HandoverDTO handover;

    @ApiModelProperty("出库交接明细")
    private HandoverDetailDTO handoverDetail;

//    @ApiModelProperty("包裹分配明细")
//    private List<AllocationOrderDTO> allocationOrderList;

    @ApiModelProperty("包裹日志")
    private PackageLogDTO packageLog;

    @ApiModelProperty("出库单日志")
    private ShipmentOrderLogDTO shipmentOrderLog;

    @ApiModelProperty("溯源码")
    List<OutSourceCodeDTO> outSourceCodeDTOList;

    @ApiModelProperty("核销")
    List<StockTransactionDTO> stockTransactionDTOList;

}