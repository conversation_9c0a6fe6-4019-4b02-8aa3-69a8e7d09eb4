package com.dt.platform.wms.client;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.dt.component.common.dto.BaseDTO;
import com.dt.component.common.enums.*;
import com.dt.component.common.enums.base.WorkBenchStatusEnum;
import com.dt.component.common.enums.base.WorkBenchTypeEnum;
import com.dt.component.common.enums.bill.OrderTagEnum;
import com.dt.component.common.enums.bill.ShipmentOrderEnum;
import com.dt.component.common.enums.cargo.CargoConfigParamEnum;
import com.dt.component.common.enums.cargo.CargoConfigStatusEnum;
import com.dt.component.common.enums.material.Is4PLEnum;
import com.dt.component.common.enums.material.MaterialTagEnum;
import com.dt.component.common.enums.material.MaterialTypeEnum;
import com.dt.component.common.enums.pick.PickBatchCheckEnum;
import com.dt.component.common.enums.pick.PickEnum;
import com.dt.component.common.enums.pick.PickErrorEnum;
import com.dt.component.common.enums.pkg.*;
import com.dt.component.common.enums.rec.ReceivingWorkBenchError;
import com.dt.component.common.enums.rs.OpAbnormalTypeEnum;
import com.dt.component.common.enums.rs.OpExceptionCallbackStateEnum;
import com.dt.component.common.enums.rs.OpScenarioTypeEnum;
import com.dt.component.common.enums.sku.SkuQualityEnum;
import com.dt.component.common.enums.sku.SkuStatusEnum;
import com.dt.component.common.enums.sku.SkuTagEnum;
import com.dt.component.common.enums.sku.SkuUpcDefaultEnum;
import com.dt.component.common.enums.wave.CollectWaveErrorEnum;
import com.dt.component.common.exceptions.BaseException;
import com.dt.component.common.holder.CurrentRouteHolder;
import com.dt.component.common.holder.CurrentUserHolder;
import com.dt.component.common.result.Result;
import com.dt.component.common.vo.IdNameVO;
import com.dt.domain.base.dto.*;
import com.dt.domain.base.dto.sku.SkuMaterialDTO;
import com.dt.domain.base.param.PackageMaterialParam;
import com.dt.domain.base.param.SkuParam;
import com.dt.domain.base.param.SkuUomParam;
import com.dt.domain.base.param.SkuUpcParam;
import com.dt.domain.base.param.sku.SkuMaterialParam;
import com.dt.domain.bill.bo.BatchCheckBO;
import com.dt.domain.bill.bo.PackCheckBackBO;
import com.dt.domain.bill.dto.*;
import com.dt.domain.bill.dto.material.MaterialUseRecordDTO;
import com.dt.domain.bill.dto.material.ShipmentDetailMaterialOtherDTO;
import com.dt.domain.bill.dto.performance.SystemEventDTO;
import com.dt.domain.bill.dto.sourceCode.OutSourceCodeDTO;
import com.dt.domain.bill.param.*;
import com.dt.domain.bill.param.material.MaterialUseRecordParam;
import com.dt.domain.bill.param.sourceCode.OutSourceCodeParam;
import com.dt.domain.stock.dto.sn.SnStockDTO;
import com.dt.domain.stock.param.sn.SnStockParam;
import com.dt.platform.utils.ConverterUtil;
import com.dt.platform.wms.biz.*;
import com.dt.platform.wms.biz.config.WmsOtherConfig;
import com.dt.platform.wms.biz.dto.PackCheckSkuAndAllocationDTO;
import com.dt.platform.wms.biz.taotian.DefaultOpexceptionReport;
import com.dt.platform.wms.biz.taotian.DefaultOpexceptionReportAbnormalLine;
import com.dt.platform.wms.biz.taotian.DefaultOpexceptionReportSenderInfo;
import com.dt.platform.wms.dto.check.*;
import com.dt.platform.wms.dto.check.back.PackBackPickBizDTO;
import com.dt.platform.wms.dto.check.back.PackExpressBizDTO;
import com.dt.platform.wms.dto.material.MaterialCalculateResultV2;
import com.dt.platform.wms.integration.*;
import com.dt.platform.wms.integration.material.IRemoteMaterialUseRecordClient;
import com.dt.platform.wms.integration.material.IRemoteStructMaterialClient;
import com.dt.platform.wms.integration.mercury.IRemoteMercuryClient;
import com.dt.platform.wms.integration.performance.IRemoteSystemEventClient;
import com.dt.platform.wms.integration.sku.IRemoteSkuMaterialClient;
import com.dt.platform.wms.integration.sn.IRemoteSnStockClient;
import com.dt.platform.wms.integration.sourceCode.IRemoteOutSourceCodeClient;
import com.dt.platform.wms.param.CodeParam;
import com.dt.platform.wms.param.check.*;
import com.dt.platform.wms.param.material.MaterialCalculateParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2020/10/18 16:22
 */
@DubboService(version = "${dubbo.service.version}")
@Slf4j
public class B2CCheckBenchBizClientImpl implements IB2CCheckBenchBizClient {

    @Resource
    IPackageMaterialBiz packageMaterialBiz;

    @Resource
    IRemotePackageClient iRemotePackageClient;

    @Resource
    IRemoteShipmentOrderClient iRemoteShipmentOrderClient;

    @Resource
    IRemotePickClient iRemotePickClient;

    @Resource
    IRemotePickDetailClient remotePickDetailClient;

    @Resource
    IRemoteOrderInterceptClient interceptClient;

    @Resource
    IRemoteWorkBenchClient iRemoteWorkBenchClient;

    @Resource
    IRemoteDecimalPlaceClient iRemoteDecimalPlaceClient;

    @Resource
    IRemoteSkuClient iRemoteSkuClient;

    @Resource
    IRemoteSnStockClient remoteSnStockClient;

    @Resource
    IRemoteSkuMaterialClient remoteSkuMaterialClient;

    @Resource
    IRemoteCargoConfigClient iRemoteCargoConfigClient;

    @Resource
    IRemotePackageMaterialClient iRemotePackageMaterialClient;

    @Resource
    IRemoteSeqRuleClient iRemoteSeqRuleClient;

    @Resource
    IRemoteBillContextClient iRemoteBillContextClient;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    InterceptionManagerBiz interceptionManagerBiz;

    @Resource
    IRemoteCargoConfigClient remoteCargoConfigClient;

    @Resource
    ICommitPackBiz iCommitPackBiz;

    @Resource
    IBusinessLogBiz iBusinessLogBiz;

    @Resource
    IRemoteOutSourceCodeClient remoteOutSourceCodeClient;

    @Resource
    IRemoteSystemEventClient remoteSystemEventClient;

    @Resource
    ICheckPackMaterialBiz iCheckPackMaterialBiz;

    @Resource
    IRemoteWarehouseClient remoteWarehouseClient;

    @Resource
    WmsOtherConfig wmsOtherConfig;

    @Resource
    private ICheckPackWeightAndVolumeBiz iCheckPackWeightAndVolumeBiz;

    @Resource
    private IRemoteMaterialUseRecordClient remoteMaterialUseRecordClient;

    @Resource
    private IStructMaterialBiz structMaterialBiz;

    @Resource
    private IRemoteStructMaterialClient remoteStructMaterialClient;

    @Resource
    private IRemoteMercuryClient remoteMercuryClient;

    @Resource
    private RemoteTenantHelper remoteTenantHelper;

    @Override
    public Result<PackBackPickBizDTO> checkPickCode(PackCheckBackParam param) {
        PickDTO pickDTO = checkPick(param.getPickCode());
        if (!pickDTO.getPickFlag().equals(PickEnum.PickFlagEnum.ORIGIN.getCode())) {
            throw new BaseException(BaseBizEnum.TIP, "拣选单复核工作台,不允许合单主单和子拣选单复核");
        }
        PickParam pickParam = new PickParam();
        pickParam.setPickCode(pickDTO.getPickCode());
        List<PickDetailDTO> pickDetailDTOList = iRemotePickClient.getPickDetailList(pickParam);
        if (CollectionUtils.isEmpty(pickDetailDTOList)) {
            throw new BaseException(PickErrorEnum.PACK_DETAIL_DATA_ERROR, param.getPickCode());
        }
        int formatNum = iRemoteDecimalPlaceClient.getNumberFormat(pickDTO.getWarehouseCode(), pickDTO.getCargoCode());
        PackBackPickBizDTO packBackPickBizDTO = new PackBackPickBizDTO();
        packBackPickBizDTO.setPickCode(pickDTO.getPickCode());
        BigDecimal expQty = pickDetailDTOList.stream().filter(a -> !Objects.equals(a.getPackageStatus(), PackEnum.STATUS.PART_ASSIGN_STATUS.getCode())).filter(a -> !Objects.equals(a.getPackageStatus(), PackEnum.STATUS.PART_ASSIGN_CANCEL_STATUS.getCode())).map(PickDetailDTO::getPickQty).reduce(BigDecimal.ZERO, BigDecimal::add);
        packBackPickBizDTO.setExpQty(expQty.setScale(formatNum, RoundingMode.FLOOR));

        BigDecimal checkQty = pickDetailDTOList.stream().filter(a -> !Objects.equals(a.getPackageStatus(), PackEnum.STATUS.PART_ASSIGN_STATUS.getCode())).filter(a -> !Objects.equals(a.getPackageStatus(), PackEnum.STATUS.PART_ASSIGN_CANCEL_STATUS.getCode())).map(PickDetailDTO::getCheckQty).reduce(BigDecimal.ZERO, BigDecimal::add);
        packBackPickBizDTO.setCheckQty(checkQty.setScale(formatNum, RoundingMode.FLOOR));

        packBackPickBizDTO.setCheckPackNum((int) pickDetailDTOList.stream().filter(a -> !Objects.equals(a.getPackageStatus(), PackEnum.STATUS.PART_ASSIGN_STATUS.getCode())).filter(a -> !Objects.equals(a.getPackageStatus(), PackEnum.STATUS.PART_ASSIGN_CANCEL_STATUS.getCode())).filter(entity -> Objects.equals(entity.getPackageStatus(), PackEnum.STATUS.CHECK_COMPELETE_STATUS.getCode())).count());
        packBackPickBizDTO.setExpPackNum((int) pickDetailDTOList.stream().filter(a -> !Objects.equals(a.getPackageStatus(), PackEnum.STATUS.PART_ASSIGN_STATUS.getCode())).filter(a -> !Objects.equals(a.getPackageStatus(), PackEnum.STATUS.PART_ASSIGN_CANCEL_STATUS.getCode())).count());
        //标识拣选单复核
        packBackPickBizDTO.setFlag("pick");
        //拣选结束状态---修改为质检开始
        if (Objects.equals(pickDTO.getStatus(), PickEnum.PickStatusEnum.PICK_END_STATUS.getCode()) || Objects.equals(pickDTO.getStatus(), PickEnum.PickStatusEnum.SPLIT_END_STATUS.getCode())) {
            pickDTO.setStatus(PickEnum.PickStatusEnum.ZJ_BEGIN_STATUS.getCode());
            pickDTO.setCheckStartTime(System.currentTimeMillis());
            iRemotePickClient.update(pickDTO);
        }
        packBackPickBizDTO.setType(pickDTO.getType());
        packBackPickBizDTO.setTypeName(PickEnum.PickOrderTypeEnum.matchOpCode(pickDTO.getType()).getMessage());
        return Result.success(packBackPickBizDTO);
    }

    /**
     * @param code
     * @return com.dt.domain.bill.dto.PickDTO
     * @author: WuXian
     * description:  检查拣选单
     * create time: 2021/11/1 14:27
     */
    private PickDTO checkPick(String code) {
        PickDTO pickDTO = iRemotePickClient.queryPickByPickCode(code);
        if (pickDTO == null) {
            throw new BaseException(PackCheckErrorEnum.PICK_DATA_ERROR, code);
        }
        //
        if (!Objects.equals(pickDTO.getBusinessType(), ShipmentOrderEnum.BUSSINESS_TYPE.B2C.name())) {
            throw new BaseException(BaseBizEnum.TIP, "当前复核数据不属于B2C");
        }
        //拣选单取消
        if (Objects.equals(pickDTO.getStatus(), PickEnum.PickStatusEnum.CANCEL_STATUS.getCode())) {
            throw new BaseException(PackCheckErrorEnum.PICK_CANCLEE, code);
        }
        //拣选单未确认
        if (Objects.equals(pickDTO.getStatus(), PickEnum.PickStatusEnum.CREATE_STATUS.getCode())) {
            throw new BaseException(PackCheckErrorEnum.PICK_FROBIDDEN, code);
        }
        //拣选单未开始拣选
        if (Objects.equals(pickDTO.getStatus(), PickEnum.PickStatusEnum.CONFIRM_STATUS.getCode())) {
            throw new BaseException(PackCheckErrorEnum.PICK_NO_START, code);
        }
        //拣选单未交回
        if (Objects.equals(pickDTO.getStatus(), PickEnum.PickStatusEnum.PICK_BEGIN_STATUS.getCode())) {
            throw new BaseException(PickErrorEnum.PICK_NO_RESTORE, code);
        }
        //拣选单已质检
        if (Objects.equals(pickDTO.getStatus(), PickEnum.PickStatusEnum.ZJ_END_STATUS.getCode())) {
            throw new BaseException(BaseBizEnum.TIP_MESSAGE_END, "当前拣选单已复核结束");
        }
        //RF作业+先拣后分+B2C的单品和多品，必须分拣结束才可以复核
        if (pickDTO.getWorkType().equalsIgnoreCase(PickEnum.PickWorkTypeEnum.PICK_METHOD_1.getCode()) && pickDTO.getPickMethod().equalsIgnoreCase(PickEnum.PickMethodEnum.PICK_METHOD_0.getCode()) && Arrays.asList(PickEnum.PickOrderTypeEnum.MORE.getCode(), PickEnum.PickOrderTypeEnum.SINGLE.getCode()).contains(pickDTO.getType())) {
            if (Arrays.asList(PickEnum.PickStatusEnum.SPLIT_BEGIN_STATUS.getCode(), PickEnum.PickStatusEnum.PICK_END_STATUS.getCode()).contains(pickDTO.getStatus())) {
                throw new BaseException(BaseBizEnum.TIP, "作业方式为【RF】和拣选方式为【先拣后分】必须分拣结束才能复核");
            }
        }
        return pickDTO;
    }

    @Override
    public Result<WarehouseCrossBorderBizDTO> checkWorkbench(PackCheckWorkbenchParam param) {
        WorkBenchDTO dto = iRemoteWorkBenchClient.queryByCode(param.getWorkbenchCode());
        if (dto == null) {
            throw new BaseException(ReceivingWorkBenchError.WORK_BENCH_DATA_ERROR);
        }
        if (!Objects.equals(dto.getStatus(), WorkBenchStatusEnum.ENABLE.value())) {
            throw new BaseException(ReceivingWorkBenchError.WORK_BENCH_DATA_ACTIVE_ERROR);
        }
        if (!Objects.equals(dto.getType(), WorkBenchTypeEnum.SALE.getType())) {
            throw new BaseException(ReceivingWorkBenchError.WORK_BENCH_DATA_TYPE_ERROR);
        }
        //
        WarehouseCrossBorderBizDTO warehouseCrossBorderBizDTO = new WarehouseCrossBorderBizDTO();
        warehouseCrossBorderBizDTO.setCrossBorder(getWarehouseCrossBorder(dto.getWarehouseCode()));

        return Result.success(warehouseCrossBorderBizDTO);
    }

    /**
     * 获取仓库是否跨境
     * INTERNATIONAL 跨境
     * <p>
     * INLAND  国内
     *
     * @param warehouseCode
     * @return
     */
    private String getWarehouseCrossBorder(String warehouseCode) {
        WarehouseDTO warehouseDTO = remoteWarehouseClient.queryByCode(warehouseCode);
        if (warehouseDTO == null) {
            throw new BaseException(BaseBizEnum.TIP, "仓库信息查询失败");
        }
        if (warehouseDTO.getType().equals(TaxTypeEnum.TYPE_BONDED_TAX.getCode())) {
            return "INTERNATIONAL";
        }
        return "INLAND";
    }

    /**
     * @param pickCode
     * @param packageCode
     * @param basketNo
     * @param shipmentOrderCode
     * @param pickDetailDTOList
     * @return com.dt.platform.wms.dto.check.back.PackExpressBizDTO
     * <AUTHOR>
     * @describe: 组装前端返回数据和校验包裹信息
     * @date 2023/8/1 10:31
     */
    private PackExpressBizDTO checkAndSubmitPack(String pickCode, String packageCode, String basketNo, String shipmentOrderCode, List<PickDetailDTO> pickDetailDTOList) {
        int formatNum = 0;
        //获取当前复核包裹号
        PackageDTO packageDTO = iRemotePackageClient.getPackageByCode(packageCode);
        if (packageDTO == null || Objects.equals(packageDTO.getStatus(), PackEnum.STATUS.CHECK_COMPELETE_STATUS.getCode())) {
            throw new BaseException(PickErrorEnum.PACK_BASKET_DATA_ERROR, basketNo);
        }
        //获取包裹明细
        PackageDetailParam packageDetailParam = new PackageDetailParam();
        packageDetailParam.setPackageCodeList(Arrays.asList(packageCode));
        List<PackageDetailDTO> packageDetailDTOOriginList = iRemotePackageClient.getPackageDetailListByListCode(packageDetailParam);
        List<PackageDetailDTO> packageDetailDTOListTemp = ObjectUtil.cloneByStream(packageDetailDTOOriginList);
        if (CollectionUtils.isEmpty(packageDetailDTOOriginList) || packageDetailDTOOriginList.stream().allMatch(entity -> Objects.equals(entity.getStatus(), PackEnum.STATUS.CHECK_COMPELETE_STATUS.getCode()))) {
            throw new BaseException(PickErrorEnum.PACK_DETAIL_DATA_ERROR, packageCode);
        }
        if (packageDTO.getIsPre().equalsIgnoreCase(PackEnum.TYPE.PRE.getCode())) {
            packageDetailDTOOriginList = packageDetailDTOOriginList.stream().filter(a -> a.getIsPre().equalsIgnoreCase(PackIsPreEnum.PRE.getCode()) || a.getIsPre().equalsIgnoreCase(PackIsPreEnum.LAST.getCode())).collect(Collectors.toList());
        } else {
            packageDetailDTOOriginList = packageDetailDTOOriginList.stream().filter(a -> a.getIsPre().equalsIgnoreCase(PackIsPreEnum.NORMAL.getCode())).collect(Collectors.toList());
        }
        //组装返回参数
        PackExpressBizDTO packBackExpressBizDTO = new PackExpressBizDTO();
        packBackExpressBizDTO.setPickCode(pickCode);
        BigDecimal expQty = pickDetailDTOList.stream().filter(a -> !Objects.equals(a.getPackageStatus(), PackEnum.STATUS.PART_ASSIGN_STATUS.getCode())).filter(a -> !Objects.equals(a.getPackageStatus(), PackEnum.STATUS.PART_ASSIGN_CANCEL_STATUS.getCode())).map(PickDetailDTO::getPickQty).reduce(BigDecimal.ZERO, BigDecimal::add);
        packBackExpressBizDTO.setExpQty(expQty.setScale(formatNum, RoundingMode.FLOOR));

        BigDecimal checkQty = pickDetailDTOList.stream().filter(a -> !Objects.equals(a.getPackageStatus(), PackEnum.STATUS.PART_ASSIGN_STATUS.getCode())).filter(a -> !Objects.equals(a.getPackageStatus(), PackEnum.STATUS.PART_ASSIGN_CANCEL_STATUS.getCode())).map(PickDetailDTO::getCheckQty).reduce(BigDecimal.ZERO, BigDecimal::add);
        packBackExpressBizDTO.setCheckQty(checkQty.setScale(formatNum, RoundingMode.FLOOR));

        packBackExpressBizDTO.setCheckPackNum((int) pickDetailDTOList.stream().filter(a -> !Objects.equals(a.getPackageStatus(), PackEnum.STATUS.PART_ASSIGN_STATUS.getCode())).filter(a -> !Objects.equals(a.getPackageStatus(), PackEnum.STATUS.PART_ASSIGN_CANCEL_STATUS.getCode())).filter(entity -> Objects.equals(entity.getPackageStatus(), PackEnum.STATUS.CHECK_COMPELETE_STATUS.getCode())).count());
        packBackExpressBizDTO.setExpPackNum((int) pickDetailDTOList.stream().filter(a -> !Objects.equals(a.getPackageStatus(), PackEnum.STATUS.PART_ASSIGN_STATUS.getCode())).filter(a -> !Objects.equals(a.getPackageStatus(), PackEnum.STATUS.PART_ASSIGN_CANCEL_STATUS.getCode())).count());
        packBackExpressBizDTO.setExpressNo(packageDTO.getExpressNo());
        packBackExpressBizDTO.setPackageCode(packageCode);
        packBackExpressBizDTO.setBasketNo(basketNo);
        packBackExpressBizDTO.setExpressCheckSkuQty(BigDecimal.ZERO);

        List<String> skuCodeList = packageDetailDTOOriginList.stream().map(PackageDetailDTO::getSkuCode).collect(Collectors.toList());
        //获取商品耗材信息
        List<SkuMaterialDTO> skuMaterialDTOList;
        //如果是淘天的订单
        if (!StringUtils.isEmpty(wmsOtherConfig.getTaotainWarehouseCodeList()) && wmsOtherConfig.getTaotainWarehouseCodeList().contains(packageDTO.getWarehouseCode())) {
            List<ShipmentDetailMaterialOtherDTO> detailMaterialOtherDTOList = iCommitPackBiz.buildTaoTianMaterialInfo(packageDTO);
            skuMaterialDTOList = iCommitPackBiz.buildTaoTianMaterial(packageDTO, skuCodeList, detailMaterialOtherDTOList);
        } else {
            SkuMaterialParam skuMaterialParam = new SkuMaterialParam();
            skuMaterialParam.setCargoCode(packageDTO.getCargoCode());
            skuMaterialParam.setSkuCodeList(packageDetailDTOListTemp.stream().map(PackageDetailDTO::getSkuCode).collect(Collectors.toList()));
            skuMaterialDTOList = remoteSkuMaterialClient.getList(skuMaterialParam);
            if (!CollectionUtils.isEmpty(skuMaterialDTOList)) {
                skuMaterialDTOList.forEach(it -> it.setSkuMaterialTip(true));
            }
        }

        List<PackageMaterialDTO> packageMaterialDTOList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(skuMaterialDTOList)) {
            //获取所有耗材名称
            PackageMaterialParam packageMaterialParam = new PackageMaterialParam();
            packageMaterialParam.setCargoCode(packageDTO.getCargoCode());
            packageMaterialParam.setCodeList(skuMaterialDTOList.stream().map(SkuMaterialDTO::getMaterialCode).collect(Collectors.toList()));
            packageMaterialDTOList = iRemotePackageMaterialClient.getList(packageMaterialParam);
        }
        //查询推荐包材
        packBackExpressBizDTO.setPackMaterialVO(getPackageMaterial(shipmentOrderCode, packageDTO, packageDetailDTOListTemp, skuMaterialDTOList, packageMaterialDTOList));
        //是否开启溯源码
        Boolean isCheck = false;
//        //预包包裹不校验溯源码  TODO 2024-03-19 废除溯源码
//        if (packageDTO.getIsPre().equalsIgnoreCase(PackEnum.TYPE.NORMAL.getCode())) {
//            isCheck = isCheckSource(packageDTO.getCargoCode());
//        }
        //查询uom
        SkuUomParam skuUomParam = new SkuUomParam();
        skuUomParam.setCargoCode(packageDTO.getCargoCode());
        skuUomParam.setSkuCodeList(skuCodeList);
        List<SkuUomDTO> skuUomList = iRemoteSkuClient.getSkuUomList(skuUomParam);
        Map<String, List<SkuUomDTO>> skuUomDTOMap = skuUomList.stream().collect(Collectors.groupingBy(SkuUomDTO::getSkuCode));
        //校验商品 查询upc
        SkuUpcParam upcParam = new SkuUpcParam();
        upcParam.setSkuCodeList(skuCodeList);
        upcParam.setCargoCode(packageDTO.getCargoCode());
        upcParam.setStatus(SkuStatusEnum.STATUS_ENABLED.getStatus());
        List<SkuUpcDTO> skuUpcList = iRemoteSkuClient.getSkuUpcList(upcParam);
        Map<String, List<SkuUpcDTO>> skuUpcDTOMap = skuUpcList.stream().collect(Collectors.groupingBy(SkuUpcDTO::getSkuCode));
        //获取商品
        SkuParam skuParam = new SkuParam();
        skuParam.setCargoCode(packageDTO.getCargoCode());
        skuParam.setCodeList(skuCodeList);
        List<SkuDTO> skuDTOList = iRemoteSkuClient.getList(skuParam);
        Map<String, SkuDTO> skuDTOMap = skuDTOList.stream().collect(Collectors.toMap(SkuDTO::getCode, Function.identity()));
        //获取包裹分配明细
        List<PackCheckSkuAndAllocationDTO> packCheckSkuAndAllocationDTOList = iCommitPackBiz.getPackCheckSkuAndAllocation(packageDTO, skuDTOMap);
        //组装返回当前包裹明细
        List<PackSkuBizDTO> packSkuVOS = new ArrayList<>();
        int i = 1;
        for (PackCheckSkuAndAllocationDTO packCheckSkuAndAllocationDTO : packCheckSkuAndAllocationDTOList) {
            List<PackageDetailDTO> packageDetailDTOList = packageDetailDTOOriginList.stream().filter(a -> Objects.equals(a.getSkuCode(), packCheckSkuAndAllocationDTO.getSkuCode())).collect(Collectors.toList());
            PackSkuBizDTO packBackSkuBizDTO = new PackSkuBizDTO();
            packBackSkuBizDTO.setId(i++);
            packBackSkuBizDTO.setExpireDate(packCheckSkuAndAllocationDTO.getExpireDate());
            packBackSkuBizDTO.setExpireDateDesc(ConverterUtil.convertVoTime(packCheckSkuAndAllocationDTO.getExpireDate(), "yyyy-MM-dd"));
            packBackSkuBizDTO.setProductionNo(packCheckSkuAndAllocationDTO.getProductionNo());

            packBackSkuBizDTO.setSkuCode(packCheckSkuAndAllocationDTO.getSkuCode());
            packBackSkuBizDTO.setSkuName(packageDetailDTOList.get(0).getSkuName());
            packBackSkuBizDTO.setExpQty(packCheckSkuAndAllocationDTO.getExpQty().setScale(formatNum, RoundingMode.FLOOR));
            packBackSkuBizDTO.setQty(BigDecimal.ZERO);
            List<SkuUpcDTO> skuUpcDTOList = skuUpcDTOMap.get(packCheckSkuAndAllocationDTO.getSkuCode());
            List<SkuUomDTO> skuUomDTOList = skuUomDTOMap.get(packCheckSkuAndAllocationDTO.getSkuCode());
            SkuUpcDTO skuUpcDTO = skuUpcDTOList.stream().filter(a -> a.getIsDefault().equals(SkuUpcDefaultEnum.YES.getStatus())).findFirst().orElse(null);
            if (CollectionUtils.isEmpty(skuUpcDTOList)) {
                throw new BaseException(BaseBizEnum.TIP, String.format("请维护商品:%s,条形码", packCheckSkuAndAllocationDTO.getSkuCode()));
            }
            if (skuUpcDTO == null) {
                packBackSkuBizDTO.setUpcCode("");
            } else {
                packBackSkuBizDTO.setUpcCode(skuUpcDTO.getUpcCode());
            }
            packBackSkuBizDTO.setHcDesc("");
            if (!CollectionUtils.isEmpty(skuMaterialDTOList)) {
                List<SkuMaterialDTO> materialDTOList = skuMaterialDTOList.stream()
                        .filter(a -> a.getSkuMaterialTip())
                        .filter(a -> a.getSkuCode().equals(packCheckSkuAndAllocationDTO.getSkuCode()))
                        .filter(a -> a.getCargoCode().equals(packageDTO.getCargoCode())).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(materialDTOList)) {
                    List<String> hcNameList = new ArrayList<>();
                    List<PackageMaterialDTO> finalPackageMaterialDTOList = packageMaterialDTOList;
                    materialDTOList.forEach(skuMaterialDTO -> {
                        PackageMaterialDTO packageMaterialDTO = finalPackageMaterialDTOList.stream()
                                .filter(a -> a.getCargoCode().equals(packageDTO.getCargoCode()))
                                .filter(a -> a.getBarCode().equals(skuMaterialDTO.getBarCode())).findFirst().orElse(null);
                        hcNameList.add(String.format("%s x %s", packageMaterialDTO.getName(), skuMaterialDTO.getMaterialNum()));
                    });
                    hcNameList.stream().sorted(Comparator.comparing(String::new));
                    packBackSkuBizDTO.setHcDesc(hcNameList.stream().collect(Collectors.joining(";")));
                }
            }
            //冷链 只需要扫描包材前扫描一个
            packBackSkuBizDTO.setIsScanColdChainBox(false);

            SkuDTO skuDTO = skuDTOMap.getOrDefault(packCheckSkuAndAllocationDTO.getSkuCode(), null);
            if (skuDTO != null && SkuTagEnum.NumToEnum(skuDTO.getSkuTag()).stream().anyMatch(a -> a.getCode().equals(SkuTagEnum.COLD_CHAIN_BOX.getCode()))) {
                packBackSkuBizDTO.setIsScanColdChainBox(true);
            }
            //是否开启SN
            packBackSkuBizDTO.setIsSNMgmt(false);
            if (skuDTO != null && skuDTO.getSNMgmtOutNeed()) {
                packBackSkuBizDTO.setIsSNMgmt(true);
            }
            //"冷链", "贵品" TODO 目前淘天独有
            packBackSkuBizDTO.setSkuTagList(packCheckSkuAndAllocationDTO.getSkuTagList());
            //wms封口贴 当前上商品每扫一个都需要扫描一个封口贴
            packBackSkuBizDTO.setIsScanSealingTape(false);
            packBackSkuBizDTO.setShowMesg("");
//            if (skuDTO != null && SkuTagEnum.NumToEnum(skuDTO.getSkuTag()).stream().anyMatch(a -> a.getCode().equals(SkuTagEnum.SCAN_SEALING_TAPE.getCode()))) {
//                packBackSkuBizDTO.setIsScanSealingTape(true);
//                packBackSkuBizDTO.setShowMesg("封口贴");
//            }
            //TODO 淘天【溯源码、防伪扣、易撕贴】包裹中的商品命中其中一个（且只会命中一个） 2024-10-17 增加非淘天
            if (skuDTO != null) {
                if (skuDTO.getNeedUptracSourceCodeTag()) {
                    packBackSkuBizDTO.setIsScanSealingTape(true);
                    packBackSkuBizDTO.setShowMesg("溯源码");
                }
                if (skuDTO.getAntiCounterfeitingBuckleTag()) {
                    packBackSkuBizDTO.setIsScanSealingTape(true);
                    packBackSkuBizDTO.setShowMesg("防伪扣");
                }
                if (skuDTO.getPullTapeTag()) {
                    packBackSkuBizDTO.setIsScanSealingTape(true);
                    packBackSkuBizDTO.setShowMesg("易撕贴");
                }
            }
            log.info("ScanSealingTape:{}", packageDTO.getPackageCode() + ":" + packBackSkuBizDTO.getSkuCode()
                    + ":" + packBackSkuBizDTO.getShowMesg() + ":"
                    + packBackSkuBizDTO.getIsScanSealingTape());
            //如果开启了溯源码，则查找最小包装数量的upc
            if (isCheck) {
                //首先根据upc 筛选 符合的uom
                List<String> upcPackageUnitCode = skuUpcDTOList.stream().map(SkuUpcDTO::getPackageUnitCode).distinct().collect(Collectors.toList());
                skuUomDTOList = skuUomDTOList.stream().filter(skuUomDTO -> upcPackageUnitCode.contains(skuUomDTO.getPackageUnitCode())).collect(Collectors.toList());
                //获取uom 最小的包装单位数量
                SkuUomDTO skuUomDTO = skuUomDTOList.stream().min(Comparator.comparing(SkuUomDTO::getPackageQty)).get();
                //根据最小值获取符合的uom
                List<SkuUomDTO> minSkuUomDTOList = skuUomDTOList.stream().filter(uomDTO -> uomDTO.getPackageQty().compareTo(skuUomDTO.getPackageQty()) == 0).collect(Collectors.toList());
                List<String> packageUnitCodeList = minSkuUomDTOList.stream().map(SkuUomDTO::getPackageUnitCode).distinct().collect(Collectors.toList());
                //根据包装单位code获取最终的upc
                skuUpcDTOList = skuUpcDTOList.stream().filter(skuUpc -> packageUnitCodeList.contains(skuUpc.getPackageUnitCode())).collect(Collectors.toList());
            }
            if (CollectionUtils.isEmpty(skuUpcDTOList)) {
                throw new BaseException(PickErrorEnum.PACK_BASKET_NO_UPC_DATA_ERROR, packCheckSkuAndAllocationDTO.getSkuCode());
            }
            packBackSkuBizDTO.setUpcCodeS(buildPackUpc(skuUpcDTOList, skuUomDTOList));
            packSkuVOS.add(packBackSkuBizDTO);
        }
        if (!CollectionUtils.isEmpty(skuMaterialDTOList) && !CollectionUtils.isEmpty(packageMaterialDTOList)
                && skuMaterialDTOList.stream().anyMatch(a -> !a.getSkuMaterialTip())
                && Objects.equals(packageDTO.getIsPre(), PackEnum.TYPE.NORMAL.getCode())) {
            List<IdNameVO> showPackMaterialList = new ArrayList<>();
            List<PackageMaterialDTO> finalPackageMaterialDTOList = packageMaterialDTOList;
            skuMaterialDTOList.stream().filter(a -> !a.getSkuMaterialTip()).collect(Collectors.toList()).forEach(it -> {
                PackageMaterialDTO packageMaterialDTO = finalPackageMaterialDTOList.stream()
                        .filter(a -> a.getCargoCode().equals(packageDTO.getCargoCode()))
                        .filter(a -> a.getBarCode().equals(it.getBarCode())).findFirst().orElse(null);
                if (packageMaterialDTO != null) {
                    showPackMaterialList.add(IdNameVO.build(packageMaterialDTO.getName(), it.getMaterialNum() + ""));
                }
            });
            packBackExpressBizDTO.setShowPackMaterialList(mergeShowPackMaterialList(showPackMaterialList));
        }
        packBackExpressBizDTO.setWaitQty(packSkuVOS.stream().map(PackSkuBizDTO::getExpQty).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(formatNum, RoundingMode.FLOOR));
        packBackExpressBizDTO.setPackSkuVOS(packSkuVOS);
        packBackExpressBizDTO.setShipmentOrderCode(packageDTO.getShipmentOrderCode());
        packBackExpressBizDTO.setPoNo(packageDTO.getPoNo());
        packBackExpressBizDTO.setSoNo(packageDTO.getSoNo());
        packBackExpressBizDTO.setCheckSourceCode(isCheck);

        ShipmentOrderDTO shipmentOrderDTO = iRemoteShipmentOrderClient.getShipmentOrderByCode(shipmentOrderCode);
        if (shipmentOrderDTO != null) {
            packBackExpressBizDTO.setRemark("");
            if (!StringUtils.isEmpty(shipmentOrderDTO.getRemark())) {
                packBackExpressBizDTO.setRemark(shipmentOrderDTO.getRemark());
            }
            packBackExpressBizDTO.setDeliveryRequirement(shipmentOrderDTO.getDeliveryRequirement());
        }
        return packBackExpressBizDTO;
    }

    private List<IdNameVO> mergeShowPackMaterialList(List<IdNameVO> showPackMaterialList) {
        if (CollectionUtils.isEmpty(showPackMaterialList)) {
            return new ArrayList<>();
        }
        List<IdNameVO> idNameVOList = new ArrayList<>();
        showPackMaterialList.stream().map(IdNameVO::getId).distinct().forEach(it -> {
            int sum = showPackMaterialList.stream().filter(a -> Objects.equals(a.getId(), it))
                    .mapToInt(a -> {
                        if (NumberUtil.isInteger(a.getName() + "")) {
                            return Integer.valueOf(a.getName() + "");
                        } else {
                            return 0;
                        }
                    }).sum();
            idNameVOList.add(IdNameVO.build(it + "", sum + ""));
        });
        return idNameVOList;
    }


    /**
     * 获取订单备注
     *
     * @param shipmentOrderCode
     * @return
     */
    private String buildRemark(String shipmentOrderCode) {
        ShipmentOrderDTO shipmentOrderDTO = iRemoteShipmentOrderClient.getShipmentOrderByCode(shipmentOrderCode);
        if (shipmentOrderDTO == null) {
            return "";
        }
        if (!StringUtils.isEmpty(shipmentOrderDTO.getRemark())) {
            return shipmentOrderDTO.getRemark();
        }
        return "";
    }

    /**
     * @param cargoCode
     * @return java.lang.Boolean
     * @author: WuXian
     * description:  是否检验溯源码 (true 校验  false 不校验)
     * create time: 2021/9/13 10:33
     */
    private Boolean isCheckSource(String cargoCode) {
        CargoConfigDTO cargoConfigDTO = remoteCargoConfigClient.queryByCargoCodeAndpropKey("", cargoCode, CargoConfigParamEnum.SOURCE_CODE.getCode());
        if (cargoConfigDTO != null && cargoConfigDTO.getStatus().equals(CargoConfigStatusEnum.ENABLE.getValue()) && Objects.equals(cargoConfigDTO.getPropValue(), "1")) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    /**
     * 功能描述:  组装条形码
     * 创建时间:  2021/4/14 2:11 下午
     *
     * @param skuUpcDTOS:
     * @param skuUomDTOList:
     * @return java.util.List<com.dt.platform.wms.dto.check.PackUpcBizDTO>
     * <AUTHOR>
     */
    private List<PackUpcBizDTO> buildPackUpc(List<SkuUpcDTO> skuUpcDTOS, List<SkuUomDTO> skuUomDTOList) {
        Map<String, SkuUomDTO> stringSkuUomDTOMap = skuUomDTOList.stream().collect(Collectors.toMap(SkuUomDTO::getPackageUnitCode, Function.identity()));
        //校验商品
        List<PackUpcBizDTO> packBackUpcBizDTOS = new ArrayList<>();
        for (SkuUpcDTO entity : skuUpcDTOS) {
            PackUpcBizDTO upcBizDTO = new PackUpcBizDTO();
            upcBizDTO.setSkuCode(entity.getSkuCode());
            upcBizDTO.setUpcCode(entity.getUpcCode());
            SkuUomDTO skuUomDTO = stringSkuUomDTOMap.get(entity.getPackageUnitCode());
            if (skuUomDTO == null) {
                throw new BaseException(PickErrorEnum.PACK_BASKET_NO_UOM_DATA_ERROR, entity.getSkuCode());
            }
            upcBizDTO.setQty(skuUomDTO.getPackageQty());
            packBackUpcBizDTOS.add(upcBizDTO);
        }
        return packBackUpcBizDTOS;
    }

    /**
     * 组装提交数据
     *
     * @param pickDTO
     * @param pickDetailDTOList
     * @param packMaterialBizDTO
     */
    private void submitPackCheckBackBO(PickDTO pickDTO, List<PickDetailDTO> pickDetailDTOList, PackCheckPackMaterialBizDTO packMaterialBizDTO) {
        String key = pickDTO.getWarehouseCode() + "" + pickDTO.getPickCode();
        RLock lock = redissonClient.getLock("dt_wms_back_pack_pick_code_lock:" + key);
        Boolean tryLock = false;
        try {
            tryLock = lock.tryLock(1, 10, TimeUnit.SECONDS);
            if (!tryLock) {
                throw new BaseException(BaseBizEnum.TIP, "当前单据有正在处理的任务,请稍后重试");
            }
            PackCheckBackBO packCheckBackBO = new PackCheckBackBO();
            //拣选结束状态---修改为质检开始
            if (Objects.equals(pickDTO.getStatus(), PickEnum.PickStatusEnum.PICK_END_STATUS.getCode()) || Objects.equals(pickDTO.getStatus(), PickEnum.PickStatusEnum.SPLIT_END_STATUS.getCode())) {
                //原始拣选单复核
                if (pickDTO.getPickFlag().equals(PickEnum.PickFlagEnum.ORIGIN.getCode())) {
                    pickDTO.setStatus(PickEnum.PickStatusEnum.ZJ_BEGIN_STATUS.getCode());
                    pickDTO.setCheckStartTime(System.currentTimeMillis());
                    packCheckBackBO.setPickDTOList(Arrays.asList(pickDTO));
                } else {
                    if (StringUtils.isEmpty(pickDTO.getMergePickCode())) {
                        throw new BaseException(BaseBizEnum.TIP, "聚合拣选单复核异常,请核查");
                    }
                    //获取所有聚合拣选单子单据
                    PickParam mergeParam = new PickParam();
                    mergeParam.setMergePickCode(pickDTO.getMergePickCode());
                    List<PickDTO> pickDTOList = iRemotePickClient.getList(mergeParam);
                    if (CollectionUtils.isEmpty(pickDTOList)) {
                        throw new BaseException(BaseBizEnum.TIP, "聚合拣选单复核异常,请核查1");
                    }
                    List<PickDTO> commitPickList = new ArrayList<>();
                    commitPickList.addAll(pickDTOList);
                    PickDTO mergePickDTO = iRemotePickClient.queryPickByPickCode(pickDTO.getMergePickCode());
                    if (mergePickDTO == null) {
                        throw new BaseException(BaseBizEnum.TIP, "聚合拣选单复核异常,请核查2");
                    }
                    commitPickList.add(mergePickDTO);
                    commitPickList.forEach(a -> {
                        a.setStatus(PickEnum.PickStatusEnum.ZJ_BEGIN_STATUS.getCode());
                        a.setCheckStartTime(System.currentTimeMillis());
                    });
                    packCheckBackBO.setPickDTOList(commitPickList);
                }
            }
            PackageDTO packageDTO = iRemotePackageClient.getPackageByCode(pickDetailDTOList.get(0).getPackageCode());
            if (packageDTO == null) {
                throw new BaseException(PickErrorEnum.PACK_DETAIL_DATA_ERROR, pickDTO.getPickCode());
            }
            //如果拣选完成--更改为复核开始
            Boolean isLog = false;
            if (Objects.equals(packageDTO.getStatus(), PackEnum.STATUS.PICK_COMPELETE_STATUS.getCode())) {
                packageDTO.setStatus(PackEnum.STATUS.CHECK_BEGIN_STATUS.getCode());
                packageDTO.setCheckStartDate(System.currentTimeMillis());
                packageDTO.setRecPackUpc(packMaterialBizDTO.getMaterialUpcCode());
                packCheckBackBO.setPackageDTO(packageDTO);
                //拣选单明细与包裹状态码保持一致
                pickDetailDTOList.forEach(a -> {
                    a.setPackageStatus(PackEnum.STATUS.CHECK_BEGIN_STATUS.getCode());
                });
                packCheckBackBO.setPickDetailDTOList(pickDetailDTOList);
                isLog = true;
            }
            //修改出库单状态码
            ShipmentOrderDTO shipmentOrderDTO = iRemoteShipmentOrderClient.getShipmentOrderByCode(packageDTO.getShipmentOrderCode());
            if (StringUtils.isEmpty(shipmentOrderDTO)) {
                throw new BaseException(CollectWaveErrorEnum.STOCK_SHIPMENT_ERROR);
            }
            if (shipmentOrderDTO.getStatus().equals(ShipmentOrderEnum.STATUS.COLLECT_STATUS.getCode())) {
                shipmentOrderDTO.setStatus(ShipmentOrderEnum.STATUS.BEGIN_CHECK_STATUS.getCode());
                shipmentOrderDTO.setCheckStartDate(System.currentTimeMillis());
                List<ShipmentOrderDetailDTO> shipmentOrderDetailDTOS = iRemoteShipmentOrderClient.queryShipmentOrderDetailList(packageDTO.getShipmentOrderCode());
                if (CollectionUtils.isEmpty(shipmentOrderDetailDTOS)) {
                    throw new BaseException(CollectWaveErrorEnum.STOCK_SHIPMENT_ERROR);
                }
                shipmentOrderDetailDTOS.forEach(a -> a.setStatus(ShipmentOrderEnum.STATUS.BEGIN_CHECK_STATUS.getCode()));
                shipmentOrderDTO.setListShipmentOrderDetailDTO(shipmentOrderDetailDTOS);
                packCheckBackBO.setShipmentOrderDTO(shipmentOrderDTO);
            }
            if (packCheckBackBO.getPackageDTO() == null && CollectionUtils.isEmpty(packCheckBackBO.getPickDetailDTOList()) && CollectionUtils.isEmpty(packCheckBackBO.getPickDTOList()) && packCheckBackBO.getShipmentOrderDTO() == null) {
                return;
            }
            iRemoteBillContextClient.submitPackCheckBackBO(packCheckBackBO);
            if (isLog) {
                iBusinessLogBiz.savePackLog(packageDTO.getWarehouseCode(), packageDTO.getCargoCode(), packageDTO.getPackageCode(), CurrentUserHolder.getUserName(), String.format("包裹复核开始,单号:%s", packageDTO.getPackageCode()));
                iBusinessLogBiz.saveShipmentLog(packageDTO.getWarehouseCode(), packageDTO.getCargoCode(), packageDTO.getShipmentOrderCode(), CurrentUserHolder.getUserName(), String.format("出库单复核开始,单号:%s", packageDTO.getShipmentOrderCode()));
            }
        } catch (Exception e) {
            log.error("包裹复核异常：e:{}", e.getMessage());
            throw new BaseException(BaseBizEnum.TIP, e.getMessage());
        } finally {
            if (tryLock) {
                lock.unlock();
            }
        }
    }

    /**
     * 获取包材
     *
     * @param shipmentOrderCode
     * @return
     */
    private PackCheckPackMaterialBizDTO getPackageMaterial(String shipmentOrderCode, PackageDTO packageDTO, List<PackageDetailDTO> packageDetailDTOList, List<SkuMaterialDTO> skuMaterialDTOList, List<PackageMaterialDTO> packageMaterialDTOList) {
        ShipmentOrderParam shipmentOrderParam = new ShipmentOrderParam();
        shipmentOrderParam.setShipmentOrderCode(packageDTO.getShipmentOrderCode());
        ShipmentOrderDTO shipmentOrderDTO = iRemoteShipmentOrderClient.get(shipmentOrderParam);
        if (ObjectUtil.isEmpty(shipmentOrderDTO)) {
            throw new BaseException(BaseBizEnum.TIP, "出库单" + packageDTO.getShipmentOrderCode() + "不存在");
        }
        List<ShipmentOrderMaterialDTO> shipmentOrderMaterialDTOS = iRemoteShipmentOrderClient.queryOrderMaterialByOrderCode(shipmentOrderCode);
        PackCheckPackMaterialBizDTO packMaterialBizDTO;
        if (CollectionUtils.isEmpty(shipmentOrderMaterialDTOS)) {
            packMaterialBizDTO = new PackCheckPackMaterialBizDTO();
            packMaterialBizDTO.setCode("");
            packMaterialBizDTO.setName("");
        } else {
            packMaterialBizDTO = new PackCheckPackMaterialBizDTO();
            //默认取第一个包材 下发已校验包材
            packMaterialBizDTO.setCode(shipmentOrderMaterialDTOS.stream().map(ShipmentOrderMaterialDTO::getRecPackUpcCode).collect(Collectors.joining(",")));
            packMaterialBizDTO.setName("");
        }
        List<PackageDetailDTO> packageDetailDTONewList;
        if (packageDTO.getIsPre().equalsIgnoreCase(PackEnum.TYPE.NORMAL.getCode())) {
            packageDetailDTONewList = packageDetailDTOList.stream().filter(a -> a.getIsPre().equalsIgnoreCase(PackIsPreEnum.NORMAL.getCode())).collect(Collectors.toList());
        } else {
            packageDetailDTONewList = packageDetailDTOList.stream().filter(a -> a.getIsPre().equalsIgnoreCase(PackIsPreEnum.PRE.getCode()) || a.getIsPre().equalsIgnoreCase(PackIsPreEnum.LAST.getCode())).collect(Collectors.toList());
        }
        //展示耗材明细组
        packMaterialBizDTO.setMaterialShowName("");
        packMaterialBizDTO.setLockMaterial(false);
        MaterialCalculateParam materialCalculateParam = MaterialCalculateParam.builder().warehouseCode(packageDTO.getWarehouseCode()).cargoCode(packageDTO.getCargoCode()).is4PL(OrderTagEnum.NumToEnum(shipmentOrderDTO.getOrderTag()).contains(OrderTagEnum.FOUR_TAG) ? Is4PLEnum.YES : Is4PLEnum.NO).skuDetailList(packageDetailDTONewList.stream().map(it -> MaterialCalculateParam.SkuDetail.builder().skuCode(it.getSkuCode()).quantity(it.getPickQty()).build()).collect(Collectors.toList())).build();
        MaterialCalculateResultV2 materialCalculateResultV2 = packageMaterialBiz.calculateBCAndHC(materialCalculateParam);
        if (materialCalculateResultV2 != null) {
            if (materialCalculateResultV2.getStructMaterialDTO() != null) {
                packMaterialBizDTO.setLockMaterial(true);
            }
            if (!CollectionUtils.isEmpty(materialCalculateResultV2.getPackageMaterialBizDTOList())) {
                materialCalculateResultV2.getPackageMaterialBizDTOList().stream().filter(packageMaterialBizDTO -> packageMaterialBizDTO.getType().equals(MaterialTypeEnum.BC.getCode())).findFirst().ifPresent(packageMaterialBizDTO -> {
                    packMaterialBizDTO.setMaterialShowName(packageMaterialBizDTO.getName());
                    packMaterialBizDTO.setMaterialUpcCode(packageMaterialBizDTO.getBarCode());
                });
            }
        }
        return packMaterialBizDTO;
    }


    @Override
    public Result<Boolean> checkPackMaterial(PackCheckMaterialParam param) {
        //校验提交数量大于0
        if (CollectionUtils.isEmpty(param.getSkuBackParamList()) || param.getSkuBackParamList().stream().anyMatch(a -> a.getQty().compareTo(BigDecimal.ZERO) <= 0)) {
            throw new BaseException(BaseBizEnum.TIP, "明细数量要大于0");
        }
        //查询包裹
        PackageDTO packageDTO = iRemotePackageClient.getPackageByCode(param.getPackageCode());
        PackageMaterialDTO packageMaterialDTO = iRemotePackageMaterialClient.queryByUpcCode(packageDTO.getCargoCode(), param.getPackageMaterialCode());
        if (packageMaterialDTO == null) {
            throw new BaseException(BaseBizEnum.TIP, String.format("包材:%s,包材未找到", param.getPackageMaterialCode()));
        }
        if (!Objects.equals(packageMaterialDTO.getStatus(), PackageMaterialStatusEnum.ENABLE.getValue())) {
            throw new BaseException(BaseBizEnum.TIP, String.format("包材:%s,包材被禁用", packageMaterialDTO.getBarCode()));
        }
        if (!packageMaterialDTO.getType().equals(MaterialTypeEnum.BC.getCode())) {
            throw new BaseException(BaseBizEnum.TIP, String.format("包材:%s,请扫描正确的包材条码", packageMaterialDTO.getBarCode()));
        }
        if (!StringUtils.isEmpty(packageDTO.getOrderTag()) && OrderTagEnum.NumToEnum(packageDTO.getOrderTag()).stream().anyMatch(a -> a.getCode().equals(OrderTagEnum.FOUR_TAG.code()))) {
            if (!packageMaterialDTO.getIs4pl().equals(Is4PLEnum.YES.getCode())) {
                throw new BaseException(BaseBizEnum.TIP, String.format("包材:%s,包裹是4PL订单,请扫描4PL包材", param.getPackageMaterialCode()));
            }
        } else {
            if (!packageMaterialDTO.getIs4pl().equals(Is4PLEnum.NO.getCode())) {
                throw new BaseException(BaseBizEnum.TIP, String.format("包材:%s,包裹是非4PL订单,请扫描非4PL包材", param.getPackageMaterialCode()));
            }
        }
        //---
        Map<String, BigDecimal> skuMap = param.getSkuBackParamList().stream().collect(Collectors.toMap(PackSubExpNoParam::getSkuCode, PackSubExpNoParam::getQty, BigDecimal::add));
        log.info("checkPackMaterial skuMap:{}", JSONUtil.toJsonStr(skuMap));
        //固定结构
        String structSign = structMaterialBiz.structSign(structMaterialBiz.struct(skuMap));
        //包材强校验
        iCheckPackMaterialBiz.checkPackMaterial(param.getPackageMaterialCode(), packageDTO, structSign);
        return Result.success(true);
    }


    @Override
    public Result<PackCheckResultBizDTO> submitPackage(PackSubmitParam param) throws Exception {
        log.info("B2C submitPackage warehouseCode:{} param:{}", CurrentRouteHolder.getWarehouseCode(), JSONUtil.toJsonStr(param));
        //校验提交数量大于0
        if (CollectionUtils.isEmpty(param.getSkuBackParamList()) || param.getSkuBackParamList().stream().anyMatch(a -> a.getQty().compareTo(BigDecimal.ZERO) <= 0)) {
            throw new BaseException(BaseBizEnum.TIP, "明细数量要大于0");
        }
        //返回打印标识---前置和后置
        PackCheckResultBizDTO packCheckResultBizDTO = new PackCheckResultBizDTO();
        String key = CurrentRouteHolder.getWarehouseCode() + "" + param.getPickCode() + "" + param.getPackageCode();
        RLock lock = redissonClient.getLock("dt_wms_back_pack_submit_lock:" + key);
        Boolean tryLock = false;
        try {
            tryLock = lock.tryLock(0, 30, TimeUnit.SECONDS);
            if (!tryLock) {
                throw new BaseException(BaseBizEnum.TIP, "提交太快了,当前包裹正在提交,请稍后刷新确认");
            }
            //TODO 此拣选单可能为聚合单号或原始拣选单号
            PickDTO pickDTO = checkPick(param.getPickCode());
            PickParam pickParam = new PickParam();
            pickParam.setPickCode(pickDTO.getPickCode());
            List<PickDetailDTO> pickDetailDTOList = iRemotePickClient.getPickDetailList(pickParam);
            if (CollectionUtils.isEmpty(pickDetailDTOList)) {
                throw new BaseException(PickErrorEnum.PACK_DETAIL_DATA_ERROR, param.getPickCode());
            }
            if (ObjectUtils.isEmpty(param.getSkuBackParamList())) {
                throw new BaseException(BaseBizEnum.TIP, "包裹明细不能为空");
            }

            PackageDTO packageDTO = iRemotePackageClient.getPackageByCode(param.getPackageCode());
            if (StringUtils.isEmpty(packageDTO)) {
                throw new BaseException(PickErrorEnum.PACK_CODE_DATA_ERROR, param.getExpressNo());
            }
            if (!Objects.equals(packageDTO.getStatus(), PackEnum.STATUS.CHECK_BEGIN_STATUS.getCode())) {
                throw new BaseException(PickErrorEnum.PACK_STATUS_DATA_ERROR, param.getExpressNo());
            }
            Boolean is4pl = false;
            if (!StringUtils.isEmpty(packageDTO.getOrderTag()) && OrderTagEnum.NumToEnum(packageDTO.getOrderTag()).stream().anyMatch(a -> a.getCode().equals(OrderTagEnum.FOUR_TAG.code()))) {
                is4pl = true;
            }
            //包材二次检验
            PackageMaterialDTO packageMaterialDTO = null;
            if (!StringUtils.isEmpty(param.getPackageMaterialCode())) {
                packageMaterialDTO = checkSubmitPackageMaterial(param, packageDTO, is4pl);
            } else {
                throw new BaseException(BaseBizEnum.TIP, "包材不能为空");
            }
            List<PackageDetailDTO> packageDetailDTOList = iRemotePackageClient.getPackageDetailListByCode(param.getPackageCode());
            if (CollectionUtils.isEmpty(packageDetailDTOList)) {
                throw new BaseException(PickErrorEnum.PACK_CODE_DATA_ERROR, param.getExpressNo());
            }
            packageDTO.setListDetail(packageDetailDTOList);
            //拦截单触发-不拦截（返回拦截的出库单号）
            PickDetailDTO pickDetailDTO = pickDetailDTOList.stream().filter(a -> a.getPackageCode().equalsIgnoreCase(packageDTO.getPackageCode())).findFirst().orElse(null);
            if (pickDetailDTO == null) {
                throw new BaseException(BaseBizEnum.TIP, String.format("单号:%s,未找到需要复核的包裹", param.getExpressNo()));
            }
            List<String> interceptPackCodeList = interceptionManagerBiz.queryInterceptionPackTipOperation(Arrays.asList(packageDTO.getPackageCode()));
            if (!CollectionUtils.isEmpty(interceptPackCodeList)) {
                Map<String, Object> map = new HashMap<>();
                map.put("num", interceptPackCodeList.size());
                map.put("interceptionExpressNoList", Arrays.asList(pickDetailDTO.getExpressNo()));
                map.put("packageCode", pickDetailDTO.getPackageCode());
                map.put("pickCode", pickDetailDTO.getPickCode());
                throw new BaseException(JSONUtil.toJsonStr(map), BaseBizEnum.TIP_HANDLE, "包裹拦截明细");
            }
            //当前提交数量
            BigDecimal submitQty = param.getSkuBackParamList().stream().map(PackSubExpNoParam::getQty).reduce(BigDecimal.ZERO, BigDecimal::add);
            param.getSkuBackParamList().forEach(it -> {
                if (it.getQty().compareTo(BigDecimal.ZERO) <= 0) {
                    throw new BaseException(BaseBizEnum.TIP, "提交数量必须大于0");
                }
                if (StringUtils.isEmpty(it.getExpireDate())) {
                    it.setExpireDate(0L);
                }
                if (it.getProductionNo() == null) {
                    it.setProductionNo("");
                }
            });
            //校验溯源码
//            Boolean isCheckSourceCode = checkSourceParam(packageDTO, param);
            //TODO 2024-03-19 废除溯源码
            Boolean isCheckSourceCode = false;
            //拆包新包裹号
            String returnPackageCode = "";
            //00 当前未完成 30当前已完成
            String isComplete = "00";
            //部分组包不进入批量复核
            Boolean isPartComplete = false;
            //组包都要当场打印
            Boolean partPrint = false;
            //true 为最后一个包裹
            Boolean isLast = false;
            //篮号
            String basketNo = "";
            //是否淘天
            Boolean taoTianWarehouse = remoteWarehouseClient.getTaoTianWarehouse(packageDTO.getWarehouseCode());
            //是否跨境包裹
            Boolean isBondedTax = true;
            //如果拣选单复核是选择批量复核，传了批量复核的值,即使开启溯源码都不提示扫描溯源码 isBatchCheck = true 前端开启批量复核且是秒杀单
            Boolean isBatchCheck = !StringUtils.isEmpty(param.getIsBatchCheck()) && param.getIsBatchCheck().equalsIgnoreCase("batch") && pickDTO.getType().equalsIgnoreCase(PickEnum.PickOrderTypeEnum.SPIKE.getCode());
            if (packageDTO.getPackageSkuQty().compareTo(submitQty) > 0) {
                if (isBatchCheck) {
                    throw new BaseException(BaseBizEnum.TIP, "秒杀拣选单开启批量，不允许拆包，请选择逐单复核");
                }
                //拆包逻辑
                ShipmentOrderParam shipmentOrderParam = new ShipmentOrderParam();
                shipmentOrderParam.setShipmentOrderCode(packageDTO.getShipmentOrderCode());
                ShipmentOrderDTO shipmentOrderDTO = iRemoteShipmentOrderClient.get(shipmentOrderParam);
                if (shipmentOrderDTO.getTaxType().equalsIgnoreCase(TaxTypeEnum.TYPE_BONDED_TAX.getCode())) {
                    throw new BaseException(BaseBizEnum.TIP, "保税C单不允许组包");
                }
                packageDTO.setActualPackUpc(param.getPackageMaterialCode());
                if (packageDTO.getIsPre().equalsIgnoreCase(PackEnum.TYPE.PRE.getCode())) {
                    returnPackageCode = iCommitPackBiz.commitB2CPrePack(param, pickDTO, pickDetailDTOList, packageDTO, isCheckSourceCode);
                } else {
                    returnPackageCode = iCommitPackBiz.commitB2CPack(param, pickDTO, pickDetailDTOList, packageDTO, isCheckSourceCode);
                }
                if (StringUtils.isEmpty(returnPackageCode)) {
                    throw new BaseException(BaseBizEnum.TIP, String.format("单号:%s,组包失败!!", param.getPackageCode()));
                }
                partPrint = true;
                isPartComplete = true;
            } else {
                returnPackageCode = packageDTO.getPackageCode();
                //检查SKU
                checkSkuQty(packageDetailDTOList, param, packageDTO);
                //当前拣选单结束(整个)
                isLast = pickDetailDTOList.stream().filter(entity -> !Objects.equals(entity.getPackageCode(), param.getPackageCode())).allMatch(entity -> Integer.parseInt(entity.getPackageStatus()) > Integer.parseInt(PackEnum.STATUS.CHECK_BEGIN_STATUS.getCode()));
                //组装提交数据
                PackCheckBackBO packCheckBackBO = new PackCheckBackBO();
                String pickCode = pickDTO.getPickCode();
                //拣选单明细修改
                if (pickDTO.getPickFlag().equalsIgnoreCase(PickEnum.PickFlagEnum.ORIGIN.getCode())) {
                    pickDetailDTO.setPackageStatus(PackEnum.STATUS.CHECK_COMPELETE_STATUS.getCode());
                    pickDetailDTO.setCheckQty(pickDetailDTO.getPickQty());
                    packCheckBackBO.setPickDetailDTOList(Arrays.asList(pickDetailDTO));
                    basketNo = pickDetailDTO.getBasketNo();
                } else {
                    //聚合单包裹明细
                    List<PickDetailDTO> commitPickDetailList = new ArrayList<>();
                    pickDetailDTO.setPackageStatus(PackEnum.STATUS.CHECK_COMPELETE_STATUS.getCode());
                    pickDetailDTO.setCheckQty(pickDetailDTO.getPickQty());
                    basketNo = pickDetailDTO.getBasketNo();
                    commitPickDetailList.add(pickDetailDTO);
                    //原始包裹明细
                    PickDetailParam pickDetailParam = new PickDetailParam();
                    pickDetailParam.setPackageCode(packageDTO.getPackageCode());
                    pickDetailParam.setDetailFlag(PickEnum.PickDetailFlagEnum.ORIGIN.getCode());
                    pickDetailParam.setPackageStatus(PackEnum.STATUS.CHECK_BEGIN_STATUS.getCode());
                    List<PickDetailDTO> detailClientList = remotePickDetailClient.getList(pickDetailParam);
                    if (CollectionUtils.isEmpty(detailClientList)) {
                        throw new BaseException(BaseBizEnum.TIP, "聚合拣选单未找到合单原始明细");
                    }
                    pickCode = detailClientList.get(0).getPickCode();
                    detailClientList.forEach(a -> {
                        a.setPackageStatus(PackEnum.STATUS.CHECK_COMPELETE_STATUS.getCode());
                        a.setCheckQty(a.getPickQty());
                    });
                    commitPickDetailList.addAll(detailClientList);
                    packCheckBackBO.setPickDetailDTOList(commitPickDetailList);
                }
                //包裹主单修改
                packageDTO.setStatus(PackEnum.STATUS.CHECK_COMPELETE_STATUS.getCode());
                packageDTO.setCheckCompleteDate(System.currentTimeMillis());
                packageDTO.setActualPackUpc(param.getPackageMaterialCode());
                //包裹重量计算
                List<String> skuCodeList = param.getSkuBackParamList().stream().flatMap(a -> Stream.of(a.getSkuCode())).distinct().collect(Collectors.toList());
                SkuParam skuParam = new SkuParam();
                skuParam.setCodeList(skuCodeList);
                skuParam.setCargoCode(packageDTO.getCargoCode());
                List<SkuDTO> skuList = iRemoteSkuClient.getList(skuParam);

                //校验封口贴
                List<OutSourceCodeDTO> sourceCodeSealingTapeDTOList;
                //校验是否扫描封口贴
                List<SkuDTO> sealingTapeSkuList;
//                if (taoTianWarehouse) {
                sealingTapeSkuList = skuList.stream().filter(a -> a.getNeedUptracSourceCodeTag() || a.getPullTapeTag() || a.getAntiCounterfeitingBuckleTag()).collect(Collectors.toList());
//                } else {
//                    sealingTapeSkuList = skuList.stream().filter(a -> SkuTagEnum.NumToEnum(a.getSkuTag()).contains(SkuTagEnum.SCAN_SEALING_TAPE)).collect(Collectors.toList());
//                }
                if (!CollectionUtils.isEmpty(sealingTapeSkuList) && CollectionUtils.isEmpty(param.getSealingTapeParamList())) {
//                    if (taoTianWarehouse) {
                    throw new BaseException(BaseBizEnum.TIP, "当前组包商品需要扫描【溯源码】或【易撕贴】或【防伪扣】,请扫描");
//                    } else {
//                        throw new BaseException(BaseBizEnum.TIP, "当前组包商品需要扫描封口贴,请扫描封口贴");
//                    }
                }
                sourceCodeSealingTapeDTOList = iCommitPackBiz.checkSealingTape(param, packageDTO, skuList);
                //计算重量
                iCheckPackWeightAndVolumeBiz.calculationVolumetricAndWeight(packageDTO, packageDTO.getListDetail(), skuList);
                //获取包材
                packageDTO.setActualPackNum(1);
                packageDTO.setActualPackWeight(packageMaterialDTO.getGrossWeight());
                //计算体积重
                iCheckPackWeightAndVolumeBiz.calculationVolumetricWeight(packageDTO);
                packCheckBackBO.setPackageDTO(packageDTO);
                //包裹明细修改
                packageDetailDTOList.forEach(entity -> {
                    entity.setStatus(PackEnum.STATUS.CHECK_COMPELETE_STATUS.getCode());
                    entity.setCheckQty(entity.getPickQty());
                });
                packCheckBackBO.setPackageDetailDTOList(packageDetailDTOList);
                //4pl订单记录耗材使用量
                //TODO 订单都记录耗材使用量 2022-07-12
                List<MaterialUseRecordDTO> materialUseRecordDTOList = iCommitPackBiz.buildMaterialUseRecord(packageDTO, packageDetailDTOList, skuList);
                //冷链耗材
                if (!StringUtils.isEmpty(param.getHcUpcCode())) {
                    MaterialUseRecordDTO materialUseRecordDTO = iCommitPackBiz.buildMaterialUseRecordByColdChainBox(packageDTO, param.getHcUpcCode(), skuList);
                    materialUseRecordDTOList.add(materialUseRecordDTO);
                }
                //记录赠品耗材
                List<MaterialUseRecordDTO> giftPickToCheckList = iCommitPackBiz.buildMaterialUseRecordByGift(pickCode, packageDTO);
                if (!CollectionUtils.isEmpty(giftPickToCheckList)) {
                    materialUseRecordDTOList.addAll(giftPickToCheckList);
                }

                packCheckBackBO.setMaterialUseRecordDTOList(materialUseRecordDTOList);
                StringBuilder saveShipmentLog = new StringBuilder();
                //包裹对应的出库单修改
                ShipmentOrderDTO shipmentOrderDTO = modifyShipment(packageDTO, saveShipmentLog);
                if (shipmentOrderDTO.getTaxType().equalsIgnoreCase(TaxTypeEnum.TYPE_DUTY_TAX.getCode())) {
                    isBondedTax = false;
                }
                packCheckBackBO.setShipmentOrderDTO(shipmentOrderDTO);
                if (isLast) {
                    //最后一箱 拣选主单修改
                    if (pickDTO.getPickFlag().equalsIgnoreCase(PickEnum.PickFlagEnum.ORIGIN.getCode())) {
                        pickDTO.setStatus(PickEnum.PickStatusEnum.ZJ_END_STATUS.getCode());
                        pickDTO.setCheckEndTime(System.currentTimeMillis());
                        packCheckBackBO.setPickDTOList(Arrays.asList(pickDTO));
                    } else {
                        //当前pickDTO为合单拣选单号
                        PickParam originPickParam = new PickParam();
                        originPickParam.setMergePickCode(pickDTO.getPickCode());
                        List<PickDTO> childPickDTOList = iRemotePickClient.getList(originPickParam);
                        if (CollectionUtils.isEmpty(childPickDTOList)) {
                            throw new BaseException(BaseBizEnum.TIP, "聚合拣选单未找到合单明细");
                        }
                        List<PickDTO> commitPickList = new ArrayList<>();
                        commitPickList.add(pickDTO);
                        commitPickList.addAll(childPickDTOList);
                        commitPickList.forEach(a -> {
                            a.setStatus(PickEnum.PickStatusEnum.ZJ_END_STATUS.getCode());
                            a.setCheckEndTime(System.currentTimeMillis());
                        });
                        packCheckBackBO.setPickDTOList(commitPickList);
                    }
                }
                //组装包裹复核主单和复核单明细
                PackageCheckDTO packageCheckDTO = buildPackageCheck(packageDTO, param.getWorkbenchCode(), pickDTO.getPickCode(), basketNo);
                packCheckBackBO.setPackageCheckDTO(packageCheckDTO);
                //如果秒杀单开启批量复核,不扫溯源码 isCheckSourceCode = true 开启溯源码  isBatchCheck = true 前端开启批量复核且是秒杀单
                List<OutSourceCodeDTO> outSourceCodeDTOList = new ArrayList<>();
                if (isCheckSourceCode && !isBatchCheck) {
                    for (PackSourceCodeParam packSourceCodeParam : param.getSourceCodeParamList()) {
                        if (StringUtils.isEmpty(packSourceCodeParam.getSourceCode()) || StringUtils.isEmpty(packSourceCodeParam.getSkuCode()) || StringUtils.isEmpty(packSourceCodeParam.getUpcCode())) {
                            throw new BaseException(BaseBizEnum.TIP, "溯源码参数不能为空");
                        }
                        OutSourceCodeDTO outSourceCodeDTO = ConverterUtil.convert(packageDTO, OutSourceCodeDTO.class);
                        outSourceCodeDTO.setSkuCode(packSourceCodeParam.getSkuCode());
                        outSourceCodeDTO.setUpcCode(packSourceCodeParam.getUpcCode());
                        skuList.stream().filter(a -> Objects.equals(a.getCode(), packSourceCodeParam.getSkuCode())).filter(a -> Objects.equals(a.getCargoCode(), packageDTO.getCargoCode())).findFirst().ifPresent(a -> outSourceCodeDTO.setSkuName(a.getName()));
                        outSourceCodeDTO.setScanType(SourceCodeScanTypeEnum.SOURCE_CODE.getCode());
                        outSourceCodeDTO.setSnCode(packSourceCodeParam.getSourceCode());
                        outSourceCodeDTO.setQty(new BigDecimal("1"));
                        outSourceCodeDTO.setId(null);
                        outSourceCodeDTOList.add(outSourceCodeDTO);
                    }
                }
                if (!CollectionUtils.isEmpty(sourceCodeSealingTapeDTOList)) {
                    outSourceCodeDTOList.addAll(sourceCodeSealingTapeDTOList);
                }
                // SN  check
                if (skuList.stream().anyMatch(skuDTO -> skuDTO.getSNMgmtOutNeed())) {
                    if (CollectionUtils.isEmpty(param.getSNCodeParamList())) {
                        throw new BaseException(BaseBizEnum.TIP, "当前包裹有需要扫描SN,请核查");
                    }
                    List<OutSourceCodeDTO> snOutSourceCodeDTOList = iCommitPackBiz.checkSN(skuList, packageDTO, param);
                    if (!CollectionUtils.isEmpty(snOutSourceCodeDTOList)) {
                        outSourceCodeDTOList.addAll(snOutSourceCodeDTOList);
                        //包裹和出库单都加上 扫描SN
                        Set<OrderTagEnum> orderTagEnumShipmentList = OrderTagEnum.NumToEnum(packCheckBackBO.getShipmentOrderDTO().getOrderTag());
                        orderTagEnumShipmentList.add(OrderTagEnum.SCAN_SN);
                        packCheckBackBO.getShipmentOrderDTO().setOrderTag(OrderTagEnum.enumToNum(orderTagEnumShipmentList.stream().collect(Collectors.toList())));

                        Set<OrderTagEnum> orderTagEnumPackList = OrderTagEnum.NumToEnum(packCheckBackBO.getPackageDTO().getOrderTag());
                        orderTagEnumPackList.add(OrderTagEnum.SCAN_SN);
                        packCheckBackBO.getPackageDTO().setOrderTag(OrderTagEnum.enumToNum(orderTagEnumPackList.stream().collect(Collectors.toList())));
                    }
                }
                if (!CollectionUtils.isEmpty(outSourceCodeDTOList)) {
                    packCheckBackBO.setOutSourceCodeDTOList(outSourceCodeDTOList);
                }

                SystemEventDTO systemEventDTO = buildSystemEventDTO(packCheckBackBO.getPackageDTO());
                packCheckBackBO.setSystemEventDTO(systemEventDTO);
                log.info("packCheckBackBO:{}", JSONUtil.toJsonStr(packCheckBackBO));
                iRemoteBillContextClient.submitPackCheckBackBO(packCheckBackBO);
                isComplete = "30";
                iBusinessLogBiz.savePackLog(packageDTO.getWarehouseCode(), packageDTO.getCargoCode(), packageDTO.getPackageCode(), CurrentUserHolder.getUserName(), String.format("包裹复核完成,单号:%s", packageDTO.getPackageCode()));
                if (saveShipmentLog.toString().equals("30")) {
                    iBusinessLogBiz.saveShipmentLog(packageDTO.getWarehouseCode(), packageDTO.getCargoCode(), packageDTO.getShipmentOrderCode(), CurrentUserHolder.getUserName(), String.format("出库单复核完成,单号:%s", packageDTO.getShipmentOrderCode()));
                }
            }
            if (partPrint) {
                packCheckResultBizDTO.setExpressBillType(PickEnum.PickPrintMethodEnum.PICK_PRINT_METHOD_1.getCode());
                packCheckResultBizDTO.setGoodsLitType(PickEnum.PickPrintMethodEnum.PICK_PRINT_METHOD_1.getCode());
                packCheckResultBizDTO.setPackMarkType(PickEnum.PickPrintMethodEnum.PICK_PRINT_METHOD_1.getCode());
            } else {
                packCheckResultBizDTO.setExpressBillType(pickDTO.getExpressBillType());
                packCheckResultBizDTO.setGoodsLitType(pickDTO.getGoodsListType());
                packCheckResultBizDTO.setPackMarkType(pickDTO.getPackMarkType());
                //大贸拆包
                if (!isBondedTax) {
                    //有前置打印
                    if (pickDTO.getPackMarkType().equalsIgnoreCase(PickEnum.PickPrintMethodEnum.PICK_PRINT_METHOD_0.getCode()) || pickDTO.getGoodsListType().equalsIgnoreCase(PickEnum.PickPrintMethodEnum.PICK_PRINT_METHOD_0.getCode())) {
                        PackageParam packageParam = new PackageParam();
                        packageParam.setCargoCode(packageDTO.getCargoCode());
                        packageParam.setShipmentOrderCode(packageDTO.getShipmentOrderCode());
                        packageParam.setOriginPackageCode(packageDTO.getPackageCode());
                        Boolean checkExits = iRemotePackageClient.checkExits(packageParam);
                        if (checkExits) {
                            packCheckResultBizDTO.setGoodsLitType(PickEnum.PickPrintMethodEnum.PICK_PRINT_METHOD_1.getCode());
                            packCheckResultBizDTO.setPackMarkType(PickEnum.PickPrintMethodEnum.PICK_PRINT_METHOD_1.getCode());
                        }
                    }
                }
            }
            packCheckResultBizDTO.setPackageCode(returnPackageCode);
            packCheckResultBizDTO.setIsComplete(isComplete);
            if (Objects.equals(param.getFlag(), "pick") && !isPartComplete && !isLast) {
                packCheckResultBizDTO.setIsBatchComplete(checkBatchCheck(pickDTO));
            } else {
                packCheckResultBizDTO.setIsBatchComplete(false);
            }
            log.info("wmsCheckPackSuccess:{}", packageDTO.getWarehouseCode() + "#" + packageDTO.getPackageCode() + "");
        } catch (Exception e) {
            log.error("包裹复核异常：e:{}", e.getMessage());
            throw e;
        } finally {
            if (tryLock) {
                lock.unlock();
            }
        }
        return Result.success(packCheckResultBizDTO);
    }

    /**
     * @param param
     * @param packageDTO
     * @return PackageMaterialDTO
     * <AUTHOR>
     * @describe:
     * @date 2023/8/1 10:03
     */
    private PackageMaterialDTO checkSubmitPackageMaterial(PackSubmitParam param, PackageDTO packageDTO, Boolean is4pl) {
        PackageMaterialDTO packageMaterialDTO = iRemotePackageMaterialClient.queryByUpcCode(packageDTO.getCargoCode(), param.getPackageMaterialCode());
        if (packageMaterialDTO == null) {
            throw new BaseException(BaseBizEnum.TIP, String.format("包材:%s,包材未找到", param.getPackageMaterialCode()));
        }
        if (!Objects.equals(packageMaterialDTO.getStatus(), PackageMaterialStatusEnum.ENABLE.getValue())) {
            throw new BaseException(BaseBizEnum.TIP, String.format("包材:%s,包材被禁用", packageMaterialDTO.getBarCode()));
        }
        if (!packageMaterialDTO.getType().equals(MaterialTypeEnum.BC.getCode())) {
            throw new BaseException(BaseBizEnum.TIP, String.format("包材:%s,请扫描正确的包材条码", packageMaterialDTO.getBarCode()));
        }
        if (is4pl && !packageMaterialDTO.getIs4pl().equals(Is4PLEnum.YES.getCode())) {
            throw new BaseException(BaseBizEnum.TIP, String.format("包材:%s,包裹是4PL订单,请扫描4PL包材", param.getPackageMaterialCode()));
        }
        if (!is4pl && !packageMaterialDTO.getIs4pl().equals(Is4PLEnum.NO.getCode())) {
            throw new BaseException(BaseBizEnum.TIP, String.format("包材:%s,包裹是非4PL订单,请扫描非4PL包材", param.getPackageMaterialCode()));
        }
        //解决大小写
        param.setPackageMaterialCode(packageMaterialDTO.getBarCode());
        Map<String, BigDecimal> skuMap = param.getSkuBackParamList().stream().collect(Collectors.toMap(PackSubExpNoParam::getSkuCode, PackSubExpNoParam::getQty, BigDecimal::add));
        log.info("checkPackMaterial skuMap:{}", JSONUtil.toJsonStr(skuMap));
        //固定结构
        String structSign = structMaterialBiz.structSign(structMaterialBiz.struct(skuMap));
        //校验包材
        iCheckPackMaterialBiz.checkPackMaterial(param.getPackageMaterialCode(), packageDTO, structSign);
        return packageMaterialDTO;
    }

    private SystemEventDTO buildSystemEventDTO(PackageDTO packageDTO) {
        SystemEventDTO systemEventDTO = new SystemEventDTO();
        systemEventDTO.setType(SystemEventEnum.PACK_REVIEW.getCode());
        systemEventDTO.setWarehouseCode(packageDTO.getWarehouseCode());
        systemEventDTO.setCargoCode(packageDTO.getCargoCode());
        systemEventDTO.setWorker(CurrentUserHolder.getUserName());
        systemEventDTO.setWorkTime(System.currentTimeMillis());
        systemEventDTO.setWorkDate(DateUtil.parse(DateUtil.date(System.currentTimeMillis()).toDateStr()).getTime());
        systemEventDTO.setBillNo(packageDTO.getPackageCode());
        systemEventDTO.setHashKey(packageDTO.getShipmentOrderCode());
        return systemEventDTO;
    }

//    /**
//     * 校验溯源码
//     *
//     * @param packageDTO
//     * @param param
//     */
//    private Boolean checkSourceParam(PackageDTO packageDTO, PackSubmitParam param) {
//        Boolean isCheck = isCheckSource(packageDTO.getCargoCode());
//        if (isCheck) {
//            CargoConfigDTO cargoConfigDTO = remoteCargoConfigClient
//                    .queryByCargoCodeAndpropKey(packageDTO.getWarehouseCode(), packageDTO.getCargoCode(), CargoConfigParamEnum.SOURCE_CODE_PATTERN.getCode());
//            if (CollectionUtils.isEmpty(param.getSourceCodeParamList())) {
//                throw new BaseException(BaseBizEnum.TIP, "当前货主开启溯源码,溯源码不能为空");
//            }
//            BigDecimal qty = param.getSkuBackParamList().stream().map(PackSubExpNoParam::getQty).reduce(BigDecimal.ZERO, BigDecimal::add);
//            Long num = param.getSourceCodeParamList().stream().map(PackSourceCodeParam::getSourceCode).distinct().count();
//            if (qty.compareTo(new BigDecimal(num + "")) != 0) {
//                throw new BaseException(BaseBizEnum.TIP, "溯源码,与商品数不匹配");
//            }
//            param.getSourceCodeParamList().forEach(a -> {
//                if (cargoConfigDTO != null
//                        && !StringUtils.isEmpty(cargoConfigDTO.getPropValue())
//                        && !Pattern.matches(cargoConfigDTO.getPropValue(), a.getSourceCode())) {
//                    throw new BaseException(BaseBizEnum.TIP, String.format("溯源码%s格式规则不符合", a.getSourceCode()));
//                }
//            });
//        }
//        return isCheck;
//    }

    /**
     * 检查是否批量复核
     *
     * @param pickDTO
     * @return
     */
    private Boolean checkBatchCheck(PickDTO pickDTO) {
        //非秒杀单不提示
        if (!Objects.equals(pickDTO.getType(), PickEnum.PickOrderTypeEnum.SPIKE.getCode())) {
            return false;
        }
        //拣选单质检结束不提示
        if (pickDTO.getStatus().equals(PickEnum.PickStatusEnum.ZJ_END_STATUS.getCode())) {
            return false;
        }
        //货主未开启最小复核数不提示
        CargoConfigDTO cargoConfigDTO = iRemoteCargoConfigClient.queryByCargoCodeAndpropKey(pickDTO.getWarehouseCode(), pickDTO.getCargoCode(), CargoConfigParamEnum.BATCH_CHECK_NOTE.getCode());
        if (cargoConfigDTO == null || cargoConfigDTO.getStatus().equals(CargoConfigStatusEnum.DISABLE.getValue())) {
            return false;
        }
        //最小复核数
        Integer minCheckNum = Integer.valueOf(CargoConfigParamEnum.BATCH_CHECK_NOTE.getValue());
        if (cargoConfigDTO != null && cargoConfigDTO.getStatus().equals(CargoConfigStatusEnum.ENABLE.getValue())) {
            minCheckNum = Integer.valueOf(cargoConfigDTO.getPropValue());
        }
        //货主未开启最小复核数为0不提示
        if (minCheckNum == 0) {
            return false;
        }
        PickParam pickParam = new PickParam();
        pickParam.setPickCode(pickDTO.getPickCode());
        List<PickDetailDTO> pickDetailDTOS = iRemotePickClient.getPickDetailList(pickParam);
        if (CollectionUtils.isEmpty(pickDetailDTOS)) {
            return false;
        }
        //拣选单拆包不提示
        PackageParam packageParam = new PackageParam();
        packageParam.setPackageCodeList(pickDetailDTOS.stream().map(PickDetailDTO::getPackageCode).distinct().collect(Collectors.toList()));
        List<PackageDTO> packageDTOList = iRemotePackageClient.getList(packageParam);
        if (!CollectionUtils.isEmpty(packageDTOList) && packageDTOList.stream().anyMatch(a -> !StringUtils.isEmpty(a.getOriginPackageCode()))) {
            return false;
        }
        //非4pl和4pl同一个拣选单不允许批量复核
        Set<String> orderTagSet = new HashSet<>();
        packageDTOList.forEach(packageDTO -> {
            if (OrderTagEnum.NumToEnum(packageDTO.getOrderTag()).stream().anyMatch(a -> a.getCode().equals(OrderTagEnum.FOUR_TAG.code()))) {
                orderTagSet.add(OrderTagEnum.FOUR_TAG.name());
            } else {
                orderTagSet.add("EMPTY");
            }
        });
        if (!CollectionUtils.isEmpty(orderTagSet) && orderTagSet.size() > 1) {
            return false;
        }

        //拣选单复核未达到最小复核数不提示
        if (pickDetailDTOS.stream().filter(a -> Objects.equals(a.getPackageStatus(), PackEnum.STATUS.CHECK_COMPELETE_STATUS.getCode())).count() < minCheckNum) {
            return false;
        }
        //拣选未找到复核的包裹数不提示
        List<PickDetailDTO> pickDetailDTOS1 = pickDetailDTOS.stream().filter(a -> a.getPackageStatus().equals(PackEnum.STATUS.CHECK_COMPELETE_STATUS.getCode())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(pickDetailDTOS1)) {
            return false;
        }
//        TODO 2022-08-14 移除扫描包材不一致,不提示批量复核
//        packageParam.setPackageCodeList(pickDetailDTOS1.stream().map(PickDetailDTO::getPackageCode).distinct().collect(Collectors.toList()));
//        List<PackageDTO> packageClientList = iRemotePackageClient.getList(packageParam);
//        //已复核的包裹使用包材不一样不提示
//        if (CollectionUtils.isEmpty(packageClientList) ||
//                packageClientList.stream().map(PackageDTO::getActualPackUpc).distinct().count() > 1) {
//            return false;
//        }
//        //拣选单的出库单数量
//        List<String> pickShipmentCode = pickDetailDTOS.stream().map(PickDetailDTO::getShipmentOrderCode).distinct().collect(Collectors.toList());
//        //查询出库单的包材
//        ShipmentOrderParam param = new ShipmentOrderParam();
//        param.setShipmentOrderCodeList(pickDetailDTOS.stream().map(PickDetailDTO::getShipmentOrderCode).distinct().collect(Collectors.toList()));
//        List<ShipmentOrderMaterialDTO> shipmentOrderMaterialDTOS = iRemoteShipmentOrderClient.getPackMaterialList(param);
//        if (!CollectionUtils.isEmpty(shipmentOrderMaterialDTOS)) {
//            //如何一个出库单未找到包材，不提示
//            List<String> packShipmentCode = shipmentOrderMaterialDTOS.stream().map(ShipmentOrderMaterialDTO::getShipmentOrderCode).distinct().collect(Collectors.toList());
//            if (packShipmentCode.size() != pickShipmentCode.size()) {
//                return false;
//            }
//            //
//            long count = shipmentOrderMaterialDTOS.stream().map(ShipmentOrderMaterialDTO::getActualPackUpcCode).distinct().count();
//            if (count > 1) {
//                return false;
//            }
//
//            //查询单个出库单的包材数量 和其他包材数量不一致
//            String shipmentOrderCode = pickDetailDTOS.get(0).getShipmentOrderCode();
//            Long singlePackMaterialSize = shipmentOrderMaterialDTOS.stream().filter(a -> Objects.equals(a.getShipmentOrderCode(), shipmentOrderCode)).count();
//            if (pickShipmentCode.stream().anyMatch(a -> {
//                Long tempSize = shipmentOrderMaterialDTOS.stream().filter(a1 -> Objects.equals(a1.getShipmentOrderCode(), a)).count();
//                if (!Objects.equals(tempSize, singlePackMaterialSize)) {
//                    return true;
//                } else {
//                    return false;
//                }
//            })) {
//                return false;
//            }
//        }
        return true;
    }

    /**
     * @param packageDTO
     * @param param
     * @return java.lang.Boolean
     * @author: WuXian
     * description:  是否校验溯源码 true 校验 false 不校验
     * create time: 2021/9/13 10:32
     */
    private Boolean checkSourceParam(PackageDTO packageDTO, PackSubmitParam param) {
        //是否扫描溯源码
        Boolean isCheck = false;
        //预包包裹不校验溯源码
        if (packageDTO.getIsPre().equalsIgnoreCase(PackEnum.TYPE.NORMAL.getCode())) {
            isCheck = isCheckSource(packageDTO.getCargoCode());
        }
        if (isCheck) {
            CargoConfigDTO cargoConfigDTO = remoteCargoConfigClient.queryByCargoCodeAndpropKey(packageDTO.getWarehouseCode(), packageDTO.getCargoCode(), CargoConfigParamEnum.SOURCE_CODE_PATTERN.getCode());
            if (CollectionUtils.isEmpty(param.getSourceCodeParamList())) {
                throw new BaseException(BaseBizEnum.TIP, "当前货主开启溯源码,溯源码不能为空");
            }
            if (cargoConfigDTO == null || !cargoConfigDTO.getStatus().equals(CargoConfigStatusEnum.ENABLE.getValue())) {
                throw new BaseException(BaseBizEnum.TIP, "当前货主溯源码规则未启用或者未配置");
            }

            if (StringUtils.isEmpty(cargoConfigDTO.getPropValue())) {
                throw new BaseException(BaseBizEnum.TIP, "当前货主开启溯源码,溯源码规则不能为空,请维护!!!");
            }
            BigDecimal qty = param.getSkuBackParamList().stream().map(PackSubExpNoParam::getQty).reduce(BigDecimal.ZERO, BigDecimal::add);
            Long num = param.getSourceCodeParamList().stream().map(PackSourceCodeParam::getSourceCode).distinct().count();
            if (qty.compareTo(new BigDecimal(num + "")) != 0) {
                throw new BaseException(BaseBizEnum.TIP, "溯源码,与商品数不匹配");
            }
            param.getSourceCodeParamList().forEach(a -> {
                if (cargoConfigDTO != null && !StringUtils.isEmpty(cargoConfigDTO.getPropValue()) && !Pattern.matches(cargoConfigDTO.getPropValue(), a.getSourceCode())) {
                    throw new BaseException(BaseBizEnum.TIP, String.format("溯源码%s格式规则不符合", a.getSourceCode()));
                }
                OutSourceCodeParam outSourceCodeParam = new OutSourceCodeParam();
                outSourceCodeParam.setSnCode(a.getSourceCode());
                if (remoteOutSourceCodeClient.checkExits(outSourceCodeParam)) {
                    throw new BaseException(BaseBizEnum.TIP, String.format("溯源码:%s已使用", a.getSourceCode()));
                }
            });
        }
        return isCheck;
    }

    /**
     * 修改出库单状态码
     *
     * @param packageDTO
     * @return
     */
    private ShipmentOrderDTO modifyShipment(PackageDTO packageDTO, StringBuilder saveShipmentLog) {
        ShipmentOrderDTO shipmentOrderDTO = iRemoteShipmentOrderClient.getShipmentOrderByCode(packageDTO.getShipmentOrderCode());
        if (StringUtils.isEmpty(shipmentOrderDTO)) {
            throw new BaseException(CollectWaveErrorEnum.STOCK_SHIPMENT_ERROR);
        }
        if (shipmentOrderDTO.getStatus().equals(ShipmentOrderEnum.STATUS.BEGIN_CHECK_STATUS.getCode())) {
            saveShipmentLog.append("30");
        }
        if (shipmentOrderDTO.getStatus().equals(ShipmentOrderEnum.STATUS.BEGIN_CHECK_STATUS.getCode())) {
            shipmentOrderDTO.setStatus(ShipmentOrderEnum.STATUS.CHECK_COMPLETE_STATUS.getCode());
        }
        shipmentOrderDTO.setCheckCompleteDate(System.currentTimeMillis());
        List<ShipmentOrderDetailDTO> shipmentOrderDetailDTOS = iRemoteShipmentOrderClient.queryShipmentOrderDetailList(packageDTO.getShipmentOrderCode());
        if (CollectionUtils.isEmpty(shipmentOrderDetailDTOS)) {
            throw new BaseException(CollectWaveErrorEnum.STOCK_SHIPMENT_ERROR);
        }
        List<PackageDetailDTO> packageDetailDTOS = packageDTO.getListDetail();
        for (ShipmentOrderDetailDTO entity : shipmentOrderDetailDTOS) {
            PackageDetailDTO packageDetailDTO = packageDetailDTOS.stream().filter(a -> a.getIsPre().equalsIgnoreCase(PackIsPreEnum.NORMAL.getCode())).filter(a -> Objects.equals(a.getPUid(), entity.getId())).findFirst().orElse(null);
            if (packageDetailDTO != null) {
                entity.setCheckQty(entity.getCheckQty().add(packageDetailDTO.getCheckQty()));
                //TODO add 2021-04-12 复核数量不能大于计划数量
                if (entity.getCheckQty().compareTo(entity.getExpSkuQty()) > 0) {
                    throw new BaseException(BaseBizEnum.TIP, String.format("出库单:%s商品:%s复核数量不能大于计划数量", entity.getShipmentOrderCode(), entity.getSkuCode()));
                }
                if (entity.getStatus().equals(ShipmentOrderEnum.STATUS.BEGIN_CHECK_STATUS.getCode())) {
                    entity.setStatus(ShipmentOrderEnum.STATUS.CHECK_COMPLETE_STATUS.getCode());
                }
            }
        }
        shipmentOrderDTO.setListShipmentOrderDetailDTO(shipmentOrderDetailDTOS);
        return shipmentOrderDTO;
    }

    /**
     * 检查sku
     *
     * @param packageDetailDTOS
     * @param param
     */
    private void checkSkuQty(List<PackageDetailDTO> packageDetailDTOS, PackSubmitParam param, PackageDTO packageDTO) {
        List<PackageDetailDTO> packageDetailDTOList;
        if (packageDTO.getIsPre().equalsIgnoreCase(PackEnum.TYPE.PRE.getCode())) {
            packageDetailDTOList = packageDetailDTOS.stream().filter(a -> a.getIsPre().equalsIgnoreCase(PackIsPreEnum.PRE.getCode()) || a.getIsPre().equalsIgnoreCase(PackIsPreEnum.LAST.getCode())).collect(Collectors.toList());
        } else {
            packageDetailDTOList = packageDetailDTOS.stream().filter(a -> a.getIsPre().equalsIgnoreCase(PackIsPreEnum.NORMAL.getCode())).collect(Collectors.toList());
        }
        //校验包裹数量和商品是否一致
        List<PackSubExpNoParam> skuBackParamList = param.getSkuBackParamList();
        List<String> expSkuList = packageDetailDTOList.stream().map(PackageDetailDTO::getSkuCode).collect(Collectors.toList());
        List<String> subSkuList = skuBackParamList.stream().map(PackSubExpNoParam::getSkuCode).collect(Collectors.toList());
        for (String entity : expSkuList) {
            BigDecimal subQty = skuBackParamList.stream().filter(a -> Objects.equals(a.getSkuCode(), entity)).map(PackSubExpNoParam::getQty).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal expQty = packageDetailDTOList.stream().filter(a -> Objects.equals(a.getSkuCode(), entity)).map(PackageDetailDTO::getPickQty).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (subQty.compareTo(expQty) != 0) {
                throw new BaseException(BaseBizEnum.TIP, String.format("商品编码:%s,提交数量和包裹不一致", param.getPickCode()));
            }
        }
        expSkuList.removeAll(subSkuList);
        if (!CollectionUtils.isEmpty(expSkuList)) {
            throw new BaseException(BaseBizEnum.TIP, String.format("提交包裹商品与包裹不一致,请核查"));
        }
    }

    @Override
    public Result<PackExpressBizDTO> checkExpressNoOrPackageCode(PackCheckFrontParam param) {
        //校验有效运单号 针对多个相同运单号处理 只找拣选单明细包裹为 TODO 原始包裹的数据
        PickDetailDTO pickDetailDTO = checkPackageCodeOrExpressNo(param.getCode());
        if (pickDetailDTO == null) {
            throw new BaseException(BaseBizEnum.TIP, String.format("单据号:%s未找到需要复核包裹", param.getCode()));
        }
        //TODO 原始拣选单号
        PickDTO pickDTO = checkPick(pickDetailDTO.getPickCode());
        //拦截单
        List<String> interceptPackCodeList = interceptionManagerBiz.queryInterceptionPackTipOperation(Arrays.asList(pickDetailDTO.getPackageCode()));
        if (!CollectionUtils.isEmpty(interceptPackCodeList)) {
            Map<String, Object> map = new HashMap<>();
            map.put("num", interceptPackCodeList.size());
            map.put("interceptionExpressNoList", Arrays.asList(pickDetailDTO.getExpressNo()));
            map.put("packageCode", pickDetailDTO.getPackageCode());
            map.put("pickCode", pickDetailDTO.getPickCode());
            throw new BaseException(JSONUtil.toJsonStr(map), BaseBizEnum.TIP_HANDLE, "包裹拦截明细");
        }
        if (Integer.valueOf(pickDetailDTO.getPackageStatus()) < Integer.valueOf(PackEnum.STATUS.PICK_COMPELETE_STATUS.getCode())) {
            throw new BaseException(PickErrorEnum.PACK_STRING_ERROR, String.format("单号:%s包裹不能进行复核", param.getCode()));
        }
        if (Integer.valueOf(pickDetailDTO.getPackageStatus()) >= Integer.valueOf(PackEnum.STATUS.CHECK_COMPELETE_STATUS.getCode())) {
            throw new BaseException(PickErrorEnum.PACK_STRING_ERROR, String.format("单号:%s包裹已复核完成", param.getCode()));
        }
        PackExpressBizDTO packBackExpressBizDTO;
        //原始拣选单
        PickParam pickParam = new PickParam();
        if (pickDTO.getPickFlag().equals(PickEnum.PickFlagEnum.ORIGIN.getCode())) {
            pickParam.setPickCode(pickDTO.getPickCode());
            List<PickDetailDTO> pickDetailList = iRemotePickClient.getPickDetailList(pickParam);
            if (CollectionUtils.isEmpty(pickDetailList)) {
                throw new BaseException(BaseBizEnum.TIP, String.format("拣选单号:%s,未找到需要复核的包裹", pickDTO.getPickCode()));
            }
            packBackExpressBizDTO = checkAndSubmitPack(pickDTO.getPickCode(), pickDetailDTO.getPackageCode(), pickDetailDTO.getBasketNo(), pickDetailDTO.getShipmentOrderCode(), pickDetailList);
            //提交数据
            submitPackCheckBackBO(pickDTO, Arrays.asList(pickDetailDTO), packBackExpressBizDTO.getPackMaterialVO());
        } else {
            pickParam.setPickCode(pickDTO.getMergePickCode());
            List<PickDetailDTO> pickDetailList = iRemotePickClient.getPickDetailList(pickParam);
            if (CollectionUtils.isEmpty(pickDetailList)) {
                throw new BaseException(BaseBizEnum.TIP, String.format("拣选单号:%s,未找到需要复核的包裹", pickDTO.getMergePickCode()));
            }
            PickDetailDTO pickMergeDetailDTO = pickDetailList.stream().filter(a -> a.getExpressNo().equalsIgnoreCase(param.getCode()) || a.getPackageCode().equalsIgnoreCase(param.getCode())).findFirst().orElse(null);
            if (pickMergeDetailDTO == null) {
                throw new BaseException(BaseBizEnum.TIP, String.format("单据号:%s未找到需要复核包裹", param.getCode()));
            }
            PickDTO mergePickDTO = iRemotePickClient.queryPickByPickCode(pickDTO.getMergePickCode());
            if (mergePickDTO == null) {
                throw new BaseException(BaseBizEnum.TIP, "聚合拣选单复核异常,请核查2");
            }
            packBackExpressBizDTO = checkAndSubmitPack(mergePickDTO.getPickCode(), pickMergeDetailDTO.getPackageCode(), pickMergeDetailDTO.getBasketNo(), pickMergeDetailDTO.getShipmentOrderCode(), pickDetailList);
            submitPackCheckBackBO(pickDTO, Arrays.asList(pickDetailDTO, pickMergeDetailDTO), packBackExpressBizDTO.getPackMaterialVO());
        }
        packBackExpressBizDTO.setWarehouseCode(pickDTO.getWarehouseCode());
        packBackExpressBizDTO.setTaotainTag(remoteWarehouseClient.getTaoTianWarehouse(pickDTO.getWarehouseCode()));
        //复核赠品展示
        packBackExpressBizDTO.setShowGiftList(iRemotePickClient.getGiftPickToCheck(pickDTO.getPickCode(), pickDetailDTO.getPackageCode()));
        return Result.success(packBackExpressBizDTO);
    }

    /**
     * @param code
     * @return void
     * @author: WuXian
     * description:
     * create time: 2021/11/1 13:41
     */
    private PickDetailDTO checkPackageCodeOrExpressNo(String code) {
        if (StringUtils.isEmpty(code)) {
            throw new BaseException(BaseBizEnum.TIP, "单号不能为空");
        }
        PackageParam packageParam = new PackageParam();
        if (code.toUpperCase().startsWith(SeqEnum.PACK_CODE_000001.getPrefix())) {
            packageParam.setPackageCode(code);
        } else {
            packageParam.setExpressNo(code);
        }
        List<PackageDTO> packageDTOList = iRemotePackageClient.getList(packageParam);
        if (CollectionUtils.isEmpty(packageDTOList)) {
            throw new BaseException(BaseBizEnum.TIP, String.format("单据号:%s未找到有效的包裹,请核查单据号", code));
        }
        //无效单据号状态码
        List<String> invalidStatusList = new ArrayList<>();
        invalidStatusList.add(PackEnum.STATUS.PART_ASSIGN_COMPLETE_STATUS.getCode());
        invalidStatusList.add(PackEnum.STATUS.PART_ASSIGN_CANCEL_STATUS.getCode());
        invalidStatusList.add(PackEnum.STATUS.PART_ASSIGN_STATUS.getCode());

        //可复核有效单据号状态码
        List<String> checkStatusList = new ArrayList<>();
        checkStatusList.add(PackEnum.STATUS.PICK_COMPELETE_STATUS.getCode());
        checkStatusList.add(PackEnum.STATUS.CHECK_BEGIN_STATUS.getCode());
        if (packageDTOList.size() == 1) {
            PackageDTO packageDTO = packageDTOList.get(0);
            return checkPack(packageDTO, invalidStatusList, checkStatusList, code);
        } else {
            //移除无效的单据
            packageDTOList = packageDTOList.stream().filter(a -> !invalidStatusList.contains(a.getStatus())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(packageDTOList)) {
                throw new BaseException(BaseBizEnum.TIP, String.format("单据号:%s未找到有效的包裹,请核查单据号", code));
            }
            //不同的快递公司运单号相同
            if (!code.toUpperCase().startsWith(SeqEnum.PACK_CODE_000001.getPrefix()) && packageDTOList.stream().map(PackageDTO::getCarrierCode).distinct().count() > 1) {
                throw new BaseException(BaseBizEnum.TIP, String.format("单据号:%s找到两个快递公司的有效包裹,请使用包裹号复核", code));
            }
            if (packageDTOList.size() == 1) {
                PackageDTO packageDTO = packageDTOList.get(0);
                return checkPack(packageDTO, invalidStatusList, checkStatusList, code);
            }
            //packageDTOList.size() 大于1 证明有拦截单,后置未触发拦截
            if (!code.toUpperCase().startsWith(SeqEnum.PACK_CODE_000001.getPrefix()) && packageDTOList.stream().map(PackageDTO::getPoNo).distinct().count() > 1) {
                throw new BaseException(BaseBizEnum.TIP, String.format("单据号:%s找到同一个快递公司且上游单据号不同的有效包裹多个,请使用包裹号复核", code));
            }
            //过滤拦截单 此运单号对应多个有效发货状态包裹，请确认后继续操作
            throw new BaseException(BaseBizEnum.TIP, String.format("单据号:%s对应多个有效发货状态包裹，请确认后继续操作", code));
        }
    }

    /**
     * @param packageDTO
     * @param invalidStatusList
     * @param checkStatusList
     * @param code
     * @return com.dt.domain.bill.dto.PickDetailDTO
     * @author: WuXian
     * description:  复核校验包裹
     * create time: 2021/11/1 14:10
     */
    private PickDetailDTO checkPack(PackageDTO packageDTO, List<String> invalidStatusList, List<String> checkStatusList, String code) {
        if (invalidStatusList.contains(packageDTO.getStatus())) {
            throw new BaseException(BaseBizEnum.TIP, String.format("单据号:%s已被取消或拦截,请核查单据号", code));
        }
        if (!checkStatusList.contains(packageDTO.getStatus())) {
            throw new BaseException(BaseBizEnum.TIP, String.format("包裹%s不可操作复核，请检查包裹状态后继续操作", code));
        }
        if (packageDTO.getStatus().equalsIgnoreCase(PackEnum.STATUS.CHECK_COMPELETE_STATUS.getCode())) {
            throw new BaseException(BaseBizEnum.TIP, String.format("单据号:%s包裹已完成复核", code));
        }
        if (packageDTO.getStatus().equalsIgnoreCase(PackEnum.STATUS.PREPARE_HANDLER_STATUS.getCode())) {
            throw new BaseException(BaseBizEnum.TIP, String.format("单据号:%s包裹已出库", code));
        }
        PickParam pickParam = new PickParam();
        pickParam.setPackageCode(packageDTO.getPackageCode());
        List<PickDetailDTO> pickDetailDTOList = iRemotePickClient.getPickDetailList(pickParam);
        if (CollectionUtils.isEmpty(pickDetailDTOList)) {
            throw new BaseException(BaseBizEnum.TIP, String.format("单据号:%s拣选单未找到包裹,请核查单据号", code));
        }
        pickDetailDTOList = pickDetailDTOList.stream().filter(a -> a.getFlag().equals(PickEnum.PickDetailFlagEnum.ORIGIN.getCode())).filter(a -> !invalidStatusList.contains(a.getPackageStatus())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(pickDetailDTOList)) {
            throw new BaseException(BaseBizEnum.TIP, String.format("单据号:%s拣选单未找到包裹,请核查单据号", code));
        }
        return pickDetailDTOList.get(0);
    }

    @Override
    public Result<PackExpressBizDTO> scanUpcCode(PackCheckUpcCodeParam param) {
        //校验拣选单号
        PickDTO pickDTO = checkPick(param.getPickCode());
        //查询当前拣选单复核-对应所有的包裹
        PickParam pickParam = new PickParam();
        pickParam.setPickCode(pickDTO.getPickCode());
        List<PickDetailDTO> pickDetailDTOList = iRemotePickClient.getPickDetailList(pickParam);
        if (CollectionUtils.isEmpty(pickDetailDTOList)) {
            throw new BaseException(PickErrorEnum.PACK_DETAIL_DATA_ERROR, param.getPickCode());
        }
        if (pickDetailDTOList.stream().allMatch(entity -> Objects.equals(entity.getPackageStatus(), PackEnum.STATUS.CHECK_COMPELETE_STATUS.getCode()))) {
            throw new BaseException(PickErrorEnum.PICK_CONPLETE, param.getPickCode());
        }
        //upc查找sku
        SkuUpcParam upcParam = new SkuUpcParam();
        upcParam.setWarehouseCode(pickDTO.getWarehouseCode());
        upcParam.setCargoCode(pickDTO.getCargoCode());
        upcParam.setUpcCode(param.getUpcCode());
        List<SkuUpcDTO> skuUpcDTOList = iRemoteSkuClient.getSkuUpcList(upcParam);
        if (CollectionUtils.isEmpty(skuUpcDTOList)) {
            throw new BaseException(PickErrorEnum.PACK_UPC_ERROR_DATA_ERROR, param.getUpcCode());
        }
        SkuUpcDTO skuUpcDTO = skuUpcDTOList.get(0);
        if (Objects.equals(SkuStatusEnum.STATUS_FORBIDDEN.getStatus(), skuUpcDTO.getStatus())) {
            throw new BaseException(BaseBizEnum.TIP, String.format("商品条码:%s状态是禁用，不允许使用。", param.getUpcCode()));
        }
        upcParam.setSkuCode(skuUpcDTO.getSkuCode());
        upcParam.setUpcCode(null);
        List<SkuUpcDTO> allSkuUpcDTOList = iRemoteSkuClient.getSkuUpcList(upcParam);

        if (skuUpcDTOList.stream().map(SkuUpcDTO::getSkuCode).distinct().count() > 1) {
            throw new BaseException(PickErrorEnum.PACK_UPC_SKU_ERROR_DATA_ERROR, param.getUpcCode());
        }
        String skuCode = skuUpcDTO.getSkuCode();
        SkuUomDTO skuUomDTO = iRemoteSkuClient.querySkuUomBySkuCode(skuUpcDTO.getCargoCode(), skuCode, skuUpcDTO.getPackageUnitCode());
        if (skuUomDTO == null) {
            throw new BaseException(BaseBizEnum.TIP, String.format("商品条码:%s对应UOM异常,请维护", param.getUpcCode()));
        }
        //找出当前拣选单最小蓝号的包裹
        List<String> packageCodeS = pickDetailDTOList.stream().filter(entity -> (Objects.equals(PackEnum.STATUS.PICK_COMPELETE_STATUS.getCode(), entity.getPackageStatus()) || Objects.equals(PackEnum.STATUS.CHECK_BEGIN_STATUS.getCode(), entity.getPackageStatus()))).map(PickDetailDTO::getPackageCode).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(packageCodeS)) {
            throw new BaseException(BaseBizEnum.TIP, String.format("条码:%s,未找到需要复核的包裹", param.getUpcCode()));
        }
        PackageDetailParam packageDetailParam = new PackageDetailParam();
        packageDetailParam.setPackageCodeList(packageCodeS);
        packageDetailParam.setWarehouseCode(pickDTO.getWarehouseCode());
        packageDetailParam.setCargoCode(pickDTO.getCargoCode());
        List<PackageDetailDTO> originPackageDetailDTOList = iRemotePackageClient.getPackageDetailListByListCode(packageDetailParam);
        if (CollectionUtils.isEmpty(originPackageDetailDTOList)) {
            throw new BaseException(PickErrorEnum.PACK_CODE_DATA_ERROR, param.getPickCode());
        }
        //考虑预包包裹
        List<PackageDetailDTO> packageDetailDTOS = buildPrePackageDetail(packageCodeS, originPackageDetailDTOList);
        //找出能匹配的商品的包裹号
        packageCodeS = packageDetailDTOS.stream().filter(entity -> Objects.equals(skuCode, entity.getSkuCode())).map(PackageDetailDTO::getPackageCode).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(packageCodeS)) {
            throw new BaseException(PickErrorEnum.PACK_CODE_DATA_ERROR, param.getPickCode());
        }
        //找出最小蓝号的包裹
        List<String> finalPackageCodeS = packageCodeS;
        List<String> basketNoList = pickDetailDTOList.stream().filter(entity -> finalPackageCodeS.contains(entity.getPackageCode())).map(a -> {
            return String.format("%02d", Integer.parseInt(a.getBasketNo()));
        }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(basketNoList)) {
            throw new BaseException(PickErrorEnum.PACK_CODE_DATA_ERROR, param.getPickCode());
        }
        String basketNo = basketNoList.stream().sorted().findFirst().orElse("");
        if (StringUtils.isEmpty(basketNo)) {
            throw new BaseException(PickErrorEnum.PACK_UPC_ERROR_ERROR, param.getUpcCode());
        }
        basketNo = String.valueOf(Integer.valueOf(basketNo));
        //获取蓝号指定的包裹
        String finalBasketNo = basketNo;
        PickDetailDTO pickDetailDTO = pickDetailDTOList.stream().filter(entity -> Objects.equals(finalBasketNo, entity.getBasketNo())).filter(entity -> (Objects.equals(entity.getPackageStatus(), PackEnum.STATUS.CHECK_BEGIN_STATUS.getCode())) || Objects.equals(entity.getPackageStatus(), PackEnum.STATUS.PICK_COMPELETE_STATUS.getCode())).findFirst().orElse(null);
        if (pickDetailDTO == null || Objects.equals(pickDetailDTO.getPackageStatus(), PackEnum.STATUS.CHECK_COMPELETE_STATUS.getCode())) {
            throw new BaseException(PickErrorEnum.PACK_UPC_ERROR_ERROR, param.getUpcCode());
        }
        //拦截单
        List<String> interceptPackCodeList = interceptionManagerBiz.queryInterceptionPackTipOperation(Arrays.asList(pickDetailDTO.getPackageCode()));
        if (!CollectionUtils.isEmpty(interceptPackCodeList)) {
            Map<String, Object> map = new HashMap<>();
            map.put("num", interceptPackCodeList.size());
            map.put("interceptionExpressNoList", Arrays.asList(pickDetailDTO.getExpressNo()));
            map.put("packageCode", pickDetailDTO.getPackageCode());
            map.put("pickCode", pickDetailDTO.getPickCode());
            throw new BaseException(JSONUtil.toJsonStr(map), BaseBizEnum.TIP_HANDLE, "包裹拦截明细");
        }

        PackExpressBizDTO packBackExpressBizDTO = checkAndSubmitPack(pickDTO.getPickCode(), pickDetailDTO.getPackageCode(), pickDetailDTO.getBasketNo(), pickDetailDTO.getShipmentOrderCode(), pickDetailDTOList);
        //是否开启溯源码
        //如果开启了溯源码，校验是否是最小包装单位的条码
        if (packBackExpressBizDTO.getCheckSourceCode()) {
            //查询uom
            SkuUomParam skuUomParam = new SkuUomParam();
            skuUomParam.setCargoCode(pickDTO.getCargoCode());
            skuUomParam.setSkuCode(skuCode);
            List<SkuUomDTO> skuUomDTOList = iRemoteSkuClient.getSkuUomList(skuUomParam);
            //首先根据upc 筛选 符合的uom
            List<String> upcPackageUnitCode = allSkuUpcDTOList.stream().map(SkuUpcDTO::getPackageUnitCode).distinct().collect(Collectors.toList());
            skuUomDTOList = skuUomDTOList.stream().filter(skuUom -> upcPackageUnitCode.contains(skuUom.getPackageUnitCode())).collect(Collectors.toList());
            //获取uom 最小的包装单位数量
            SkuUomDTO minxSkuUomDTO = skuUomDTOList.stream().min(Comparator.comparing(SkuUomDTO::getPackageQty)).get();
            //根据最小值获取符合的uom
            List<SkuUomDTO> minSkuUomDTOList = skuUomDTOList.stream().filter(skuUomDTO1 -> skuUomDTO1.getPackageQty().compareTo(minxSkuUomDTO.getPackageQty()) == 0).collect(Collectors.toList());
            List<Long> skuUomIdList = minSkuUomDTOList.stream().map(BaseDTO::getId).collect(Collectors.toList());
            if (!skuUomIdList.contains(skuUomDTO.getId())) {
                throw new BaseException(BaseBizEnum.TIP, String.format("货主开启溯源码扫描，必须扫描最小包装件数的条码%s", param.getUpcCode()));
            }
        }
        //当前扫描将扫描数量添加进去
        if (packBackExpressBizDTO.getWaitQty().compareTo(skuUomDTO.getPackageQty()) < 0) {
            throw new BaseException(BaseBizEnum.TIP, String.format("商品条码:%s对应UOM超出待扫数量,请核查商品条码", param.getUpcCode()));
        }
        packBackExpressBizDTO.setExpressCheckSkuQty(skuUomDTO.getPackageQty());
        List<PackSkuBizDTO> packSkuBizDTOList = packBackExpressBizDTO.getPackSkuVOS();
        for (PackSkuBizDTO packSkuBizDTO : packSkuBizDTOList) {
            if (Objects.equals(packSkuBizDTO.getSkuCode(), skuCode)) {
                if (packSkuBizDTO.getExpQty().subtract(packSkuBizDTO.getQty()).compareTo(skuUomDTO.getPackageQty()) < 0) {
                    throw new BaseException(BaseBizEnum.TIP, String.format("商品条码:%s对应UOM超出待扫数量,请核查商品条码", param.getUpcCode()));
                }
                packSkuBizDTO.setQty(packSkuBizDTO.getQty().add(skuUomDTO.getPackageQty()));
                break;
            }
        }
        packBackExpressBizDTO.setPackSkuVOS(packSkuBizDTOList);
        //提交数据
        submitPackCheckBackBO(pickDTO, Arrays.asList(pickDetailDTO), packBackExpressBizDTO.getPackMaterialVO());
        packBackExpressBizDTO.setTaotainTag(remoteWarehouseClient.getTaoTianWarehouse(pickDTO.getWarehouseCode()));
        packBackExpressBizDTO.setWarehouseCode(pickDTO.getWarehouseCode());
        packBackExpressBizDTO.setDeliveryRequirement("");
        packBackExpressBizDTO.setShowGiftList(iRemotePickClient.getGiftPickToCheck(pickDTO.getPickCode(), pickDetailDTO.getPackageCode()));
        return Result.success(packBackExpressBizDTO);
    }

    /**
     * @param packageCodeS
     * @param originPackageDetailDTOList
     * @return java.util.List<com.dt.domain.bill.dto.PackageDetailDTO>
     * @author: WuXian
     * description:
     * create time: 2021/9/10 17:51
     */
    private List<PackageDetailDTO> buildPrePackageDetail(List<String> packageCodeS, List<PackageDetailDTO> originPackageDetailDTOList) {
        PackageParam packageParam = new PackageParam();
        packageParam.setPackageCodeList(packageCodeS);
        List<PackageDTO> packageDTOList = iRemotePackageClient.getList(packageParam);
        if (CollectionUtils.isEmpty(packageDTOList)) {
            throw new BaseException(BaseBizEnum.TIP, "包裹不存在");
        }
        List<String> packageIsPreList = packageDTOList.stream().filter(a -> a.getIsPre().equalsIgnoreCase(PackEnum.TYPE.PRE.getCode())).map(PackageDTO::getPackageCode).collect(Collectors.toList());
        List<PackageDetailDTO> packageDetailDTOList = new ArrayList<>();
        //非预包包裹
        packageDetailDTOList.addAll(originPackageDetailDTOList.stream().filter(a -> !packageIsPreList.contains(a.getPackageCode())).collect(Collectors.toList()));
        //预包包裹
        packageDetailDTOList.addAll(originPackageDetailDTOList.stream().filter(a -> packageIsPreList.contains(a.getPackageCode())).filter(a -> a.getIsPre().equalsIgnoreCase(PackIsPreEnum.PRE.getCode()) || a.getIsPre().equalsIgnoreCase(PackIsPreEnum.LAST.getCode())).collect(Collectors.toList()));
        return packageDetailDTOList;
    }

//    @Override
//    public Result<Boolean> checkSourceCode(PackCheckSourceCodeParam param) {
//        PackageDTO packageDTO = iRemotePackageClient.getPackageByCode(param.getPackageCode());
//        if (packageDTO == null) {
//            throw new BaseException(BaseBizEnum.TIP, "包裹未找到!!!");
//        }
//        CargoConfigDTO cargoConfigDTO = remoteCargoConfigClient.queryByCargoCodeAndpropKey(packageDTO.getWarehouseCode(), packageDTO.getCargoCode(), CargoConfigParamEnum.SOURCE_CODE.getCode());
//        if (cargoConfigDTO == null
//                || !cargoConfigDTO.getStatus().equals(CargoConfigStatusEnum.ENABLE.getValue())
//                || !cargoConfigDTO.getPropValue().equals("1")) {
//            throw new BaseException(BaseBizEnum.TIP, "当前货主未开启溯源码");
//        }
//        cargoConfigDTO = remoteCargoConfigClient.queryByCargoCodeAndpropKey(packageDTO.getWarehouseCode(), packageDTO.getCargoCode(), CargoConfigParamEnum.SOURCE_CODE_PATTERN.getCode());
//        if (cargoConfigDTO != null
//                && !StringUtils.isEmpty(cargoConfigDTO.getPropValue())
//                && !Pattern.matches(cargoConfigDTO.getPropValue(), param.getSourceCode())) {
//            throw new BaseException(BaseBizEnum.TIP, "溯源码格式规则不符合");
//        }
//        OutSourceCodeParam outSourceCodeParam = new OutSourceCodeParam();
//        outSourceCodeParam.setCargoCode(packageDTO.getCargoCode());
//        outSourceCodeParam.setSnCode(param.getSourceCode());
//        if (remoteOutSourceCodeClient.checkExits(outSourceCodeParam)) {
//            throw new BaseException(BaseBizEnum.TIP, "溯源码已使用");
//        }
//        return Result.success(Boolean.TRUE);
//    }

    @Override
    public Result<PackBatchCheckResultBizDTO> batchCheckComplete(CodeParam param) throws Exception {
        log.info("B2C batchCheckComplete warehouseCode:{} param:{}", CurrentRouteHolder.getWarehouseCode(), JSONUtil.toJsonStr(param));
        String key = CurrentRouteHolder.getWarehouseCode() + "" + param.getCode();
        RLock lock = redissonClient.getLock("dt_wms_batch_pack_pick_code_lock:" + key);
        //返回打印参数
        PackBatchCheckResultBizDTO packCheckResultBizDTO = new PackBatchCheckResultBizDTO();
        Boolean tryLock = false;
        try {
            tryLock = lock.tryLock(1, 120, TimeUnit.SECONDS);
            if (!tryLock) {
                throw new BaseException(BaseBizEnum.TIP, "不能重复提交,请稍后提交");
            }
            //批量复核要不要考虑面单，清单，箱唛前置打印
            PickDTO pickDTO = checkPick(param.getCode());
            Boolean isFlag = checkBatchCheck(pickDTO);
            if (!isFlag) {
                throw new BaseException(BaseBizEnum.TIP, "当前拣选单不能批量复核");
            }
            PickParam pickParam = new PickParam();
            pickParam.setPickCode(pickDTO.getPickCode());
            List<PickDetailDTO> pickDetailDTOList = iRemotePickClient.getPickDetailList(pickParam);
            if (CollectionUtils.isEmpty(pickDetailDTOList)) {
                throw new BaseException(BaseBizEnum.TIP, String.format("拣选单号:%s,未找到需要复核的包裹", param.getCode()));
            }
            //如果秒杀单有局部单据已出库,不允许批量出库
            checkPickOrderPackageStatus(pickDTO.getPickCode(), pickDetailDTOList.stream().map(PickDetailDTO::getPackageCode).distinct().collect(Collectors.toList()));

            //修改拣选单的状态
            pickDTO.setStatus(PickEnum.PickStatusEnum.ZJ_END_STATUS.getCode());
            pickDTO.setCheckEndTime(System.currentTimeMillis());
            //只处理拣选结束和复核开始的包裹
            List<PickDetailDTO> batchPickDetailList = pickDetailDTOList.stream().filter(a -> Objects.equals(a.getPackageStatus(), PackEnum.STATUS.PICK_COMPELETE_STATUS.getCode()) || Objects.equals(a.getPackageStatus(), PackEnum.STATUS.CHECK_BEGIN_STATUS.getCode())).collect(Collectors.toList());
            //拦截单触发-不拦截（返回拦截的出库单号）
            List<String> interceptPackCodeList = interceptionManagerBiz.queryInterceptionPackTipOperation(batchPickDetailList.stream().map(PickDetailDTO::getPackageCode).collect(Collectors.toList()));
            if (!CollectionUtils.isEmpty(interceptPackCodeList)) {
                Map<String, Object> map = new HashMap<>();
                map.put("num", interceptPackCodeList.size());
                //只处理拣选完成和复核开始的包裹
                List<String> interceptExpressNoList = batchPickDetailList.stream().filter(a -> interceptPackCodeList.contains(a.getPackageCode())).filter(a -> Arrays.asList(PackEnum.STATUS.PICK_COMPELETE_STATUS.getCode(), PackEnum.STATUS.CHECK_BEGIN_STATUS.getCode()).contains(a.getPackageStatus())).map(PickDetailDTO::getExpressNo).collect(Collectors.toList());
                map.put("interceptionExpressNoList", interceptExpressNoList);
                if (!CollectionUtils.isEmpty(interceptExpressNoList)) {
                    throw new BaseException(JSONUtil.toJsonStr(map), BaseBizEnum.TIP_HANDLE_TWO, "包裹拦截明细");
                }
            }
// -----------------------------------------------------------
//            //待复核包裹号
//            List<String> originPackCodeList = batchPickDetailListTemp.stream().map(PickDetailDTO::getPackageCode).collect(Collectors.toList());
//            //批量过滤拦截单 返回需要拦截包裹号
//            List<String> packCodeList = interceptionManagerBiz.interceptionBatch(PackReturnEnum.INTERCEPT_STATUS.PACKAGE_CHECK_POINT.getCode(), batchPickDetailListTemp);
//
//            if (CollectionUtils.isEmpty(packCodeList) || originPackCodeList.size() != packCodeList.size()) {
//                Boolean isComplete = checkPickComplete(pickDTO);
//                //处理拦截单
//                originPackCodeList.removeAll(packCodeList);
//                batchPickDetailListTemp = batchPickDetailListTemp.stream().filter(a -> originPackCodeList.contains(a.getPackageCode())).collect(Collectors.toList());
//                List<IdNameValueVO> idNameVOList = IdNameValueVO.build(batchPickDetailListTemp, "expressNo", "basketNo", "packageCode");
//                idNameVOList.forEach(a -> a.setName(String.format("%02d", Integer.parseInt(a.getName() + ""))));
//                if (isComplete) {
//                    pickDTO.setStatus(PickEnum.PickStatusEnum.ZJ_END_STATUS.getCode());
//                    iRemotePickClient.update(pickDTO);
//                    throw new BaseException(JSONUtil.toJsonStr(idNameVOList), BaseBizEnum.TIP_HANDLE_THREE, "包裹拦截明细");
//                } else {
//                    throw new BaseException(JSONUtil.toJsonStr(idNameVOList), BaseBizEnum.TIP_HANDLE, "包裹拦截明细");
//                }
//            }
//            List<PickDetailDTO> batchPickDetailList = batchPickDetailListTemp.stream().filter(a -> interceptPackCodeList.contains(a.getPackageCode())).collect(Collectors.toList());
//            if (CollectionUtils.isEmpty(interceptPackCodeList)) {
//                throw new BaseException(BaseBizEnum.TIP, "无批量复核的包裹");
//            }
// -----------------------------------------------------------
            BatchCheckBO batchCheckBO = new BatchCheckBO();
            //取后修改时间大的包裹
            PickDetailDTO completePickDetailDTO = pickDetailDTOList.stream().filter(a -> Objects.equals(a.getPackageStatus(), PackEnum.STATUS.CHECK_COMPELETE_STATUS.getCode())).sorted(Comparator.comparing(PickDetailDTO::getUpdatedTime, Comparator.reverseOrder())).findFirst().orElse(null);
            if (completePickDetailDTO == null) {
                throw new BaseException(BaseBizEnum.TIP, String.format("拣选单号:%s,未找到已复核的包裹", param.getCode()));
            }
            PackageDTO completePackageDTO = iRemotePackageClient.getPackageByCode(completePickDetailDTO.getPackageCode());
            if (completePackageDTO == null) {
                throw new BaseException(BaseBizEnum.TIP, String.format("拣选单号:%s,未找到已复核的包裹", param.getCode()));
            }
            if (completePackageDTO.getStatus().equalsIgnoreCase(PackEnum.STATUS.PREPARE_HANDLER_STATUS.getCode())) {
                throw new BaseException(BaseBizEnum.TIP, String.format("拣选单号:%s,运单号:%s已出库,不允许批量复核", param.getCode(), completePackageDTO.getExpressNo()));
            }
            if (!completePackageDTO.getStatus().equals(PackEnum.STATUS.CHECK_COMPELETE_STATUS.getCode())) {
                throw new BaseException(BaseBizEnum.TIP, String.format("拣选单号:%s,未找到已复核的包裹", param.getCode()));
            }
            //修改拣选单明细的复核状态码
            batchPickDetailList.forEach(a -> {
                a.setCheckQty(a.getPickQty());
                a.setPackageStatus(PackEnum.STATUS.CHECK_COMPELETE_STATUS.getCode());
            });

            pickDTO.setDetailList(batchPickDetailList);
            pickDTO.setBatchCheckStatus(PickBatchCheckEnum.YES_BATCH.getValue());
            batchCheckBO.setPickDTO(pickDTO);
            //修改包裹
            PackageParam packageParam = new PackageParam();
            packageParam.setPackageCodeList(batchPickDetailList.stream().map(PickDetailDTO::getPackageCode).distinct().collect(Collectors.toList()));
            List<PackageDTO> batchPackList = iRemotePackageClient.getList(packageParam);
            if (CollectionUtils.isEmpty(batchPackList)) {
                throw new BaseException(BaseBizEnum.TIP, "包裹查询异常");
            }
            batchPackList.forEach(a -> {
                a.setStatus(PackEnum.STATUS.CHECK_COMPELETE_STATUS.getCode());
                a.setCheckCompleteDate(System.currentTimeMillis());
                a.setCheckStartDate(System.currentTimeMillis());
                a.setRecPackUpc(completePackageDTO.getRecPackUpc());
                a.setActualPackUpc(completePackageDTO.getActualPackUpc());
                a.setActualPackNum(completePackageDTO.getActualPackNum());
                a.setActualPackWeight(completePackageDTO.getActualPackWeight());
                a.setVolume(completePackageDTO.getVolume());
                a.setWeight(completePackageDTO.getWeight());
                a.setVolumetricWeight(completePackageDTO.getVolumetricWeight());
                List<PackageDetailDTO> packageDetailDTOS = iRemotePackageClient.getPackageDetailListByCode(a.getPackageCode());
                if (CollectionUtils.isEmpty(packageDetailDTOS)) {
                    throw new BaseException(BaseBizEnum.TIP, "包裹明细查询异常");
                }
                packageDetailDTOS.forEach(entity -> {
                    entity.setCheckQty(entity.getPickQty());
                    entity.setStatus(PackEnum.STATUS.CHECK_COMPELETE_STATUS.getCode());
                });
                a.setListDetail(packageDetailDTOS);
            });
            batchCheckBO.setPackageDTOList(batchPackList);
            //修改出库单
            ShipmentOrderParam shipmentOrderParam = new ShipmentOrderParam();
            shipmentOrderParam.setShipmentOrderCodeList(batchPickDetailList.stream().map(PickDetailDTO::getShipmentOrderCode).distinct().collect(Collectors.toList()));
            List<ShipmentOrderDTO> batchShipmentList = iRemoteShipmentOrderClient.getList(shipmentOrderParam);
            if (CollectionUtils.isEmpty(batchShipmentList)) {
                throw new BaseException(BaseBizEnum.TIP, "出库单查询异常");
            }
            batchShipmentList.forEach(a -> {
                if (a.getStatus().equals(ShipmentOrderEnum.STATUS.COLLECT_STATUS.getCode()) || a.getStatus().equals(ShipmentOrderEnum.STATUS.BEGIN_CHECK_STATUS.getCode())) {
                    a.setStatus(ShipmentOrderEnum.STATUS.CHECK_COMPLETE_STATUS.getCode());
                    a.setCheckStartDate(System.currentTimeMillis());
                    a.setCheckCompleteDate(System.currentTimeMillis());
                }
                List<ShipmentOrderDetailDTO> shipmentOrderDetailDTOS = iRemoteShipmentOrderClient.getDetailList(a.getShipmentOrderCode());
                if (CollectionUtils.isEmpty(shipmentOrderDetailDTOS)) {
                    throw new BaseException(BaseBizEnum.TIP, "出库单明细查询异常");
                }
                shipmentOrderDetailDTOS.forEach(entity -> {
                    if (entity.getStatus().equals(ShipmentOrderEnum.STATUS.COLLECT_STATUS.getCode()) || entity.getStatus().equals(ShipmentOrderEnum.STATUS.BEGIN_CHECK_STATUS.getCode())) {
                        entity.setStatus(ShipmentOrderEnum.STATUS.CHECK_COMPLETE_STATUS.getCode());
                    }
                    List<PackageDetailDTO> packageDetailDTOList = new ArrayList<>();
                    List<PackageDTO> packageDTOList = batchPackList.stream().filter(a1 -> Objects.equals(a1.getShipmentOrderCode(), a.getShipmentOrderCode())).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(packageDTOList)) {
                        for (PackageDTO sto : packageDTOList) {
                            packageDetailDTOList.addAll(sto.getListDetail().stream().filter(a1 -> a1.getIsPre().equalsIgnoreCase(PackIsPreEnum.NORMAL.getCode())).collect(Collectors.toList()));
                        }
                        BigDecimal qty = packageDetailDTOList.stream().filter(b1 -> entity.getId().equals(b1.getPUid())).map(PackageDetailDTO::getCheckQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                        entity.setCheckQty(entity.getCheckQty().add(qty));
                        //TODO add 2021-04-12 复核数量不能大于计划数量
                        if (entity.getCheckQty().compareTo(entity.getExpSkuQty()) > 0) {
                            throw new BaseException(BaseBizEnum.TIP, String.format("出库单:%s商品:%s复核数量不能大于计划数量", entity.getShipmentOrderCode(), entity.getSkuCode()));
                        }
                    }
                });
                a.setListShipmentOrderDetailDTO(shipmentOrderDetailDTOS);
            });
            batchCheckBO.setShipmentOrderDTOList(batchShipmentList);

            List<SystemEventDTO> systemEventDTOS = batchPackList.stream().map(it -> {
                SystemEventDTO systemEventDTO = buildSystemEventDTO(it);
                return systemEventDTO;
            }).collect(Collectors.toList());
            batchCheckBO.setSystemEventDTOS(systemEventDTOS);
            //复核记录
            List<PackageCheckDTO> packageCheckDTOList = new ArrayList<>();
            for (PackageDTO packageDTO : batchPackList) {
                PickDetailDTO pickDetailDTO = pickDetailDTOList.stream().filter(a -> a.getPackageCode().equals(packageDTO.getPackageCode())).findFirst().orElse(null);
                if (pickDetailDTO == null) {
                    throw new BaseException(BaseBizEnum.TIP, String.format("拣选单明细未找到当前包裹，单号%s", packageDTO.getExpressNo()));
                }
                packageCheckDTOList.add(buildPackageCheck(packageDTO, "", pickDTO.getPickCode(), pickDetailDTO.getBasketNo()));
            }
            batchCheckBO.setPackageCheckDTOList(packageCheckDTOList);
//            TODO 订单都记录耗材使用量 2022-07-12
            List<MaterialUseRecordDTO> materialUseRecordDTOList = new ArrayList<>();
            List<PackageDetailDTO> packageDetailDTOS = iRemotePackageClient.getPackageDetailListByCode(completePackageDTO.getPackageCode());
            if (CollectionUtils.isEmpty(packageDetailDTOS)) {
                throw new BaseException(BaseBizEnum.TIP, "包裹明细查询异常");
            }
            MaterialUseRecordParam materialUseRecordParam = new MaterialUseRecordParam();
            materialUseRecordParam.setPackageCode(completePackageDTO.getPackageCode());
            List<MaterialUseRecordDTO> materialUseRecordDTOS = remoteMaterialUseRecordClient.getList(materialUseRecordParam);
            if (!CollectionUtils.isEmpty(materialUseRecordDTOS)) {
                batchPackList.forEach(packageDTO -> {
                    List<MaterialUseRecordDTO> useRecordDTOList = ObjectUtil.cloneByStream(materialUseRecordDTOS);
                    useRecordDTOList.forEach(a -> {
                        a.setShipmentOrderCode(packageDTO.getShipmentOrderCode());
                        a.setPackageCode(packageDTO.getPackageCode());
                        a.setPoNo(packageDTO.getPoNo());
                        a.setSoNo(packageDTO.getSoNo());
                    });
                    materialUseRecordDTOList.addAll(useRecordDTOList);
                });
                batchCheckBO.setMaterialUseRecordDTOList(materialUseRecordDTOList);
            }
            //查询最近一次的封口贴
            OutSourceCodeParam outSourceCodeParam = new OutSourceCodeParam();
            outSourceCodeParam.setPackageCode(completePackageDTO.getPackageCode());
//            outSourceCodeParam.setScanType(SourceCodeScanTypeEnum.SEALING_TAPE.getCode());
            List<OutSourceCodeDTO> outSourceCodeDTOList = remoteOutSourceCodeClient.getList(outSourceCodeParam);
            if (!CollectionUtils.isEmpty(outSourceCodeDTOList)) {
                List<OutSourceCodeDTO> outSourceCodeCommitDTOList = new ArrayList<>();
                batchPackList.forEach(packageDTO -> {
                    outSourceCodeDTOList.forEach(outSourceCodeDTO -> {
                        OutSourceCodeDTO sourceCodeDTO = ConverterUtil.convert(outSourceCodeDTO, OutSourceCodeDTO.class);
                        sourceCodeDTO.setExpressNo(packageDTO.getExpressNo());
                        sourceCodeDTO.setPackageCode(packageDTO.getPackageCode());
                        sourceCodeDTO.setSoNo(packageDTO.getSoNo());
                        sourceCodeDTO.setPoNo(packageDTO.getPoNo());
                        sourceCodeDTO.setShipmentOrderCode(packageDTO.getShipmentOrderCode());
                        outSourceCodeCommitDTOList.add(sourceCodeDTO);
                    });
                });
                batchCheckBO.setOutSourceCodeDTOList(outSourceCodeCommitDTOList);
            }
            //提交数据
            iRemoteBillContextClient.submitBatchPack(batchCheckBO);

            //记录日志
            this.saveLog(CurrentUserHolder.getUserName(), batchPackList, batchShipmentList);

            packCheckResultBizDTO.setExpressBillType(pickDTO.getExpressBillType());
            packCheckResultBizDTO.setGoodsLitType(pickDTO.getGoodsListType());
            packCheckResultBizDTO.setPackMarkType(pickDTO.getPackMarkType());
            packCheckResultBizDTO.setPickCode(pickDTO.getPickCode());
            packCheckResultBizDTO.setPackageCodeList(batchPickDetailList.stream().map(PickDetailDTO::getPackageCode).distinct().collect(Collectors.toList()));

        } catch (Exception e) {
            log.error("批量复核异常", e);
            throw e;
        } finally {
            if (tryLock) {
                lock.unlock();
            }
        }
        return Result.success(packCheckResultBizDTO);
    }

    /**
     * 校验拣选单部分出库，不允许批量复核完成
     *
     * @param pickCode
     * @param packageCodeList
     */
    private void checkPickOrderPackageStatus(String pickCode, List<String> packageCodeList) {
        PackageParam packageParam = new PackageParam();
        packageParam.setPackageCodeList(packageCodeList);
        List<PackageDTO> batchPackList = iRemotePackageClient.getList(packageParam);
        if (CollectionUtils.isEmpty(batchPackList)) {
            throw new BaseException(BaseBizEnum.TIP, "包裹查询异常");
        }
        if (batchPackList.stream().anyMatch(a -> a.getStatus().equalsIgnoreCase(PackEnum.STATUS.PREPARE_HANDLER_STATUS.getCode()))) {
            PackageDTO packageDTO = batchPackList.stream().filter(a -> a.getStatus().equalsIgnoreCase(PackEnum.STATUS.PREPARE_HANDLER_STATUS.getCode())).findFirst().orElse(null);
            String expressNo = "-";
            if (packageDTO != null) {
                expressNo = packageDTO.getExpressNo();
            }
            throw new BaseException(BaseBizEnum.TIP, String.format("拣选单号:%s,运单号:%s已出库,不允许批量复核", pickCode, expressNo));
        }
    }


    /**
     * 拣选单是否全部完成
     *
     * @param pickDTO
     * @return
     */
    private boolean checkPickComplete(PickDTO pickDTO) {
        PickParam pickParam = new PickParam();
        pickParam.setPickCode(pickDTO.getPickCode());
        List<PickDetailDTO> pickDetailList = iRemotePickClient.getPickDetailList(pickParam);
        if (CollectionUtils.isEmpty(pickDetailList)) {
            throw new BaseException(BaseBizEnum.TIP, "查询拣选单异常");
        }
        if (pickDetailList.stream().noneMatch(a -> Objects.equals(a.getPackageStatus(), PackEnum.STATUS.PICK_COMPELETE_STATUS.getCode()) || Objects.equals(a.getPackageStatus(), PackEnum.STATUS.CHECK_BEGIN_STATUS.getCode()))) {
            return true;
        }
        return false;
    }

    /**
     * 插入日志
     *
     * @param opBy
     * @param packageDTOList
     * @param shipmentOrderDTOList
     */
    private void saveLog(String opBy, List<PackageDTO> packageDTOList, List<ShipmentOrderDTO> shipmentOrderDTOList) {
        List<PackageLogDTO> packageLogDTOS = new ArrayList<>();
        for (PackageDTO packageDTO : packageDTOList) {
            PackageLogDTO packageLogDTO = new PackageLogDTO();
            packageLogDTO.setOpDate(System.currentTimeMillis());
            packageLogDTO.setCargoCode(packageDTO.getCargoCode());
            packageLogDTO.setPackageCode(packageDTO.getPackageCode());
            packageLogDTO.setWarehouseCode(packageDTO.getWarehouseCode());
            packageLogDTO.setOpBy(opBy);
            packageLogDTO.setOpContent(String.format("包裹批量复核,单号:%s", packageDTO.getPackageCode()));
            packageLogDTOS.add(packageLogDTO);
        }
        iBusinessLogBiz.savePackLogList(packageLogDTOS);

        List<ShipmentOrderLogDTO> shipmentOrderLogDTOS = new ArrayList<>();
        for (ShipmentOrderDTO entity : shipmentOrderDTOList) {
            ShipmentOrderLogDTO shipmentOrderLogDTO = new ShipmentOrderLogDTO();
            shipmentOrderLogDTO.setOpDate(System.currentTimeMillis());
            shipmentOrderLogDTO.setCargoCode(entity.getCargoCode());
            shipmentOrderLogDTO.setWarehouseCode(entity.getWarehouseCode());
            shipmentOrderLogDTO.setShipmentOrderCode(entity.getShipmentOrderCode());
            shipmentOrderLogDTO.setOpBy(opBy);
            shipmentOrderLogDTO.setOpContent(String.format("出库单复核完成,单号:%s", entity.getShipmentOrderCode()));
            if (entity.getStatus().equals(ShipmentOrderEnum.STATUS.CHECK_COMPLETE_STATUS.getCode())) {
                shipmentOrderLogDTOS.add(shipmentOrderLogDTO);
            }
        }
        iBusinessLogBiz.saveShipmentLogList(shipmentOrderLogDTOS);
    }

    @Override
    public Result<Boolean> checkSourceCode(PackCheckSourceCodeParam param) {
        PackageDTO packageDTO = iRemotePackageClient.getPackageByCode(param.getPackageCode());
        if (packageDTO == null) {
            throw new BaseException(BaseBizEnum.TIP, "包裹未找到!!!");
        }
        CargoConfigDTO cargoConfigDTO = remoteCargoConfigClient.queryByCargoCodeAndpropKey(packageDTO.getWarehouseCode(), packageDTO.getCargoCode(), CargoConfigParamEnum.SOURCE_CODE.getCode());
        if (cargoConfigDTO == null || !cargoConfigDTO.getStatus().equals(CargoConfigStatusEnum.ENABLE.getValue()) || !cargoConfigDTO.getPropValue().equals("1")) {
            throw new BaseException(BaseBizEnum.TIP, "当前货主未开启溯源码");
        }

        cargoConfigDTO = remoteCargoConfigClient.queryByCargoCodeAndpropKey(packageDTO.getWarehouseCode(), packageDTO.getCargoCode(), CargoConfigParamEnum.SOURCE_CODE_PATTERN.getCode());
        if (cargoConfigDTO == null || !cargoConfigDTO.getStatus().equals(CargoConfigStatusEnum.ENABLE.getValue())) {
            throw new BaseException(BaseBizEnum.TIP, "当前货主溯源码规则未启用或者未配置");
        }

        if (StringUtils.isEmpty(cargoConfigDTO.getPropValue())) {
            throw new BaseException(BaseBizEnum.TIP, "当前货主开启溯源码,溯源码规则不能为空,请维护!!!");
        }

        if (cargoConfigDTO != null && !StringUtils.isEmpty(cargoConfigDTO.getPropValue()) && !Pattern.matches(cargoConfigDTO.getPropValue(), param.getSourceCode())) {
            throw new BaseException(BaseBizEnum.TIP, "溯源码格式规则不符合");
        }
        OutSourceCodeParam outSourceCodeParam = new OutSourceCodeParam();
        outSourceCodeParam.setSnCode(param.getSourceCode());
        if (remoteOutSourceCodeClient.checkExits(outSourceCodeParam)) {
            throw new BaseException(BaseBizEnum.TIP, "溯源码已使用");
        }
        return Result.success(Boolean.TRUE);
    }

    @Override
    public Result<Boolean> checkPackHC(PackCheckHCParam param) {
        if (param == null || StringUtils.isEmpty(param.getPackageCode()) || StringUtils.isEmpty(param.getHcUpcCode()) || CollectionUtils.isEmpty(param.getSkuCodeList())) {
            throw new BaseException(BaseBizEnum.TIP, "参数不能为空");
        }
        PackageParam packageParam = new PackageParam();
        packageParam.setPackageCode(param.getPackageCode());
        PackageDTO packageDTO = iRemotePackageClient.get(packageParam);
        if (packageDTO == null) {
            throw new BaseException(BaseBizEnum.TIP, "包裹不存在");
        }
        Boolean is4PL = Boolean.FALSE;
        if (!StringUtils.isEmpty(packageDTO.getOrderTag()) && OrderTagEnum.NumToEnum(packageDTO.getOrderTag()).stream().anyMatch(a -> a.getCode().equals(OrderTagEnum.FOUR_TAG.code()))) {
            is4PL = Boolean.TRUE;
        }
        SkuParam skuParam = new SkuParam();
        skuParam.setCargoCode(packageDTO.getCargoCode());
        skuParam.setCodeList(param.getSkuCodeList());
        List<SkuDTO> skuList = iRemoteSkuClient.getList(skuParam);
        if (CollectionUtils.isEmpty(skuList)) {
            throw new BaseException(BaseBizEnum.TIP, "商品不存在");
        }
        SkuDTO skuDTO = skuList.stream().filter(sku -> SkuTagEnum.NumToEnum(sku.getSkuTag()).stream().anyMatch(a -> a.getCode().equals(SkuTagEnum.COLD_CHAIN_BOX.getCode()))).findFirst().orElse(null);
        if (skuDTO == null) {
            throw new BaseException(BaseBizEnum.TIP, "当前提交的商品中,无使用冷链泡沫箱的商品");
        }
        //查询包材
        PackageMaterialDTO materialDTO = iRemotePackageMaterialClient.queryByUpcCode(packageDTO.getCargoCode(), param.getHcUpcCode());
        if (materialDTO == null) {
            throw new BaseException(BaseBizEnum.TIP, "复核扫描耗材未找到");
        }
        if (Objects.equals(materialDTO.getStatus(), PackageMaterialStatusEnum.DISABLE.getValue())) {
            throw new BaseException(BaseBizEnum.TIP, "复核扫描耗材禁用,请确认");
        }
        if (!materialDTO.getType().equalsIgnoreCase(MaterialTypeEnum.HC.getCode())) {
            throw new BaseException(BaseBizEnum.TIP, "复核扫描耗材冷链泡沫箱属性必须是耗材");
        }
        if (MaterialTagEnum.NumToEnum(materialDTO.getMaterialTag()).stream().noneMatch(a -> a.getCode().equals(MaterialTagEnum.COLD_CHAIN_BOX.getCode()))) {
            throw new BaseException(BaseBizEnum.TIP, "复核扫描耗材未维护未冷链泡沫箱");
        }
        if (is4PL && !materialDTO.getIs4pl().equals(Is4PLEnum.YES.getCode())) {
            throw new BaseException(BaseBizEnum.TIP, "复核扫描耗材4PL订单只能扫 4PL的耗材");
        }
        if (!is4PL && !materialDTO.getIs4pl().equals(Is4PLEnum.NO.getCode())) {
            throw new BaseException(BaseBizEnum.TIP, "复核扫描耗材非4PL订单只能扫非4PL耗材");
        }
        return Result.success(Boolean.TRUE);
    }

    @Override
    public Result<Boolean> checkSealingTape(CheckSealingTapeParam param) {
        if (param == null || StringUtils.isEmpty(param.getPackageCode()) || StringUtils.isEmpty(param.getSealingTapeCode()) || StringUtils.isEmpty(param.getSkuCode())) {
            throw new BaseException(BaseBizEnum.TIP, "参数不能为空");
        }
        PackageParam packageParam = new PackageParam();
        packageParam.setPackageCode(param.getPackageCode());
        PackageDTO packageDTO = iRemotePackageClient.get(packageParam);
        if (packageDTO == null) {
            throw new BaseException(BaseBizEnum.TIP, "包裹不存在");
        }
        SkuParam skuParam = new SkuParam();
        skuParam.setCargoCode(packageDTO.getCargoCode());
        skuParam.setCode(param.getSkuCode());
        SkuDTO skuDTO = iRemoteSkuClient.get(skuParam);
        if (skuDTO == null) {
            throw new BaseException(BaseBizEnum.TIP, "商品不存在");
        }
//        if (remoteWarehouseClient.getTaoTianWarehouse(packageDTO.getWarehouseCode())) {
        String scanTag = "";
        //溯源码
        if (skuDTO.getNeedUptracSourceCodeTag()) {
            scanTag = "SYM";
        }
        //防伪扣
        if (skuDTO.getAntiCounterfeitingBuckleTag()) {
            scanTag = "FWK";
        }
        //易撕贴
        if (skuDTO.getPullTapeTag()) {
            scanTag = "YST";
        }
//        if (Objects.equals(scanTag, "SYM") && !Pattern.matches("[0-9A-Za-z]{1,60}", param.getSealingTapeCode())) {
        if (Objects.equals(scanTag, "SYM") && !Pattern.matches(skuDTO.getNeedUptracSourceCodeRule(), param.getSealingTapeCode())) {
            throw new BaseException(BaseBizEnum.TIP, "溯源码,校验规则不通过");
        }
        //取淘天规则
        if (Objects.equals(scanTag, "FWK")) {
            if (!Pattern.matches(skuDTO.getAntiCounterfeitingBuckleRule(), param.getSealingTapeCode())) {
                throw new BaseException(BaseBizEnum.TIP, "防伪扣,校验规则不通过");
            }
        }
        //取淘天规则
        if (Objects.equals(scanTag, "YST")) {
            if (!Pattern.matches(skuDTO.getPullTapeRule(), param.getSealingTapeCode())) {
                throw new BaseException(BaseBizEnum.TIP, "易撕贴,校验规则不通过");
            }
        }
//        } else {
//            if (!SkuTagEnum.NumToEnum(skuDTO.getSkuTag()).contains(SkuTagEnum.SCAN_SEALING_TAPE)) {
//                throw new BaseException(BaseBizEnum.TIP, "当前提交的商品中,不需要扫描封口贴的商品");
//            }
//            if (CollectionUtils.isEmpty(wmsOtherConfig.getSealingTapeList())) {
//                throw new BaseException(BaseBizEnum.TIP, "请联系wms技术维护封口贴条码");
//            }
//            if (!wmsOtherConfig.getSealingTapeList().contains(param.getSealingTapeCode())) {
//                throw new BaseException(BaseBizEnum.TIP, "封口贴条码不在维护的配置中,请核查扫描是否正确");
//            }
//        }
        return Result.success(Boolean.TRUE);
    }

    @Override
    public Result<String> interceptionBatchPack(InterceptionBatchPackParam param) throws Exception {
        log.info("interceptionBatchPack:{} warehouseCode;{}", JSONUtil.toJsonStr(param), CurrentRouteHolder.getWarehouseCode());
        if (param == null || StringUtils.isEmpty(param.getPackageCode()) || StringUtils.isEmpty(param.getInterceptionStatus())) {
            throw new BaseException(BaseBizEnum.TIP, "参数不能为空");
        }
        String key = CurrentRouteHolder.getWarehouseCode() + "" + param.getPackageCode();
        RLock lock = redissonClient.getLock("dt_wms_interception_submit_lock:" + key);
        Boolean tryLock = false;
        try {
            tryLock = lock.tryLock(0, 30, TimeUnit.SECONDS);
            if (!tryLock) {
                throw new BaseException(BaseBizEnum.TIP, "提交太快了,当前拦截单正在提交,请稍后确认");
            }
            if (!Arrays.asList("10", "20").contains(param.getInterceptionStatus())) {
                throw new BaseException(BaseBizEnum.TIP, "节点参数异常");
            }
            if (param.getInterceptionStatus().equals("10")) {
                param.setInterceptionStatus(PackReturnEnum.INTERCEPT_STATUS.PACKAGE_CHECK_POINT.getCode());
            } else {
                param.setInterceptionStatus(PackReturnEnum.INTERCEPT_STATUS.WEIGH_HAND_POINT.getCode());
            }
            //批量操作台,拣选单号必填
            if (Objects.equals(param.getInterceptionType(), "20") && StringUtils.isEmpty(param.getPickCode())) {
                throw new BaseException(BaseBizEnum.TIP, "批量拦截,拣选单号不能为空");
            }
            PackageParam packageParam = new PackageParam();
            packageParam.setPackageCode(param.getPackageCode());
            PackageDTO packageDTO = iRemotePackageClient.get(packageParam);
            if (packageDTO == null) {
                //批量复核和批量出库
                packageDTO = checkInterceptionExpressNo(param);
            } else {
                if (!StringUtils.isEmpty(param.getPickCode())) {
                    PickDetailParam pickDetailParam = new PickDetailParam();
                    pickDetailParam.setPickCode(param.getPickCode());
                    pickDetailParam.setPackageCode(param.getPackageCode());
                    List<PickDetailDTO> pickDetailDTOList = remotePickDetailClient.getList(pickDetailParam);
                    if (CollectionUtils.isEmpty(pickDetailDTOList)) {
                        throw new BaseException(BaseBizEnum.TIP, String.format("拣选单号%s单号:%s未找到包裹", param.getPickCode(), param.getPackageCode()));
                    }
                }
            }
            if (packageDTO.getStatus().equalsIgnoreCase(PackEnum.STATUS.PREPARE_HANDLER_STATUS.getCode())) {
                throw new BaseException(BaseBizEnum.TIP, "包裹已出库,不允许拦截");
            }
            if (Arrays.asList(PackEnum.STATUS.PART_ASSIGN_STATUS.getCode(), PackEnum.STATUS.PART_ASSIGN_CANCEL_STATUS.getCode()).contains(packageDTO.getStatus())) {
                throw new BaseException(BaseBizEnum.TIP, "包裹已拦截,不需要重复操作");
            }
            //复核完成的包裹需要出库去拦截
            if (param.getInterceptionStatus().equalsIgnoreCase(PackReturnEnum.INTERCEPT_STATUS.PACKAGE_CHECK_POINT.getCode()) && packageDTO.getStatus().equalsIgnoreCase(PackEnum.STATUS.CHECK_COMPELETE_STATUS.getCode())) {
                throw new BaseException(BaseBizEnum.TIP, "包裹已复核完成,复核节点不拦截已复核完成的包裹,请到称重节点拦截");
            }
            OrderInterceptParam orderInterceptParam = new OrderInterceptParam();
            orderInterceptParam.setShipmentOrderCode(packageDTO.getShipmentOrderCode());
            Boolean checkExits = interceptClient.checkExits(orderInterceptParam);
            if (!checkExits) {
                throw new BaseException(BaseBizEnum.TIP, "当前包裹无拦截单");
            }
            PickDTO pickDTO = null;

            //复核-拣选单号必填
            if (param.getInterceptionStatus().equalsIgnoreCase(PackReturnEnum.INTERCEPT_STATUS.PACKAGE_CHECK_POINT.getCode())) {
                if (StringUtils.isEmpty(param.getPickCode())) {
                    throw new BaseException(BaseBizEnum.TIP, "复核拦截,拣选单号不能为空");
                }
            }
            if (!StringUtils.isEmpty(param.getPickCode())) {
                PickParam pickParam = new PickParam();
                pickParam.setPickCode(param.getPickCode());
                pickDTO = iRemotePickClient.getPickAndDetailList(pickParam);
            }
            String interceptionPack = interceptionManagerBiz.interceptionPack(packageDTO, param.getWorkbenchCode(), param.getInterceptionStatus(), pickDTO);
            //批量台复核判定是否全部拦截完成 interceptionPack 未完成 interceptionType 批量拦截
            if (interceptionPack.equals("10") && Objects.equals(param.getInterceptionType(), "20")) {
                //获取拣选单明细
                PickDetailParam pickDetailParam = new PickDetailParam();
                pickDetailParam.setPickCode(pickDTO.getPickCode());
                List<PickDetailDTO> pickDetailDTOList = remotePickDetailClient.getList(pickDetailParam);
                if (CollectionUtils.isEmpty(pickDetailDTOList)) {
                    throw new BaseException(BaseBizEnum.TIP, "拣选单明细不存在");
                }
                //获取所有的拦截单
                OrderInterceptParam orderInterceptParamNew = new OrderInterceptParam();
                orderInterceptParamNew.setShipmentOrderCodeList(pickDetailDTOList.stream().map(PickDetailDTO::getShipmentOrderCode).distinct().collect(Collectors.toList()));
                List<OrderInterceptDTO> orderInterceptDTOList = interceptClient.getList(orderInterceptParamNew);
                if (CollectionUtils.isEmpty(orderInterceptDTOList)) {
                    throw new BaseException(BaseBizEnum.TIP, "当前拣选单无拦截单");
                }
                //拦截单的所有出库单号
                List<String> interceptShipmentOrderCodeList = orderInterceptDTOList.stream().map(OrderInterceptDTO::getShipmentOrderCode).distinct().collect(Collectors.toList());
                //获取当前拣选单明细的所有包裹
                PackageParam packageParamNew = new PackageParam();
                packageParamNew.setPackageCodeList(pickDetailDTOList.stream().map(PickDetailDTO::getPackageCode).collect(Collectors.toList()));
                List<PackageDTO> packageDTOList = iRemotePackageClient.getList(packageParamNew);
                if (CollectionUtils.isEmpty(packageDTOList)) {
                    throw new BaseException(BaseBizEnum.TIP, "查询包裹异常");
                }
                List<PackageDTO> packageDTOInterceptionAll;
                if (param.getInterceptionStatus().equalsIgnoreCase(PackReturnEnum.INTERCEPT_STATUS.PACKAGE_CHECK_POINT.getCode())) {
                    packageDTOInterceptionAll = packageDTOList.stream().filter(a -> Arrays.asList(PackEnum.STATUS.PICK_COMPELETE_STATUS.getCode(), PackEnum.STATUS.CHECK_BEGIN_STATUS.getCode()).contains(a.getStatus())).filter(a -> interceptShipmentOrderCodeList.contains(a.getShipmentOrderCode())).collect(Collectors.toList());
                } else {
                    packageDTOInterceptionAll = packageDTOList.stream().filter(a -> interceptShipmentOrderCodeList.contains(a.getShipmentOrderCode())).collect(Collectors.toList());
                }
                if (packageDTOInterceptionAll.stream().allMatch(a -> Arrays.asList(PackEnum.STATUS.PART_ASSIGN_STATUS.getCode(), PackEnum.STATUS.PART_ASSIGN_CANCEL_STATUS.getCode()).contains(a.getStatus()))) {
                    interceptionPack = "20";
                }
            }
            Map<String, String> map = new HashMap<>();
            map.put("code", interceptionPack);
            map.put("packageCode", packageDTO.getPackageCode());
            return Result.success(JSONUtil.toJsonStr(map));
        } catch (Exception e) {
            log.error("包裹复核异常：e:{}", e.getMessage());
            throw e;
        } finally {
            if (tryLock) {
                lock.unlock();
            }
        }
    }

    private PackageDTO checkInterceptionExpressNo(InterceptionBatchPackParam param) {
        //批量复核和出库
        if (Objects.equals(param.getInterceptionType(), "20")) {
            PickDetailParam pickDetailParam = new PickDetailParam();
            pickDetailParam.setPickCode(param.getPickCode());
            pickDetailParam.setExpressNo(param.getPackageCode());
            List<PickDetailDTO> pickDetailDTOList = remotePickDetailClient.getList(pickDetailParam);
            if (CollectionUtils.isEmpty(pickDetailDTOList)) {
                throw new BaseException(BaseBizEnum.TIP, String.format("单号:%s未找到包裹", param.getPackageCode()));
            }
            if (pickDetailDTOList.size() > 1) {
                throw new BaseException(BaseBizEnum.TIP, String.format("单号:%s找到多个包裹，请使用包裹号触发拦截", param.getPackageCode()));
            }
            PackageParam packageParam = new PackageParam();
            packageParam.setPackageCode(pickDetailDTOList.get(0).getPackageCode());
            PackageDTO packageDTO = iRemotePackageClient.get(packageParam);
            if (packageDTO == null) {
                throw new BaseException(BaseBizEnum.TIP, String.format("单号:%s未找到包裹", param.getPackageCode()));
            }
            return packageDTO;
        }
        PackageParam packageParam = new PackageParam();
        packageParam.setExpressNo(param.getPackageCode());
        List<PackageDTO> packageDTOList = iRemotePackageClient.getList(packageParam);
        if (CollectionUtils.isEmpty(packageDTOList)) {
            throw new BaseException(BaseBizEnum.TIP, String.format("单号:%s未找到包裹", param.getPackageCode()));
        }
        if (packageDTOList.size() > 1) {
            throw new BaseException(BaseBizEnum.TIP, String.format("单号:%s找到多个包裹，请使用包裹号触发拦截", param.getPackageCode()));
        }
        return packageDTOList.get(0);
    }

    @Override
    public Result<PackCheckInterceptionResultBizDTO> queryInterceptionBatchPackInfo(InterceptionBatchPackParam param) {
        if (param == null || StringUtils.isEmpty(param.getPickCode())) {
            throw new BaseException(BaseBizEnum.TIP, "参数不能为空");
        }
        PickParam pickParam = new PickParam();
        pickParam.setPickCode(param.getPickCode());
        PickDTO pickDTO = iRemotePickClient.get(pickParam);
        if (pickDTO == null) {
            throw new BaseException(BaseBizEnum.TIP, "拣选单不存在");
        }
        if (!pickDTO.getType().equalsIgnoreCase(PickEnum.PickOrderTypeEnum.SPIKE.getCode())) {
            throw new BaseException(BaseBizEnum.TIP, "非秒杀拣选单不需要在此页面拦截");
        }
        Boolean isOutWorkbench = false;
        if (pickDTO.getStatus().equals(PickEnum.PickStatusEnum.ZJ_END_STATUS.getCode())) {
            isOutWorkbench = true;
        }
        //获取拣选单明细
        PickDetailParam pickDetailParam = new PickDetailParam();
        pickDetailParam.setPickCode(pickDTO.getPickCode());
        List<PickDetailDTO> pickDetailDTOList = remotePickDetailClient.getList(pickDetailParam);
        if (CollectionUtils.isEmpty(pickDetailDTOList)) {
            throw new BaseException(BaseBizEnum.TIP, "拣选单明细不存在");
        }
        //获取所有的拦截单
        OrderInterceptParam orderInterceptParam = new OrderInterceptParam();
        orderInterceptParam.setShipmentOrderCodeList(pickDetailDTOList.stream().map(PickDetailDTO::getShipmentOrderCode).distinct().collect(Collectors.toList()));
        List<OrderInterceptDTO> orderInterceptDTOList = interceptClient.getList(orderInterceptParam);
        if (CollectionUtils.isEmpty(orderInterceptDTOList)) {
            throw new BaseException(BaseBizEnum.TIP, "当前拣选单无拦截单");
        }
        //拦截单的所有出库单号
        List<String> interceptShipmentOrderCodeList = orderInterceptDTOList.stream().map(OrderInterceptDTO::getShipmentOrderCode).distinct().collect(Collectors.toList());
        //获取当前拣选单明细的所有包裹
        PackageParam packageParam = new PackageParam();
        packageParam.setPackageCodeList(pickDetailDTOList.stream().map(PickDetailDTO::getPackageCode).collect(Collectors.toList()));
        List<PackageDTO> packageDTOList = iRemotePackageClient.getList(packageParam);
        if (CollectionUtils.isEmpty(packageDTOList)) {
            throw new BaseException(BaseBizEnum.TIP, "查询包裹异常");
        }
        //获取拦截包裹(包含未触发的)
        List<PackageDTO> packageDTOInterceptionAll;
        //获取待拦截的
        List<PackageDTO> waitPackageDTOInterceptionAll;
        //出库拦截所有
        if (isOutWorkbench) {
            packageDTOInterceptionAll = packageDTOList.stream().filter(a -> interceptShipmentOrderCodeList.contains(a.getShipmentOrderCode())).collect(Collectors.toList());

            waitPackageDTOInterceptionAll = packageDTOList.stream().filter(a -> !a.getStatus().equalsIgnoreCase(PackEnum.STATUS.PART_ASSIGN_STATUS.getCode())).filter(a -> !a.getStatus().equalsIgnoreCase(PackEnum.STATUS.PART_ASSIGN_CANCEL_STATUS.getCode())).filter(a -> interceptShipmentOrderCodeList.contains(a.getShipmentOrderCode())).collect(Collectors.toList());
        } else {
            packageDTOInterceptionAll = packageDTOList.stream().filter(a -> interceptShipmentOrderCodeList.contains(a.getShipmentOrderCode())).filter(a -> Arrays.asList(PackEnum.STATUS.PICK_COMPELETE_STATUS.getCode(), PackEnum.STATUS.CHECK_BEGIN_STATUS.getCode(), PackEnum.STATUS.PART_ASSIGN_STATUS.getCode(), PackEnum.STATUS.PART_ASSIGN_CANCEL_STATUS.getCode()).contains(a.getStatus())).collect(Collectors.toList());

            waitPackageDTOInterceptionAll = packageDTOList.stream().filter(a -> !a.getStatus().equalsIgnoreCase(PackEnum.STATUS.PART_ASSIGN_STATUS.getCode())).filter(a -> !a.getStatus().equalsIgnoreCase(PackEnum.STATUS.PART_ASSIGN_CANCEL_STATUS.getCode())).filter(a -> Arrays.asList(PackEnum.STATUS.PICK_COMPELETE_STATUS.getCode(), PackEnum.STATUS.CHECK_BEGIN_STATUS.getCode()).contains(a.getStatus())).filter(a -> interceptShipmentOrderCodeList.contains(a.getShipmentOrderCode())).collect(Collectors.toList());
        }
        PackCheckInterceptionResultBizDTO packCheckInterceptionResultBizDTO = new PackCheckInterceptionResultBizDTO();
        packCheckInterceptionResultBizDTO.setPickCode(pickDTO.getPickCode());
        packCheckInterceptionResultBizDTO.setInterceptionSum(packageDTOInterceptionAll.size());
        packCheckInterceptionResultBizDTO.setInterceptionNum(packageDTOInterceptionAll.size() - waitPackageDTOInterceptionAll.size());
        //获取最小篮号待拦截的包裹
        List<String> waitInterceptionPack = waitPackageDTOInterceptionAll.stream().map(PackageDTO::getPackageCode).collect(Collectors.toList());
        packCheckInterceptionResultBizDTO.setWaitInterceptionBasketNo("");
        packCheckInterceptionResultBizDTO.setWaitInterceptionExpressNo("");
        packCheckInterceptionResultBizDTO.setWaitInterceptionPackageCode("");
        if (!CollectionUtils.isEmpty(waitPackageDTOInterceptionAll)) {
            PickDetailDTO pickDetailDTO = pickDetailDTOList.stream().filter(a -> waitInterceptionPack.contains(a.getPackageCode())).map(a -> {
                a.setBasketNo(String.format("%03d", Integer.valueOf(a.getBasketNo())));
                return a;
            }).sorted(Comparator.comparing(PickDetailDTO::getBasketNo)).findFirst().orElse(null);
            if (pickDetailDTO != null) {
                packCheckInterceptionResultBizDTO.setWaitInterceptionBasketNo(pickDetailDTO.getBasketNo());
                packCheckInterceptionResultBizDTO.setWaitInterceptionExpressNo(pickDetailDTO.getExpressNo());
                packCheckInterceptionResultBizDTO.setWaitInterceptionPackageCode(pickDetailDTO.getPackageCode());
            }
        }
        //获取已拦截的
        List<PackageDTO> packageDTOInterceptionList = packageDTOList.stream().filter(a -> a.getStatus().equalsIgnoreCase(PackEnum.STATUS.PART_ASSIGN_STATUS.getCode()) || a.getStatus().equalsIgnoreCase(PackEnum.STATUS.PART_ASSIGN_CANCEL_STATUS.getCode())).filter(a -> interceptShipmentOrderCodeList.contains(a.getShipmentOrderCode())).collect(Collectors.toList());
        List<PackCheckInterceptionDetailResultBizDTO> detailResultBizDTOList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(packageDTOInterceptionList)) {
            packageDTOInterceptionList.forEach(packageDTO -> {
                PackCheckInterceptionDetailResultBizDTO packCheckInterceptionDetailResultBizDTO = new PackCheckInterceptionDetailResultBizDTO();
                packCheckInterceptionDetailResultBizDTO.setBasketNo("");
                pickDetailDTOList.stream().filter(a -> a.getPackageCode().equalsIgnoreCase(packageDTO.getPackageCode())).findFirst().ifPresent(a -> packCheckInterceptionDetailResultBizDTO.setBasketNo(a.getBasketNo()));
                packCheckInterceptionDetailResultBizDTO.setExpressNo(packageDTO.getExpressNo());
                packCheckInterceptionDetailResultBizDTO.setPackageCode(packageDTO.getPackageCode());
                packCheckInterceptionDetailResultBizDTO.setStatusName(PackEnum.STATUS.findEnumDesc(packageDTO.getStatus()).getDesc());
                detailResultBizDTOList.add(packCheckInterceptionDetailResultBizDTO);
            });
        }
        packCheckInterceptionResultBizDTO.setDetailResultBizDTOList(detailResultBizDTOList);
        return Result.success(packCheckInterceptionResultBizDTO);
    }

    @Override
    public Result<Boolean> oversizeBoxRegulation(CodeParam param) {
        PackageDTO packageDTO = iRemotePackageClient.getPackageByCode(param.getCode());
        if (packageDTO == null || Objects.equals(packageDTO.getStatus(), PackEnum.STATUS.CHECK_COMPELETE_STATUS.getCode())) {
            throw new BaseException(PickErrorEnum.PACK_BASKET_DATA_ERROR, param.getCode());
        }
        List<PackageDetailDTO> packageDetailDTOList = iRemotePackageClient.getPackageDetailListByCode(packageDTO.getPackageCode());
        DefaultOpexceptionReport report = new DefaultOpexceptionReport();
        report.setOwnerCode(packageDTO.getCargoCode());
        report.setWarehouseCode(packageDTO.getWarehouseCode());
        report.setAbnormalOrderId(packageDTO.getPackageCode());
        report.setAbnormalType(OpAbnormalTypeEnum.T_2001.getCode());
        report.setScenarioType(OpScenarioTypeEnum.SALE_DELIVERY.getCode());
        report.setOrderCode(packageDTO.getPoNo());
        // 外部业务幂等码（消息ID，用于幂等去重）
        report.setOutBizCode(System.currentTimeMillis() + "");
        report.setOperateTime(ConverterUtil.convertVoTime(System.currentTimeMillis()));
        report.setStatus(OpExceptionCallbackStateEnum.REGISTER.getCode());
        report.setMailNo(packageDTO.getExpressNo());
        report.setLogisticsCode(packageDTO.getCarrierCode());
        report.setLogisticsName(packageDTO.getCarrierName());

        //调用两个方法，
        ShipmentOrderDTO shipmentOrderDTO = iRemoteShipmentOrderClient.getShipmentOrderByCode(packageDTO.getShipmentOrderCode());
        if (shipmentOrderDTO == null) {
            throw new RuntimeException("出库单号未找到");
        }
        // 寄件人信息
        DefaultOpexceptionReportSenderInfo senderInfo = new DefaultOpexceptionReportSenderInfo();
        senderInfo.setName(shipmentOrderDTO.getSenderMan());
        senderInfo.setPhone(shipmentOrderDTO.getSenderTel());
        senderInfo.setProvince(shipmentOrderDTO.getSenderProvName());
        senderInfo.setCity(shipmentOrderDTO.getSenderCityName());
        senderInfo.setArea(shipmentOrderDTO.getSenderAreaName());
        senderInfo.setDetailAddress(shipmentOrderDTO.getSenderAddress());
        report.setSenderInfo(senderInfo);

        List<DefaultOpexceptionReportAbnormalLine> abnormalLineList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(packageDetailDTOList)) {
            for (PackageDetailDTO packageDetailDTO : packageDetailDTOList) {
                DefaultOpexceptionReportAbnormalLine abnormalLine = new DefaultOpexceptionReportAbnormalLine();
                abnormalLine.setAbnormalLineId(packageDetailDTO.getLineSeq());
                abnormalLine.setBarCode(packageDetailDTO.getUpcCode());
                abnormalLine.setItemCode(packageDetailDTO.getSkuCode());
                abnormalLine.setItemName(packageDetailDTO.getSkuName());
                abnormalLine.setQty(packageDetailDTO.getSkuQty());
                abnormalLine.setInventoryType(SkuQualityEnum.SKU_QUALITY_AVL.equals(packageDetailDTO.getSkuQuality()) ? "ZP" : "CC");
//                abnormalLine.setAttachmentUrls(new String[]{opExceptionDetailDTO.getAttachmentUrls()});
//                abnormalLine.setRemark(packageDetailDTO.getRemark());
                // SN 可先不管
//                abnormalLine.setSn("");
                abnormalLineList.add(abnormalLine);
            }
        }
        report.setAbnormalLines(abnormalLineList);
        report.setDtWmsCargoCode(packageDTO.getCargoCode());
        report.setDtWmsWarehouseCode(packageDTO.getWarehouseCode());
        //=---
        //设置租户 TODO ADD 2024-12-05
        report.setTenantId(remoteTenantHelper.queryTenantId(shipmentOrderDTO.getWarehouseCode(), shipmentOrderDTO.getCargoCode()));

        String reportStr = JSONUtil.toJsonStr(report);
        remoteMercuryClient.warehouseJobExceptionCallbackSync(reportStr, packageDTO.getWarehouseCode());
        //原包裹日志
        PackageLogDTO packageLogDTO = new PackageLogDTO();
        packageLogDTO.setCargoCode(packageDTO.getCargoCode());
        packageLogDTO.setPackageCode(packageDTO.getPackageCode());
        packageLogDTO.setWarehouseCode(packageDTO.getWarehouseCode());
        packageLogDTO.setOpBy(CurrentUserHolder.getUserName());
        packageLogDTO.setOpDate(System.currentTimeMillis());
        packageLogDTO.setOpContent("包裹超箱规回告淘天，质检台：" + param.getBenchCode());
        iRemotePackageClient.savePackageLog(packageLogDTO);
        return Result.success(true);
    }

    @Override
    public Result<Boolean> checkPackSN(PackCheckSNParam param) {
        PackageParam packageParam = new PackageParam();
        packageParam.setPackageCode(param.getPackageCode());
        PackageDTO packageDTO = iRemotePackageClient.get(packageParam);
        if (packageDTO == null) {
            throw new BaseException(BaseBizEnum.TIP, "包裹不存在");
        }
        SkuParam skuParam = new SkuParam();
        skuParam.setCargoCode(packageDTO.getCargoCode());
        skuParam.setCode(param.getSkuCode());
        SkuDTO skuDTO = iRemoteSkuClient.get(skuParam);
        if (skuDTO == null) {
            throw new BaseException(BaseBizEnum.TIP, "商品不存在");
        }
        if (!skuDTO.getSNMgmtOutNeed()) {
            throw new BaseException(BaseBizEnum.TIP, "当前商品不需要扫描SN,请核查");
        }
        //出入库都有
        List<SnStockDTO> snStockDTOList = new ArrayList<>();
        if (skuDTO.getSNMgmtOutAndIn()) {
            //校验库存
            SnStockParam snStockParam = new SnStockParam();
            snStockParam.setSn(param.getSnCode());
            snStockParam.setHasQty(true);
            snStockDTOList = remoteSnStockClient.getList(snStockParam);
            if (CollectionUtils.isEmpty(snStockDTOList)) {
                throw new BaseException(BaseBizEnum.TIP, "当前货主商品下,SN开启出入库模式,当前SN无库存,请核查");
            }
        }
        //校验SN
        iCommitPackBiz.checkSkuSnRule(skuDTO, param.getSnCode(), snStockDTOList);
        return Result.success(true);
    }

    @Override
    public Result<Boolean> noCheckPackageMaterial(CodeParam param) {
        PackageParam packageParam = new PackageParam();
        packageParam.setPackageCode(param.getCode());
        PackageDTO packageDTO = iRemotePackageClient.get(packageParam);
        if (packageDTO == null) {
            throw new BaseException(BaseBizEnum.TIP, "包裹不存在");
        }
        if (!OrderTagEnum.NumToEnum(packageDTO.getOrderTag()).contains(OrderTagEnum.NO_CHECK_MATERIAL)) {
            Set<OrderTagEnum> orderTagEnums = OrderTagEnum.NumToEnum(packageDTO.getOrderTag());
            orderTagEnums.add(OrderTagEnum.NO_CHECK_MATERIAL);
            packageDTO.setOrderTag(OrderTagEnum.enumToNum(orderTagEnums.stream().collect(Collectors.toList())));
            iRemotePackageClient.updatePackage(packageDTO);
            //add log
            iBusinessLogBiz.savePackLog(packageDTO.getWarehouseCode(), packageDTO.getCargoCode(), packageDTO.getPackageCode(),
                    CurrentUserHolder.getUserName(), "操作人:"+CurrentUserHolder.getUserName() + ",包裹上打上标记【不校验包材】");
        }
        return Result.success(true);
    }


    /**
     * 组装拣选单复核
     *
     * @param packageDTO
     * @return
     */
    private PackageCheckDTO buildPackageCheck(PackageDTO packageDTO, String workbenchCode, String pickCode, String basketNo) {
        PackageCheckDTO packageCheckDTO = new PackageCheckDTO();
        packageCheckDTO.setWarehouseCode(packageDTO.getWarehouseCode());
        packageCheckDTO.setCargoCode(packageDTO.getCargoCode());
        packageCheckDTO.setPackageCode(packageDTO.getPackageCode());
        packageCheckDTO.setPackageCheckCode(iRemoteSeqRuleClient.findSequence(SeqEnum.PACK_CHECK_CODE_000001));
        packageCheckDTO.setBenchCode(workbenchCode);
        packageCheckDTO.setCheckBy(CurrentUserHolder.getUserName());
        packageCheckDTO.setPickCode(pickCode);
        packageCheckDTO.setStatus(PackCheckEnum.STATUS.COMPLETE_STATUS.getCode());
        packageCheckDTO.setPoNo(packageDTO.getPoNo());
        packageCheckDTO.setSoNo(packageDTO.getSoNo());
        packageCheckDTO.setExpressNo(packageDTO.getExpressNo());
        packageCheckDTO.setBusinessType(packageDTO.getBusinessType());
        packageCheckDTO.setShipmentOrderCode(packageDTO.getShipmentOrderCode());
        packageCheckDTO.setActualPackUpc(packageDTO.getActualPackUpc());
        packageCheckDTO.setSalePlatform(packageDTO.getSalePlatform());
        packageCheckDTO.setSaleShopId(packageDTO.getSaleShopId());
        packageCheckDTO.setCarrierCode(packageDTO.getCarrierCode());
        packageCheckDTO.setCarrierName(packageDTO.getCarrierName());
        packageCheckDTO.setBasketNo(basketNo);

        packageCheckDTO.setExpressBranch(packageDTO.getExpressBranch());
        packageCheckDTO.setExpressBranchName(packageDTO.getExpressBranchName());
        packageCheckDTO.setExpressAccount(packageDTO.getExpressAccount());


        List<PackageCheckDetailDTO> detailDTOList = new ArrayList<>();
        List<PackageDetailDTO> packageDetailDTOList = packageDTO.getListDetail();
        if (packageDTO.getIsPre().equalsIgnoreCase(PackEnum.TYPE.PRE.getCode())) {
            packageDetailDTOList = packageDetailDTOList.stream().filter(a -> a.getIsPre().equalsIgnoreCase(PackIsPreEnum.LAST.getCode()) || a.getIsPre().equalsIgnoreCase(PackIsPreEnum.PRE.getCode())).collect(Collectors.toList());
        } else {
            packageDetailDTOList = packageDetailDTOList.stream().filter(a -> a.getIsPre().equalsIgnoreCase(PackIsPreEnum.NORMAL.getCode())).collect(Collectors.toList());
        }
        for (PackageDetailDTO entity : packageDetailDTOList) {
            PackageCheckDetailDTO packageCheckDetailDTO = new PackageCheckDetailDTO();
            packageCheckDetailDTO.setPackageCheckCode(packageCheckDTO.getPackageCheckCode());
            packageCheckDetailDTO.setWarehouseCode(entity.getWarehouseCode());
            packageCheckDetailDTO.setCargoCode(entity.getCargoCode());
            packageCheckDetailDTO.setCheckQty(entity.getCheckQty());
            packageCheckDetailDTO.setQty(entity.getSkuQty());
            packageCheckDetailDTO.setSkuCode(entity.getSkuCode());
            packageCheckDetailDTO.setUpcCode(entity.getUpcCode());
            packageCheckDetailDTO.setSkuName(entity.getSkuName());
            packageCheckDetailDTO.setSkuQuality(entity.getSkuQuality());
            packageCheckDetailDTO.setStatus(PackCheckEnum.STATUS.COMPLETE_STATUS.getCode());
            detailDTOList.add(packageCheckDetailDTO);
        }
        packageCheckDTO.setQty(detailDTOList.stream().map(PackageCheckDetailDTO::getQty).reduce(BigDecimal.ZERO, BigDecimal::add));
        packageCheckDTO.setCheckQty(detailDTOList.stream().map(PackageCheckDetailDTO::getCheckQty).reduce(BigDecimal.ZERO, BigDecimal::add));
        packageCheckDTO.setDetailDTOList(detailDTOList);
        return packageCheckDTO;
    }
}
