package com.dt.platform.wms.transaction.bo;

import com.dt.domain.bill.dto.CollectWaveTaskDTO;
import com.dt.domain.bill.dto.PackageDTO;
import com.dt.domain.bill.dto.PickDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/10/20 13:11
 */
@Data
public class CollectPickBillBizBO implements Serializable {

    String key;

    List<PackageDTO> packageDTOS;

    List<PickDTO> pickDTOList;

//    List<ShipmentOrderDTO> shipmentOrderDTOS;

    CollectWaveTaskDTO collectWaveTaskDTO;
}
