package com.dt.platform.wms.transaction.bo;

import com.dt.domain.base.dto.ContainerDTO;
import com.dt.domain.base.dto.contLog.ContainerLogDTO;
import com.dt.domain.bill.dto.*;
import com.dt.domain.bill.dto.rec.ReceiptExtraDTO;
import com.dt.domain.bill.dto.rec.ReceiptExtraDetailDTO;
import com.dt.platform.wms.transaction.context.StockCommitContext;
import lombok.Data;

import java.util.List;

/**
 * Created by nobody on 2021/5/6 17:52
 */
@Data
public class AsnCancelReceiptBO  implements java.io.Serializable  {

    private AsnDTO asnDTO;
    private AsnLogDTO asnLogDTO;
    private List<AsnDetailDTO> asnDetailDTOS;
    private List<ContainerDTO> containerDTOS;
    private List<ContainerLogDTO> containerLogDTOS;
    private List<ReceiptDTO> receiptDTOS;
    private List<String> recIdListNeedCancelStock;
    private List<ReceiptDetailDTO> receiptDetailDTOS;
    private StockCommitContext stockCommitContext;
    private List<ShelfDTO> shelfDTOS;
    private List<ShelfDetailDTO> shelfDetailDTOS;
    private List<ReceiptExtraDTO> receiptExtraDTOS;
    private List<ReceiptExtraDetailDTO> receiptExtraDetailDTOS;
}