package com.dt.platform.wms.client;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.enums.pre.SkuIsPreEnum;
import com.dt.component.common.enums.sku.SkuTagEnum;
import com.dt.component.common.enums.sku.SkuUpcDefaultEnum;
import com.dt.component.common.enums.stock.StockSkuTypeEnum;
import com.dt.component.common.holder.CurrentRouteHolder;
import com.dt.component.common.result.Result;
import com.dt.domain.base.dto.CargoOwnerDTO;
import com.dt.domain.base.dto.SkuDTO;
import com.dt.domain.base.dto.SkuUpcDTO;
import com.dt.domain.base.dto.WarehouseDTO;
import com.dt.domain.base.param.CargoOwnerParam;
import com.dt.domain.base.param.SkuParam;
import com.dt.domain.base.param.SkuUpcParam;
import com.dt.domain.base.param.WarehouseParam;
import com.dt.domain.core.stock.dto.StockZoneDTO;
import com.dt.domain.core.stock.param.StockZoneParam;
import com.dt.platform.utils.ConverterUtil;
import com.dt.platform.wms.dto.stock.StockZoneBizDTO;
import com.dt.platform.wms.integration.IRemoteCargoOwnerClient;
import com.dt.platform.wms.integration.IRemoteSkuClient;
import com.dt.platform.wms.integration.IRemoteStockZoneClient;
import com.dt.platform.wms.integration.IRemoteWarehouseClient;
import com.dt.platform.wms.param.stock.StockZoneBizParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@DubboService(version = "${dubbo.service.version}")
public class StockZoneBizClient implements IStockZoneBizClient {

    @Resource
    private IRemoteStockZoneClient remoteStockZoneClient;

    @Resource
    private IRemoteWarehouseClient remoteWarehouseClient;

    @Resource
    private IRemoteCargoOwnerClient remoteCargoOwnerClient;

    @Resource
    private IRemoteSkuClient remoteSkuClient;

    @Override
    public Result<StockZoneBizDTO> getDetail(StockZoneBizParam param) {
        StockZoneParam bizParam = ConverterUtil.convert(param, StockZoneParam.class);
        StockZoneDTO result = remoteStockZoneClient.get(bizParam);
        StockZoneBizDTO skuBiz = ConverterUtil.convert(result, StockZoneBizDTO.class);
        if (!ObjectUtils.isEmpty(skuBiz)) {
            WarehouseDTO warehouse = remoteWarehouseClient.queryByCode(skuBiz.getWarehouseCode());
            CargoOwnerDTO cargoOwner = remoteCargoOwnerClient.queryByCode(skuBiz.getCargoCode());
            SkuParam skuParam = new SkuParam();
            skuParam.setCode(skuBiz.getSkuCode());
            skuParam.setCargoCode(skuBiz.getCargoCode());
            SkuDTO sku = remoteSkuClient.get(skuParam);

            SkuUpcParam skuUpcParam = new SkuUpcParam();
            skuUpcParam.setCargoCode(cargoOwner.getCode());
            skuUpcParam.setSkuCode(skuBiz.getSkuCode());
            skuUpcParam.setIsDefault(SkuUpcDefaultEnum.YES.getStatus());
            SkuUpcDTO upc = remoteSkuClient.getSkuUpc(skuUpcParam);

            skuBiz.setWarehouseName(ObjectUtils.isEmpty(warehouse) ? "" : warehouse.getName());
            skuBiz.setCargoName(ObjectUtils.isEmpty(cargoOwner) ? "" : cargoOwner.getName());
            skuBiz.setSkuName(ObjectUtils.isEmpty(sku) ? "" : sku.getName());
            skuBiz.setUpcCode(ObjectUtils.isEmpty(upc) ? "" : upc.getUpcCode());

        }
        return Result.success(skuBiz);
    }

    @Override
    public Result<Page<StockZoneBizDTO>> getPage(StockZoneBizParam param) {

        List<Map<String, String>> skuCodePairQueryList = new ArrayList<Map<String, String>>();
        if (!StringUtils.isEmpty(param.getUpcCodeList())) {
            SkuUpcParam skuUpcParam = new SkuUpcParam();
            skuUpcParam.setUpcCodeList(param.getUpcCodeList());
            List<SkuUpcDTO> skuUpcList = remoteSkuClient.getSkuUpcList(skuUpcParam);
            if (!CollectionUtils.isEmpty(skuUpcList)) {
                List<String> conditions = skuUpcList.stream()
                        .flatMap(a -> Stream.of(String.join("|", a.getSkuCode(), a.getCargoCode())))
                        .distinct()
                        .collect(Collectors.toList());
                for (String condition : conditions) {
                    Map pair = new HashMap();
                    String[] pairs = org.apache.commons.lang3.StringUtils.split(condition, "|");
                    pair.put(pairs[0], pairs[1]);
                    skuCodePairQueryList.add(pair);
                }
            } else {
                Map pair = new HashMap();
                pair.put("", "");
                skuCodePairQueryList.add(pair);
            }
        }
        StockZoneParam bizParam = ConverterUtil.convert(param, StockZoneParam.class);
        bizParam.setSkuCodePairQueryList(skuCodePairQueryList);

        Page<StockZoneDTO> page = remoteStockZoneClient.getPage(bizParam);
        Page<StockZoneBizDTO> result = ConverterUtil.convertPage(page, StockZoneBizDTO.class);
        if (!CollectionUtils.isEmpty(result.getRecords())) {
            List<String> warehouseCodeList = result.getRecords()
                    .stream()
                    .flatMap(a -> Stream.of(a.getWarehouseCode()))
                    .distinct()
                    .collect(Collectors.toList());
            WarehouseParam warehouseParam = new WarehouseParam();
            warehouseParam.setCodeList(warehouseCodeList);
            List<WarehouseDTO> warehouseList = remoteWarehouseClient.queryList(warehouseParam);

            List<String> cargoCodeList = result.getRecords()
                    .stream()
                    .flatMap(a -> Stream.of(a.getCargoCode()))
                    .distinct()
                    .collect(Collectors.toList());
            CargoOwnerParam cargoOwnerParam = new CargoOwnerParam();
            cargoOwnerParam.setCodeList(cargoCodeList);
            List<CargoOwnerDTO> cargoOwnerList = remoteCargoOwnerClient.queryList(cargoOwnerParam);

            List<String> skuCodeList = result.getRecords()
                    .stream()
                    .flatMap(a -> Stream.of(a.getSkuCode()))
                    .distinct()
                    .collect(Collectors.toList());
            SkuParam skuParam = new SkuParam();
            skuParam.setCodeList(skuCodeList);
            List<SkuDTO> skuList = remoteSkuClient.getList(skuParam);

            SkuUpcParam skuUpcParam = new SkuUpcParam();
            skuUpcParam.setSkuCodeList(skuCodeList);
            skuUpcParam.setIsDefault(SkuUpcDefaultEnum.YES.getStatus());
            List<SkuUpcDTO> upcList = remoteSkuClient.getSkuUpcList(skuUpcParam);


            result.getRecords().stream().forEach(a -> {
                WarehouseDTO warehouse = warehouseList.stream()
                        .filter(b -> b.getCode().equals(a.getWarehouseCode()))
                        .findAny()
                        .orElse(null);
                CargoOwnerDTO cargoOwner = cargoOwnerList.stream()
                        .filter(b -> b.getCode().equals(a.getCargoCode()))
                        .findAny().orElse(null);

                SkuDTO sku = skuList.stream()
                        .filter(b -> b.getCargoCode().equals(a.getCargoCode()))
                        .filter(b -> b.getCode().equals(a.getSkuCode()))
                        .findAny().orElse(null);

                SkuUpcDTO skuUpc = upcList.stream()
                        .filter(b -> b.getCargoCode().equals(a.getCargoCode()))
                        .filter(b -> b.getSkuCode().equals(a.getSkuCode()))
                        .findAny().orElse(null);

                a.setWarehouseName(ObjectUtils.isEmpty(warehouse) ? "" : warehouse.getName());
                a.setCargoName(ObjectUtils.isEmpty(cargoOwner) ? "" : cargoOwner.getName());
                a.setSkuName(ObjectUtils.isEmpty(sku) ? "" : sku.getName());
                a.setIsPre(ObjectUtils.isEmpty(sku) ? "" : sku.getIsPre());
                a.setUpcCode(ObjectUtils.isEmpty(skuUpc) ? "" : skuUpc.getUpcCode());

            });


        }
        return Result.success(result);
    }

    @Override
    public Result<String> initStockSkuType() {
        String warehouseCode = CurrentRouteHolder.getWarehouseCode();
        log.info(StrUtil.join(StrUtil.COLON, "开始处理仓库", warehouseCode,"库区维度"));
        initPreStockSkuType(warehouseCode);
        initBoxStockSkuType(warehouseCode);
        initBoxDetailStockSkuType(warehouseCode);
        return Result.success(StrUtil.join(StrUtil.COLON, "仓库", warehouseCode, "库区维度处理完成"));
    }

    private void initPreStockSkuType(String warehouseCode) {
        // 处理预包
        SkuParam skuParam = new SkuParam();
        skuParam.setIsPre(SkuIsPreEnum.PRE.getCode());
        List<SkuDTO> skuDTOList = remoteSkuClient.getList(skuParam);
        if (CollectionUtil.isNotEmpty(skuDTOList)) {
            log.info(StrUtil.join(StrUtil.COLON, "开始处理仓库", warehouseCode, "库区维度预包库存"));
            log.info(skuDTOList.stream().map(SkuDTO::getCode).collect(Collectors.joining(StrUtil.COMMA)));
            skuDTOList.forEach(skuDTO -> {
                initStockSkuType(skuDTO,StockSkuTypeEnum.PRE);
            });
            log.info(StrUtil.join(StrUtil.COLON, "仓库", warehouseCode, "库区维度预包库存处理完成"));
        }
    }

    private void initStockSkuType(SkuDTO skuDTO,StockSkuTypeEnum stockSkuTypeEnum) {
        StockZoneParam stockZoneParam = new StockZoneParam();
        stockZoneParam.setCargoCode(skuDTO.getCargoCode());
        stockZoneParam.setSkuCode(skuDTO.getCode());
        stockZoneParam.setStockSkuType(StockSkuTypeEnum.NORMAL.getCode());
        List<StockZoneDTO> stockZoneDTOList = remoteStockZoneClient.getList(stockZoneParam);
        stockZoneDTOList.forEach(stockZoneDTO -> {
            stockZoneDTO.setStockSkuType(stockSkuTypeEnum.getCode());
            remoteStockZoneClient.update(stockZoneDTO);
        });
    }

    private void initBoxStockSkuType(String warehouseCode) {
        // 处理套盒
        SkuParam skuParam = new SkuParam();
        skuParam.setSkuTag(SkuTagEnum.enumToNum(SkuTagEnum.BOX_SKU));
        List<SkuDTO> skuDTOList = remoteSkuClient.getList(skuParam);

        if (CollectionUtil.isNotEmpty(skuDTOList)) {
            log.info(StrUtil.join(StrUtil.COLON, "开始处理仓库", warehouseCode, "库区维度套盒库存"));
            log.info(skuDTOList.stream().map(SkuDTO::getCode).collect(Collectors.joining(StrUtil.COMMA)));
            skuDTOList.forEach(skuDTO -> {
                if (SkuTagEnum.NumToEnum(skuDTO.getSkuTag()).contains(SkuTagEnum.BOX_SKU)) {
                    initStockSkuType(skuDTO,StockSkuTypeEnum.BOX);
                }
            });
            log.info(StrUtil.join(StrUtil.COLON, "仓库", warehouseCode, "库区维度套盒库存处理完成"));
        }
    }

    private void initBoxDetailStockSkuType(String warehouseCode) {
        // 处理套盒明细
        SkuParam skuParam = new SkuParam();
        skuParam.setSkuTag(SkuTagEnum.enumToNum(SkuTagEnum.BOX_SKU_DETAIL));
        List<SkuDTO> skuDTOList = remoteSkuClient.getList(skuParam);

        if (CollectionUtil.isNotEmpty(skuDTOList)) {
            log.info(StrUtil.join(StrUtil.COLON, "开始处理仓库", warehouseCode, "库区维度套盒明细库存"));
            log.info(skuDTOList.stream().map(SkuDTO::getCode).collect(Collectors.joining(StrUtil.COMMA)));
            skuDTOList.forEach(skuDTO -> {
                if (SkuTagEnum.NumToEnum(skuDTO.getSkuTag()).contains(SkuTagEnum.BOX_SKU_DETAIL)) {
                   initStockSkuType(skuDTO,StockSkuTypeEnum.BOX_DETAIL);
                }
            });
            log.info(StrUtil.join(StrUtil.COLON, "仓库", warehouseCode, "库区维度套盒明细库存处理完成"));
        }
    }
}
