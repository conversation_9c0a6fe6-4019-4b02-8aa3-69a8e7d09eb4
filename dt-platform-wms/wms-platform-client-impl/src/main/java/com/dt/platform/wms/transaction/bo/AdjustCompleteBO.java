package com.dt.platform.wms.transaction.bo;

import com.dt.domain.bill.dto.AdjustDTO;
import com.dt.domain.bill.dto.AdjustDetailDTO;
import com.dt.domain.bill.dto.message.MessageMqDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="移位单操作DTO", description="移位操作")
public class AdjustCompleteBO extends AbsWarehouseBO  implements java.io.Serializable  {

    @ApiModelProperty("调整单信息（带明细数据）")
    private AdjustDTO adjust;

    private List<AdjustDetailDTO> adjustDetailList;

    // 是否需要erp审核
    private Boolean needERPCheck;

    private List<MessageMqDTO> messageMqDTOList;

}