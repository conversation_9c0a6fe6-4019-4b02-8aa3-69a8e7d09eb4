package com.dt.platform.wms.biz.impl.location.recommend;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.RandomUtil;
import com.dt.component.common.enums.base.ZoneTypeEnum;
import com.dt.domain.base.dto.rule.ShelfRuleDTO;
import com.dt.platform.wms.biz.impl.RecommendLocationBizImpl;

import java.util.List;
import java.util.stream.Collectors;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.when;

/**
 * - 拣选区、存储区均无正品或次品库存
 * - 有合适库位但是库区被限制了最终无法被推荐
 */
public class CaseEmptyStock0003 extends RecommendLocationBizImplCommon {

    private String someZoneCode = "someZoneCode";

    public void prepareData(RecommendLocationBizImpl recommendLocationBiz) {
        when(recommendLocationBiz.getRemoteSkuClient().get(any())).thenReturn(skuDTO());
        when(recommendLocationBiz.getRemoteSkuLotClient().get(any())).thenReturn(skuLotDTO());
        when(recommendLocationBiz.getRemoteTurnoverRuleClient().queryTurnoverRuleByCode(any())).thenReturn(turnoverRuleDTO());
        when(recommendLocationBiz.getRemoteShelfRuleClient().getList(any())).thenReturn(shelfRuleDTOListWithZoneLimit());
        when(recommendLocationBiz.getRemoteLocationClient().getList(argThat(locationParam -> locationParam != null
                && locationParam.getZoneType().equals(ZoneTypeEnum.ZONE_TYPE_PICK.getType())))).thenReturn(RandomUtil.randomEle(EmptyLocationCase.validCaseList));
        when(recommendLocationBiz.getRemoteLocationClient().getList(argThat(locationParam -> locationParam != null
                && locationParam.getZoneType().equals(ZoneTypeEnum.ZONE_TYPE_STORE.getType())))).thenReturn(RandomUtil.randomEle(EmptyLocationCase.invalidCaseList));
        when(recommendLocationBiz.getRemoteLocationClient().getList(argThat(locationParam -> locationParam != null
                && CollectionUtil.isNotEmpty(locationParam.getZoneCodeList())
                && locationParam.getZoneCodeList().contains(someZoneCode)))).thenReturn(RandomUtil.randomEle(EmptyLocationCase.invalidCaseList));
        when(recommendLocationBiz.getRemoteStockLocationClient().getList(any())).thenReturn(stockLocationDTOList());
    }

    public List<ShelfRuleDTO> shelfRuleDTOListWithZoneLimit() {
        List<ShelfRuleDTO> shelfRuleDTOS = shelfRuleDTOList();
        List<ShelfRuleDTO> shelfRuleDTOList = shelfRuleDTOS.stream().map(shelfRuleDTO -> BeanUtil.toBean(shelfRuleDTO, ShelfRuleDTO.class)).collect(Collectors.toList());
        for (ShelfRuleDTO shelfRuleDTO : shelfRuleDTOList) {
            shelfRuleDTO.setZoneCodeAll(someZoneCode);
        }
        return shelfRuleDTOList;
    }
}
