version: '1.1'

stages:

  - stage:
      - git-checkout:
          alias: deploy_config_repo

  - stage:
      - java:
          alias: wms-platform-starter
          params:
            build_type: maven
            workdir: ${deploy_config_repo}
            target: ./wms-platform-starter/target/wms-platform-starter.jar
            container_type: spring-boot

  - stage:
      - release:
          params:
            dice_yml: ${deploy_config_repo}/dice.yml
            image:
              wms-platform-starter: ${wms-platform-starter:OUTPUT:image}

  - stage:
      - dice:
          params:
            release_id_path: ${release}