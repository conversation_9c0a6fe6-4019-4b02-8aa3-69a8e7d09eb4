package com.dt.platform.wms.foroms.client.calculate;

import com.dt.platform.wms.foroms.param.CalculateWeightParam;
import com.dt.platform.wms.foroms.result.Result;
import com.dt.platform.wms.foroms.dto.CalculateWeightResult;

/**
 * {@link ICalculateRpcClient#packageWeight}
 * 试算接口
 */
public interface ICalculateRpcClient {

    /**
     * 试算包裹重量(商品重量 + 耗材重量 + 包材重量)
     * @param calculateWeightParam
     * @return
     */
    Result<CalculateWeightResult> packageWeight(CalculateWeightParam calculateWeightParam);
}
