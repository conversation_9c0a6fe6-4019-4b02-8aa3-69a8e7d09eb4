package com.dt.platform.wms.param.shipment;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class ShipmentDetailSplitBizParam  implements Serializable {
    @ApiModelProperty(value = "批次ID")
    private String skuLotNo;
    @ApiModelProperty(value = "计划商品数量")
    private BigDecimal expSkuQty;
    @ApiModelProperty(value = "出单编码")
    private String shipmentOrderCode;
    @ApiModelProperty(value = "物理主键")
    private Long id;


}
