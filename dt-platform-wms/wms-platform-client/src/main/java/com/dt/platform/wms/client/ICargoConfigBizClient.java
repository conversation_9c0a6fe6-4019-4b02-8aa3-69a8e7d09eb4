package com.dt.platform.wms.client;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.dt.component.common.result.Result;
import com.dt.component.common.vo.IdNameVO;
import com.dt.platform.wms.dto.cargo.CargoConfigBizDTO;
import com.dt.platform.wms.dto.cargo.CargoConfigParamBizDTO;
import com.dt.platform.wms.form.CargoConfigAddBizForm;
import com.dt.platform.wms.form.CargoConfigUpdateBizForm;
import com.dt.platform.wms.param.cargo.CargoConfigBizParam;
import com.dt.platform.wms.param.cargo.CargoConfigEnableBizParam;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/9/15 15:31
 */
public interface ICargoConfigBizClient {
    /**
     * 分页
     *
     * @param param
     * @return
     */
    Result<IPage<CargoConfigBizDTO>> queryPage(CargoConfigBizParam param);

    /**
     * 禁用和启用
     *
     * @param param
     * @return
     */
    Result<Boolean> enable(CargoConfigEnableBizParam param);

    /**
     * 获取全部状态码
     *
     * @return
     */
    Result<List<IdNameVO>> queryStatusAll();

    /**
     * 获取全部状态码
     *
     * @return
     */
    Result<List<IdNameVO>> queryStatus();

    /**
     * 查询单个货主配置
     *
     * @param id
     * @return
     */
    Result<CargoConfigBizDTO> queryById(Long id);

    /**
     * 货主参数分组
     *
     * @return
     */
    Result<List<IdNameVO>> codeGroup();

    /**
     * 货主参数分组
     *
     * @return
     */
    Result<List<IdNameVO>> codeGroupAll();

    /**
     * 货主参数分组
     *
     * @return
     */
    Result<List<CargoConfigParamBizDTO>> paramList();

    /**
     * 新增
     *
     * @param param
     * @return
     */
    Result<Boolean> add(CargoConfigAddBizForm param);

    /**
     * 修改
     *
     * @param param
     * @return
     */
    Result<Boolean> update(CargoConfigUpdateBizForm param);
}
