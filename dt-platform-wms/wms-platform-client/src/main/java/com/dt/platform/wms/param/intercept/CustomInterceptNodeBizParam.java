package com.dt.platform.wms.param.intercept;

import com.dt.component.common.param.BaseSearchParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;


/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="CustomInterceptNode对象", description="")
public class CustomInterceptNodeBizParam extends BaseSearchParam  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;
    /**
     * 仓库编码 不允许修改
     */
    @ApiModelProperty(value = "仓库编码 不允许修改")
    private String warehouseCode;

    /**
     * 货主编码
     */
    @ApiModelProperty(value = "货主编码")
    private String cargoCode;
    private List<String> cargoCodeList;
    /**
     * 层级
     */
    @ApiModelProperty(value = "层级")
    private String level;
    private List<String> levelList;
    /**
     * 平台编码
     */
    @ApiModelProperty(value = "平台编码")
    private String salePlatform;
    private List<String> salePlatformList;
    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型")
    private String businessType;

    /**
     * 拦截节点
     */
    @ApiModelProperty(value = "拦截节点")
    private String interceptNode;
    private List<String> interceptNodeList;
    /**
     * 状态码
     */
    @ApiModelProperty(value = "状态码")
    private Integer status;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "修改人")
    private String updatedBy;
}