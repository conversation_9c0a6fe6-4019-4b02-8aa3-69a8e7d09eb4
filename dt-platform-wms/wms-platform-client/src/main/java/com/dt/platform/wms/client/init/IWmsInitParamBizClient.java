package com.dt.platform.wms.client.init;

import java.util.Map;

public interface IWmsInitParamBizClient {


    /**
     * @param warehouseCode
     * @return void
     * <AUTHOR>
     * @describe:
     * @date 2024/10/8 13:20
     */
    void initCargoConfigParam(String warehouseCode);

    /**
     * @param map
     * @return java.util.Map<java.lang.String, java.lang.Long>
     * <AUTHOR>
     * @describe:
     * @date 2024/10/8 13:18
     */
    Map<String, Long> initSeqRuleRedisIncrease(Map<String, String> map);

}
