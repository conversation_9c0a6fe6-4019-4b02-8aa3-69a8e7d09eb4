package com.dt.platform.wms.client;

import com.dt.component.common.result.Result;
import com.dt.platform.wms.dto.wave.CollectWaveBizDTO;
import com.dt.platform.wms.param.wave.CollectWaveBillCommitParam;
import com.dt.platform.wms.param.wave.CollectWaveBillQueryParam;

/**
 * <AUTHOR>
 * @date 2020/10/15 10:43
 */
public interface ICollectWaveBillClient {
    /**
     * 波次查询
     * @param param
     * @return
     */
    Result<CollectWaveBizDTO> queryCollectWaveBill(CollectWaveBillQueryParam param);

    /**
     * 波次汇总
     * @param param
     * @return
     */
    Result<Boolean> commitCollectWaveBill(CollectWaveBillCommitParam param);

    /**
     * 策略二
     * @param warehouseCode
     */
    void allocationStockSyncPick(String warehouseCode,Long time);

    /**
     * 指定库区汇单任务
     * @return
     */
    Result<Integer> queryTaskCollect();

    void dropTaskDataOldByCollectTask(String param, String warehouseCode);
}
