package com.dt.platform.wms.client;

import com.dt.domain.bill.dto.ShipmentOrderDTO;
import com.dt.domain.bill.param.ShipmentOrderParam;

/**
 * 功能描述:
 * 创建时间:  2021/7/19 2:43 下午
 *
 * <AUTHOR>
 */
public interface IShipmentOrderDecryptBizClient {

    ShipmentOrderDTO encryptShipment(ShipmentOrderDTO shipmentOrderDTO);

    ShipmentOrderParam encryptShipment(ShipmentOrderParam shipmentOrderParam);
}
