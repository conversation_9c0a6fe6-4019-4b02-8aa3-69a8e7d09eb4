package com.dt.platform.wms.param.platformSendAddress;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class PlatformSendAddressBizEnableParam  implements java.io.Serializable  {

    @ApiModelProperty(value = "发件地址参数ID")
    @NotEmpty(message = "发件地址参数ID不能为空")
    private List<Long> idList;

    @NotNull(message = "状态不能为空")
    @Range(min = -1, max = 1, message = "启用、禁用状态不正确")
    @ApiModelProperty(value = "-1:禁用 1启用 ")
    private Integer status;
}