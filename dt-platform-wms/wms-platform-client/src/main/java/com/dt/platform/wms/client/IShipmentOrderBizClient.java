package com.dt.platform.wms.client;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.domain.bill.dto.ShipmentOrderDetailDTO;
import com.dt.domain.bill.dto.ShipmentOrderMaterialDTO;
import com.dt.domain.bill.param.ShipmentOrderParam;
import com.dt.platform.wms.dto.box.AnalysisCommitBizDTO;
import com.dt.platform.wms.dto.box.AnalysisErrorBizDTO;
import com.dt.platform.wms.dto.shipment.ShipmentOrderBizDTO;
import com.dt.platform.wms.dto.shipment.ShipmentOrderBizDTO2;
import com.dt.platform.wms.dto.shipment.ShipmentOrderDetailBizDTO;
import com.dt.platform.wms.dto.tally.TallyBizDTO;
import com.dt.platform.wms.form.Shipment.OrderTagOperationParam;
import com.dt.platform.wms.form.Shipment.ShipmentAddForm;
import com.dt.platform.wms.form.Shipment.ShipmentModifyForm;
import com.dt.platform.wms.param.CodeListParam;
import com.dt.platform.wms.param.CodeParam;
import com.dt.platform.wms.param.shipment.*;

import java.util.List;
import java.util.Map;

public interface IShipmentOrderBizClient {

    Result<List<ShipmentOrderBizDTO>> initCount();

    Result<Page<ShipmentOrderBizDTO>> getPage(ShipmentOrderBizParam param);

    Result<ShipmentOrderBizDTO2> get(ShipmentOrderBizParam param);

    Result<Page<ShipmentOrderDetailBizDTO>> getPage2(ShipmentOrderBizParam param);

    Result<ShipmentOrderBizDTO> getDetail(ShipmentOrderBizParam param);

    /**
     * @param param
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * @author: WuXian
     * description:  修改快递公司
     * create time: 2021/8/20 14:22
     */
    Result<Boolean> modifyCarrier(ModifyCarrierBizParam param);

    /**
     * @param param
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * @author: WuXian
     * description:  修改运单号
     * create time: 2021/8/20 14:22
     */
    Result<Boolean> modifyExpressNo(ModifyExpressNoBizParam param);

    /**
     * @param listShipmentOrderCode
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * @author: WuXian
     * description:  取消预处理
     * create time: 2021/8/20 14:24
     */
    Result<Boolean> rollback(List<String> listShipmentOrderCode);


    List<ShipmentOrderBizDTO> getList(ShipmentOrderBizParam param);

    List<String> getCodeList(ShipmentOrderBizParam param);

    /**
     * 修改出库单名称批次
     *
     * @param shipmentOrderBizParam
     * @return
     */
    Boolean assignSkuLotNo(ShipmentOrderBizParam shipmentOrderBizParam);

    /**
     * 查询明细
     *
     * @param param
     * @return
     */
    List<ShipmentOrderDetailDTO> getShipmentDetailSkuMsg(ShipmentOrderBizParam param);

    /**
     * 查询包材
     *
     * @param shipmentOrderCode
     * @return
     */
    List<ShipmentOrderMaterialDTO> getShipmentMaterial(String shipmentOrderCode);

    /**
     * @param param
     * @return
     */
    Boolean oneClickOutBound(CodeParam param) throws Exception;

    /**
     * @param form
     * @return java.lang.Boolean
     * @author: WuXian
     * description:
     * create time: 2021/7/15 17:35
     */
    Result<Boolean> wmsAddShipment(ShipmentAddForm form);

    /**
     * @param form
     * @return java.lang.Boolean
     * @author: WuXian
     * description:
     * create time: 2021/7/15 17:35
     */
    Result<Boolean> wmsModifyShipment(ShipmentModifyForm form);

    /**
     * @param param
     * @return com.dt.component.common.result.Result<java.lang.String>
     * @author: WuXian
     * description: 取消出库单
     * create time: 2021/7/16 14:16
     */
    Result<String> wmsCancelShipment(ShipmentCancelParam param);

    /**
     * @param param
     * @return com.dt.component.common.result.Result<com.dt.platform.wms.dto.shipment.ShipmentOrderBizDTO>
     * @author: WuXian
     * description: 获取修改出库单明细
     * create time: 2021/7/16 14:16
     */
    Result<ShipmentOrderBizDTO> wmsModifyShipmentDetail(CodeParam param);

    /**
     * @param param
     * @return
     * @author: WuXian
     * description:  分销出库
     * create time: 2021/12/2 13:57
     */
    Result<Boolean> distributionOutBound(CodeParam param) throws Exception;

    /**
     * @param param
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * @author: WuXian
     * description:
     * create time: 2022/1/14 17:40
     */
    Result<Boolean> wmsAddShipmentTally(CodeParam param);

    Result<ShipmentOrderBizDTO> decryptForShipment(ShipmentOrderBizParam bizParam);

    /**
     * 推荐快递
     *
     * @param warehouseCode
     * @param cargoCode
     * @param shipmentOrderCode
     */
    String maintainCarrierIfNeed(String warehouseCode, String cargoCode, String shipmentOrderCode);

    /**
     * 维护重量
     *
     * @param warehouseCode
     * @param cargoCode
     * @param shipmentOrderCode
     */
    void maintainWeight(String warehouseCode, String cargoCode, String shipmentOrderCode);

    /**
     * @param param
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * @author: WuXian
     * description:
     * create time: 2022/5/10 13:41
     */
    Result<Boolean> modifyOrderTag(OrderTagOperationParam param);

    /**
     * @param param
     * @return com.dt.component.common.result.Result<java.util.List < java.lang.Integer>>
     * @author: WuXian
     * description:
     * create time: 2022/5/10 16:50
     */
    Result<List<Integer>> getOrderTagPartToOperationOccupy(CodeParam param);
    /**
     * @param param
     * @return com.dt.component.common.result.Result<com.dt.platform.wms.dto.box.AnalysisErrorBizDTO>
     * <AUTHOR>
     * @describe:
     * @date 2022/9/20 14:43
     */
    Result<AnalysisErrorBizDTO> getAnalysisError(CodeParam param);
    /**
     * @param commitBizDTO
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     * @describe:
     * @date 2022/9/20 16:18
     */
    Result<Boolean> commitAnalysisData(AnalysisCommitBizDTO commitBizDTO);

    Result<Boolean> xtBatchOut(ShipmentOrderBizParam param);

    Result<Boolean>  outOfStockNotify(OutOfStockNotifyParam param);

    Result<List<ShipmentOrderDetailDTO>> outOfStockNotifyView(OutOfStockNotifyParam param);

    /**
     * @param param
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     * @describe: 销退仓一键出库
     * @date 2024/5/11 13:19
     */
    Result<Boolean> oneClickOutBoundByXt(ShipmentOrderBizParam param);

    Result<Page<ShipmentOrderDetailBizDTO>> getPageDetail(ShipmentOrderBizParam shipmentOrderBizParam);
    /**
     * @param param
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     * @describe: 0出库
     * @date 2024/11/27 13:28
     */
    Result<Boolean> zeroOutStock(ShipmentOrderBizParam param);
    /**
     * @param param
     * @return com.dt.component.common.result.Result<java.lang.Object>
     * <AUTHOR>
     * @describe:
     * @date 2025/1/13 13:13
     */
    Result<Object> signAndNotify(ShipmentOrderBizParam param);
    /**
     * @param param
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     * @describe:
     * @date 2025/1/15 9:01
     */
    Result<Boolean> fixMergeShipOut(ShipmentOrderBizParam param);
    /**
     * @param shipmentOrderCodeList
     * @return java.util.List<com.dt.platform.wms.dto.tally.TallyBizDTO>
     * <AUTHOR>
     * @describe:
     * @date 2025/2/26 13:21
     */
    Map<String, String> queryShipTallyStatus(List<String> shipmentOrderCodeList);
}
