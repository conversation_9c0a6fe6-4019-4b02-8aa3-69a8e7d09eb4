package com.dt.platform.wms.client;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.dt.component.common.result.Result;
import com.dt.component.common.vo.IdNameVO;
import com.dt.platform.wms.dto.pkg.PackageCheckBizDTO;
import com.dt.platform.wms.param.CodeParam;
import com.dt.platform.wms.param.pkg.PackageCheckBizParam;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/10/18 16:19
 * @date 2020/10/29 14:53
 */
public interface IPackageCheckBizClient {

    /**
     * 扫描蓝号
     * 分页
     * @param param
     * @return
     */
    Result<IPage<PackageCheckBizDTO>> queryPage(PackageCheckBizParam param);

    /**
     * 扫描包材
     * 查询详情
     * @param param
     * @return
     */
    Result<PackageCheckBizDTO> queryDetail(CodeParam param);

    /**
     *
     * @return
     */
    Result<List<IdNameVO>> queryStatus();

    /**
     *
     * @return
     */
    Result<List<IdNameVO>> queryStatusAll();
}

