package com.dt.platform.wms.param.carryover;

import com.dt.component.common.param.BaseSearchParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import java.math.BigDecimal;

/**
 * <p>
 * 账册结转单明细
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="BookCarryoverDetail对象", description="账册结转单明细")
public class BookCarryoverDetailBizParam extends BaseSearchParam  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    /**
     * 仓库编码
     */
    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    /**
     * 结转单号
     */
    @ApiModelProperty(value = "结转单号")
    private String carryoverCode;

    /**
     * 商品编码
     */
    @ApiModelProperty(value = "商品编码")
    private String skuCode;

    /**
     * 统一料号
     */
    @ApiModelProperty(value = "统一料号")
    private String itemCode;

    /**
     * 商品条形码
     */
    @ApiModelProperty(value = "商品条形码")
    private String upcCode;

    /**
     * 商品名
     */
    @ApiModelProperty(value = "商品名")
    private String skuName;

    /**
     * 变动数量
     */
    @ApiModelProperty(value = "变动数量")
    private BigDecimal changeQty;

    /**
     * 来源企业
     */
    @ApiModelProperty(value = "来源企业")
    private String originEnterprise;

    /**
     * 目标企业
     */
    @ApiModelProperty(value = "目标企业")
    private String targetEnterprise;

    /**
     * 来源库区
     */
    @ApiModelProperty(value = "来源库区")
    private String originZoneCode;

    /**
     * 目标库区
     */
    @ApiModelProperty(value = "目标库区")
    private String targetZoneCode;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "修改人")
    private String updatedBy;
}