package com.dt.platform.wms.param.wave;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class WaveNavigationQueryBizParam implements Serializable {

    @ApiModelProperty(value = "货主编码")
    private List<String> cargoList;

    @ApiModelProperty(value = "快递编码")
    private List<String> carrierList;

    @ApiModelProperty(value = "店铺编码")
    private List<String> saleShopList;

    @ApiModelProperty(value = "平台编码")
    private List<String> salePlatformList;

    @ApiModelProperty(value = "今日 昨日 两天前")
    private List<String> lastShipTimeList;

    @ApiModelProperty(value = "结构")
    private String packageStruct;

    @ApiModelProperty(value = "前端是否选中级别")
    private String checkLevel;

    @ApiModelProperty(value = "分析类型")
    private String analysisType;

    @ApiModelProperty(value = "包裹编码")
    List<String> packCodeList;
    List<Long> idList;


}
