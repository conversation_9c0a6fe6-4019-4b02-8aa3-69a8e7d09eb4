package com.dt.platform.wms.param.rule;

import com.dt.component.common.param.BaseSearchParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import java.math.BigDecimal;

/**
 * <p>
 * 上架规则表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ShelfRule对象", description="上架规则表")
public class ShelfRuleBizParam extends BaseSearchParam  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    /**
     * 仓库编码 不允许修改
     */
    @ApiModelProperty(value = "仓库编码 不允许修改")
    private String warehouseCode;

    /**
     * 上架规则编码
     */
    @ApiModelProperty(value = "上架规则编码")
    private String shelfRuleCode;

    /**
     * 单据类型
     */
    @ApiModelProperty(value = "单据类型")
    private String billType;

    /**
     * 库区优先级
     */
    @ApiModelProperty(value = "库区优先级")
    private String zonePriority;

    /**
     * 无库存库区编码
     */
    @ApiModelProperty(value = "无库存库区编码")
    private String zoneCodeAll;

    /**
     * 无库存连续库位数量
     */
    @ApiModelProperty(value = "无库存连续库位数量")
    private Integer continueNum;

    @ApiModelProperty(value = "状态码")
    private Integer status;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "修改人")
    private String updatedBy;
}