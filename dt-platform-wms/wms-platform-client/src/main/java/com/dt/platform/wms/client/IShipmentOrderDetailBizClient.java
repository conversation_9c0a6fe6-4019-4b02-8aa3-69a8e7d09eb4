package com.dt.platform.wms.client;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.domain.bill.dto.ShipmentOrderDetailDTO;
import com.dt.domain.bill.param.ShipmentOrderDetailParam;
import com.dt.platform.wms.dto.shipment.ShipmentOrderBizDTO;
import com.dt.platform.wms.dto.shipment.ShipmentOrderBizDTO2;
import com.dt.platform.wms.dto.shipment.ShipmentOrderDetailBizDTO;
import com.dt.platform.wms.param.shipment.*;

import java.util.List;

public interface IShipmentOrderDetailBizClient {

    /**
     * 获取出库单明细信息
     *
     * @param param
     * @return
     */
    Result<ShipmentOrderDetailDTO> get(ShipmentOrderDetailParam param);

    Result<List<ShipmentOrderDetailDTO>> getList(ShipmentOrderDetailParam param);

    /**
     * 出库单指定批次，拆分
     * @param shipmentDetailSplitParam
     * @return
     */
    Result<Boolean> doSplitDetail(ShipmentDetailSplitParam shipmentDetailSplitParam);

}
