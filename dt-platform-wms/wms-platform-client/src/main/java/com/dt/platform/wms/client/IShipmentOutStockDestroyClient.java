package com.dt.platform.wms.client;

import com.dt.platform.wms.dto.shipment.ShipmentCheckSkuLotBizDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/8/5 11:32
 */
public interface IShipmentOutStockDestroyClient {

    /**
     * @param shipmentCheckSkuLotBizDTO
     * @return java.lang.String
     * @author: WuXian
     * description:  校验销毁出库批次
     * create time: 2021/8/5 11:40
     */
    void checkOutStockDestroySkuLot(ShipmentCheckSkuLotBizDTO shipmentCheckSkuLotBizDTO);

    /**
     * @param shipmentCheckSkuLotBizDTO
     * @return java.lang.String
     * @author: WuXian
     * description:  校验出库批次存在性
     * create time: 2021/8/5 11:40
     */
    void checkOutStockSkuLotExists(ShipmentCheckSkuLotBizDTO shipmentCheckSkuLotBizDTO);

}
