package com.dt.platform.wms.param.check.b2b;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class B2BSubmitParamNew implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "packageCode")
    private String packageCode;

    @ApiModelProperty(value = "工作台号")
    private String workBenchCode;

    List<B2BSubmitSkuParamNew> skuParamList;

}
