package com.dt.platform.wms.client;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.domain.bill.dto.AllocationBillDTO;
import com.dt.domain.bill.dto.AllocationOrderDTO;
import com.dt.domain.bill.param.AllocationBillParam;
import com.dt.domain.bill.param.AllocationOrderParam;
import com.dt.platform.wms.dto.shipment.ShipmentOrderBizDTO;
import com.dt.platform.wms.param.shipment.ShipmentOrderBizParam;

import java.util.List;

/**
 * <AUTHOR>
 * @Create 2021/02/25  18:40
 * @Describe
 **/
public interface IAllocationOrderClient {

    /**
     * 提交分配单
     * @param allocationOrderDTOS
     * @return
     */
    Result<Boolean> submitCollect(List<AllocationOrderDTO> allocationOrderDTOS);

    /**
     * 查询指定的分配单分析
     * @param orderParam
     * @return
     */
    Result<List<AllocationOrderDTO>> getWaveList(AllocationOrderParam orderParam);

    /**
     * 获取分配单
     * @param packageCode
     * @param waveCode
     * @return
     */
    Result<List<AllocationOrderDTO>> getPackList(List<String> packageCode,String waveCode);

    /**
     * 获取分配库位信息
     * @param param
     * @return
     */
    List<AllocationOrderDTO> getList(AllocationOrderParam param);

    /**
     * 获取出库明细分配信息
     * @param param
     * @return
     */
    Result<Page<AllocationOrderDTO>> getShipmentOrderPage(ShipmentOrderBizParam param);


    /**
     * 拣选区库存到拣选暂存位
     * @param billParam
     * @return
     */
    Result<List<AllocationBillDTO>> getPackGroupByLocationAndSkuLotNo(AllocationBillParam billParam);
}
