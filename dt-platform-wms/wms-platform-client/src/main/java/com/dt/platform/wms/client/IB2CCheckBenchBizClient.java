package com.dt.platform.wms.client;

import com.dt.component.common.result.Result;
import com.dt.platform.wms.dto.check.PackBatchCheckResultBizDTO;
import com.dt.platform.wms.dto.check.PackCheckInterceptionResultBizDTO;
import com.dt.platform.wms.dto.check.PackCheckResultBizDTO;
import com.dt.platform.wms.dto.check.WarehouseCrossBorderBizDTO;
import com.dt.platform.wms.dto.check.back.PackBackPickBizDTO;
import com.dt.platform.wms.dto.check.back.PackExpressBizDTO;
import com.dt.platform.wms.param.CodeParam;
import com.dt.platform.wms.param.check.*;

/**
 * <AUTHOR>
 * @date 2020/10/18 16:19
 */
public interface IB2CCheckBenchBizClient {
    /**
     * @param param
     * @return com.dt.component.common.result.Result<com.dt.platform.wms.dto.check.back.PackBackPickBizDTO>
     * @author: WuXian
     * description:  扫描拣选单号
     * create time: 2021/9/13 9:39
     */
    Result<PackBackPickBizDTO> checkPickCode(PackCheckBackParam param);

    /**
     * @param param
     * @return com.dt.component.common.result.Result<com.dt.platform.wms.dto.check.WarehouseCrossBorderBizDTO>
     * @author: WuXian
     * description:  扫描工作台号
     * create time: 2021/9/13 9:39
     */
    Result<WarehouseCrossBorderBizDTO> checkWorkbench(PackCheckWorkbenchParam param);


    /**
     * @param param
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * @author: WuXian
     * description:  校验包材
     * create time: 2021/9/13 9:39
     */
    Result<Boolean> checkPackMaterial(PackCheckMaterialParam param);

    /**
     * @param param
     * @return com.dt.component.common.result.Result<com.dt.platform.wms.dto.check.PackCheckResultBizDTO>
     * @author: WuXian
     * description:  组包
     * create time: 2021/9/13 9:40
     */
    Result<PackCheckResultBizDTO> submitPackage(PackSubmitParam param) throws Exception;

    /**
     * @param param
     * @return com.dt.component.common.result.Result<com.dt.platform.wms.dto.check.back.PackExpressBizDTO>
     * @author: WuXian
     * description:  散单复核(运单号或包裹号)
     * create time: 2021/9/13 9:40
     */
    Result<PackExpressBizDTO> checkExpressNoOrPackageCode(PackCheckFrontParam param);

    /**
     * @param param
     * @return com.dt.component.common.result.Result<com.dt.platform.wms.dto.check.back.PackExpressBizDTO>
     * @author: WuXian
     * description:  扫描商品条形码-获取当前拣选单最小蓝号包裹
     * create time: 2021/9/13 9:40
     */
    Result<PackExpressBizDTO> scanUpcCode(PackCheckUpcCodeParam param);

    /**
     * @param param
     * @return com.dt.component.common.result.Result<com.dt.platform.wms.dto.check.PackBatchCheckResultBizDTO>
     * @author: WuXian
     * description:  拣选单复核
     * create time: 2021/9/13 9:40
     */
    Result<PackBatchCheckResultBizDTO> batchCheckComplete(CodeParam param) throws Exception;

    /**
     * @param param
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * @author: WuXian
     * description:  校验溯源码规则是否匹配和溯源码是否已使用
     * create time: 2021/9/13 9:41
     */
    Result<Boolean> checkSourceCode(PackCheckSourceCodeParam param);

    /**
     * @param param
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     * @describe: 冷链泡沫箱校验
     * @date 2022/8/2 15:23
     */
    Result<Boolean> checkPackHC(PackCheckHCParam param);

    /**
     * @param param
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     * @describe: 封口贴校验
     * @date 2022/8/2 15:23
     */
    Result<Boolean> checkSealingTape(CheckSealingTapeParam param);

    /**
     * @param param
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     * @describe:
     * @date 2022/10/8 10:19
     */
    Result<String> interceptionBatchPack(InterceptionBatchPackParam param) throws Exception;

    /**
     * @param param
     * @return com.dt.component.common.result.Result<java.lang.Object>
     * <AUTHOR>
     * @describe:
     * @date 2022/10/8 10:20
     */
    Result<PackCheckInterceptionResultBizDTO> queryInterceptionBatchPackInfo(InterceptionBatchPackParam param);

    Result<Boolean> oversizeBoxRegulation(CodeParam param);
    /**
     * @param param
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     * @describe: 复核校验SN
     * @date 2024/5/9 14:06
     */
    Result<Boolean> checkPackSN(PackCheckSNParam param);
    /**
     * @param param
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     * @describe:
     * @date 2024/12/4 14:47
     */
    Result<Boolean> noCheckPackageMaterial(CodeParam param);
}
