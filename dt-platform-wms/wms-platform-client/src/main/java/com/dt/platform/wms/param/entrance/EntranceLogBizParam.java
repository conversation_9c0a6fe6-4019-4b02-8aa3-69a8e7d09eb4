package com.dt.platform.wms.param.entrance;

import com.dt.component.common.param.BaseSearchParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import java.math.BigDecimal;

/**
 * <p>
 * entrance log
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="EntranceLog对象", description="entrance log")
public class EntranceLogBizParam extends BaseSearchParam  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    /**
     * 仓库编码
     */
    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    /**
     * 货主编码
     */
    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    /**
     * 提单号
     */
    @ApiModelProperty(value = "提单号")
    private String ladingNo;

    /**
     * 
     */
    @ApiModelProperty(value = "")
    private String msg;

    /**
     * 操作人
     */
    @ApiModelProperty(value = "操作人")
    private String opBy;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "修改人")
    private String updatedBy;
}