package com.dt.platform.wms.constant;

public interface PretreatmentConstant {

    String SPLIT_TAG = "@#@";

    //手工预处理常量
    String PRETREATMENT_CACHE_SEARCH = "DT_PRETREATMENT:CACHE:%s*";
    String PRETREATMENT_CHOOSE_SEARCH = "DT_PRETREATMENT:CHOOSE:%s*";
    String PRETREATMENT_SHIPMENT_LOCK_STOCK_SEARCH = "DT_PRETREATMENT:SHIPMENT_LOCK_STOCK_SUCCESS:%s*";
    String PRETREATMENT_SHIPMENT_EXPRESS_MACH_SEARCH= "DT_PRETREATMENT:SHIPMENT_EXPRESS_MACH_SUCCESS:%s*";

    String PRETREATMENT_CACHE_PREFIX = "DT_PRETREATMENT:CACHE:%s";
    String PRETREATMENT_CHOOSE_PREFIX = "DT_PRETREATMENT:CHOOSE:%s";
    String PRETREATMENT_CACHE = PRETREATMENT_CACHE_PREFIX + ":%s";
    String PRETREATMENT_CHOOSE = PRETREATMENT_CHOOSE_PREFIX + ":%s";

    //预处理常量
    String PRETREATMENT_LOCK_STOCK = "DT_PRETREATMENT:LOCK_STOCK:%s:%s";
    String PRETREATMENT_RELEASE_STOCK = "DT_PRETREATMENT:RELEASE_STOCK:%s:%s";
    String PRETREATMENT_EXPRESS_MACH= "DT_PRETREATMENT:EXPRESS_MACH:%s:%s";
    String PRETREATMENT_LOCK_STOCK_ZONE= "DT_PRETREATMENT:LOCK_STOCK_ZONE:%s:%s";
    String PRETREATMENT_RELEASE_STOCK_ZONE = "DT_PRETREATMENT:RELEASE_STOCK_ZONE:%s:%s";


}
