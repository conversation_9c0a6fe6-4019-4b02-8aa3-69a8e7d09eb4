package com.dt.platform.wms.client.message;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.platform.wms.dto.message.LargeMessageBizDTO;
import com.dt.platform.wms.param.message.LargeMessageBizParam;
import com.dt.platform.wms.param.message.MessageMqBizParam;

import java.util.List;


/**
 * <p>
 * 超大字段记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-13
 */
public interface ILargeMessageBizClient {

    /**
     * 新增超大字段记录
     *
     * @param largeMessageBizDTO
     * @return
     */
    Result<Boolean> save(LargeMessageBizDTO largeMessageBizDTO);

    /**
     * 批量新增超大字段记录
     *
     * @param largeMessageBizDTOList
     * @return
     */
    Result<Boolean> saveBatch(List<LargeMessageBizDTO> largeMessageBizDTOList);

    /**
     * 分页获取超大字段记录
     *
     * @param param
     * @return
     */
    Result<Page<LargeMessageBizDTO>> getPage(LargeMessageBizParam param);

    /**
     * 获取报文
     * @param param
     * @return
     */
    Result<String> getLargeMessage(MessageMqBizParam param);
}

