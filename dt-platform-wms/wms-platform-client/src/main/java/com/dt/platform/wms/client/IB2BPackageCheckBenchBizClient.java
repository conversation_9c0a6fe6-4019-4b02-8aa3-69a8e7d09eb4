package com.dt.platform.wms.client;

import com.dt.component.common.result.Result;
import com.dt.platform.wms.dto.check.b2b.*;
import com.dt.platform.wms.param.base.WorkBenchDetailBizParam;
import com.dt.platform.wms.param.check.b2b.*;


public interface IB2BPackageCheckBenchBizClient {
    /**
     * 工作台
     *
     * @param param
     * @return
     */
    Boolean checkWorkBench(WorkBenchDetailBizParam param);

    /**
     * 扫描条码
     *
     * @param param
     * @return
     */
    Result<B2BScanUpcCodeBackBizDTO> scanSkuUpcCode(B2BScanSkuParam param);

    /**
     * 扫描包材
     *
     * @param param
     * @return
     */
    Result<Boolean> checkPackMaterial(B2BScanMaterialParam param);

    /**
     * 提交包裹
     *
     * @param param
     * @return
     */
    Result<B2BPackBackResultBizDTO> submitPackage(B2BSubmitPackageParam param);

    /**
     * 扫描出库单号 或 包裹号 运单号
     *
     * @param param
     * @return
     */
    Result<B2BScanBillNoBackBizDTO> scanExpressNoOrPackageCode(B2BScanBillNoParam param);

    /**
     * 提交商品
     *
     * @param param
     * @return
     */
    Result<B2BSubmitSkuBackBizDTO> submitSkuAndCheckSku(B2BSubmitSkuParam param);

    /**
     * 反扫移除商品
     *
     * @param param
     * @return
     */
    Result<Boolean> removePackSkuQty(RemovePackSkuQtyParam param);

    /**
     * @param param
     * @return com.dt.component.common.result.Result<java.lang.Object>
     * <AUTHOR>
     * @describe: B2B复核工作台扫描单据号新
     * @date 2024/10/28 13:28
     */
    Result<B2BScanBillNoBackBizDTO> scanBillNoNew(B2BScanBillNoParam param);

    /**
     * @param param
     * @return com.dt.component.common.result.Result<com.dt.platform.wms.dto.check.b2b.B2BScanUpcCodeBackBizDTO>
     * <AUTHOR>
     * @describe:
     * @date 2024/10/29 13:29
     */
    Result<B2BScanUpcCodeBackBizDTO> scanUpcCodeNew(B2BScanSkuParam param);

    /**
     * @param b2BSubmitParam
     * @return com.dt.component.common.result.Result<com.dt.platform.wms.dto.check.b2b.B2BSubmitSkuBackBizDTO>
     * <AUTHOR>
     * @describe:
     * @date 2024/10/29 13:44
     */
    Result<B2BSubmitSkuBackBizNewDTO> submitSkuAndCheckSkuNew(B2BSubmitParamNew b2BSubmitParam);

    /**
     * @param param
     * @return com.dt.component.common.result.Result<com.dt.platform.wms.dto.check.b2b.B2BPackBackResultBizDTO>
     * <AUTHOR>
     * @describe:
     * @date 2024/10/29 16:56
     */
    Result<B2BPackBackResultBizDTO> submitPackageNew(B2BSubmitPackageParamNew param);

    /**
     * @param param
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     * @describe:
     * @date 2024/10/29 16:57
     */
    Result<Boolean> checkPackMaterialNew(B2BScanMaterialParamNew param);
}
