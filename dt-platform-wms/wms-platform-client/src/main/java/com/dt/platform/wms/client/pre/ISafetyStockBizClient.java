package com.dt.platform.wms.client.pre;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.platform.wms.dto.log.BillLogBizDTO;
import com.dt.platform.wms.dto.pre.SafetyStockBizDTO;
import com.dt.platform.wms.param.pre.SafetyStockBizParam;

import java.util.List;


/**
 * <p>
 * 商品安全库存 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-26
 */
public interface ISafetyStockBizClient {

    /**
     * 新增商品安全库存
     *
     * @param safetyStockBizDTO
     * @return
     */
    Result<Boolean> save(SafetyStockBizDTO safetyStockBizDTO);

    /**
     * 批量新增商品安全库存
     *
     * @param safetyStockBizDTOList
     * @return
     */
    Result<Boolean> saveBatch(List<SafetyStockBizDTO> safetyStockBizDTOList);

    /**
     * 修改商品安全库存
     *
     * ID | Code 二选一
     * @param safetyStockBizDTO
     * @return
     */
    Result<Boolean> modify(SafetyStockBizDTO safetyStockBizDTO);

    /**
     * 批量修改商品安全库存
     *
     * @param safetyStockBizDTOList
     * @return
     */
    Result<Boolean> modifyBatch(List<SafetyStockBizDTO> safetyStockBizDTOList);

    /**
     * 根据条件查询是否存在
     *
     * @param param
     * @return
     */
    Result<Boolean> checkExits(SafetyStockBizParam param);

    /**
     * 获取商品安全库存
     *
     * @param param
     * @return
     */
    Result<SafetyStockBizDTO> get(SafetyStockBizParam param);

    /**
     * 获取商品安全库存列表
     * @param param
     * @return
     */
    Result<List<SafetyStockBizDTO>> getList(SafetyStockBizParam param);

    /**
     * 分页获取商品安全库存
     *
     * @param param
     * @return
     */
    Result<Page<SafetyStockBizDTO>> getPage(SafetyStockBizParam param);

    /**
     * 功能描述:  删除商品安全库存
     * 创建时间:  2021/1/8 11:22 上午
     *
     * @param param:
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     */
    Result<Boolean> remove(SafetyStockBizParam param);

    Result<List<BillLogBizDTO>> getLog(SafetyStockBizParam param);
}

