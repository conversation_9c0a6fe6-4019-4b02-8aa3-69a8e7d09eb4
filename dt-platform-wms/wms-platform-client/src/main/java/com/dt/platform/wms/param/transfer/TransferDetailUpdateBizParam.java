package com.dt.platform.wms.param.transfer;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 增加转移单
 * Created by nobody on 2020/12/28 17:29
 */
@Data
@ApiModel(description = "库存转移单新增参数")
public class TransferDetailUpdateBizParam implements Serializable {
    private Long id;

    @ApiModelProperty(value = "转移单号")
    private String transferCode;

    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    @ApiModelProperty(value = "来源商品编码")
    private String originSkuCode;
    private String skuCode;

    @ApiModelProperty(value = "来源批次ID")
    private String originSkuLotNo;
    private String skuLotNo;

    @ApiModelProperty(value = "来源库位 取值：库区档案，不包含系统默认共用库区中的库位")
    private String originLoactionCode;
    private String locationCode;

    @ApiModelProperty(value = "实物数量")
    private BigDecimal originQty;
    private BigDecimal availableQty;

    @ApiModelProperty(value = "目标商品编码")
    @NotBlank
    private String targetSkuCode;

    @ApiModelProperty(value = "目标库位")
    @NotBlank
    private String targetLocationCode;

    @ApiModelProperty(value = "目标批次ID")
    private String targetSkuLotNo;

    @ApiModelProperty(value = "转移数量")
    private BigDecimal changeQty;

    @ApiModelProperty(value = "来源入库日期")
    private Long originReceiveDate;
    private Long receiveDate;

    @ApiModelProperty(value = "来源生产日期")
    private Long originManufDate;
    private Long manufDate;

    @ApiModelProperty(value = "来源过期日期")
    private Long originExpireDate;
    private Long expireDate;

    @ApiModelProperty(value = "来源商品属性")
    private String originSkuQuality;
    private String skuQuality;

    @ApiModelProperty(value = "来源生产批次号")
    private String originProductionNo;
    private String productionNo;

    @ApiModelProperty(value = "来源禁售日期")
    private Long originWithdrawDate;
    private Long withdrawDate;

    @ApiModelProperty(value = "目标入库日期")
    private Long targetReceiveDate;

    @ApiModelProperty(value = "目标生产日期")
    private Long targetManufDate;

    @ApiModelProperty(value = "目标过期日期")
    private Long targetExpireDate;

    @ApiModelProperty(value = "目标商品属性")
    private String targetSkuQuality;

    @ApiModelProperty(value = "目标生产批次号")
    private String targetProductionNo;

    @ApiModelProperty(value = "目标禁售日期")
    private Long targetWithdrawDate;

    @ApiModelProperty(value = "时间标志")
    private String dateFlag;

    @ApiModelProperty(value = "目标生产或失效时间")
    private Long targetManufOrExpireDate;

    @ApiModelProperty(value = "入库关联号")
    private String targetExternalLinkBillNo;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "调整原因")
    private String reason;

    @ApiModelProperty(value = "责任方")
    private String rp;

    @ApiModelProperty(value = "证明材料")
    private String evidenceInfo;

    @ApiModelProperty(value = "残次等级")
    private String originInventoryType;
    private String targetInventoryType;
    private String originInventoryTypeDesc;
    private String targetInventoryTypeDesc;
}
