package com.dt.platform.wms.param.excel;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.Map;

@Data
public class ExcelExportParam implements Serializable {

    @ApiModelProperty(value = "模板代码")
    @NotEmpty(message ="模板代码不能为空")
    private String funcCode;

    @ApiModelProperty(value = "模板请求参数")
    private Map<String,Object> param;
}
