package com.dt.platform.wms.param.move;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 根据Code获取数据
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-11
 */
@Data
@Accessors(chain = true)
@ApiModel(value="Code 对象", description="根据Code获取数据")
public class ScanTargetLocationParam implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "来源库位")
    @NotEmpty(message = "来源库位不能为空")
    private String originLocationCode;

    @ApiModelProperty(value = "目标库位")
    @NotEmpty(message = "目标库位不能为空")
    private String targetLocationCode;

    @ApiModelProperty(value = "商品编码")
    @NotEmpty(message = "商品编码不能为空")
    private String skuCode;

    @ApiModelProperty(value = "商品条码")
    @NotEmpty(message = "商品条码不能为空")
    private String skuUpcCode;

    @ApiModelProperty(value = "商品批次")
    @NotEmpty(message = "商品批次不能为空")
    private String skuLotNo;

    @ApiModelProperty(value = "商品属性")
    @NotEmpty(message = "商品属性不能为空")
    private String skuQuality;

    @ApiModelProperty(value = "移位数量")
    @NotNull(message = "移位数量不能为空")
    private BigDecimal moveSkuQty;

    @ApiModelProperty("移位单")
    private String moveCode;
}
