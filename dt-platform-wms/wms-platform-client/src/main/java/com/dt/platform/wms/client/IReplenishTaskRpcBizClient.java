package com.dt.platform.wms.client;

import com.dt.domain.bill.dto.ReplenishTaskDTO;
import com.dt.domain.bill.param.ReplenishTaskParam;
import com.dt.platform.wms.dto.replenish.ReplenishRecommendB2BNewDTO;
import com.dt.platform.wms.dto.replenish.ReplenishRecommendDTO;
import com.dt.platform.wms.dto.replenish.ReplenishTaskNewDTO;

import java.util.List;

/**
 * ReplenishTaskBiz 的服务化
 * <AUTHOR>
 * @date 2022/09/19 10:47
 */
public interface IReplenishTaskRpcBizClient {
//    /**
//     * @param replenishTaskDTOList
//     * @return java.util.List<com.dt.platform.wms.dto.ReplenishTaskNewDTO>
//     * @author: WuXian
//     * description: 获取当前补货指引商品需要的数量
//     * create time: 2021/7/5 17:12
//     */
//    List<ReplenishTaskNewDTO> getReplenishTaskNewList(List<ReplenishTaskDTO> replenishTaskDTOList);

    /**
     * @param replenishTaskNewList
     * @return java.util.List<com.dt.platform.wms.dto.replenish.ReplenishRecommendDTO>
     * @author: WuXian
     * description: 获取当前补货指引推荐库存
     * create time: 2021/7/6 13:43
     */
    List<ReplenishRecommendDTO> getReplenishRecommendList(List<ReplenishTaskNewDTO> replenishTaskNewList);
    /**
     * @param shipmentOrderCodeList
     * @return java.util.List<java.lang.Object>
     * <AUTHOR>
     * @describe: B2B单据获取补货明细
     * @date 2022/10/27 14:26
     */
    List<ReplenishRecommendB2BNewDTO> getReplenishTaskB2BNewList(List<String> shipmentOrderCodeList);
    /**
     * @param replenishTaskParam
     * @return java.util.List<com.dt.platform.wms.dto.replenish.ReplenishTaskNewDTO>
     * <AUTHOR>
     * @describe:
     * @date 2022/12/7 12:27
     */
    List<ReplenishTaskNewDTO> getReplenishTaskNewV2List(ReplenishTaskParam replenishTaskParam);

}
