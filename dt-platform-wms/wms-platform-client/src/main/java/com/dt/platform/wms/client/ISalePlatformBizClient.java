package com.dt.platform.wms.client;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.domain.base.dto.SalePlatformDTO;
import com.dt.platform.wms.dto.sale.SalePlatformBizDTO;
import com.dt.platform.wms.param.CodeParam;
import com.dt.platform.wms.param.sale.SalePlatformBizParam;
import com.dt.platform.wms.param.sale.SalePlatformQueryBizParam;

import java.util.List;
import java.util.Map;

public interface ISalePlatformBizClient {
    /**
     * 新增销售平台信息
     *
     * @param param
     * @return
     */
    Result<Boolean> save(SalePlatformBizParam param);
    /**
     * 修改销售平台信息
     * ID | Code 二选一
     *
     * @param param
     * @return
     */
    Result<Boolean> modify(SalePlatformBizParam param);
    /**
     * 获取销售平台信息
     *
     * @param param
     * @return
     */
    Result<SalePlatformBizDTO> get(CodeParam param);
    /**
     * 分页获取销售平台
     *
     * @param param
     * @return
     */
    Result<Page<SalePlatformBizDTO>> getPage(SalePlatformQueryBizParam param);
    Result<List<SalePlatformBizDTO>> getList(SalePlatformQueryBizParam param);

    Result<Map<String, SalePlatformDTO>> salePlatformMap(List<String> salePlatformList);
}
