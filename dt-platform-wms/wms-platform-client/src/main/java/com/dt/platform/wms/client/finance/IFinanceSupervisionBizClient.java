package com.dt.platform.wms.client.finance;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.platform.wms.dto.finance.FinanceSupervisionBizDTO;
import com.dt.platform.wms.param.finance.FinanceSupervisionBizParam;

import java.util.List;


/**
 * <p>
 * 金融监管单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-13
 */
public interface IFinanceSupervisionBizClient {

    /**
     * 新增金融监管单
     *
     * @param financeSupervisionBizDTO
     * @return
     */
    Result<Boolean> save(FinanceSupervisionBizDTO financeSupervisionBizDTO);

    /**
     * 批量新增金融监管单
     *
     * @param financeSupervisionBizDTOList
     * @return
     */
    Result<Boolean> saveBatch(List<FinanceSupervisionBizDTO> financeSupervisionBizDTOList);

    /**
     * 修改金融监管单
     *
     * ID | Code 二选一
     * @param financeSupervisionBizDTO
     * @return
     */
    Result<Boolean> modify(FinanceSupervisionBizDTO financeSupervisionBizDTO);

    /**
     * 批量修改金融监管单
     *
     * @param financeSupervisionBizDTOList
     * @return
     */
    Result<Boolean> modifyBatch(List<FinanceSupervisionBizDTO> financeSupervisionBizDTOList);

    /**
     * 根据条件查询是否存在
     *
     * @param param
     * @return
     */
    Result<Boolean> checkExits(FinanceSupervisionBizParam param);

    /**
     * 获取金融监管单
     *
     * @param param
     * @return
     */
    Result<FinanceSupervisionBizDTO> get(FinanceSupervisionBizParam param);

    /**
     * 获取金融监管单列表
     * @param param
     * @return
     */
    Result<List<FinanceSupervisionBizDTO>> getList(FinanceSupervisionBizParam param);

    /**
     * 分页获取金融监管单
     *
     * @param param
     * @return
     */
    Result<Page<FinanceSupervisionBizDTO>> getPage(FinanceSupervisionBizParam param);

    /**
     * 功能描述:  删除金融监管单
     * 创建时间:  2021/1/8 11:22 上午
     *
     * @param param:
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     */
    Result<Boolean> remove(FinanceSupervisionBizParam param);

    /**
     * 金融监管移位提交
     */
    Result<Boolean> submitMove(FinanceSupervisionBizParam param);

}

