package com.dt.platform.wms.param.intercept;

import com.dt.component.common.param.BaseSearchParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 快递拦截
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="ExpressIntercept对象", description="快递拦截")
public class ExpressInterceptBizParam extends BaseSearchParam  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    /**
     * 仓库编码
     */
    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    /**
     * 货主编码
     */
    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    /**
     * 出库单号
     */
    @ApiModelProperty(value = "出库单号")
    private String shipmentOrderCode;

    /**
     * 客户单号
     */
    @ApiModelProperty(value = "客户单号")
    private String poNo;
    

    /**
     * 上游单号
     */
    @ApiModelProperty(value = "上游单号")
    private String soNo;

    @ApiModelProperty(value = "店铺")
    private String saleShop;

    /**
     * 销售平台
     */
    @ApiModelProperty(value = "销售平台")
    private String salePlatform;

    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型")
    private String businessType;

    /**
     * 快递公司编码
     */
    @ApiModelProperty(value = "快递公司编码")
    private String carrierCode;

    /**
     * 快递单号
     */
    @ApiModelProperty(value = "快递单号")
    private String expressNo;

    /**
     * 汇单时间
     */
    @ApiModelProperty(value = "汇单时间")
    private Long collectTime;

    /**
     * 收货省份名称
     */
    @ApiModelProperty(value = "收货省份名称")
    private String receiverProvName;

    /**
     * 收货人市名称
     */
    @ApiModelProperty(value = "收货人市名称")
    private String receiverCityName;

    /**
     * 收货人区名称
     */
    @ApiModelProperty(value = "收货人区名称")
    private String receiverAreaName;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "修改人")
    private String updatedBy;

    private List<String> cargoCodeList;
    private List<String> shipmentOrderCodeList;
    private List<String> poNoList;
    private List<String> soNoList;
}