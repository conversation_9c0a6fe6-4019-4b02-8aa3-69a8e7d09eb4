package com.dt.platform.wms.client.trucking;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.platform.wms.dto.trucking.TruckingBizDTO;
import com.dt.platform.wms.dto.trucking.TruckingDetailBizDTO;
import com.dt.platform.wms.param.CodeParam;
import com.dt.platform.wms.param.trucking.TruckingBizParam;
import com.dt.platform.wms.param.trucking.TruckingScanHandoverParam;

import java.util.List;


/**
 * <p>
 * 装载主表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-05
 */
public interface ITruckingBizClient {

    /**
     * 获取装载主表
     *
     * @param param
     * @return
     */
    Result<TruckingBizDTO> get(TruckingBizParam param);

    /**
     * 获取装载主表列表
     *
     * @param param
     * @return
     */
    Result<List<TruckingBizDTO>> getList(TruckingBizParam param);

    /**
     * 分页获取装载主表
     *
     * @param param
     * @return
     */
    Result<Page<TruckingBizDTO>> getPage(TruckingBizParam param);

    /**
     * @param param
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     * @describe:
     * @date 2022/9/5 15:17
     */
    Result<Boolean> cancelTrucking(CodeParam param);

    /**
     * @param carNo
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     * @describe:
     * @date 2022/9/5 15:41
     */
    Result<String> checkCarNoAndCreateTruckingBill(String carNo);
    /**
     * @param param
     * @return com.dt.component.common.result.Result<com.dt.platform.wms.dto.trucking.TruckingDetailBizDTO>
     * <AUTHOR>
     * @describe:
     * @date 2022/9/5 15:57
     */
    Result<List<TruckingDetailBizDTO>> getTruckingDetailShow(CodeParam param);
    /**
     * @param param
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     * @describe:
     * @date 2022/9/5 16:06
     */
    Result<Boolean> scanHandoverCode(TruckingScanHandoverParam param);
    /**
     * @param param
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     * @describe:
     * @date 2022/9/5 17:02
     */
    Result<Boolean> cancelHandoverCode(TruckingScanHandoverParam param);
    /**
     * @param param
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     * @describe:
     * @date 2022/9/5 17:02
     */
    Result<Boolean> completeTrucking(CodeParam param);
    /**
     * @param param
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     * @describe:
     * @date 2022/9/13 9:09
     */
    Result<Boolean> cancelScanHandoverCode(TruckingScanHandoverParam param);
}

