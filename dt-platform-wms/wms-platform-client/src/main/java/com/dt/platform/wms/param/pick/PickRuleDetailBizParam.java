package com.dt.platform.wms.param.pick;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
@Data
public class PickRuleDetailBizParam  implements java.io.Serializable{

    @ApiModelProperty(value = "ID记录")
    private Long id;

    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    private String pickRuleCode;

    private String name;

    @ApiModelProperty(value = "描述")
    private String msg;

    @ApiModelProperty(value = "状态码 字典组：ABLE_STATUS")
    private Integer status;

    @ApiModelProperty(value = "优先级，越大级别越高")
    private Integer priority;

    @ApiModelProperty(value = "拣选单类型")
    private String pickOrderType;

    @ApiModelProperty(value = "包裹结构")
    private String packageStruct;

    @ApiModelProperty(value = "拣货方式(纸质和RF)")
    private String workType;

    @ApiModelProperty(value = "拣选方式(边捡边分，先拣后分)")
    private String pickWorkType;

    @ApiModelProperty(value = "清单打印节点")
    private String goodsListType;

    @ApiModelProperty(value = "面单打印节点")
    private String expressBillType;

    @ApiModelProperty(value = "箱唛打印节点")
    private String packMarkType;

    @ApiModelProperty(value = "订单下限数量")
    private Long orderMinQty;

    @ApiModelProperty(value = "订单上限数量")
    private Long orderMaxQty;

    @ApiModelProperty(value = "总装箱节点")
    private String allBoxType;
    @ApiModelProperty(value = "打印组")
    private String printGroup;
}
