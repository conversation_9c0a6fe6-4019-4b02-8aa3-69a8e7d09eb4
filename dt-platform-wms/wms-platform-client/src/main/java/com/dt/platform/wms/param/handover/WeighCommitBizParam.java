package com.dt.platform.wms.param.handover;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class WeighCommitBizParam implements Serializable {


    @ApiModelProperty(value = "交接批次号")
    @NotEmpty(message = "交接批次号不能为空")
    private String handoverCode;

    @ApiModelProperty(value = "包裹编码")
    @NotEmpty(message = "包裹编码不能为空")
    private String packageCode;

    @ApiModelProperty(value = "运单编码")
    @NotEmpty(message = "运单编码不能为空")
    private String waybillNo;

    @ApiModelProperty(value = "实际重量")
    private BigDecimal realWeight;

    @ApiModelProperty(value = "手动或电子秤自动输入")
    private String weightAutomation;

    @ApiModelProperty(value = "批量复核增加拣选单号")
    private String pickCode;
}
