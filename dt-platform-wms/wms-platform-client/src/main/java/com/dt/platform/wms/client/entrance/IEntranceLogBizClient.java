package com.dt.platform.wms.client.entrance;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.platform.wms.dto.entrance.EntranceLogBizDTO;
import com.dt.platform.wms.param.entrance.EntranceLogBizParam;

import java.util.List;


/**
 * <p>
 * entrance log 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-16
 */
public interface IEntranceLogBizClient {

    /**
     * 新增entrance log
     *
     * @param entranceLogBizDTO
     * @return
     */
    Result<Boolean> save(EntranceLogBizDTO entranceLogBizDTO);

    /**
     * 批量新增entrance log
     *
     * @param entranceLogBizDTOList
     * @return
     */
    Result<Boolean> saveBatch(List<EntranceLogBizDTO> entranceLogBizDTOList);

    /**
     * 修改entrance log
     *
     * ID | Code 二选一
     * @param entranceLogBizDTO
     * @return
     */
    Result<Boolean> modify(EntranceLogBizDTO entranceLogBizDTO);

    /**
     * 批量修改entrance log
     *
     * @param entranceLogBizDTOList
     * @return
     */
    Result<Boolean> modifyBatch(List<EntranceLogBizDTO> entranceLogBizDTOList);

    /**
     * 根据条件查询是否存在
     *
     * @param param
     * @return
     */
    Result<Boolean> checkExits(EntranceLogBizParam param);

    /**
     * 获取entrance log
     *
     * @param param
     * @return
     */
    Result<EntranceLogBizDTO> get(EntranceLogBizParam param);

    /**
     * 获取entrance log列表
     * @param param
     * @return
     */
    Result<List<EntranceLogBizDTO>> getList(EntranceLogBizParam param);

    /**
     * 分页获取entrance log
     *
     * @param param
     * @return
     */
    Result<Page<EntranceLogBizDTO>> getPage(EntranceLogBizParam param);

    /**
     * 功能描述:  删除entrance log
     * 创建时间:  2021/1/8 11:22 上午
     *
     * @param param:
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     */
    Result<Boolean> remove(EntranceLogBizParam param);

}

