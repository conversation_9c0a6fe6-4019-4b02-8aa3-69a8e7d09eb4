package com.dt.platform.wms.client;

import com.dt.component.common.result.Result;
import com.dt.platform.wms.param.wave.*;

public interface IWaveNavigationClient {
//    /**
//     * @param
//     * @return com.dt.platform.wms.param.wave.WaveNavigationHeadResultDTO
//     * @author: <PERSON><PERSON>ian
//     * description:  波次导航head
//     * create time: 2021/12/17 9:34
//     */
//    Result<WaveNavigationHeadResultBizDTO> getWaveNavigationHead(WaveNavigationQueryBizParam param);
//    /**
//     * @param param
//     * @return com.dt.component.common.result.Result<com.dt.platform.wms.param.wave.WaveNavigationBodyResultDTO>
//     * @author: WuXian
//     * description:
//     * create time: 2021/12/17 9:30
//     */
//    Result<WaveNavigationBodyResultBizDTO> getWaveNavigationBody(WaveNavigationQueryBizParam param);
    /**
     * @param param
     * @return com.dt.component.common.result.Result<com.dt.platform.wms.param.wave.WaveNavigationBodyResultDTO>
     * @author: <PERSON><PERSON><PERSON>
     * description: 立即汇总
     * create time: 2021/12/17 9:30
     */
    Result<String> waveNavigationCollectCommit(WaveNavigationCollectCommitBizParam param);
    /**
     * @param param
     * @return com.dt.component.common.result.Result<com.dt.platform.wms.param.wave.WaveNavigationResultBizNewDTO>
     * <AUTHOR>
     * @describe:
     * @date 2023/3/16 9:41
     */
    Result<WaveNavigationResultBizNewDTO> QueryWaveNavigationNew(WaveNavigationQueryBizParam param);
}
