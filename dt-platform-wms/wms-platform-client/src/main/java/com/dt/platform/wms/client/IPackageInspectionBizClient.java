package com.dt.platform.wms.client;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.platform.wms.dto.inspection.PackageInspectionBizDTO;
import com.dt.platform.wms.dto.inspection.ScanPackageInspectionBizDTOResult;
import com.dt.platform.wms.param.inspection.PackageInspectionBizParam;

import java.math.BigDecimal;
import java.util.Map;

/**
 * Created by nobody on 2021/1/26 14:28
 */
public interface IPackageInspectionBizClient {

    /**
     * 扫描包裹或者运单号
     * @param packageCodeOrExpressNo
     * @return
     */
    Result<ScanPackageInspectionBizDTOResult> scanPackageCodeOrExpressNo(String packageCodeOrExpressNo);

    /**
     * 扫描商品
     * @param param
     * @return
     */
    Result<Object> scanUpcCode(PackageInspectionBizParam param);

    /**
     * 分页查询
     * @param searchPackageParam
     * @return
     */
    Result<Page<PackageInspectionBizDTO>> queryPage(PackageInspectionBizParam searchPackageParam);

    /**
     * 提交抽检
     * @param param
     * @return
     */
    Result<Map<String,Object>> submitInspection(PackageInspectionBizParam param);


}
