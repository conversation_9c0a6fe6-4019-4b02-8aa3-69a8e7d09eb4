package com.dt.platform.wms.client.cargoOwnerLog;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.platform.wms.dto.cargoOwnerLog.CargoOwnerLogBizDTO;
import com.dt.platform.wms.param.cargoOwnerLog.CargoOwnerLogBizParam;

import java.util.List;


/**
 * <p>
 * 货主档案日志 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-09
 */
public interface ICargoOwnerLogBizClient {

    /**
     * 新增货主档案日志
     *
     * @param cargoOwnerLogBizDTO
     * @return
     */
    Result<Boolean> save(CargoOwnerLogBizDTO cargoOwnerLogBizDTO);

    /**
     * 批量新增货主档案日志
     *
     * @param cargoOwnerLogBizDTOList
     * @return
     */
    Result<Boolean> saveBatch(List<CargoOwnerLogBizDTO> cargoOwnerLogBizDTOList);

    /**
     * 修改货主档案日志
     *
     * ID | Code 二选一
     * @param cargoOwnerLogBizDTO
     * @return
     */
    Result<Boolean> modify(CargoOwnerLogBizDTO cargoOwnerLogBizDTO);

    /**
     * 批量修改货主档案日志
     *
     * @param cargoOwnerLogBizDTOList
     * @return
     */
    Result<Boolean> modifyBatch(List<CargoOwnerLogBizDTO> cargoOwnerLogBizDTOList);

    /**
     * 根据条件查询是否存在
     *
     * @param param
     * @return
     */
    Result<Boolean> checkExits(CargoOwnerLogBizParam param);

    /**
     * 获取货主档案日志
     *
     * @param param
     * @return
     */
    Result<CargoOwnerLogBizDTO> get(CargoOwnerLogBizParam param);

    /**
     * 获取货主档案日志列表
     * @param param
     * @return
     */
    Result<List<CargoOwnerLogBizDTO>> getList(CargoOwnerLogBizParam param);

    /**
     * 分页获取货主档案日志
     *
     * @param param
     * @return
     */
    Result<Page<CargoOwnerLogBizDTO>> getPage(CargoOwnerLogBizParam param);

    /**
     * 功能描述:  删除货主档案日志
     * 创建时间:  2021/1/8 11:22 上午
     *
     * @param param:
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     */
    Result<Boolean> remove(CargoOwnerLogBizParam param);

}

