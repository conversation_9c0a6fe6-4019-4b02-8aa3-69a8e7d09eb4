package com.dt.platform.wms.client.receipt;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.platform.wms.dto.receipt.ReceiptBillRecordBizDTO;
import com.dt.platform.wms.param.receipt.ReceiptBillRecordBizParam;

import java.util.List;


/**
 * <p>
 * 收货凭据表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-18
 */
public interface IReceiptBillRecordBizClient {

    /**
     * 新增收货凭据表
     *
     * @param receiptBillRecordBizDTO
     * @return
     */
    Result<Boolean> save(ReceiptBillRecordBizDTO receiptBillRecordBizDTO);

    /**
     * 批量新增收货凭据表
     *
     * @param receiptBillRecordBizDTOList
     * @return
     */
    Result<Boolean> saveBatch(List<ReceiptBillRecordBizDTO> receiptBillRecordBizDTOList);

    /**
     * 修改收货凭据表
     *
     * ID | Code 二选一
     * @param receiptBillRecordBizDTO
     * @return
     */
    Result<Boolean> modify(ReceiptBillRecordBizDTO receiptBillRecordBizDTO);

    /**
     * 批量修改收货凭据表
     *
     * @param receiptBillRecordBizDTOList
     * @return
     */
    Result<Boolean> modifyBatch(List<ReceiptBillRecordBizDTO> receiptBillRecordBizDTOList);

    /**
     * 根据条件查询是否存在
     *
     * @param param
     * @return
     */
    Result<Boolean> checkExits(ReceiptBillRecordBizParam param);

    /**
     * 获取收货凭据表
     *
     * @param param
     * @return
     */
    Result<ReceiptBillRecordBizDTO> get(ReceiptBillRecordBizParam param);

    /**
     * 获取收货凭据表列表
     * @param param
     * @return
     */
    Result<List<ReceiptBillRecordBizDTO>> getList(ReceiptBillRecordBizParam param);

    /**
     * 分页获取收货凭据表
     *
     * @param param
     * @return
     */
    Result<Page<ReceiptBillRecordBizDTO>> getPage(ReceiptBillRecordBizParam param);

    /**
     * 功能描述:  删除收货凭据表
     * 创建时间:  2021/1/8 11:22 上午
     *
     * @param param:
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     */
    Result<Boolean> remove(ReceiptBillRecordBizParam param);

}

