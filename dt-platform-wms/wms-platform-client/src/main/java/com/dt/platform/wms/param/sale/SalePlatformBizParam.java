package com.dt.platform.wms.param.sale;
import com.dt.component.common.param.BaseSearchParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="SalePlatform对象", description="销售平台")
public class SalePlatformBizParam extends BaseSearchParam
{
    @ApiModelProperty(value = "名称")
    @NotEmpty(message = "名称不能为空")
    private String name;
    @ApiModelProperty(value = "编码")
    @NotEmpty(message = "编码不能为空")
    private String code;
    @ApiModelProperty(value = "状态")
    @NotEmpty(message = "状态不能为空")
    private String status;

    @ApiModelProperty(value = "备注")
    private String remark;
}