package com.dt.platform.wms.param.adjust;

import com.dt.component.common.param.BaseSearchParam;
import com.dt.platform.wms.dto.adjust.AdjustDetailBizDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * 库存调整
 * </p>
 *
 * <AUTHOR> xy
 * @since 2020-09-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "Adjust对象", description = "库存调整")
public class AdjustBizParam extends BaseSearchParam  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "调整单编号")
    private String code;

    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    @ApiModelProperty(value = "单据状态 字典组：ADJUST_STATUS")
    private String status;

    @ApiModelProperty(value = "调整原因 字典组:REASON_TYPE")
    private String reason;

    @ApiModelProperty(value = "调整类型， 调增，调减，")
    private String type;

    @ApiModelProperty(value = "调整描叙")
    private String note;
    private String noteLike;

    @ApiModelProperty(value = "审核日期 时间戳")
    private Long checkerDate;

    @ApiModelProperty(value = "审核人")
    private String checker;

    @ApiModelProperty(value = "完成日期 时间戳")
    private Long completeDate;

    @ApiModelProperty(value = "操作人")
    private String opBy;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "更新人")
    private String updatedBy;

    @ApiModelProperty(value = "业务场景")
    private String businessType;
    private List<String> businessTypeList;

    @ApiModelProperty(value = "来源单据")
    private String billNo;

    private String skuCode;
    private String skuUpcCode;
    private String skuLotNo;
    private String zoneCode;
    private String locationCode;
    private Long completeDateStart;
    private Long completeDateEnd;
    private List<String> codeList;
    private List<String> cargoCodeList;
    private List<String> skuCodeList;
    private List<String> skuUpcCodeList;
    private List<String> zoneCodeList;
    private List<String> locationCodeList;
    private List<String> adjustCodeList;
    private List<String> statusList;

    private List<AdjustDetailBizDTO> detailList;
    @ApiModelProperty(value = "标记")
    private Integer tag;
    private List<Integer> tagList;

}