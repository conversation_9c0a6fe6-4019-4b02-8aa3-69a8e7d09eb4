package com.dt.platform.wms.param.tally;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class TallyBizCommitCheckDetailParam implements Serializable {

    @ApiModelProperty(value = "异常类型")
    private String abnormalType;

    @ApiModelProperty(value = "图片地址")
    private String imageUrlJson;

    @ApiModelProperty(value = "效期码")
    private String validityCode;

    @ApiModelProperty(value = "数量")
    private BigDecimal qty;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "残次等级")
    private String inventoryType;

//    --------------------------------

    @ApiModelProperty(value = "生产日期")
    private Long manufDate;

    @ApiModelProperty(value = "失效日期")
    private Long expireDate;

    @ApiModelProperty(value = "商品属性")
    private String skuQuality;

    @ApiModelProperty(value = "默认正常(NORMAL),多件(OVERSHIP)，多品(OVERSHIP_SKU)")
    private String extraGoods;

    @ApiModelProperty(value = "是否拓传")
    private String callBackUpper;

    @ApiModelProperty(value = "托盘码")
    private String palletCode;

}
