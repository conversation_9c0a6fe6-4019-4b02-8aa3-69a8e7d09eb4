package com.dt.platform.wms.param.pick;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2020/10/23 17:10
 */
@Data
public class PickBillTaskParam implements Serializable {

    /**
     * 拣选单号
     */
    @NotEmpty(message = "拣选单号不能为空")
    private String pickCode;
    /**
     * 拣选人
     */
    @NotEmpty(message = "拣选人不能为空")
    private String pickBy;


}
