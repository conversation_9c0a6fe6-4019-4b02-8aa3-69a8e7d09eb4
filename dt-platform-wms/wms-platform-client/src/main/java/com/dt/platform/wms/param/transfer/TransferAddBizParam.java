package com.dt.platform.wms.param.transfer;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * 增加转移单
 * Created by nobody on 2020/12/28 17:29
 */
@Data
@ApiModel(description="库存转移单新增参数")
public class TransferAddBizParam implements Serializable {

    @ApiModelProperty("仓库编码")
    private String warehouseCode;

    @ApiModelProperty("货主编码")
    @NotBlank
    private String cargoCode;

    @ApiModelProperty("转移原因")
    @NotBlank
    private String reason;

    @ApiModelProperty("转移描述")
    @NotBlank
    private String note;

    @ApiModelProperty(value = "业务场景")
    private String businessType;

    @ApiModelProperty(value = "订单标记")
    private Integer orderTag;
}
