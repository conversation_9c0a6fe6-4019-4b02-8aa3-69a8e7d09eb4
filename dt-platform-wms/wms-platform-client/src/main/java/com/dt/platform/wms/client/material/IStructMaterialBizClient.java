package com.dt.platform.wms.client.material;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.domain.bill.dto.PackageDTO;
import com.dt.platform.wms.dto.material.StructMaterialBizDTO;
import com.dt.platform.wms.param.material.StructMaterialBizParam;
import com.dt.platform.wms.param.material.StructMaterialRecordBizParam;


/**
 * <p>
 * 包裹结构绑定包材 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-22
 */
public interface IStructMaterialBizClient {

    /**
     * 详情
     * @param param
     * @return
     */
    Result<StructMaterialBizDTO> detail(StructMaterialBizParam param);

    /**
     * 新增包裹结构绑定包材
     *
     * @param structMaterialBizDTO
     * @return
     */
    Result<Boolean> save(StructMaterialBizDTO structMaterialBizDTO);

    /**
     * 分页获取包裹结构绑定包材
     *
     * @param param
     * @return
     */
    Result<Page<StructMaterialBizDTO>> getPage(StructMaterialBizParam param);

}

