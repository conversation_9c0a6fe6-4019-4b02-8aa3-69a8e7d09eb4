package com.dt.platform.wms.client.upc;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.platform.wms.dto.upc.UpcStockSerialBizDTO;
import com.dt.platform.wms.param.upc.UpcStockSerialBizParam;

import java.util.List;


/**
 * <p>
 * 包耗材库存流水 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-09
 */
public interface IUpcStockSerialBizClient {

    /**
     * 新增包耗材库存流水
     *
     * @param upcStockSerialBizDTO
     * @return
     */
    Result<Boolean> save(UpcStockSerialBizDTO upcStockSerialBizDTO);

    /**
     * 批量新增包耗材库存流水
     *
     * @param upcStockSerialBizDTOList
     * @return
     */
    Result<Boolean> saveBatch(List<UpcStockSerialBizDTO> upcStockSerialBizDTOList);

    /**
     * 修改包耗材库存流水
     *
     * ID | Code 二选一
     * @param upcStockSerialBizDTO
     * @return
     */
    Result<Boolean> modify(UpcStockSerialBizDTO upcStockSerialBizDTO);

    /**
     * 批量修改包耗材库存流水
     *
     * @param upcStockSerialBizDTOList
     * @return
     */
    Result<Boolean> modifyBatch(List<UpcStockSerialBizDTO> upcStockSerialBizDTOList);

    /**
     * 根据条件查询是否存在
     *
     * @param param
     * @return
     */
    Result<Boolean> checkExits(UpcStockSerialBizParam param);

    /**
     * 获取包耗材库存流水
     *
     * @param param
     * @return
     */
    Result<UpcStockSerialBizDTO> get(UpcStockSerialBizParam param);

    /**
     * 获取包耗材库存流水列表
     * @param param
     * @return
     */
    Result<List<UpcStockSerialBizDTO>> getList(UpcStockSerialBizParam param);

    /**
     * 分页获取包耗材库存流水
     *
     * @param param
     * @return
     */
    Result<Page<UpcStockSerialBizDTO>> getPage(UpcStockSerialBizParam param);

    /**
     * 功能描述:  删除包耗材库存流水
     * 创建时间:  2021/1/8 11:22 上午
     *
     * @param param:
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     */
    Result<Boolean> remove(UpcStockSerialBizParam param);

}

