package com.dt.platform.wms.param;

import com.dt.component.common.param.BaseSearchParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/10/20 10:47
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class AllocationOrderBizParam extends BaseSearchParam  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    @ApiModelProperty(value = "分配单编码")
    private String allocationOrderCode;
    private List<String> allocationOrderCodeList;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;
    private  List<String> cargoCodeList;

    @ApiModelProperty(value = "波次号")
    private String waveCode;
    private  List<String> waveCodeList;

    @ApiModelProperty(value = "包裹编码")
    private String packageCode;
    private  List<String> packageCodeList;

    @ApiModelProperty(value = "包裹明细")
    private Long pUid;
    private  List<Long> pUidList;

    @ApiModelProperty(value = "库区编码")
    private String zoneCode;
    private  List<String> zoneCodeList;

    @ApiModelProperty(value = "巷道编码")
    private String tunnelCode;
    private  List<String> tunnelCodeList;

    @ApiModelProperty(value = "库位编码")
    private String locationCode;
    private  List<String> locationCodeList;

    @ApiModelProperty(value = "取值分配的货品批次号")
    private String skuLotNo;
    private  List<String> skuLotNoList;

    @ApiModelProperty(value = "商品编码")
    private String skuCode;
    private List<String> skuCodeList;

    @ApiModelProperty(value = "计划数量")
    private BigDecimal expQty;

    @ApiModelProperty(value = "实发数量")
    private BigDecimal realQty;

    @ApiModelProperty(value = "拣选数量")
    private BigDecimal pickQty;

    @ApiModelProperty(value = "出库单号")
    private String shipmentOrderCode;

    @ApiModelProperty(value = "拣选单号")
    private String pickCode;

    private String status;
    private List<String> statusList;

}