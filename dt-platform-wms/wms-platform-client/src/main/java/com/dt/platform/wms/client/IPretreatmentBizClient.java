package com.dt.platform.wms.client;

import com.dt.component.common.result.Result;
import com.dt.platform.wms.dto.PretreatmentCountDTO;
import com.dt.platform.wms.param.pretreatment.PretreatmentBizParam;
import com.dt.platform.wms.param.pretreatment.PretreatmentExecParam;
import com.dt.platform.wms.param.pretreatment.PretreatmentRetryParam;

public interface IPretreatmentBizClient {

    Result<PretreatmentCountDTO> getPretreatmentCount(PretreatmentBizParam param);

    Result<Integer> doPretreatmentCount(PretreatmentExecParam param);

    Result<Boolean> retryPretreatment(PretreatmentRetryParam param);

    Result<Boolean> autoCompensation(PretreatmentBizParam param);

    /**
     * 预处理 -- 维护出库单预处理状态
     *
     * @param param
     * @return
     * @throws Exception
     */
    Result<Boolean> maintainShipmentOrderPretreatmentStatus(PretreatmentExecParam param);

}
