package com.dt.platform.wms.param.stock;

import com.dt.component.common.param.BaseSearchParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import java.math.BigDecimal;

/**
 * <p>
 * 单据库存
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="StockPledge对象", description="单据库存")
public class StockPledgeBizParam extends BaseSearchParam {

    private static final long serialVersionUID = 1L;

    /**
     * 货主编码
     */
    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    /**
     * 商品编码
     */
    @ApiModelProperty(value = "商品编码")
    private String skuCode;

    /**
     * 单据类型 BillTypeEnum
     */
    @ApiModelProperty(value = "单据类型 BillTypeEnum")
    private String billType;

    /**
     * 单据编码
     */
    @ApiModelProperty(value = "单据编码")
    private String billNo;

    /**
     * 库区编码
     */
    @ApiModelProperty(value = "库区编码")
    private String zoneCode;

    /**
     * 库区类型
     */
    @ApiModelProperty(value = "库区类型")
    private String zoneType;

    /**
     * 库位编码
     */
    @ApiModelProperty(value = "库位编码")
    private String locationCode;

    /**
     * 库位类型
     */
    @ApiModelProperty(value = "库位类型")
    private String locationType;

    /**
     * 批次ID
     */
    @ApiModelProperty(value = "批次ID")
    private String skuLotNo;


    @ApiModelProperty(value = "冻结数量")
    private BigDecimal frozenQty;

    @ApiModelProperty(value = "可用数量")
    private BigDecimal availableQty;
    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "修改人")
    private String updatedBy;

    @ApiModelProperty(value = "用户ID")
    private Long userId;

    @ApiModelProperty(value = "用户名称")
    private String userName;

    @ApiModelProperty(value = "用户ID（金融）")
    private Long financialUserId;

    @ApiModelProperty(value = "用户名称（金融）")
    private String financialUserName;
}
