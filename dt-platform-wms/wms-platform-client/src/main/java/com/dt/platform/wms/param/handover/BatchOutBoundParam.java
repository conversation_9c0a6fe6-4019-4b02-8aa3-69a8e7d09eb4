package com.dt.platform.wms.param.handover;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/1/31 15:57
 */
@Data
public class BatchOutBoundParam implements Serializable {

    @ApiModelProperty(value = "拣选单号")
    @NotEmpty(message = "拣选单号不能为空")
    private String pickCode;

    @ApiModelProperty(value = "批量出库包裹号")
    @NotEmpty(message = "包裹号不能为空")
    private String packageCode;

    @ApiModelProperty(value = "批量出库包裹号")
    @NotEmpty(message = "交接单号不能为空")
    private String handoverCode;

}
