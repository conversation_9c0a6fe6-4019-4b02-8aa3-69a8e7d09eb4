package com.dt.platform.wms.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * <p>
 * 根据Code获取数据
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-11
 */
@Data
@Accessors(chain = true)
@ApiModel(value="Code 对象", description="根据Code获取数据")
public class CodeParam implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "编码 不允许修改，唯一")
    @NotEmpty(message = "编码不能为空")
    private String code;

    /**
     * 是否通过 {@link com.dt.component.common.enums.AuditEnum}
     */
    private String pass;

    /**
     * 审核说明
     */
    private String remark;

    private String type;

    @ApiModelProperty(value = "质检台号")
    private String benchCode;
}
