package com.dt.platform.wms.client.upc;

import com.dt.component.common.result.Result;


/**
 * <p>
 * 每日包材使用记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-22
 */
public interface IMaterialUseBizClient {

    /**
     * 统计包材使用记录
     */
    Result<Boolean> generate(Long date);

    /**
     * 处理
     */
    Result<Boolean> process();

    /**
     * 报告，方便洞察程序处理状态
     * @param date
     */
    Result<String> report(Long date);
}

