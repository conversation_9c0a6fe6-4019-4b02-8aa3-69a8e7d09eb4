package com.dt.platform.wms.client;

import com.dt.domain.bill.dto.ShipmentOrderDTO;
import com.dt.domain.bill.dto.ShipmentOrderDetailDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/25 13:20
 */
public interface IShipmentOrderParamValidationClient {
    /**
     * 校验出库单库位 批次等参数
     * @param shipmentOrderDTO
     * @param detailDTOList
     * @return
     */
    Boolean checkShipmentLocationAndSkuLot(ShipmentOrderDTO shipmentOrderDTO, List<ShipmentOrderDetailDTO> detailDTOList);
}
