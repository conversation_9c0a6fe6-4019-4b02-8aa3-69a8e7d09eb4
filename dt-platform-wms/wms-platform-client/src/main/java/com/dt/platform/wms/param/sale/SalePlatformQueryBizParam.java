package com.dt.platform.wms.param.sale;

import com.dt.component.common.param.BaseSearchParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "SalePlatform对象", description = "销售平台")
public class SalePlatformQueryBizParam extends BaseSearchParam  implements java.io.Serializable  {
    @ApiModelProperty(value = "名称")
    private String name;
    @ApiModelProperty(value = "状态")
    private String status;
    @ApiModelProperty(value = "代码")
    private String code;
    @ApiModelProperty(value = "代码")
    private List<String> codeList;

}