package com.dt.platform.wms.client;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.component.common.vo.IdNameVO;
import com.dt.platform.wms.dto.base.LocationBizDTO;
import com.dt.platform.wms.param.base.LocationBizParam;

import java.util.List;

public interface ILocationBizClient {

    /**
     * 新增库位档案
     * @param param
     * @return
     */
    Result<Boolean> create(LocationBizParam param);

    /**
     * 修改库位档案
     * @param param
     * @return
     */
    Result<Boolean> modify(LocationBizParam param);

    /**
     * 启用 1 禁用 -1
     * @param param
     * @return
     */
    Result<Boolean> enable(LocationBizParam param);
    /**
     * 库位档案分页列表
     * @param param
     * @return
     */
    Result<Page<LocationBizDTO>> getPage(LocationBizParam param);

    /**
     * 获取详情
     * @param param
     * @return
     */
    Result<LocationBizDTO> getDetail(LocationBizParam param);

    Result<List<IdNameVO>> getAllLocationList();
}
