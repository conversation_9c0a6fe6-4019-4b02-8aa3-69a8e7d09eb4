package com.dt.platform.wms.client.cw;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.platform.wms.dto.cw.CwTransferDetailBizDTO;
import com.dt.platform.wms.param.cw.CwTransferDetailBizParam;

import java.util.List;


/**
 * <p>
 * CW调拨详情 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-16
 */
public interface ICwTransferDetailBizClient {

    /**
     * 新增CW调拨详情
     *
     * @param cwTransferDetailBizDTO
     * @return
     */
    Result<Boolean> save(CwTransferDetailBizDTO cwTransferDetailBizDTO);

    /**
     * 批量新增CW调拨详情
     *
     * @param cwTransferDetailBizDTOList
     * @return
     */
    Result<Boolean> saveBatch(List<CwTransferDetailBizDTO> cwTransferDetailBizDTOList);

    /**
     * 修改CW调拨详情
     *
     * ID | Code 二选一
     * @param cwTransferDetailBizDTO
     * @return
     */
    Result<Boolean> modify(CwTransferDetailBizDTO cwTransferDetailBizDTO);

    /**
     * 批量修改CW调拨详情
     *
     * @param cwTransferDetailBizDTOList
     * @return
     */
    Result<Boolean> modifyBatch(List<CwTransferDetailBizDTO> cwTransferDetailBizDTOList);

    /**
     * 根据条件查询是否存在
     *
     * @param param
     * @return
     */
    Result<Boolean> checkExits(CwTransferDetailBizParam param);

    /**
     * 获取CW调拨详情
     *
     * @param param
     * @return
     */
    Result<CwTransferDetailBizDTO> get(CwTransferDetailBizParam param);

    /**
     * 获取CW调拨详情列表
     * @param param
     * @return
     */
    Result<List<CwTransferDetailBizDTO>> getList(CwTransferDetailBizParam param);

    /**
     * 分页获取CW调拨详情
     *
     * @param param
     * @return
     */
    Result<Page<CwTransferDetailBizDTO>> getPage(CwTransferDetailBizParam param);

    /**
     * 功能描述:  删除CW调拨详情
     * 创建时间:  2021/1/8 11:22 上午
     *
     * @param param:
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     */
    Result<Boolean> remove(CwTransferDetailBizParam param);

}

