package com.dt.platform.wms.param.location;

import com.dt.component.common.param.BaseSearchParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;


/**
 * <p>
 * 库位日志
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="LocationLog对象", description="库位日志")
public class LocationLogBizParam extends BaseSearchParam  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    /**
     * 仓库编码 不能为空，取值仓库档案
     */
    @ApiModelProperty(value = "仓库编码 不能为空，取值仓库档案")
    private String warehouseCode;

    /**
     * 编码 不允许修改,唯一
     */
    @ApiModelProperty(value = "编码 不允许修改,唯一")
    private String locationCode;

    /**
     * 操作说明
     */
    @ApiModelProperty(value = "操作说明")
    private String opContent;

    /**
     * 
     */
    @ApiModelProperty(value = "")
    private String opRemark;

    /**
     * 操作人
     */
    @ApiModelProperty(value = "操作人")
    private String opBy;

    /**
     * 
     */
    @ApiModelProperty(value = "")
    private Long opDate;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "修改人")
    private String updatedBy;
}