package com.dt.platform.wms.param.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * <p>
 * 巷道/通道管理
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-11
 */
@Data
@Accessors(chain = true)
@ApiModel(value="Tunnel对象", description="巷道/通道管理")
public class TunnelBizModifyParam implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "巷道/通道编码 不允许修改，唯一")
    @NotEmpty(message = "巷道编码不能为空")
    private String code;

}
