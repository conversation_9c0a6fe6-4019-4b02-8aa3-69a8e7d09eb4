package com.dt.platform.wms;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2021/1/28 10:31
 */
@Data
public class ReplenishSkuLotAndStockDTO implements Serializable {

    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    @ApiModelProperty(value = "商品编码")
    private String skuCode;
    @ApiModelProperty(value = "商品条码")
    private String upcCode;
    @ApiModelProperty(value = "商品描述")
    private String skuName;

    @ApiModelProperty(value = "商品属性")
    private String skuQuality;

    @ApiModelProperty(value = "库区编码")
    private String zoneCode;

    @ApiModelProperty(value = "库位编码")
    private String locationCode;

    @ApiModelProperty(value = "库位类型")
    private String locationType;

    @ApiModelProperty(value = "批次ID")
    private String skuLotNo;

    @ApiModelProperty(value = "实物数量")
    private BigDecimal physicalQty;

    @ApiModelProperty(value = "冻结数量")
    private BigDecimal frozenQty;

    @ApiModelProperty(value = "占用数量")
    private BigDecimal occupyQty;

    @ApiModelProperty(value = "待上架数量")
    private BigDecimal waitShelfQty;

    @ApiModelProperty(value = "可用数量 实物库存-冻结-占用-待上架数量=可用数")
    private BigDecimal availableQty;

    @ApiModelProperty(value = "入库日期")
    private Long receiveDate;

    @ApiModelProperty(value = "生产日期")
    private Long manufDate;

    @ApiModelProperty(value = "失效/过期日期")
    private Long expireDate;

    @ApiModelProperty(value = "外部批次ID")
    private String externalSkuLotNo;
}
