package com.dt.platform.wms.client;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.dt.component.common.result.Result;
import com.dt.component.common.vo.IdNameVO;
import com.dt.domain.base.dto.CargoOwnerDTO;
import com.dt.domain.base.param.CargoOwnerParam;
import com.dt.platform.wms.dto.cargo.CargoOwnerBizDTO;
import com.dt.platform.wms.dto.cargo.CargoOwnerExtraJsonBizDTO;
import com.dt.platform.wms.dto.cargo.CargoOwnerExtraJsonDataBizDTO;
import com.dt.platform.wms.dto.cargo.CargoQueryShow;
import com.dt.platform.wms.param.CodeParam;
import com.dt.platform.wms.param.cargo.CargoOwnerBizParam;
import com.dt.platform.wms.param.cargo.CargoOwnerExtraJsonParam;
import com.dt.platform.wms.param.cargo.CargoOwnerQueryBizParam;

/**
 * <AUTHOR>
 * @date 2020/9/15 15:31
 */
public interface ICargoOwnerBizClient {
    /**
     * 获取货主状态码-全部
     *
     * @return
     */
    Result<List<IdNameVO>> queryStatusAll();

    /**
     * 获取货主状态码
     *
     * @return
     */
    Result<List<IdNameVO>> queryStatus();

    /**
     * 分页查询
     *
     * @param param
     * @return
     */
    Result<IPage<CargoOwnerBizDTO>> queryPage(CargoOwnerBizParam param);

    /**
     * 获取货主列表
     *
     * @return
     */
    Result<List<IdNameVO>> getAllCargoOwner();

    /**
     * 启用-禁用
     *
     * @param param
     * @return
     */
    Result<Boolean> enable(CargoOwnerBizParam param);

    /**
     * 查询货主
     *
     * @param code
     * @return
     */
    Result<CargoOwnerBizDTO> queryByCode(String code);

    /**
     * 功能描述: 创建时间: 2021/3/16 4:29 下午
     *
     * @param cargoOwnerBizParam:
     * @return com.dt.component.common.result.Result<java.util.List < com.dt.platform.wms.dto.cargo.CargoOwnerBizDTO>>
     * <AUTHOR>
     */
    Result<List<CargoOwnerBizDTO>> queryList(CargoOwnerBizParam cargoOwnerBizParam);

    Result<List<CargoOwnerBizDTO>> queryListByUpc(CargoOwnerBizParam cargoOwnerBizParam);

    /**
     * 创建货主档案
     * 
     * @param cargoOwnerBizParam
     * @return
     */
    Result<Boolean> createCargoOwner(CargoOwnerBizParam cargoOwnerBizParam, String source);

    /**
     * 判断是否存在
     * 
     * @param cargoOwnerBizParam
     * @return
     */
    Result<Boolean> checkExist(CargoOwnerBizParam cargoOwnerBizParam);

    Result<Boolean> updateCargoOwner(CargoOwnerBizParam cargoOwnerBizParam, String source);

    Result<Map<String, CargoOwnerDTO>> cargoMap(List<String> cargoCodeList);
    /**
     * @param param
     * @return com.dt.component.common.result.Result<java.util.List<com.dt.component.common.vo.IdNameVO>>
     * <AUTHOR>
     * @describe:
     * @date 2023/1/9 9:19
     */
    Result<List<IdNameVO>> queryCargoOwnerByTag(CargoOwnerQueryBizParam param);
    /**
     * @param
     * @return com.dt.component.common.result.Result<java.util.List<com.dt.component.common.vo.IdNameVO>>
     * <AUTHOR>
     * @describe:
     * @date 2023/1/9 9:26
     */
    Result<List<IdNameVO>> queryCargoOwnerCargoTag();
    /**
     * @param
     * @return com.dt.component.common.result.Result<java.lang.String>
     * <AUTHOR>
     * @describe:
     * @date 2023/1/11 16:36
     */
    Result<CargoQueryShow> queryCargoOwnerCargoAllTag();
    /**
     * @param param
     * @return com.dt.component.common.result.Result<java.util.List<com.dt.component.common.vo.IdNameVO>>
     * <AUTHOR>
     * @describe:
     * @date 2023/1/12 14:12
     */
    Result<List<IdNameVO>> queryToOperationOccupy(CodeParam param);
    /**
     * @param param
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     * @describe:
     * @date 2023/1/12 14:21
     */
    Result<Boolean> modifyCargoTag(CargoOwnerParam param);
    /**
     * @param
     * @return com.dt.component.common.result.Result<java.util.List<java.lang.Object>>
     * <AUTHOR>
     * @describe:
     * @date 2023/8/16 10:20
     */
    Result<List<IdNameVO>> cwCustom();

    Result<List<CargoOwnerExtraJsonDataBizDTO>> queryCargoExtraJson(CargoOwnerBizParam cargoOwnerBizParam);

    Result<Boolean> modifyCargoExtra(CargoOwnerExtraJsonParam cargoOwnerExtraJsonFrom);
}
