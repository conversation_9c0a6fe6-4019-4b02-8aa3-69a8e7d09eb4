package com.dt.platform.wms.param.track;

import com.dt.component.common.param.BaseSearchParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR> xy
 * @since 2020-10-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "OrderTrackle对象", description = "拦截单")
public class OrderInterceptBizParam extends BaseSearchParam  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "出库单号")
    private List<String> shipmentOrderCodeList;

    @ApiModelProperty(value = "erp单号")
    private String soNo;
    private List<String> soNoList;

    @ApiModelProperty(value = "客户单号")
    private String poNo;
    private List<String> poNoList;


    @ApiModelProperty(value = "货主编码")
    private List<String> cargoCodeList;

    @ApiModelProperty(value = "状态")
    private String status;
}