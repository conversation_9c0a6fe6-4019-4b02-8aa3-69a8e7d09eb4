package com.dt.platform.wms.client;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.platform.wms.dto.pkg.PackageReturnBizDTO;
import com.dt.platform.wms.param.pkg.PackageReturnBizParam;

public interface IPackageReturnBizClient {
     Result<Page<PackageReturnBizDTO>> getPage(PackageReturnBizParam searchPackageParam);

     Result<PackageReturnBizDTO> getDetail(PackageReturnBizParam searchPackageParam);
}
