package com.dt.platform.wms.client;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.platform.wms.dto.ret.ReturnOrderBizDTO;
import com.dt.platform.wms.param.CodeParam;
import com.dt.platform.wms.param.ret.ReturnOrderBizParam;

public interface IReturnOrderBizClient {
    /**
     * 新增归位单信息
     * @param param
     * @return
     */
    Result<Boolean> save(ReturnOrderBizParam param);
    /**
     * 修改归位单信息
     * @param param
     * @return
     */
    Result<Boolean> modify(ReturnOrderBizParam param);


    /**
     * 获取异常单信息
     * @param param
     * @return
     */
    Result<ReturnOrderBizDTO> get(ReturnOrderBizParam param);



    Result<Page<ReturnOrderBizDTO>> queryPage(ReturnOrderBizParam param);

    /**
     * 完成归位单
     * @param param
     * @return
     */
    Result<Boolean> completeReturnOrder(CodeParam param);
}
