package com.dt.platform.wms.param.tally;

import com.dt.component.common.param.BaseSearchParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;


/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-02-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="Tally对象", description="")
public class TallyBizParam extends BaseSearchParam  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    /**
     * 仓库编码 取值仓库档案
     */
    @ApiModelProperty(value = "仓库编码 取值仓库档案")
    private String warehouseCode;

    /**
     * 货主编码 取值货主档案
     */
    @ApiModelProperty(value = "货主编码 取值货主档案")
    private String cargoCode;
    private List<String> cargoCodeList;


    /**
     * 理货编号
     */
    @ApiModelProperty(value = "理货编号")
    private String tallyCode;
    private List<String> tallyCodeList;

    @ApiModelProperty(value = "理货方式")
    private String opType;
    private List<String> opTypeList;

    /**
     * 理货单据
     */
    @ApiModelProperty(value = "理货单据")
    private String billNo;
    private List<String> billNoList;

    /**
     * 理货类型（参照ASN的type类型）
     */
    @ApiModelProperty(value = "理货类型（参照ASN的type类型）")
    private String type;
    private List<String> typeList;

    /**
     * 理货人员
     */
    @ApiModelProperty(value = "理货人员")
    private String tallyBy;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 理货时间
     */
    @ApiModelProperty(value = "理货时间")
    private Long opDate;
    private Long opDateStart;
    private Long opDateEnd;

    /**
     * 单据类型
     */
    @ApiModelProperty(value = "单据类型")
    private String status;
    private List<String> statusList;

    private List<TallyDetailBizParam> tallyDetailBizParams;

    @ApiModelProperty(value = "放置分区")
    private String physicalPartition;

    @ApiModelProperty(value = "理货审核时间")
    private Long authTime;

    @ApiModelProperty(value = "承租方企业")
    private String lesseeEnterprise;

    @ApiModelProperty(value = "外部单号")
    private String outOrderNo;

    @ApiModelProperty(value = "标记")
    private Integer mark;
    private List<Integer> markList;

    @ApiModelProperty(value = "是否修改动作")
    private Boolean isModifyAction;

}