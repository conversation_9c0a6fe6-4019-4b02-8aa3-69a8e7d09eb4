package com.dt.platform.wms.param.shipment;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/7/22 17:18
 */
@Data
public class ShipmentCancelParam implements Serializable {

    @ApiModelProperty(value = "编码列表")
    @NotEmpty(message = "单号不能为空")
    private List<String> codeList;

    @NotEmpty(message = "取消原因不能为空")
    private String reason;

}
