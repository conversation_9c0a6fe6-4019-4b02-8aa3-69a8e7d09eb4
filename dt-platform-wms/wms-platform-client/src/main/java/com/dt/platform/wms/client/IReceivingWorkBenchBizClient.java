package com.dt.platform.wms.client;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.dt.component.common.result.Result;
import com.dt.platform.wms.dto.asn.AsnRecBizDTO;
import com.dt.platform.wms.dto.check.WarehouseCrossBorderBizDTO;
import com.dt.platform.wms.dto.rec.*;
import com.dt.platform.wms.param.asn.AsnBizParam;
import com.dt.platform.wms.param.base.WorkBenchDetailBizParam;
import com.dt.platform.wms.param.container.ContainerOccupyParam;
import com.dt.platform.wms.param.rec.*;
import com.dt.platform.wms.param.rec.pda.QueryPrintContPDAParam;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/9/24 13:34
 */
public interface IReceivingWorkBenchBizClient {

    /**
     * @param param
     * @return com.dt.component.common.result.Result<com.dt.platform.wms.dto.check.WarehouseCrossBorderBizDTO>
     * @author: WuXian
     * description:  检查质检台
     * create time: 2022/3/3 10:27
     */
    Result<WarehouseCrossBorderBizDTO> checkWorkBench(WorkBenchDetailBizParam param);

    /**
     * @param param
     * @return com.dt.component.common.result.Result<com.dt.platform.wms.dto.rec.SkuRecContainerBizDTO>
     * @author: WuXian
     * description:  检查容器
     * create time: 2022/3/3 10:32
     */
    Result<SkuRecContainerBizDTO> checkContainerOccupy(ContainerOccupyParam param);

    /**
     * @param param
     * @return com.dt.component.common.result.Result<com.dt.platform.wms.dto.rec.SkuRecCheckBizDTO>
     * @author: WuXian
     * description:  检查商品条形码
     * create time: 2022/3/3 10:31
     */
    Result<SkuRecCheckBizDTO> checkUpcCode(CheckSkuParam param);

    /**
     * @param form
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * @author: WuXian
     * description:  检查和提交容器
     * create time: 2022/3/3 10:31
     */
    Result<Object> checkAndCompleteCont(CompleteReceiptParam form) throws Exception;

    /**
     * @param param
     * @return com.dt.component.common.result.Result<com.dt.platform.wms.dto.asn.AsnRecBizDTO>
     * @author: WuXian
     * description:  查询和检查ASN
     * create time: 2022/3/3 10:31
     */
    Result<AsnRecBizDTO> queryAndCheckAsn(AsnRecParam param);

    /**
     * @param param
     * @return com.dt.component.common.result.Result<com.dt.platform.wms.dto.rec.SkuCommitReturnBizDTO>
     * @author: WuXian
     * description:  提交sku
     * create time: 2022/3/3 10:31
     */
    Result<SkuCommitReturnBizDTO> submitAndCheckSku(SubmitSkuParam param);

    /**
     * @param param
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * @author: WuXian
     * description:  清空容器
     * create time: 2022/3/3 10:31
     */
    Result<Boolean> clearContainer(ClearContainerParam param);

    /**
     * @param param
     * @return com.dt.component.common.result.Result<com.baomidou.mybatisplus.core.metadata.IPage < com.dt.platform.wms.dto.rec.AsnSkuDetailPageBizDTO>>
     * @author: WuXian
     * description:  获取ASN明细
     * create time: 2022/3/3 10:30
     */
    Result<IPage<AsnSkuDetailPageBizDTO>> getAsnPageDetail(AsnBizParam param);

    /**
     * @param param
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * @author: WuXian
     * description:  收货校验当前容器，批次是否可以输入
     * create time: 2022/3/3 10:30
     */
    Result<Boolean> checkContainerSkuLot(CheckSkuLotParam param);

    Result<IPage<AsnSkuDetailPageBizDTO>> getAsnReturnPageDetail(AsnBizParam param);

    /**
     * @param param
     * @return com.dt.component.common.result.Result<java.lang.Object>
     * <AUTHOR>
     * @describe:
     * @date 2023/7/21 13:44
     */
    Result<List<RecPalletPrintBizDTO>> recPrintCont(QueryPrintContPDAParam param);
    /**
     * @param param
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     * @describe: 校验扫描SN
     * @date 2024/5/9 9:46
     */
    Result<Boolean> checkScanSn(CheckSNParam param);

    Result<List<RecPalletPrintLYBizDTO>> recPrintLyCont(QueryPrintContPDAParam param);
}
