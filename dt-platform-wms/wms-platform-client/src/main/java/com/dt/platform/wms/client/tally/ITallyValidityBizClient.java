package com.dt.platform.wms.client.tally;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.platform.wms.dto.tally.TallyValidityBizDTO;
import com.dt.platform.wms.param.tally.TallyValidityBizParam;

import java.util.List;


/**
 * <p>
 * 理货效期码 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-31
 */
public interface ITallyValidityBizClient {

    /**
     * 新增理货效期码
     *
     * @param tallyValidityBizDTO
     * @return
     */
    Result<Boolean> save(TallyValidityBizDTO tallyValidityBizDTO);

    /**
     * 批量新增理货效期码
     *
     * @param tallyValidityBizDTOList
     * @return
     */
    Result<Boolean> saveBatch(List<TallyValidityBizDTO> tallyValidityBizDTOList);

    /**
     * 修改理货效期码
     *
     * ID | Code 二选一
     * @param tallyValidityBizDTO
     * @return
     */
    Result<Boolean> modify(TallyValidityBizDTO tallyValidityBizDTO);

    /**
     * 批量修改理货效期码
     *
     * @param tallyValidityBizDTOList
     * @return
     */
    Result<Boolean> modifyBatch(List<TallyValidityBizDTO> tallyValidityBizDTOList);

    /**
     * 根据条件查询是否存在
     *
     * @param param
     * @return
     */
    Result<Boolean> checkExits(TallyValidityBizParam param);

    /**
     * 获取理货效期码
     *
     * @param param
     * @return
     */
    Result<TallyValidityBizDTO> get(TallyValidityBizParam param);

    /**
     * 获取理货效期码列表
     * @param param
     * @return
     */
    Result<List<TallyValidityBizDTO>> getList(TallyValidityBizParam param);

    /**
     * 分页获取理货效期码
     *
     * @param param
     * @return
     */
    Result<Page<TallyValidityBizDTO>> getPage(TallyValidityBizParam param);

    /**
     * 功能描述:  删除理货效期码
     * 创建时间:  2021/1/8 11:22 上午
     *
     * @param param:
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     */
    Result<Boolean> remove(TallyValidityBizParam param);

}

