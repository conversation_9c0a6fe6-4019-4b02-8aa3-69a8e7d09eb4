package com.dt.platform.wms.client;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.platform.wms.dto.stock.StockBizDTO;
import com.dt.platform.wms.dto.stock.StockStatisticBizDTO;
import com.dt.platform.wms.param.stock.StockBizParam;
import io.swagger.annotations.ApiOperation;

public interface IStockBizClient {
    
    /**
     * 库区档案分页列表
     * @param param
     * @return
     */
    Result<Page<StockBizDTO>> getPage(StockBizParam param);
    
    @ApiOperation("库存汇总")
    Result<StockStatisticBizDTO> getStatistic(StockBizParam param);

    /**
     * 获取详情
     * @param param
     * @return
     */
    Result<StockBizDTO> getDetail(StockBizParam param);

    /**
     * 触发修改 -- 用于库存快照生成
     * @param param
     * @return
     */
    Result<Boolean> updateStockForSnapshot(StockBizParam param);

    Result<String> initStockSkuType();

    Result<String> checkBoxDetailLevelWarehouse();

    Result<String> initBoxDetailLevelWarehouse();

    // todo 上线使用后拿掉此处代码
    @ApiOperation("处理一级库存冻结")
    void handleLevelWarehouseFrozen();
}
