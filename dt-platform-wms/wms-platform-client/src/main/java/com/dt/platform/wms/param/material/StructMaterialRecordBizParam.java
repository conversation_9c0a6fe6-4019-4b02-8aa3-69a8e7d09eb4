package com.dt.platform.wms.param.material;

import com.dt.component.common.param.BaseSearchParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import java.math.BigDecimal;

/**
 * <p>
 * 包裹结构使用包材记录
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="StructMaterialRecord对象", description="包裹结构使用包材记录")
public class StructMaterialRecordBizParam extends BaseSearchParam  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    /**
     * 仓库编码 不允许修改
     */
    @ApiModelProperty(value = "仓库编码 不允许修改")
    private String warehouseCode;

    /**
     * 货主编码
     */
    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    /**
     * 包裹结构
     */
    @ApiModelProperty(value = "包裹结构")
    private String struct;

    /**
     * 包裹结构
     */
    @ApiModelProperty(value = "包裹结构")
    private String structSignature;

    /**
     * 是否4PL
     */
    @ApiModelProperty(value = "是否4PL")
    private Integer is4pl;

    /**
     * 固定包材
     */
    @ApiModelProperty(value = "固定包材")
    private String materialCode;

    /**
     * 使用数量
     */
    @ApiModelProperty(value = "使用数量")
    private Integer usageQuantity;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "修改人")
    private String updatedBy;
}