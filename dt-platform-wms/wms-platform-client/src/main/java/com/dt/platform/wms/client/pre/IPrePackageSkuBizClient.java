package com.dt.platform.wms.client.pre;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.component.common.vo.IdNameVO;
import com.dt.platform.wms.dto.pre.PrePackageSkuBizDTO;
import com.dt.platform.wms.param.pre.PrePackageSkuBizParam;

import java.util.List;


/**
 * <p>
 * 预包商品条码 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-25
 */
public interface IPrePackageSkuBizClient {

    /**
     * 新增预包商品条码
     *
     * @param prePackageSkuBizDTO
     * @return
     */
    Result<Boolean> save(PrePackageSkuBizDTO prePackageSkuBizDTO);

    /**
     * 批量新增预包商品条码
     *
     * @param prePackageSkuBizDTOList
     * @return
     */
    Result<Boolean> saveBatch(List<PrePackageSkuBizDTO> prePackageSkuBizDTOList);

    /**
     * 修改预包商品条码
     *
     * ID | Code 二选一
     * @param prePackageSkuBizDTO
     * @return
     */
    Result<Boolean> modify(PrePackageSkuBizDTO prePackageSkuBizDTO);

    /**
     * 批量修改预包商品条码
     *
     * @param prePackageSkuBizDTOList
     * @return
     */
    Result<Boolean> modifyBatch(List<PrePackageSkuBizDTO> prePackageSkuBizDTOList);

    /**
     * 根据条件查询是否存在
     *
     * @param param
     * @return
     */
    Result<Boolean> checkExits(PrePackageSkuBizParam param);

    /**
     * 获取预包商品条码
     *
     * @param param
     * @return
     */
    Result<PrePackageSkuBizDTO> get(PrePackageSkuBizParam param);

    /**
     * 获取预包商品条码列表
     * @param param
     * @return
     */
    Result<List<PrePackageSkuBizDTO>> getList(PrePackageSkuBizParam param);

    /**
     * 分页获取预包商品条码
     *
     * @param param
     * @return
     */
    Result<Page<PrePackageSkuBizDTO>> getPage(PrePackageSkuBizParam param);

    /**
     * 功能描述:  删除预包商品条码
     * 创建时间:  2021/1/8 11:22 上午
     *
     * @param param:
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     */
    Result<Boolean> remove(PrePackageSkuBizParam param);

    /**
     * 预包条码确认
     * @param param
     * @return
     */
    Result<Boolean> docStatusEnable(PrePackageSkuBizParam param);

    /**
     * 预包条码启用禁用
     * @param param
     * @return
     */
    Result<Boolean> statusEnable(PrePackageSkuBizParam param);

    /**
     * 预包条码状态
     * @param isAll
     * @return
     */
    Result<List<IdNameVO>> statusSelectItem(boolean isAll);

    /**
     * 预包条码档案状态
     * @param isAll
     * @return
     */
    Result<List<IdNameVO>> docStatusSelectItem(boolean isAll);

    /**
     * 预包条码详情
     * @param param
     * @return
     */
    Result<PrePackageSkuBizDTO> detail(PrePackageSkuBizParam param);
}

