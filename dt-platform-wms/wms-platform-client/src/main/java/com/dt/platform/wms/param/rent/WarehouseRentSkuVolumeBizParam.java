package com.dt.platform.wms.param.rent;

import com.dt.component.common.param.BaseSearchParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 仓租商品体积
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="WarehouseRentSkuVolume对象", description="仓租商品体积")
public class WarehouseRentSkuVolumeBizParam extends BaseSearchParam  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    /**
     * 仓库编码
     */
    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    /**
     * 货主编码
     */
    @ApiModelProperty(value = "货主编码")
    private String cargoCode;
    private List<String> cargoCodeList;

    /**
     * 上游单号
     */
    @ApiModelProperty(value = "上游单号")
    private String billNo;
    private List<String> billNoList;

    /**
     * 商品编码
     */
    @ApiModelProperty(value = "商品编码")
    private String skuCode;
    private String upcCode;
    private List<String> skuCodeList;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private String status;

    /**
     * 长(cm) 默认为0，保留一位小数
     */
    @ApiModelProperty(value = "长(cm) 默认为0，保留一位小数")
    private BigDecimal length;

    /**
     * 宽(cm) 默认为0，保留一位小数
     */
    @ApiModelProperty(value = "宽(cm) 默认为0，保留一位小数")
    private BigDecimal width;

    /**
     * 高(cm) 默认为0，保留一位小数
     */
    @ApiModelProperty(value = "高(cm) 默认为0，保留一位小数")
    private BigDecimal height;

    /**
     * 拓传字段json
     */
    @ApiModelProperty(value = "拓传字段json")
    private String extraJson;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "修改人")
    private String updatedBy;
}