package com.dt.platform.wms.client;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.platform.wms.dto.stock.StockSerialBizDTO;
import com.dt.platform.wms.param.stock.StockSerialBizParam;

/**
 * Created by nobody on 2020/12/9 9:19
 */
public interface IStockSerialBizClient {

    Result<Page<StockSerialBizDTO>> getPage(StockSerialBizParam param);

    /**
     * 迁移库存流水
     * - 只迁移三个月之前的数据
     * - 只迁移已出库出库单和包裹对应的数据
     */
    Result<Boolean> stockSerialMigrate(StockSerialBizParam param);
}
