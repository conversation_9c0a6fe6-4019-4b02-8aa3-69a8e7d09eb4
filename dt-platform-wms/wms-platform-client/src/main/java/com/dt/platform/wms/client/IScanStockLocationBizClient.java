package com.dt.platform.wms.client;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.component.common.vo.IdNameVO;
import com.dt.domain.core.stock.param.StockLocationParam;
import com.dt.platform.wms.dto.stock.StockLocationBizDTO;
import com.dt.platform.wms.dto.stock.StockLocationWithLotBizDTO;
import com.dt.platform.wms.param.CodeListParam;
import com.dt.platform.wms.param.CodePageParam;
import com.dt.platform.wms.param.CodeParam;
import com.dt.platform.wms.param.sku.SkuLotBizParam;
import com.dt.platform.wms.param.stock.StockLocationBizParam;
import com.dt.platform.wms.param.stock.StockLocationSkuLotParam;
import com.dt.platform.wms.param.stock.StockLocationStatisticsBizParam;
import com.dt.platform.wms.param.stock.StockLocationWithLotBizParam;

import java.util.List;
import java.util.Map;

public interface IScanStockLocationBizClient {


//    /**
//     * 查询三级库存信息
//     * @param param
//     * @return
//     */
//    Result<List<StockLocationBizDTO>>  getScanSkuStockList(CodeListParam param);
//

    Result<IPage<StockLocationBizDTO>> getScanSkuStockPage(StockLocationParam param);
    /**
     * 查询三级库存信息
     * @param param
     * @return
     */
    Result<Map> findLocationSumQty(CodeParam param);
    Result<IPage<StockLocationBizDTO>> getScanLocationStockPage(StockLocationParam param);

}
