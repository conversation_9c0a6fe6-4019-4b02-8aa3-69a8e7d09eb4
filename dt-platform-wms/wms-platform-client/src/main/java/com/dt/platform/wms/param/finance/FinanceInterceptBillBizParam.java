package com.dt.platform.wms.param.finance;

import com.dt.component.common.param.BaseSearchParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import java.math.BigDecimal;

/**
 * <p>
 * 金融单据拦截单
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="FinanceInterceptBill对象", description="金融单据拦截单")
public class FinanceInterceptBillBizParam extends BaseSearchParam  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    /**
     * 仓库编码
     */
    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    /**
     * 货主编码
     */
    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    /**
     * 单据号
     */
    @ApiModelProperty(value = "单据号")
    private String billNo;

    /**
     * 单据类型
     */
    @ApiModelProperty(value = "单据类型")
    private String billType;

    /**
     * erp单号
     */
    @ApiModelProperty(value = "erp单号")
    private String erpCode;

    /**
     * 单据状态
     */
    @ApiModelProperty(value = "单据状态")
    private String status;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "修改人")
    private String updatedBy;
}