package com.dt.platform.wms.client;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.dt.component.common.result.Result;
import com.dt.component.common.vo.IdNameVO;
import com.dt.platform.wms.dto.pkg.PackageMaterialBizDTO;
import com.dt.platform.wms.form.PackageMaterialAddBizForm;
import com.dt.platform.wms.form.PackageMaterialUpdateBizForm;
import com.dt.platform.wms.param.pkg.PackageMaterialBizParam;
import com.dt.platform.wms.param.pkg.PackageMaterialEnableBizParam;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/9/16 14:29
 */
public interface IPackageMaterialBizClient {
    /**
     * 分页
     *
     * @param param
     * @return
     */
    Result<IPage<PackageMaterialBizDTO>> queryPage(PackageMaterialBizParam param);

    Result<List<PackageMaterialBizDTO>> getList(PackageMaterialBizParam param);

    /**
     * @return
     */
    Result<List<IdNameVO>> queryStatus();

    /**
     * 启用
     *
     * @param param
     * @return
     */
    Result<Boolean> enable(PackageMaterialEnableBizParam param);

    /**
     * 所有状态码
     *
     * @return
     */
    Result<List<IdNameVO>> queryStatusAll();

    /**
     * @param code
     * @return
     */
    Result<PackageMaterialBizDTO> queryByCode(String code);

    Result<PackageMaterialBizDTO> queryByBarCode(String cargoCode, String upcCode);

    /**
     * 修改
     *
     * @param packageMaterialUpdateBizForm
     * @return
     */
    Result<Boolean> update(PackageMaterialUpdateBizForm packageMaterialUpdateBizForm);

    /**
     * 新增
     *
     * @param packageMaterialAddBizForm
     * @return
     */
    Result<Boolean> add(PackageMaterialAddBizForm packageMaterialAddBizForm);

    /**
     * @return
     */
    Result<List<IdNameVO>> queryPurchaseAll();

    /**
     * @return
     */
    Result<List<IdNameVO>> queryPurchase();

    /**
     * @param param
     * @param warehouseCode
     * @return void
     * <AUTHOR>
     * @describe: 淘天货主同步耗材
     * @date 2024/4/1 14:35
     */
    void initTaoTianPackMaterialAdd(String param, String warehouseCode);
}
