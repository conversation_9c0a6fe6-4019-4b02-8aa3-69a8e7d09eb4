package com.dt.platform.wms.param.base;

import com.dt.component.common.group.Modify;
import com.dt.component.common.param.BaseSearchParam;
import com.dt.platform.wms.dto.base.LotRuleDetailBizDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <p>
 * 批次规则档案
 * </p>
 *
 * <AUTHOR> xy
 * @since 2020-09-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="LotRule对象", description="批次规则档案")
public class LotRuleBizModifyParam extends BaseSearchParam  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "规则编码 唯一 不允许修改")
    @NotEmpty(message = "规则编码不能为空",groups = Modify.class)
    private String code;

    @ApiModelProperty(value = "规则名称")
    @NotEmpty(message = "规则名称不能为空",groups = Modify.class)
    private String name;

    @ApiModelProperty(value = "规则描叙")
    private String note;

    @ApiModelProperty(value = "默认规则: 1-默认 -1-非默认")
    private Integer isDefault;

    @ApiModelProperty(value = "备注")
    private String remark;

    @NotEmpty(message = "规则属性列表不能为空",groups =  Modify.class)
    @ApiModelProperty(value = "规则详情列表")
    List<LotRuleDetailBizDTO> ruleDetailList;



}