package com.dt.platform.wms.client.rec;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.platform.wms.dto.rec.ReceiptExtraDetailBizDTO;
import com.dt.platform.wms.param.rec.ReceiptExtraDetailBizParam;

import java.util.List;


/**
 * <p>
 * 收货作业批次容器明细 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-02-20
 */
public interface IReceiptExtraDetailBizClient {

    /**
     * 新增收货作业批次容器明细
     *
     * @param receiptExtraDetailBizDTO
     * @return
     */
    Result<Boolean> save(ReceiptExtraDetailBizDTO receiptExtraDetailBizDTO);

    /**
     * 批量新增收货作业批次容器明细
     *
     * @param receiptExtraDetailBizDTOList
     * @return
     */
    Result<Boolean> saveBatch(List<ReceiptExtraDetailBizDTO> receiptExtraDetailBizDTOList);

    /**
     * 修改收货作业批次容器明细
     *
     * ID | Code 二选一
     * @param receiptExtraDetailBizDTO
     * @return
     */
    Result<Boolean> modify(ReceiptExtraDetailBizDTO receiptExtraDetailBizDTO);

    /**
     * 批量修改收货作业批次容器明细
     *
     * @param receiptExtraDetailBizDTOList
     * @return
     */
    Result<Boolean> modifyBatch(List<ReceiptExtraDetailBizDTO> receiptExtraDetailBizDTOList);

    /**
     * 根据条件查询是否存在
     *
     * @param param
     * @return
     */
    Result<Boolean> checkExits(ReceiptExtraDetailBizParam param);

    /**
     * 获取收货作业批次容器明细
     *
     * @param param
     * @return
     */
    Result<ReceiptExtraDetailBizDTO> get(ReceiptExtraDetailBizParam param);

    /**
     * 获取收货作业批次容器明细列表
     * @param param
     * @return
     */
    Result<List<ReceiptExtraDetailBizDTO>> getList(ReceiptExtraDetailBizParam param);

    /**
     * 分页获取收货作业批次容器明细
     *
     * @param param
     * @return
     */
    Result<Page<ReceiptExtraDetailBizDTO>> getPage(ReceiptExtraDetailBizParam param);

    /**
     * 功能描述:  删除收货作业批次容器明细
     * 创建时间:  2021/1/8 11:22 上午
     *
     * @param param:
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     */
    Result<Boolean> remove(ReceiptExtraDetailBizParam param);

}

