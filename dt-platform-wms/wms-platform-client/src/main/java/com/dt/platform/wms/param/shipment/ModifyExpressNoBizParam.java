package com.dt.platform.wms.param.shipment;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
public class ModifyExpressNoBizParam  implements java.io.Serializable  {
    @ApiModelProperty(value = "承运商编码 不允许修改,唯一")
    //@NotEmpty(message = "运单号为空")
    private String expressNo;

    @ApiModelProperty(value = "出库单号")
    @NotEmpty(message = "出库单号不能为空")
    private String shipmentCode;
}