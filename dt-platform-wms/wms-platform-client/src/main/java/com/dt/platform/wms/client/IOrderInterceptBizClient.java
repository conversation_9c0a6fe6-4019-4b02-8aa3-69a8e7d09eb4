package com.dt.platform.wms.client;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.platform.wms.dto.track.OrderInterceptBizDTO;
import com.dt.platform.wms.param.track.OrderInterceptBizParam;


public interface IOrderInterceptBizClient {

    /**
     * 新单信息
     * @param param
     * @return
     */
    Result<Boolean> save(OrderInterceptBizDTO param);

    /**
     * 根据条件查询是否存在
     * @param param
     * @return
     */
    Result<Boolean> checkExits(OrderInterceptBizParam param);


    /**
     * 分页获取拣选单
     * @param param
     * @return
     */
    Result<Page<OrderInterceptBizDTO>> getPage(OrderInterceptBizParam param);

    /**
     * 完成拦截单
     * @param warehouseCode
     */
    void tryCompleteOrderIntercept(String warehouseCode);
}
