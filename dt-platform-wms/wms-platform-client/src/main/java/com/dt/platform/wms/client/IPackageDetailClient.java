package com.dt.platform.wms.client;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.domain.bill.dto.PackageDetailDTO;
import com.dt.domain.bill.dto.SkuLotCodeDTO;
import com.dt.domain.bill.param.PackageDetailBatchParam;
import com.dt.domain.bill.param.PackageDetailParam;
import com.dt.platform.wms.dto.pkg.PackageDetailBizDTO;
import com.dt.platform.wms.param.sku.SkuLotBizParam;

import java.util.List;

/**
 * <AUTHOR>
 * @Create 2021/02/25  18:40
 * @Describe
 **/
public interface IPackageDetailClient {

    /**
     * 获取商品档案信息
     * @param param
     * @return
     */
    PackageDetailDTO get(PackageDetailParam param);

    /**
     * 获取商品档案列表
     * @param param
     * @return
     */
    List<PackageDetailDTO> getList(PackageDetailParam param);

    Page<PackageDetailDTO> getPage(PackageDetailParam packageDetailParam);
}
