package com.dt.platform.wms.param.wave;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class WaveNavigationShowHeadBizDTO implements Serializable {

    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "分析")
    private String analysisSku;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    @ApiModelProperty(value = "货主名")
    private String cargoName;

    @ApiModelProperty(value = "包裹数量")
    private Integer packQty;

    private List<WaveNavigationShowBodyBizDTO> waveNavigationBodyList;

}
