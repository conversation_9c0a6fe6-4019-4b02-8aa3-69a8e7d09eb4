package com.dt.platform.wms.param.wave;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class WaveNavigationQueryHeadBizDTO implements Serializable {

    @ApiModelProperty(value = "code")
    private String code;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "是否选中")
    private Boolean isCheck;

    @ApiModelProperty(value = "数量")
    private Integer num;

    @ApiModelProperty(value = "描叙")
    private String desc;


}
