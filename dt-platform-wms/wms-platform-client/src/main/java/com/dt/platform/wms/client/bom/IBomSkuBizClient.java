package com.dt.platform.wms.client.bom;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.platform.wms.dto.bom.BomSkuBizDTO;
import com.dt.platform.wms.dto.bom.BomSkuAggDTO;
import com.dt.platform.wms.param.bom.BomSkuBizParam;
import com.dt.platform.wms.param.bom.BomSkuDetailBizParam;

public interface IBomSkuBizClient {
    /**
     * @param param
     * @return com.dt.component.common.result.Result<com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.dt.platform.wms.dto.bom.BomSkuBizDTO>>
     * <AUTHOR>
     * @describe: bom商品
     * @date 2025/1/13 11:23
     */
    Result<Page<BomSkuBizDTO>> getPage(BomSkuBizParam param);

    Result<BomSkuAggDTO> getDetail(BomSkuDetailBizParam param);
}
