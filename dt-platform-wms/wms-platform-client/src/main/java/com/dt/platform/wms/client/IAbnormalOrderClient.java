package com.dt.platform.wms.client;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.component.common.vo.IdNameVO;
import com.dt.platform.wms.dto.AbnormalOrderBizDTO;
import com.dt.platform.wms.param.AbnormalOrderBizParam;
import com.dt.platform.wms.param.IdParam;

import java.util.List;

public interface IAbnormalOrderClient {
    /**
     *
     * @param searchAbnormalOrderParam
     * @return
     */
    Result<Page<AbnormalOrderBizDTO>> queryPage(AbnormalOrderBizParam searchAbnormalOrderParam);

    /**
     * 三级库存解除异常
     * @param param
     * @return
     */
    Result<Boolean> relieveAbnormal(IdNameVO param);

    /**
     * 自动重试异常单
     * @return
     */
    Result<Boolean> autoRetryAbnormal(String warehouse);
    /**
     * @author: WuXian
     * description:  异常单-解除所有汇单异常
     * create time: 2021/12/2 9:16
     *
     * @param idList
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     */
    Result<Boolean> relieveAbnormalAll(List<Long> idList);
}
