package com.dt.platform.wms.client;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.component.common.vo.IdNameVO;
import com.dt.domain.core.stock.param.StockLocationParam;
import com.dt.platform.wms.dto.lot.SkuLotSourceBizDTO;
import com.dt.platform.wms.dto.stock.StockLocationBizDTO;
import com.dt.platform.wms.dto.stock.StockLocationWithLotBizDTO;
import com.dt.platform.wms.dto.stock.StockStatisticBizDTO;
import com.dt.platform.wms.param.CodePageParam;
import com.dt.platform.wms.param.CodeParam;
import com.dt.platform.wms.param.sku.SkuLotBizParam;
import com.dt.platform.wms.param.stock.*;
import io.swagger.annotations.ApiOperation;

import java.util.List;
import java.util.Map;

public interface IStockLocationBizClient {

    Result<Boolean> scanChargingModelStatistics(StockLocationParam param);
    /**
     * 库区档案分页列表
     * @param param
     * @return
     */
    Result<Page<StockLocationBizDTO>> getPage(StockLocationBizParam param);

    @ApiOperation("库存汇总")
    Result<StockStatisticBizDTO> getStatistic(StockLocationBizParam param);

    /**
     * 分页信息
     * @param param
     * @return
     */
    Result<Page<StockLocationWithLotBizDTO>> getPageWithLot(StockLocationBizParam param);


    /**
     * 库区档案分页列表
     * @param param
     * @return
     */
    Result<Page<StockLocationBizDTO>> getStatisticsPage(StockLocationStatisticsBizParam param);


    /**
     * 库区档案分页列表
     * @param param
     * @return
     */
    Result<Page<StockLocationWithLotBizDTO>> getLocationWithLotPage(StockLocationWithLotBizParam param);
    
    @ApiOperation("货主货位批次汇总")
    Result<StockStatisticBizDTO> getLocationWithLotPageStatistic(StockLocationWithLotBizParam param);

    /**
     * 获取、商品、批次
     * @param param
     * @return
     */
    Result<Page<StockLocationWithLotBizDTO>> getStatisticsCargoLotPage(SkuLotBizParam param);

    /**
     * 获取详情
     * @param param
     * @return
     */
    Result<StockLocationBizDTO> getDetail(StockLocationBizParam param);

    /**
     * 查询
     * @param param
     * @return
     */
    Result<List<IdNameVO>> getSkuLotList(StockLocationSkuLotParam param);

    /**
     * 查询扫描库位库存信息
     * @param param
     * @return
     */
    Result<Page<StockLocationWithLotBizDTO>> getScanLocationStockPage(CodePageParam param);

    /**
     * 查询扫描upc编码库存信息
     * @param param
     * @return
     */
    Result<List<StockLocationWithLotBizDTO>> getScanUpcStockList(CodeParam param);
    
    Result<Page<StockLocationWithLotBizDTO>> getScanUpcStockPage(CodePageParam param);

    /**
     * 触发修改 -- 用于三级库存快照生成
     * @param param
     * @return
     */
    Result<Boolean> updateStockLocationForSnapshot(StockLocationBizParam param);


    /**
     * 触发修改 -- 用于三级库存类型统计
     * @param param
     * @return
     */
    Result<Boolean> updateStockLocationForChargingStatistics(StockLocationBizParam param);

    Result<String> initStockSkuType();

    Result<SkuLotSourceBizDTO> getSkuLotSource(StockLocationWithLotBizParam param);
}
