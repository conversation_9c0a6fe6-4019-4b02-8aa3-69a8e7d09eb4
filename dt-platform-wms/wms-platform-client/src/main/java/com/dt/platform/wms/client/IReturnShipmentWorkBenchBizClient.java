package com.dt.platform.wms.client;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.dt.component.common.result.Result;
import com.dt.platform.wms.dto.rec.AsnSkuDetailPageBizDTO;
import com.dt.platform.wms.dto.rtv.*;
import com.dt.platform.wms.param.CodeParam;
import com.dt.platform.wms.param.asn.AsnBizParam;
import com.dt.platform.wms.param.rtv.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/3/15 9:42
 */
public interface IReturnShipmentWorkBenchBizClient {
    /**
     * @param param
     * @return java.lang.Boolean
     * @author: WuXian
     * description:  校验工作台(退货入库工作台)的有效性
     * create time: 2022/3/15 9:14
     */
    Boolean checkWorkBenchExists(CodeParam param);

    /**
     * @param param
     * @return com.dt.component.common.result.Result<java.lang.Object>
     * @author: Wu<PERSON>ian
     * description: 扫描,校验容器绑定单据
     * create time: 2022/3/15 13:09
     */
    Result<ScanRTVContainerBizDTO> checkAndScanContainerToOccupyAndBindingBill(ContainerRTVBizParam param) throws Exception;

    /**
     * @param param
     * @return java.lang.Object
     * @author: WuXian
     * description: 扫描单据（入库单或退货运单号）
     * create time: 2022/3/15 9:20
     */
    Result<ScanBillRTVBizDTO> checkAndScanBill(ScanBillRTVBizParam param) throws Exception;

    /**
     * @param param
     * @return java.lang.Object
     * @author: WuXian
     * description:  扫描商品条码
     * create time: 2022/3/15 9:22
     */
    Result<ScanUpcRTVBizDTO> checkAndScanSkuUpc(ScanUpcRTVBizParam param) throws Exception;

    /**
     * @param param
     * @return java.lang.Object
     * @author: WuXian
     * description:  校验当前批次信息是否必填和当前容器能不能收货
     * create time: 2022/3/15 9:22
     */
    Result<Boolean> checkSkuLotRuleAndContainerInfo(CheckSkuLotRTVBizParam param) throws Exception;

    /**
     * @param param
     * @return java.lang.Object
     * @author: WuXian
     * description:  提交商品
     * create time: 2022/3/15 9:22
     */
    Result<Boolean> submitSku(SubmitSkuRTVBizParam param) throws Exception;

    /**
     * @param param
     * @return java.lang.Object
     * @author: WuXian
     * description:  完成容器，提交数据
     * create time: 2022/3/15 9:22
     */
    Result<Boolean> completeContainer(CompleteContRTVBizParam param) throws Exception;

    /**
     * @param param
     * @return java.lang.Object
     * @author: WuXian
     * description:  清空容器
     * create time: 2022/3/15 9:22
     */
    Result<Boolean> cancelContainer(CompleteContRTVBizParam param) throws Exception;

    /**
     * @param param
     * @return com.dt.component.common.result.Result<java.util.List < com.dt.platform.wms.dto.rtv.SkuRTVContainerSkuBizDTO>>
     * @author: WuXian
     * description:
     * create time: 2022/3/22 13:40
     */
    Result<SkuRTVContainerBizDTO> getContainerData(ContainerRTVBizParam param) throws Exception;

    Result<IPage<AsnSkuDetailPageBizDTO>> getAsnPageDetail(AsnBizParam param) throws Exception;
    /**
     * @param param
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     * @describe:
     * @date 2024/5/9 15:39
     */
    Result<Boolean> checkSkuSN(CheckSkuSNRTVBizParam param);

    Result<IPage<AsnSkuDetailPageBizDTO>> getAsnPageDetailReturn(AsnBizParam param);
}
