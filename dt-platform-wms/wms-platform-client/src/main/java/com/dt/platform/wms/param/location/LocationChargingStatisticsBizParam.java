package com.dt.platform.wms.param.location;

import com.dt.component.common.param.BaseSearchParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;


/**
 * <p>
 * 三级库计费规格天统计
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="LocationChargingStatistics对象", description="三级库计费规格天统计")
public class LocationChargingStatisticsBizParam extends BaseSearchParam  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    /**
     * 快照時間
     */
    @ApiModelProperty(value = "快照時間")
    private Long snapshotTime;
    @ApiModelProperty(value = "快照時間")
    private Long snapshotTimeStart;
    private Long snapshotTimeEnd;

    /**
     * 仓库编码
     */
    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    /**
     * 货主编码
     */
    @ApiModelProperty(value = "货主编码")
    private String cargoCode;
    @ApiModelProperty(value = "货主编码")
    private List<String> cargoCodeList;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String chargingModel;

    @ApiModelProperty(value = "")
    private List<String> chargingModelList;


    /**
     * 
     */
    @ApiModelProperty(value = "")
    private Long total;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "修改人")
    private String updatedBy;
}