package com.dt.platform.wms.param.performance;

import com.dt.component.common.param.BaseSearchParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;


/**
 * <p>
 * 人员操作详情
 * </p>
 *
 * <AUTHOR>
 * @since 2021-02-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="PerformanceDetail对象", description="人员操作详情")
public class PerformanceDetailBizParam extends BaseSearchParam  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    /**
     * 仓库编码
     */
    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    /**
     * 货主编码
     */
    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    /**
     * 员工
     */
    @ApiModelProperty(value = "员工")
    private String worker;

    /**
     * 操作类型
     */
    @ApiModelProperty(value = "操作类型")
    private String type;

    /**
     * 时间
     */
    @ApiModelProperty(value = "时间")
    private Long workTime;

    /**
     * 日期
     */
    @ApiModelProperty(value = "日期")
    private Long workDate;

    /**
     * 单据编号
     */
    @ApiModelProperty(value = "单据编号")
    private String billNo;

    private String detailBillNo;

    /**
     * 统计目标
     */
    @ApiModelProperty(value = "统计目标")
    private String statisticsTarget;

    /**
     * 
     */
    @ApiModelProperty(value = "")
    private BigDecimal quantity;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "修改人")
    private String updatedBy;


    @ApiModelProperty(value = "拣选单类型")
    private String pickType;

    @ApiModelProperty(value = "散单还是批量")
    private String singleOrBatch;

}