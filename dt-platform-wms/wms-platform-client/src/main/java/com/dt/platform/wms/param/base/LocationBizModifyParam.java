package com.dt.platform.wms.param.base;

import com.dt.component.common.group.Create;
import com.dt.component.common.param.BaseSearchParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 库位管理
 * </p>
 *
 * <AUTHOR> xy
 * @since 2020-09-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="Location对象", description="库位管理")
public class LocationBizModifyParam extends BaseSearchParam  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "编码 不允许修改,唯一")
    @NotEmpty(message = "库位编码不能为空")
    private String code;

    @ApiModelProperty(value = "库区编码")
    private String zoneCode;

    @ApiModelProperty(value = "拣货路由序号")
    private Long pickSeq;

    @ApiModelProperty(value = "上架路由序号")
    private Long shelfSeq;

    @ApiModelProperty(value = "库位类型")
    private String type;

    @ApiModelProperty(value = "高（cm）")
    private BigDecimal height;

    @ApiModelProperty(value = "宽（cm）")
    private BigDecimal width;

    @ApiModelProperty(value = "长（cm）")
    private BigDecimal length;

    @ApiModelProperty(value = "最大允许体积(cm³)")
    private BigDecimal volume;

    @ApiModelProperty(value = "最大允许重量(kg)")
    private BigDecimal weight;

    @ApiModelProperty(value = "存放规则 字典组:LOCATION_MIX_RULE")
    private String storageRule;

    @ApiModelProperty(value = "混放规则 取值：混放策略档案")
    private String mixRuleCode;

    @ApiModelProperty(value = "最大商品数")
    private Integer maxSkuNum;

    @ApiModelProperty(value = "最大种类数")
    private Integer maxTypeNum;

    @ApiModelProperty(value = "库位货架类型")
    private String shelfType;
    @NotEmpty(message = "计费规格不能为空")
    private String chargingModel;

    private List<Integer> locationTagList;

    @ApiModelProperty(value = "恒温仓")
    private String thermostatic;

}