package com.dt.platform.wms.client;

import com.dt.component.common.result.Result;
import com.dt.domain.bill.dto.PackageDTO;
import com.dt.platform.wms.param.CodeParam;
import com.dt.platform.wms.param.pack.PackageSerialParam;
import io.swagger.annotations.ApiOperation;

public interface IPackageSerialBizClient {

    @ApiOperation("原箱扫码【扫描运单】")
    Result<PackageDTO> scanExpressNo(CodeParam codeParam);

    @ApiOperation("原箱扫码【扫描箱码】")
    Result<Boolean> scanSerial(PackageSerialParam param);
}
