package com.dt.platform.wms.client;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.dt.component.common.result.Result;
import com.dt.component.common.vo.IdNameVO;
import com.dt.domain.bill.param.AsnDetailParam;
import com.dt.domain.bill.param.AsnThermostaticMaintainParam;
import com.dt.platform.wms.dto.asn.AsnBizDTO;
import com.dt.platform.wms.dto.asn.AsnDetailBizDTO;
import com.dt.platform.wms.dto.asn.AsnDetailDataBizDTO;
import com.dt.platform.wms.dto.asn.AsnLogBizDTO;
import com.dt.platform.wms.dto.box.AnalysisCommitBizDTO;
import com.dt.platform.wms.dto.box.AnalysisErrorBizDTO;
import com.dt.platform.wms.form.asn.AsnAddForm;
import com.dt.platform.wms.form.asn.AsnLinkBillParam;
import com.dt.platform.wms.form.asn.AsnModifyForm;
import com.dt.platform.wms.form.asn.AsnOrderTagOperationParam;
import com.dt.platform.wms.param.CodeListParam;
import com.dt.platform.wms.param.CodeParam;
import com.dt.platform.wms.param.asn.ArrivalBatchParam;
import com.dt.platform.wms.param.asn.AsnBizParam;
import com.dt.platform.wms.param.asn.AsnCancelParam;
import com.dt.platform.wms.param.asn.AsnDetailBizParam;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/9/21 17:16
 */
public interface IAsnBizClient {
    /**
     * 分页
     *
     * @param param
     * @return
     */
    Result<IPage<AsnBizDTO>> queryPage(AsnBizParam param);


    /**
     * @return
     */
    Result<List<IdNameVO>> queryStatus();

    /**
     * @return
     */
    Result<List<IdNameVO>> queryStatusAll();

    /**
     * @return
     */
    Result<List<IdNameVO>> queryType();

    /**
     * @return
     */
    Result<List<IdNameVO>> queryTypeAll();

    /**
     * 完成收货单
     *
     * @param param
     * @return
     */
    Result<Boolean> complete(AsnDetailBizParam param);

    /**
     * 获取ASN的详情
     *
     * @param param
     * @return
     */
    Result<AsnDetailDataBizDTO> getDetail(AsnDetailBizParam param);

    /**
     * 获取ASN详情 for 理货报告
     *
     * @param param
     * @return
     */
    Result<AsnDetailDataBizDTO> getDetailForTally(AsnDetailBizParam param);

    /**
     * 确认已到货
     *
     * @param param
     * @return
     */
    Result<Boolean> arrival(AsnDetailBizParam param);

    /**
     * 取消收货单
     *
     * @param param
     * @return
     */
    Result<Boolean> cancel(AsnDetailBizParam param);

    /**
     * 迷你分页
     *
     * @param param
     * @return
     */
    Result<IPage<AsnDetailBizDTO>> queryDetailPage(AsnBizParam param);

    /**
     * 取消收货
     *
     * @param param
     * @return
     */
    Result<Boolean> cancelReceipt(AsnDetailBizParam param);

    /**
     * 退货入库批量完成
     *
     * @param param
     * @return
     */
    Result<Boolean> batchComplete(CodeListParam param);

    Result<IPage<AsnLogBizDTO>> logPage(AsnBizParam param);

    /**
     * @param from
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * @author: WuXian
     * description:
     * create time: 2021/7/15 14:41
     */
    Result<Boolean> wmsAddAsn(AsnAddForm from);

    /**
     * @param from
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * @author: WuXian
     * description:
     * create time: 2021/7/15 14:41
     */
    Result<Boolean> wmsModifyAsn(AsnModifyForm from);

    /**
     * @param param
     * @return com.dt.component.common.result.Result<com.dt.platform.wms.dto.asn.AsnDetailDataBizDTO>
     * @author: WuXian
     * description:
     * create time: 2021/7/16 9:15
     */
    Result<AsnDetailDataBizDTO> getAsnModifyDetail(CodeParam param);

    /**
     * @param param
     * @return com.dt.component.common.result.Result<java.lang.String>
     * @author: WuXian
     * description:
     * create time: 2021/7/16 13:14
     */
    Result<String> wmsCancelAsn(AsnCancelParam param);

    /**
     * @param param
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * @author: WuXian
     * description:  批量到货
     * create time: 2022/3/11 16:07
     */
    Result<Boolean> batchArrival(ArrivalBatchParam param);
    /**
     * @param param
     * @return com.dt.component.common.result.Result<com.dt.platform.wms.dto.box.AnalysisErrorBizDTO>
     * <AUTHOR>
     * @describe:
     * @date 2022/9/20 14:26
     */
    Result<AnalysisErrorBizDTO> getAnalysisError(CodeParam param);
    /**
     * @param commitBizDTO
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     * @describe:
     * @date 2022/9/20 14:54
     */
    Result<Boolean> commitAnalysisData(AnalysisCommitBizDTO commitBizDTO);
    /**
     * @param param
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     * @describe:
     * @date 2023/7/20 9:53
     */
    Result<Boolean> modifyOrderTag(AsnOrderTagOperationParam param);
    /**
     * @param param
     * @return com.dt.component.common.result.Result<java.util.List<java.lang.Integer>>
     * <AUTHOR>
     * @describe:
     * @date 2023/7/20 9:53
     */
    Result<List<Integer>> getOrderTagPartToOperationOccupy(CodeParam param);
    /**
     * @param from
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     * @describe:
     * @date 2023/7/20 16:29
     */
    Result<Boolean> genTally(CodeParam from);
    /**
     * @param param
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     * @describe: 释放库存
     * @date 2023/7/24 9:52
     */
    Result<Boolean> releaseStock(CodeParam param);
    /**
     * @param param
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     * @describe:
     * @date 2023/7/26 13:20
     */
    Result<Boolean> linkBill(AsnLinkBillParam param);
    /**
     * @param param
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     * @describe: 关联报关单号
     * @date 2024/1/26 9:44
     */
    Result<Boolean> linkDeclarationOrderNo(AsnBizParam param);

    Result<Boolean> xtBatchComplete(AsnBizParam param);

    Result<Boolean> xtBatchArrival(ArrivalBatchParam param);

    Result<IPage<AsnDetailBizDTO>> thermostaticDetailPag(AsnDetailParam param);

    Result<Boolean> thermostaticMaintain(AsnThermostaticMaintainParam param);

    void scanLyCreateSku(String warehouseCode);
    /**
     * @param warehouseCode
     * @param param
     * @return com.dt.component.common.result.Result<java.string.String>
     * <AUTHOR>
     * @describe:
     * @date 2025/1/10 14:01
     */
    Result<String> asnAutoCompleteByReturn(String warehouseCode, String param);
    /**
     * @param param
     * @return com.dt.component.common.result.Result<java.lang.String>
     * <AUTHOR>
     * @describe: 零收货
     * @date 2025/2/12 13:26
     */
    Result<String> zeroReceive(AsnBizParam param);
}
