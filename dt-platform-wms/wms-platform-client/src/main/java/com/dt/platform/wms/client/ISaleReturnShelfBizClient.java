package com.dt.platform.wms.client;

import com.dt.component.common.result.Result;
import com.dt.platform.wms.dto.shelf.SaleReturnBatchShelfTipDTO;
import com.dt.platform.wms.param.shelf.ShelfBizParam;
import io.swagger.annotations.ApiOperation;

import java.util.List;

public interface ISaleReturnShelfBizClient {

    @ApiOperation("销退上架提示")
    Result<List<SaleReturnBatchShelfTipDTO>> saleReturnShelfTip(ShelfBizParam shelfBizParam);

    @ApiOperation("销退上架")
    Result<Boolean> saleReturnShelf(ShelfBizParam shelfBizParam);
}
