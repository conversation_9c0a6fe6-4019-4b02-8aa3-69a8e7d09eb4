package com.dt.platform.wms.client;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.platform.wms.dto.shelf.ShelfBizDTO;
import com.dt.platform.wms.dto.shelf.ShelfDetailBizDTO;
import com.dt.platform.wms.param.CodeListParam;
import com.dt.platform.wms.param.CodeParam;
import com.dt.platform.wms.param.shelf.*;

import java.util.List;

public interface IShelfBizClient {

    /**
     * 修改上架单上架方式
     */
    Result<Boolean> modifyShelfOpType(ShelfModifyOpTypeParam param);

    /**
     * 上架单分页列表
     *
     * @param param
     * @return
     */
    Result<Page<ShelfBizDTO>> getPage(ShelfBizParam param);


    /**
     * 扫描容器获取当前上架单
     *
     * @param param
     * @return
     */
    Result<ShelfBizDTO> scanContainer(ScanConstCodeParam param);

    /**
     * 扫描上架单明细商品编码
     *
     * @param param
     * @return
     */
    Result<ShelfDetailBizDTO> scanUpcCode(ScanUpcCodeParam param);

    /**
     * 推荐库位
     */
    Result<String> recommend(RecommendLocationParam param);

    /**
     * @return
     */
    Result<Boolean> scanLocation(ScanLocationCodeParam param);

    /**
     * 获取详情
     *
     * @param param
     * @return
     */
    Result<ShelfBizDTO> getDetail(ShelfBizParam param);

    /**
     * 完成上架单
     */
//    Result<Boolean> complete(CodeParam param);

    /**
     * 完成上架单 (PC)
     *
     * @param param
     * @return
     */
    Result<Boolean> completeWholeShelf(ShelfCompleteParam param);


    /**
     * 完成上架单明细 (RF)
     *
     * @param param
     * @return
     */
    Result<Boolean> completeDetail(ShelfDetailCompleteBizParam param);

    /**
     * 批量上架，已添加目标库位
     *
     * @return
     */
    void BatchShelfMore(String warehouseCode) throws Exception;

    Result<Boolean> commitTaoTianShelf(ShelfBizParam param);

    Result<Boolean> commitTaoTianShelfCheckSku(ShelfBizParam param);

    Result<Integer> saleReturnUnShelfCount();
}
