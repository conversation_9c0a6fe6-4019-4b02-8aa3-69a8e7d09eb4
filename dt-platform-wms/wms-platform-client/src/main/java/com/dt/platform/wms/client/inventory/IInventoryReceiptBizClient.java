package com.dt.platform.wms.client.inventory;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.platform.wms.dto.inventory.InventoryFindTaskBizDTO;
import com.dt.platform.wms.dto.inventory.InventoryReceiptBizDTO;
import com.dt.platform.wms.dto.inventory.InventoryTaskBizDTO;
import com.dt.platform.wms.param.CodeParam;
import com.dt.platform.wms.param.inventory.InventoryReceiptBizParam;
import com.dt.platform.wms.param.inventory.InventoryTaskBizParam;

import java.util.List;


/**
 * <p>
 * 盘点单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-12
 */
public interface IInventoryReceiptBizClient {

    /**
     * 新增盘点单
     *
     * @param inventoryReceiptBizDTO
     * @return
     */
    Result<Boolean> save(InventoryReceiptBizDTO inventoryReceiptBizDTO);

    /**
     * 批量新增盘点单
     *
     * @param inventoryReceiptBizDTOList
     * @return
     */
    Result<Boolean> saveBatch(List<InventoryReceiptBizDTO> inventoryReceiptBizDTOList);

    /**
     * 修改盘点单
     *
     * ID | Code 二选一
     * @param inventoryReceiptBizDTO
     * @return
     */
    Result<Boolean> modify(InventoryReceiptBizDTO inventoryReceiptBizDTO);

    /**
     * 批量修改盘点单
     *
     * @param inventoryReceiptBizDTOList
     * @return
     */
    Result<Boolean> modifyBatch(List<InventoryReceiptBizDTO> inventoryReceiptBizDTOList);

    /**
     * 根据条件查询是否存在
     *
     * @param param
     * @return
     */
    Result<Boolean> checkExits(InventoryReceiptBizParam param);

    /**
     * 获取盘点单
     *
     * @param param
     * @return
     */
    Result<InventoryReceiptBizDTO> get(InventoryReceiptBizParam param);

    /**
     * 获取盘点单列表
     * @param param
     * @return
     */
    Result<List<InventoryReceiptBizDTO>> getList(InventoryReceiptBizParam param);

    /**
     * 分页获取盘点单
     *
     * @param param
     * @return
     */
    Result<Page<InventoryReceiptBizDTO>> getPage(InventoryReceiptBizParam param);

    /**
     * 功能描述:  删除盘点单
     * 创建时间:  2021/1/8 11:22 上午
     *
     * @param param:
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     */
    Result<Boolean> remove(InventoryReceiptBizParam param);

    /**
     * 领取盘点任务
     * @param taskBizParam
     * @return
     */
    Result<Boolean> obtainTask(InventoryTaskBizParam taskBizParam);

    /**
     * 完成盘点任务
     * @param codeParam
     * @return
     */
    Result<Boolean> completeTask(CodeParam codeParam);

    Result<InventoryFindTaskBizDTO> findTask();

    Result<InventoryTaskBizDTO> adviceLocation(CodeParam codeParam);
}

