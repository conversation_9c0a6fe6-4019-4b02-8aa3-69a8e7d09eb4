package com.dt.platform.wms.param.snapshot;

import com.dt.component.common.param.BaseSearchParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;


/**
 * <p>
 * 一级库存快照
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="StockSnapshot对象", description="一级库存快照")
public class StockSnapshotBizParam extends BaseSearchParam  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private Long hid;

    /**
     * 快照时间点
     */
    private Long snapshotTime;
    private Long snapshotTimeBegin;
    private Long snapshotTimeEnd;

    /**
     * 仓库编码
     */
    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    /**
     * 货主编码
     */
    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    /**
     * 商品编码
     */
    @ApiModelProperty(value = "商品编码")
    private String skuCode;

    /**
     * 实物数量
     */
    @ApiModelProperty(value = "实物数量")
    private BigDecimal physicalQty;

    /**
     * 冻结数量
     */
    @ApiModelProperty(value = "冻结数量")
    private BigDecimal frozenQty;

    /**
     * 占用数量
     */
    @ApiModelProperty(value = "占用数量")
    private BigDecimal occupyQty;

    /**
     * 可用数量 实物库存-冻结-占用=可用数
     */
    @ApiModelProperty(value = "可用数量 实物库存-冻结-占用=可用数")
    private BigDecimal availableQty;

    /**
     * 商品属性
     */
    @ApiModelProperty(value = "商品属性")
    private String skuQuality;

    /**
     * 状态码
     */
    @ApiModelProperty(value = "状态码")
    private Integer status;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "修改人")
    private String updatedBy;
}