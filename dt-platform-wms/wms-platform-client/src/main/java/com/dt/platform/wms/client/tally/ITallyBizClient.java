package com.dt.platform.wms.client.tally;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.domain.base.dto.SkuDTO;
import com.dt.domain.base.dto.SkuUpcDTO;
import com.dt.domain.bill.dto.AsnDTO;
import com.dt.domain.bill.dto.tally.TallyDamageSkuImageListDTO;
import com.dt.domain.bill.dto.tally.TallyDetailDTO;
import com.dt.platform.wms.dto.sku.SkuBizDTO;
import com.dt.platform.wms.dto.tally.*;
import com.dt.platform.wms.param.CodeListParam;
import com.dt.platform.wms.param.CodeParam;
import com.dt.platform.wms.param.tally.TallyBizCommitCheckParam;
import com.dt.platform.wms.param.tally.TallyBizParam;
import com.dt.platform.wms.param.tally.TallyDecryptHeadBizParam;
import com.dt.platform.wms.param.tally.TallyDetailBizParam;

import java.util.List;


/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-02-16
 */
public interface ITallyBizClient {

    /**
     * 新增
     *
     * @param tallyBizDTO
     * @return
     */
    Result<String> save(TallyBizDTO tallyBizDTO);

    /**
     * 提交ERP审核
     *
     * @param tallyBizDTO
     * @return
     */
    Result<Boolean> submit(TallyBizDTO tallyBizDTO);

    /**
     * @param asnDTO
     * @return void
     * <AUTHOR>
     * @describe: 入库单校验理货报告
     * @date 2024/7/22 13:17
     */
     void checkTallyType(AsnDTO asnDTO);

    /**
     * @param detailList
     * @param cargoCode
     * @param skuDTOList
     * @param skuUpcDTOList
     * @param asnDTO
     * @param excelImport
     * @param checkQty
     * @return java.util.List<com.dt.domain.bill.dto.tally.TallyDetailDTO>
     * <AUTHOR>
     * @describe: 理货报告校验明细
     * @date 2024/7/19 14:57
     */
     List<TallyDetailDTO> checkBuildDetail(List<TallyDetailDTO> detailList, String cargoCode, List<SkuDTO> skuDTOList, List<SkuUpcDTO> skuUpcDTOList, AsnDTO asnDTO, Boolean excelImport,Boolean checkQty);


        /**
         * @param tallyBizDTO
         * @return
         */
    Result<Boolean> cancel(TallyBizDTO tallyBizDTO);

    /**
     * 修改
     * <p>
     * ID | Code 二选一
     *
     * @param tallyBizDTO
     * @return
     */
    Result<String> modify(TallyBizDTO tallyBizDTO);


    /**
     * 根据条件查询是否存在
     *
     * @param param
     * @return
     */
    Result<Boolean> checkExits(TallyBizParam param);

    /**
     * 获取
     *
     * @param param
     * @return
     */
    Result<TallyBizDTO> get(TallyBizParam param);

    /**
     * 获取列表
     *
     * @param param
     * @return
     */
    Result<List<TallyBizDTO>> getList(TallyBizParam param);

    /**
     * 分页获取
     *
     * @param param
     * @return
     */
    Result<Page<TallyBizDTO>> getPage(TallyBizParam param);

    /**
     * @param param
     * @return com.dt.component.common.result.Result<java.util.List < com.dt.component.common.vo.IdNameValueVO>>
     * @author: WuXian
     * description:获取理货报告的新增品数据，移除当前入库通知单的数据
     * create time: 2022/3/9 10:46
     */
    Result<List<SkuBizDTO>> getTallySkuList(CodeParam param);

    /**
     * @param param
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     * @describe:
     * @date 2022/9/6 14:03
     */
    Result<Boolean> forceCancelTally(CodeParam param);

    /**
     * @param param
     * @return com.dt.component.common.result.Result<java.util.List < java.lang.Object>>
     * <AUTHOR>
     * @describe:
     * @date 2023/4/4 15:41
     */
    Result<List<TallyDecryptBizDTO>> queryTallyValidity(CodeParam param);

    /**
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     * @describe:
     * @date 2023/4/4 16:08
     */
    Result<Boolean> commitDecrypt(TallyDecryptHeadBizParam param);



    List<TallyReceiveDetailBizDTO> getTallyToReceiveData(String tallyCode, List<String> skuCodeList,Boolean isAllReceive);

    /**
     * @param param
     * @return com.dt.component.common.result.Result<com.dt.platform.wms.dto.tally.TallyDetailBizDTO>
     * <AUTHOR>
     * @describe: 获取待复核数据
     * @date 2024/3/13 17:16
     */
    Result<TallyDetailBizDTO> checkTallyDetail(TallyDetailBizParam param);

    /**
     * @param param
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     * @describe: 提交复核数据
     * @date 2024/3/13 17:16
     */
    Result<Boolean> commitCheckTallyDetail(TallyBizCommitCheckParam param);

    void tallyDetailCallBackTaoTian(String param, String warehouseCode);

    TallyDamageSkuImageListDTO damageSkuImageList(String etNo);

    Result<Boolean> deleteDamageSkuImage(CodeListParam codeListParam);

    void zipDamageSkuImage(String etNo) throws Exception;
    /**
     * @param param
     * @return com.dt.component.common.result.Result<com.dt.platform.wms.dto.tally.TallyDetailCheckBizDTO>
     * <AUTHOR>
     * @describe:
     * @date 2025/2/10 15:20
     */
    Result<TallyDetailCheckBizDTO> getWaitCheckTallyDetail(TallyDetailBizParam param);

    Result<Boolean> commitCheckTallyDetailNew(TallyBizCommitCheckParam param);
}

