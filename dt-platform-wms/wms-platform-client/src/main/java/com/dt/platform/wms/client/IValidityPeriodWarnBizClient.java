package com.dt.platform.wms.client;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.domain.core.stock.dto.ValidityPeriodWarnDTO;
import com.dt.domain.core.stock.param.ValidityPeriodWarnParam;

/**
 * Created by nobody on 2021/1/8 13:20
 */
public interface IValidityPeriodWarnBizClient {

    /**
     * 效期预警分页
     * @param param
     * @return
     */
    Result<Page<ValidityPeriodWarnDTO>> getPage(ValidityPeriodWarnParam param);

    /**
     * 初始化
     * @return
     */
    Result<String> init(ValidityPeriodWarnParam param);

    void withdrawAtTomorrowWarning();
}
