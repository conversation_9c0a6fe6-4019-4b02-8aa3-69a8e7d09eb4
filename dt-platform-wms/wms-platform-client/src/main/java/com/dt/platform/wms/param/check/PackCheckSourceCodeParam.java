package com.dt.platform.wms.param.check;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/1/29 11:43
 */
@Data
public class PackCheckSourceCodeParam implements Serializable {

    @ApiModelProperty(value = "溯源码")
    @NotEmpty(message = "溯源码不能为空")
    private String sourceCode;

    @ApiModelProperty(value = "包裹号")
    @NotEmpty(message = "包裹号不能为空")
    private String packageCode;

}
