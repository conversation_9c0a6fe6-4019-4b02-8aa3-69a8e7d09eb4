package com.danding.exporter.excel.exporter;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.danding.exporter.database.stock.entity.StockTransaction;
import com.danding.exporter.database.stock.mapper.StockTransactionMapper;
import com.danding.exporter.database.stock.util.StockTransactionUtil;
import com.danding.exporter.excel.ExcelExportEnum;
import com.danding.exporter.excel.bo.StockTransactionBO;
import com.danding.exporter.rpc.CargoOwnerRpcMapper;
import com.danding.exporter.stock.dto.StockTransactionQry;
import com.dt.component.common.enums.base.ZoneTypeEnum;
import com.dt.component.common.enums.pre.SkuIsPreEnum;
import com.dt.component.common.enums.sku.SkuQualityEnum;
import com.dt.component.common.enums.stock.*;
import com.dt.component.common.filter.RpcContextUtil;
import com.dt.domain.base.dto.CargoOwnerDTO;
import com.dt.platform.utils.ConverterUtil;
import org.apache.ibatis.cursor.Cursor;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Component("stockTransactionWriteEventExporter")
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class StockTransactionExporter extends AbstractDefaultExporter<StockTransaction, StockTransactionBO> {

    @Resource
    private StockTransactionMapper stockTransactionMapper;

    @Resource
    private StockTransactionUtil stockTransactionUtil;

    @Resource
    private CargoOwnerRpcMapper cargoOwnerRpcMapper;

    @Override
    public CommonExcelHandler<StockTransaction, StockTransactionBO> createHandler() {
        return list -> {
            RpcContextUtil.setWarehouseCode(list.get(0).getWarehouseCode());

            List<String> cargoCodeList = list.stream().map(StockTransaction::getCargoCode).distinct().collect(Collectors.toList());
            Map<String, CargoOwnerDTO> cargoOwnerDTOMap = cargoOwnerRpcMapper.cargoOwnerDTOMap(cargoCodeList);

            List<StockTransactionBO> result;
            result = list.stream().flatMap(a -> Stream.of(getStockTransactionBO(a, cargoOwnerDTOMap))).collect(Collectors.toList());
            return result;
        };
    }

    @Override
    public Cursor<StockTransaction> getCursor(Map<String, Object> param) {
        StockTransactionQry stockTransactionQry = JSON.parseObject(JSON.toJSONString(param), StockTransactionQry.class);
        LambdaQueryWrapper<StockTransaction> queryWrapper = stockTransactionUtil.getQueryWrapper(stockTransactionQry);
        return stockTransactionMapper.streamQuery(queryWrapper);
    }

    @Override
    public String getSheetName() {
        return ExcelExportEnum.EXCEL_EXPORT_STOCK_TRANSACTION.getFuncName();
    }

    private StockTransactionBO getStockTransactionBO(StockTransaction stockTransaction,
                                                     Map<String, CargoOwnerDTO> cargoOwnerDTOMap) {
        StockTransactionBO stockTransactionBO = new StockTransactionBO();
        Optional.ofNullable(cargoOwnerDTOMap.get(stockTransaction.getCargoCode())).ifPresent(cargoOwnerDTO -> stockTransactionBO.setCargoName(cargoOwnerDTO.getName()));
        stockTransactionBO.setBillNo(stockTransaction.getBillNo());
        stockTransactionBO.setParentBillNo(stockTransaction.getParentBillNo());
        stockTransactionBO.setGlobalNo(stockTransaction.getGlobalNo());
        stockTransactionBO.setTradeTypeDesc(TradeTypeEnum.getEnum(stockTransaction.getTradeType()).getName());
        stockTransactionBO.setOperationTypeDesc(OperationTypeEnum.getEnum(stockTransaction.getOperationType()).getName());
        stockTransactionBO.setSkuCode(stockTransaction.getSkuCode());
        stockTransactionBO.setChangeQty(stockTransaction.getChangeQty());
        stockTransactionBO.setDoneQty(stockTransaction.getDoneQty());
        stockTransactionBO.setStatusDesc(StockTransactionStatusEnum.getEnum(stockTransaction.getStatus()).getName());
        stockTransactionBO.setStockLevelDesc(StockLevelEnum.getEnum(stockTransaction.getStockLevel()).getName());
        if (StrUtil.isNotBlank(stockTransaction.getZoneType())) {
            stockTransactionBO.setZoneTypeDesc(ZoneTypeEnum.getEnum(stockTransaction.getZoneType()).getName());
        }
        stockTransactionBO.setZoneCode(stockTransaction.getZoneCode());
        stockTransactionBO.setLocationCode(stockTransaction.getLocationCode());
        stockTransactionBO.setSkuQualityDesc(SkuQualityEnum.getEnum(stockTransaction.getSkuQuality()).getMessage());
        stockTransactionBO.setSkuLotNo(stockTransaction.getSkuLotNo());
        stockTransactionBO.setRemark(stockTransaction.getRemark());
        stockTransactionBO.setTradeTimeDesc(ConverterUtil.convertVoTime(stockTransaction.getTradeTime()));
        if (RealOrUnrealGoodsEnum.REAL.getCode().equals(stockTransaction.getRealGoods())) {
            stockTransactionBO.setIsPreDesc(SkuIsPreEnum.NORMAL.getMessage());
        } else {
            stockTransactionBO.setIsPreDesc(SkuIsPreEnum.PRE.getMessage());
        }
        return stockTransactionBO;
    }

}
