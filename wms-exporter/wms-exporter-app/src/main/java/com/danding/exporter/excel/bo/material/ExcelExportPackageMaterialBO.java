package com.danding.exporter.excel.bo.material;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2020/12/23 11:39
 */
@Data
public class ExcelExportPackageMaterialBO implements Serializable {


    @ExcelProperty(value = "货主",index = 3)
    @ColumnWidth(20)
    private String cargoName;

    @ExcelProperty(value = "包材编码",index = 0)
    @ColumnWidth(20)
    private String code;

    @ExcelProperty(value = "包材条码",index = 2)
    @ColumnWidth(20)
    private String barCode;

    @ExcelProperty(value = "是否自购",index = 4)
    @ColumnWidth(20)
    private String isPurchaseName;

    @ExcelProperty(value = "包材名称",index = 1)
    @ColumnWidth(20)
    private String name;

    @ExcelProperty(value = "长(cm)",index = 7)
    @ColumnWidth(20)
    private String length;

    @ExcelProperty(value = "宽(cm)",index = 8)
    @ColumnWidth(20)
    private String width;

    @ExcelProperty(value = "高(cm)",index = 9)
    @ColumnWidth(20)
    private String height;

    @ExcelProperty(value = "包材厚度(cm)",index =10)
    @ColumnWidth(20)
    private String thickness;

    @ExcelProperty(value = "毛重(kg)",index = 6)
    @ColumnWidth(20)
    private String grossWeight;

    @ExcelProperty(value = "状态",index = 5)
    @ColumnWidth(20)
    private String statusName;

    @ExcelProperty(value = "属性")
    @ColumnWidth(20)
    private String typeName;

    @ExcelProperty(value = "4PL包材")
    @ColumnWidth(20)
    private String is4PLName;

    @ExcelProperty(value = "最大承重")
    @ColumnWidth(20)
    private String maxBearing;

    @ExcelProperty(value = "包耗材标记")
    @ColumnWidth(20)
    private String materialTagName;
}
