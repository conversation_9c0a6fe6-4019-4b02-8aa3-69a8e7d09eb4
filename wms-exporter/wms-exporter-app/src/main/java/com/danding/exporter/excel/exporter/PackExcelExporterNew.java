package com.danding.exporter.excel.exporter;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.danding.exporter.database.pack.entity.Package;
import com.danding.exporter.database.pack.mapper.PackageMapper;
import com.danding.exporter.database.pack.util.PackageUtil;
import com.danding.exporter.database.pick.entity.Pick;
import com.danding.exporter.database.pick.entity.PickDetail;
import com.danding.exporter.database.pick.mapper.PickDetailMapper;
import com.danding.exporter.database.pick.mapper.PickMapper;
import com.danding.exporter.database.shipment.entity.ShipmentOrder;
import com.danding.exporter.database.shipment.entity.ShipmentOrderMaterial;
import com.danding.exporter.database.shipment.mapper.ShipmentOrderMapper;
import com.danding.exporter.database.shipment.mapper.ShipmentOrderMaterialMapper;
import com.danding.exporter.database.sku.mapper.SkuMapper;
import com.danding.exporter.excel.ExcelExportEnum;
import com.danding.exporter.excel.bo.pack.ExcelExportPackageNewBO;
import com.danding.exporter.pack.dto.PackageQry;
import com.danding.exporter.rpc.*;
import com.dt.component.common.enums.bill.OrderTagEnum;
import com.dt.component.common.enums.bill.ShipmentOrderEnum;
import com.dt.component.common.enums.bill.ShipmentPreSaleTypeEnum;
import com.dt.component.common.enums.pick.PickEnum;
import com.dt.component.common.enums.pkg.PackEnum;
import com.dt.domain.base.dto.CargoOwnerDTO;
import com.dt.domain.base.dto.SalePlatformDTO;
import com.dt.domain.base.dto.SkuUpcDTO;
import com.dt.domain.base.param.CargoOwnerParam;
import com.dt.domain.base.param.SalePlatformParam;
import com.dt.domain.base.param.SkuUpcParam;
import com.dt.domain.bill.dto.PackageDTO;
import com.dt.platform.utils.ConverterUtil;
import com.dt.platform.utils.LambdaHelpUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.cursor.Cursor;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Data
@Slf4j
@Component("packageWriteNewEventExporterNew")
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class PackExcelExporterNew extends AbstractDefaultExporter<Package, ExcelExportPackageNewBO>  implements java.io.Serializable  {

    @Resource
    private PackageMapper packageMapper;

    @Resource
    private PackageUtil packageUtil;

    @Resource
    private AreaRpcMapper areaRpcMapper;

    @Resource
    private CargoOwnerRpcMapper cargoOwnerRpcMapper;

    @Resource
    private SkuLotRpcMapper skuLotRpcMapper;

    @Resource
    private SkuMapper skuMapper;
    @Resource
    private SkuUpcRpcMapper skuUpcRpcMapper;

    @Resource
    private SalePlatformRpcMapper salePlatformRpcMapper;

    @Resource
    private ShipmentOrderMapper shipmentOrderMapper;

    @Resource
    private PickMapper pickMapper;

    @Resource
    private PickDetailMapper pickDetailMapper;

    @Resource
    private ShipmentOrderMaterialMapper shipmentOrderMaterialMapper;

    private static final String EXPORT_TIME_FORMAT = "yyyy/MM/dd HH:mm:ss";

    @Override
    public CommonExcelHandler<Package, ExcelExportPackageNewBO> createHandler() {
        return packageList -> {
            if (CollectionUtils.isEmpty(packageList)) {
                return Lists.newArrayList();
            }
            List<String> cargoCodeList = packageList.stream().map(Package::getCargoCode).distinct().collect(Collectors.toList());
            Map<String, CargoOwnerDTO> cargoOwnerDTOMap = Maps.newHashMap();
            if (CollectionUtil.isNotEmpty(cargoCodeList)) {
                CargoOwnerParam cargoOwnerParam = new CargoOwnerParam();
                cargoOwnerParam.setCodeList(cargoCodeList);
                cargoOwnerDTOMap.putAll(cargoOwnerRpcMapper.getList(cargoOwnerParam).stream().collect(Collectors.toMap(it -> StrUtil.join(StrUtil.COLON, it.getWarehouseCode(), it.getCode()), Function.identity())));
            }
            SalePlatformParam salePlatformParam = new SalePlatformParam();
            List<SalePlatformDTO> salePlatformDTOList = salePlatformRpcMapper.getList(salePlatformParam);
            Map<String, SalePlatformDTO> salePlatformDTOMap = salePlatformDTOList.stream().collect(Collectors.toMap(it -> it.getCode().toLowerCase(), Function.identity()));
            //获取出库单信息
            List<String> shipmentOrderCodeList = packageList.stream().map(Package::getShipmentOrderCode).distinct().collect(Collectors.toList());
            List<ShipmentOrder> shipmentOrderDTOList = shipmentOrderMapper.selectList(new QueryWrapper<ShipmentOrder>().lambda()
                    .select(ShipmentOrder::getRemark, ShipmentOrder::getReceiverAreaName, ShipmentOrder::getTradeNo, ShipmentOrder::getReceiverCityName,
                            ShipmentOrder::getSaleShop, ShipmentOrder::getReceiverProvName, ShipmentOrder::getPreSaleType, ShipmentOrder::getCreatedTime,
                            ShipmentOrder::getOrderType, ShipmentOrder::getExpShipTime, ShipmentOrder::getShipmentOrderCode)
                    .in(ShipmentOrder::getShipmentOrderCode, shipmentOrderCodeList));
            Map<String, ShipmentOrder> shipmentOrderDTOMap = shipmentOrderDTOList.stream().collect(Collectors.toMap(ShipmentOrder::getShipmentOrderCode, Function.identity()));
            //获取包材信息
            List<ShipmentOrderMaterial> shipmentOrderMaterialDTOList = Lists.newArrayList();
            if (!CollectionUtils.isEmpty(shipmentOrderDTOList)) {
                shipmentOrderMaterialDTOList = shipmentOrderMaterialMapper.selectList(new QueryWrapper<ShipmentOrderMaterial>().lambda()
                        .select(ShipmentOrderMaterial::getShipmentOrderCode, ShipmentOrderMaterial::getRecPackUpcCode).in(ShipmentOrderMaterial::getShipmentOrderCode, shipmentOrderCodeList));
                if (CollectionUtils.isEmpty(shipmentOrderMaterialDTOList)) {
                    shipmentOrderMaterialDTOList = new ArrayList<>();
                }
            }
            Map<String, List<ShipmentOrderMaterial>> shipmentOrderMaterialDTOMap = shipmentOrderMaterialDTOList.stream().collect(Collectors.groupingBy(ShipmentOrderMaterial::getShipmentOrderCode));
            //拣选单号和面单打印次数
            List<PickDetail> pickDetailDTOList = pickDetailMapper.selectList(new QueryWrapper<PickDetail>().lambda()
                    .select(PickDetail::getPickCode, PickDetail::getPackageCode, PickDetail::getPackageStatus)
                    .in(PickDetail::getPackageCode, packageList.stream().map(packageDTO -> packageDTO.getPackageCode()).distinct().collect(Collectors.toList()))
                    .eq(PickDetail::getFlag, PickEnum.PickDetailFlagEnum.ORIGIN.getCode()));
            List<Pick> pickDTOList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(pickDetailDTOList)) {
                pickDTOList = pickMapper.selectList(new QueryWrapper<Pick>().lambda()
                        .select(Pick::getPickCode, Pick::getStatus).in(Pick::getPickCode, pickDetailDTOList.stream().map(pickDetailDTO -> pickDetailDTO.getPickCode()).distinct().collect(Collectors.toList())));
                if (CollectionUtils.isEmpty(pickDTOList)) {
                    pickDTOList = new ArrayList<>();
                }
            }
            Map<String, List<PickDetail>> pickDetailMap = new HashMap<>();
            if (!CollectionUtils.isEmpty(pickDetailDTOList)) {
                pickDetailMap = pickDetailDTOList.stream().collect(Collectors.groupingBy(PickDetail::getPackageCode));
            }
            Map<String, List<PickDetail>> finalPickDetailMap = pickDetailMap;
            List<Pick> finalPickDTOList = pickDTOList;
            List<ExcelExportPackageNewBO> excelExportPackageBOList = new ArrayList<>();
            packageList.stream().forEach(packageDTO -> {
                ExcelExportPackageNewBO excelExportPackageBO = ConverterUtil.convert(packageDTO, ExcelExportPackageNewBO.class);
                ShipmentOrder shipmentOrderDTO = shipmentOrderDTOMap.get(packageDTO.getShipmentOrderCode());
                if (shipmentOrderDTO == null) {
                    excelExportPackageBO.setRemark("");
                    excelExportPackageBO.setReceiverAreaName("");
                    excelExportPackageBO.setReceiverCityName("");
                    excelExportPackageBO.setReceiverProvName("");
                    excelExportPackageBO.setTradeNo("");
                    excelExportPackageBO.setSaleShop("");
                    excelExportPackageBO.setOrdCreateTime("");
                    excelExportPackageBO.setPreSaleTypeName("");
                    excelExportPackageBO.setExpShipTimeDate("");
                    excelExportPackageBO.setTradeType("");
                    excelExportPackageBO.setPackageMaterial("");
                } else {
                    excelExportPackageBO.setRemark(shipmentOrderDTO.getRemark());
                    excelExportPackageBO.setReceiverAreaName(shipmentOrderDTO.getReceiverAreaName());
                    excelExportPackageBO.setReceiverCityName(shipmentOrderDTO.getReceiverCityName());
                    excelExportPackageBO.setReceiverProvName(shipmentOrderDTO.getReceiverProvName());
                    excelExportPackageBO.setTradeNo(shipmentOrderDTO.getTradeNo());
                    excelExportPackageBO.setSaleShop(shipmentOrderDTO.getSaleShop());
                    excelExportPackageBO.setOrdCreateTime(ConverterUtil.convertVoTime(shipmentOrderDTO.getCreatedTime(), EXPORT_TIME_FORMAT));
                    if (!StringUtils.isEmpty(shipmentOrderDTO.getPreSaleType())) {
                        excelExportPackageBO.setPreSaleTypeName(ShipmentPreSaleTypeEnum.fromCode(shipmentOrderDTO.getPreSaleType()).getMessage());
                    } else {
                        excelExportPackageBO.setPreSaleTypeName("");
                    }
                    excelExportPackageBO.setExpShipTimeDate(ConverterUtil.convertVoTime(shipmentOrderDTO.getExpShipTime() == null ? 0L : shipmentOrderDTO.getExpShipTime(), EXPORT_TIME_FORMAT));
                    excelExportPackageBO.setTradeType(ShipmentOrderEnum.ORDER_TYPE.findEnumDesc(shipmentOrderDTO.getOrderType()).getDesc());
                    if (!CollectionUtils.isEmpty(shipmentOrderMaterialDTOMap) && shipmentOrderMaterialDTOMap.containsKey(shipmentOrderDTO.getShipmentOrderCode())) {
                        excelExportPackageBO.setPackageMaterial(shipmentOrderMaterialDTOMap.get(shipmentOrderDTO.getShipmentOrderCode()).stream().map(shipmentOrderMaterialDTO -> shipmentOrderMaterialDTO.getRecPackUpcCode()).collect(Collectors.joining(",")));
                    }
                }
                if (!StringUtils.isEmpty(packageDTO.getIsPre())) {
                    excelExportPackageBO.setPackageType(PackEnum.TYPE.findEnumDesc(packageDTO.getIsPre()).getDesc());
                } else {
                    excelExportPackageBO.setPackageType(PackEnum.TYPE.NORMAL.getCode());
                }
                excelExportPackageBO.setPickCode("");
                if (!CollectionUtils.isEmpty(finalPickDetailMap) && finalPickDetailMap.containsKey(packageDTO.getPackageCode())) {
                    List<PickDetail> detailDTOList = finalPickDetailMap.get(packageDTO.getPackageCode());
                    List<String> pickCodeList = detailDTOList.stream().map(PickDetail::getPickCode).collect(Collectors.toList());
                    List<Pick> pickDTOS = finalPickDTOList.stream().filter(pickDTO -> pickCodeList.contains(pickDTO.getPickCode())).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(pickDTOS)) {
                        if (pickDTOS.size() == 1) {
                            excelExportPackageBO.setPickCode(pickDTOS.get(0).getPickCode());
                        } else {
                            Pick pickDTO = pickDTOS.stream().filter(pick -> !pick.getStatus().equals(PickEnum.PickStatusEnum.CANCEL_STATUS.getCode())).findFirst().orElse(null);
                            if (pickDTO != null) {
                                excelExportPackageBO.setPickCode(pickDTO.getPickCode());
                            }
                        }
                    }
                }
                //TODO 2023-04-04 许总要求注释
//                excelExportPackageBO.setRealWeight((packageDTO.getRealWeight() == null ? BigDecimal.ZERO : packageDTO.getRealWeight()).setScale(3, RoundingMode.FLOOR).toString());
                excelExportPackageBO.setVolumetricWeight((packageDTO.getVolumetricWeight() == null ? BigDecimal.ZERO : packageDTO.getVolumetricWeight()).setScale(3, RoundingMode.FLOOR).toString());
                Optional<CargoOwnerDTO> cargoOwnerDTO = Optional.ofNullable(cargoOwnerDTOMap.get(StrUtil.join(StrUtil.COLON, packageDTO.getWarehouseCode(), packageDTO.getCargoCode())));
                cargoOwnerDTO.ifPresent(it -> excelExportPackageBO.setCargoOwner(it.getName()));
                Optional<SalePlatformDTO> salePlatformDTO = Optional.ofNullable(salePlatformDTOMap.get(packageDTO.getSalePlatform().toLowerCase()));
                salePlatformDTO.ifPresent(it -> excelExportPackageBO.setSalePlatform(it.getName()));
                excelExportPackageBO.setExpSkuQty(packageDTO.getPackageSkuQty());
                excelExportPackageBO.setStatus(PackEnum.STATUS.findEnumDesc(packageDTO.getStatus()).getDesc());
                excelExportPackageBO.setPickCompleteSkuDate(ConverterUtil.convertVoTime(packageDTO.getPickCompleteSkuDate(), EXPORT_TIME_FORMAT));
                excelExportPackageBO.setCheckStartDate(ConverterUtil.convertVoTime(packageDTO.getCheckStartDate(), EXPORT_TIME_FORMAT));
                excelExportPackageBO.setCheckCompleteDate(ConverterUtil.convertVoTime(packageDTO.getCheckCompleteDate(), EXPORT_TIME_FORMAT));
                excelExportPackageBO.setOutStockDate(ConverterUtil.convertVoTime(packageDTO.getOutStockDate(), EXPORT_TIME_FORMAT));
                excelExportPackageBO.setInterceptCancelDate(ConverterUtil.convertVoTime(packageDTO.getInterceptCancelDate(), EXPORT_TIME_FORMAT));
                excelExportPackageBOList.add(excelExportPackageBO);
            });
            return excelExportPackageBOList;
        };
    }

    @Override
    public Cursor<Package> getCursor(Map<String, Object> param) {
        PackageQry packageQry = JSON.parseObject(JSON.toJSONString(param), PackageQry.class);
        buildQry(packageQry);
        //处理地区级联多选
        Map<String, List<String>> mapAreaMap = areaRpcMapper.getHandAreaCascade(packageQry.getReceiverProvList(), packageQry.getReceiverCityList(), packageQry.getReceiverAreaList());
        if (!CollectionUtils.isEmpty(mapAreaMap)) {
            packageQry.setReceiverProvList(mapAreaMap.getOrDefault("receiverProvList", null));
            packageQry.setReceiverCityList(mapAreaMap.getOrDefault("receiverCityList", null));
            packageQry.setReceiverAreaList(mapAreaMap.getOrDefault("receiverAreaList", null));
        }
        //获取包裹查询字段
        packageQry.setTableFieldList(LambdaHelpUtils.convertToFieldNameList(
                PackageDTO::getPackageCode, PackageDTO::getPoNo, PackageDTO::getSoNo, PackageDTO::getShipmentOrderCode,
                PackageDTO::getStatus, PackageDTO::getCargoCode, PackageDTO::getActualPackWeight, PackageDTO::getBusinessType,
                PackageDTO::getIsPre, PackageDTO::getCarrierCode, PackageDTO::getSalePlatform, PackageDTO::getSaleShopId,
                PackageDTO::getVolumetricWeight, PackageDTO::getExpressBranch, PackageDTO::getExpressBranchName, PackageDTO::getExpressNo,
                PackageDTO::getExpressAccount, PackageDTO::getRealWeight, PackageDTO::getRecPackUpc, PackageDTO::getActualPackUpc,
                PackageDTO::getPackageSkuQty, PackageDTO::getOutSkuQty, PackageDTO::getOutStockDate, PackageDTO::getPickCompleteSkuDate,
                PackageDTO::getCheckCompleteDate, PackageDTO::getInterceptCancelDate, PackageDTO::getRemark, PackageDTO::getWarehouseCode,
                PackageDTO::getCheckStartDate, PackageDTO::getCarrierName, PackageDTO::getId
        ));
        LambdaQueryWrapper<Package> queryWrapper = packageUtil.getQueryWrapper(packageQry);
        return packageMapper.streamQuery(queryWrapper);
    }

    private void buildQry(PackageQry packageQry) {
        //处理订单标记
        if (packageQry != null && !StringUtils.isEmpty(packageQry.getOrderTagList())) {
            packageQry.setOrderTag(OrderTagEnum.queryParamListToInteger(packageQry.getOrderTagList()));
        }
        //upc查询
        if (!CollectionUtils.isEmpty(packageQry.getUpcCodeList())) {
            SkuUpcParam skuUpcParam = new SkuUpcParam();
            skuUpcParam.setUpcCodeList(packageQry.getUpcCodeList());
            if (!CollectionUtils.isEmpty(packageQry.getCargoCodeList())) {
                skuUpcParam.setCargoCodeList(packageQry.getCargoCodeList());
            }
            List<SkuUpcDTO> skuUpcDTOList = skuUpcRpcMapper.getSkuUpcList(skuUpcParam);
            if (CollectionUtils.isEmpty(skuUpcDTOList)) {
                packageQry.setCargoCodeList(Arrays.asList("---------------"));
            }
            if (!CollectionUtils.isEmpty(packageQry.getSkuCodeList())) {
                List<String> skuCodeList = packageQry.getSkuCodeList();
                skuCodeList.retainAll(skuUpcDTOList.stream().map(SkuUpcDTO::getSkuCode).collect(Collectors.toList()));
                if (CollectionUtils.isEmpty(skuCodeList)) {
                    packageQry.setCargoCodeList(Arrays.asList("---------------"));
                } else {
                    packageQry.setSkuCodeList(skuCodeList);
                }
            } else {
                packageQry.setSkuCodeList(skuUpcDTOList.stream().map(SkuUpcDTO::getSkuCode).collect(Collectors.toList()));
            }
        }
    }

    @Override
    public String getSheetName() {
        return ExcelExportEnum.EXCEL_EXPORT_NEW_PACKAGE.getFuncName();
    }
}