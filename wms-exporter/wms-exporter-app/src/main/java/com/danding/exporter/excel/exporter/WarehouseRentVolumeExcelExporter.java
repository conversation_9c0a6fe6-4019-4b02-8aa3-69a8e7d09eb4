package com.danding.exporter.excel.exporter;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.danding.exporter.database.rent.entity.WarehouseRentSkuVolume;
import com.danding.exporter.database.rent.mapper.WarehouseRentSkuVolumeMapper;
import com.danding.exporter.database.rent.util.WarehouseRentSkuVolumeUtil;
import com.danding.exporter.database.sku.entity.Sku;
import com.danding.exporter.database.sku.mapper.SkuMapper;
import com.danding.exporter.excel.ExcelExportEnum;
import com.danding.exporter.excel.bo.rent.ExcelExportWarehouseRentBO;
import com.danding.exporter.excel.bo.rent.ExcelExportWarehouseRentSkuVolumeBO;
import com.danding.exporter.rent.dto.WarehouseRentSkuVolumeQry;
import com.danding.exporter.rpc.CargoOwnerRpcMapper;
import com.danding.exporter.rpc.SkuUpcRpcMapper;
import com.danding.exporter.sku.api.ISkuService;
import com.danding.exporter.sku.dto.SkuQry;
import com.danding.exporter.sku.dto.data.SkuDTO;
import com.dt.component.common.enums.sku.SkuUpcDefaultEnum;
import com.dt.domain.base.dto.CargoOwnerDTO;
import com.dt.domain.base.dto.SkuUpcDTO;
import com.dt.domain.base.param.CargoOwnerParam;
import com.dt.domain.base.param.SkuUpcParam;
import com.dt.platform.utils.ConverterUtil;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.cursor.Cursor;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Data
@Slf4j
@Component("warehouseRentVolumeWriteEventExporter")
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class WarehouseRentVolumeExcelExporter extends AbstractDefaultExporter<WarehouseRentSkuVolume, ExcelExportWarehouseRentSkuVolumeBO>  implements java.io.Serializable  {

    @Resource
    private WarehouseRentSkuVolumeMapper warehouseRentSkuVolumeMapper;


    @Resource
    private WarehouseRentSkuVolumeUtil warehouseRentSkuVolumeUtil;

    @Resource
    private CargoOwnerRpcMapper cargoOwnerRpcMapper;

    @Resource
    private SkuMapper skuMapper;

    @Resource
    private ISkuService skuService;

    @Resource
    private SkuUpcRpcMapper skuUpcRpcMapper;

    @Override
    public CommonExcelHandler<WarehouseRentSkuVolume, ExcelExportWarehouseRentSkuVolumeBO> createHandler() {
        return warehouseRentSkuVolumeList -> {
            if (CollectionUtils.isEmpty(warehouseRentSkuVolumeList)) {
                return Lists.newArrayList();
            }
            List<String> cargoCodeList = warehouseRentSkuVolumeList.stream().map(WarehouseRentSkuVolume::getCargoCode).distinct().collect(Collectors.toList());
            List<String> skuCodeList = warehouseRentSkuVolumeList.stream().map(WarehouseRentSkuVolume::getSkuCode).distinct().collect(Collectors.toList());
            //
            List<SkuDTO> skuDTOList = new ArrayList<>();
            List<CargoOwnerDTO> cargoOwnerDTOList = new ArrayList<>();
            List<SkuUpcDTO> skuUpcList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(skuCodeList)) {

//                skuDTOList = skuMapper.selectList(new QueryWrapper<Sku>().lambda().in(Sku::getCode, skuCodeList));

                SkuQry skuQry = new SkuQry();
                skuQry.setCodeList(skuCodeList);
                skuDTOList = skuService.selectList(skuQry);

                CargoOwnerParam cargoOwnerParam = new CargoOwnerParam();
                cargoOwnerParam.setCodeList(cargoCodeList);
                cargoOwnerDTOList = cargoOwnerRpcMapper.getList(cargoOwnerParam);

                SkuUpcParam skuUpcParam = new SkuUpcParam();
                skuUpcParam.setSkuCodeList(skuCodeList);
                skuUpcList = skuUpcRpcMapper.getSkuUpcList(skuUpcParam);
            }

            List<ExcelExportWarehouseRentSkuVolumeBO> excelExportPackageBOList = new ArrayList<>();
            List<SkuDTO> finalSkuDTOList = skuDTOList;
            List<CargoOwnerDTO> finalCargoOwnerDTOList = cargoOwnerDTOList;
            List<SkuUpcDTO> finalSkuUpcList = skuUpcList;
            warehouseRentSkuVolumeList.stream().forEach(warehouseRentSkuVolume -> {
                ExcelExportWarehouseRentSkuVolumeBO excelExportWarehouseRentSkuVolumeBO = ConverterUtil.convert(warehouseRentSkuVolume,ExcelExportWarehouseRentSkuVolumeBO.class);
                finalSkuDTOList.stream()
                        .filter(a -> a.getCargoCode().equalsIgnoreCase(warehouseRentSkuVolume.getCargoCode()))
                        .filter(a -> a.getCode().equalsIgnoreCase(warehouseRentSkuVolume.getSkuCode()))
                        .findFirst().ifPresent(it->{
                            excelExportWarehouseRentSkuVolumeBO.setSkuName(it.getName());
                        });
                finalSkuUpcList.stream()
                        .filter(a -> a.getCargoCode().equalsIgnoreCase(warehouseRentSkuVolume.getCargoCode()))
                        .filter(a -> a.getSkuCode().equalsIgnoreCase(warehouseRentSkuVolume.getSkuCode()))
                        .filter(a -> a.getIsDefault().equals(SkuUpcDefaultEnum.YES.getStatus()))
                        .findFirst().ifPresent(it->{
                            excelExportWarehouseRentSkuVolumeBO.setUpcCode(it.getUpcCode());
                        });
                finalCargoOwnerDTOList.stream()
                        .filter(a -> a.getCode().equalsIgnoreCase(warehouseRentSkuVolume.getCargoCode()))
                        .findFirst().ifPresent(it->{
                            excelExportWarehouseRentSkuVolumeBO.setCargoName(it.getName());
                        });
                excelExportPackageBOList.add(excelExportWarehouseRentSkuVolumeBO);
            });
            return excelExportPackageBOList;
        };
    }

    @Override
    public Cursor<WarehouseRentSkuVolume> getCursor(Map<String, Object> param) {
        WarehouseRentSkuVolumeQry packageQry = JSON.parseObject(JSON.toJSONString(param), WarehouseRentSkuVolumeQry.class);
        buildQry(packageQry);
        LambdaQueryWrapper<WarehouseRentSkuVolume> queryWrapper = warehouseRentSkuVolumeUtil.getQueryWrapper(packageQry);
        return warehouseRentSkuVolumeMapper.streamQuery(queryWrapper);
    }

    private void buildQry(WarehouseRentSkuVolumeQry WarehouseRentParam) {

    }

    @Override
    public String getSheetName() {
        return ExcelExportEnum.DT_EXCEL_EXPORT_WAREHOUSE_RENT_VOLUME.getFuncName();
    }
}