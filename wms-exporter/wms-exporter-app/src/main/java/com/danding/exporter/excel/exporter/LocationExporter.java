package com.danding.exporter.excel.exporter;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.danding.exporter.database.location.entity.Location;
import com.danding.exporter.database.location.mapper.LocationMapper;
import com.danding.exporter.database.location.util.LocationUtil;
import com.danding.exporter.domain.zone.gateway.IZoneGateway;
import com.danding.exporter.excel.ExcelExportEnum;
import com.danding.exporter.excel.bo.LocationBO;
import com.danding.exporter.location.dto.LocationQry;
import com.danding.exporter.rpc.MixRuleRpcMapper;
import com.danding.exporter.rpc.WarehouseRpcMapper;
import com.danding.exporter.zone.dto.ZoneQry;
import com.danding.exporter.zone.dto.data.ZoneDTO;
import com.dt.component.common.enums.base.*;
import com.dt.component.common.enums.sku.SkuQualityEnum;
import com.dt.component.common.enums.stock.SkuShelfTypeEnum;
import com.dt.component.common.enums.stock.StorageRuleEnum;
import com.dt.domain.base.dto.MixRuleDTO;
import com.dt.domain.base.dto.WarehouseDTO;
import com.dt.domain.base.param.MixRuleParam;
import com.dt.domain.base.param.WarehouseParam;
import org.apache.ibatis.cursor.Cursor;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022-09-20 13:48
 */
@Component("locationWriteEventExporter")
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class LocationExporter extends AbstractDefaultExporter<Location, LocationBO> {

    @Resource
    private LocationMapper locationMapper;

    @Resource
    private LocationUtil locationUtil;

    @Resource
    private WarehouseRpcMapper warehouseRpcMapper;

    @Resource
    private MixRuleRpcMapper mixRuleRpcMapper;

    @Resource
    private IZoneGateway zoneGateway;


    @Override
    public CommonExcelHandler<Location, LocationBO> createHandler() {
        return list -> {
            List<String> warehouseCodeList = list.stream().map(Location::getWarehouseCode).distinct().collect(Collectors.toList());
            WarehouseParam warehouseParam = new WarehouseParam();
            warehouseParam.setCodeList(warehouseCodeList);
            Map<String, WarehouseDTO> warehouseDTOMap = warehouseRpcMapper.getList(warehouseParam)
                    .stream().distinct().collect(Collectors.toMap(WarehouseDTO::getCode, Function.identity(), (k1, k2) -> k1));

            List<String> zoneCodeList = list.stream().map(Location::getZoneCode).distinct().collect(Collectors.toList());
            ZoneQry zoneQry = new ZoneQry();
            zoneQry.setCodeList(zoneCodeList);
            Map<String, ZoneDTO> zoneDTOMap = zoneGateway.selectList(zoneQry)
                    .stream().collect(Collectors.toMap(ZoneDTO::getCode, Function.identity(), (k1, k2) -> k1));

            List<String> mixRuleCodeList = list.stream().map(Location::getMixRuleCode).distinct().collect(Collectors.toList());
            MixRuleParam mixRuleParam = new MixRuleParam();
            mixRuleParam.setCodeList(mixRuleCodeList);
            Map<String, MixRuleDTO> mixRuleDTOMap = mixRuleRpcMapper.getList(mixRuleParam)
                    .stream().collect(Collectors.toMap(MixRuleDTO::getCode, Function.identity(), (k1, k2) -> k1));

            return list.stream().map(a -> {
                LocationBO excelLocationBO = new LocationBO();
                excelLocationBO.setThermostaticDesc(ThermostaticEnum.desc(a.getThermostatic()));
                if (!StringUtils.isEmpty(a.getWarehouseCode())) {
                    WarehouseDTO warehouseDTO = warehouseDTOMap.get(a.getWarehouseCode());
                    if (!ObjectUtils.isEmpty(warehouseDTO)) {
                        excelLocationBO.setWarehouseName(warehouseDTO.getName());
                    }
                }
                excelLocationBO.set_length((a.getLength() == null ? BigDecimal.ZERO : a.getLength()).setScale(2, BigDecimal.ROUND_FLOOR).toString());
                excelLocationBO.set_weight((a.getWeight() == null ? BigDecimal.ZERO : a.getWeight()).setScale(2, BigDecimal.ROUND_FLOOR).toString());
                excelLocationBO.set_width((a.getWidth() == null ? BigDecimal.ZERO : a.getWidth()).setScale(2, BigDecimal.ROUND_FLOOR).toString());
                excelLocationBO.set_height((a.getHeight() == null ? BigDecimal.ZERO : a.getHeight()).setScale(2, BigDecimal.ROUND_FLOOR).toString());
                excelLocationBO.set_volume((a.getVolume() == null ? BigDecimal.ZERO : a.getVolume()).setScale(2, BigDecimal.ROUND_FLOOR).toString());
                excelLocationBO.set_maxSkuNum((a.getMaxSkuNum() == null ? "" : a.getMaxSkuNum().toString()));
                excelLocationBO.set_maxTypeNum((a.getMaxTypeNum() == null ? "" : a.getMaxTypeNum().toString()));
                excelLocationBO.setLocationTypeName(LocationTypeEnum.getEnum(a.getType()).getName());
                if (!StringUtils.isEmpty(a.getChargingModel())) {
                    excelLocationBO.setChargingModelDesc(LocationChargingModelEnum.getEnum(a.getChargingModel()).getName());
                } else {
                    excelLocationBO.setChargingModelDesc("");
                }
                excelLocationBO.setPickSeq(a.getPickSeq());
                excelLocationBO.setShelfSeq(a.getShelfSeq());
                excelLocationBO.setStatusName(LocationStatusEnum.getEnum(a.getStatus()).getName());
                excelLocationBO.setTunnelCode(a.getTunnelCode());
                excelLocationBO.setCode(a.getCode());
                excelLocationBO.setZoneCode(a.getZoneCode());
                if (!StringUtils.isEmpty(a.getZoneCode())) {
                    ZoneDTO zoneDTO = zoneDTOMap.get(a.getZoneCode());
                    if (zoneDTO != null) {
                        if (!StringUtils.isEmpty(zoneDTO.getSkuQuality())) {
                            excelLocationBO.setSkuQuality(SkuQualityEnum.getEnum(zoneDTO.getSkuQuality()).getMessage());
                        }
                        excelLocationBO.setZoneName(zoneDTO.getName());
                    }
                }
                excelLocationBO.setUseModeName(LocationUseModeEnum.getEnum(a.getUseMode()).getName());

                if (!StringUtils.isEmpty(a.getStorageRule())) {
                    StorageRuleEnum storageRuleEnum = StorageRuleEnum.getEnum(a.getStorageRule());
                    excelLocationBO.setStorageRuleName(storageRuleEnum.getName());
                }

                if (!StringUtils.isEmpty(a.getMixRuleCode())) {
                    MixRuleDTO mixRuleDTO = mixRuleDTOMap.get(a.getMixRuleCode());
                    if (mixRuleDTO != null) {
                        excelLocationBO.setMixRuleName(mixRuleDTO.getName());
                    }
                }
                SkuShelfTypeEnum shelfTypeEnum = SkuShelfTypeEnum.getEnum(a.getShelfType());
                excelLocationBO.setShelfTypeName(shelfTypeEnum.getName());
                excelLocationBO.setLocationTagName("");
                if (a.getLocationTag() > 0) {
                    excelLocationBO.setLocationTagName(LocationTagEnum.NumToEnum(a.getLocationTag()).stream().map(LocationTagEnum::getDesc).collect(Collectors.joining("|")));
                }
                return excelLocationBO;
            }).collect(Collectors.toList());
        };
    }

    @Override
    public Cursor<Location> getCursor(Map<String, Object> param) {
        LocationQry locationQry = JSON.parseObject(JSON.toJSONString(param), LocationQry.class);
        //订单标记
        if (locationQry != null && !CollectionUtils.isEmpty(locationQry.getLocationTagList())) {
            locationQry.setLocationTag(LocationTagEnum.queryParamListToInteger(locationQry.getLocationTagList()));
        }
        LambdaQueryWrapper<Location> queryWrapper = locationUtil.getQueryWrapper(locationQry);
        return locationMapper.streamQuery(queryWrapper);
    }

    @Override
    public String getSheetName() {
        return ExcelExportEnum.EXCEL_EXPORT_LOCATION.getFuncName();
    }
}
