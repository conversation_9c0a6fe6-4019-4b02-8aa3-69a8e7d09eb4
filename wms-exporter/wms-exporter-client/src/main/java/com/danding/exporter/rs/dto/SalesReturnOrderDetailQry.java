package com.danding.exporter.rs.dto;

import com.danding.cola.dto.BaseSearchParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import java.math.BigDecimal;

/**
 * <p>
 * 销退单详情
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="SalesReturnOrderDetail对象", description="销退单详情")
public class SalesReturnOrderDetailQry extends BaseSearchParam  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;


    /**
     * 销退单
     */
    @ApiModelProperty(value = "销退单")
    private String salesReturnOrderNo;

    /**
     * 商品代码
     */
    @ApiModelProperty(value = "商品代码")
    private String skuCode;

    /**
     * 商品条码
     */
    @ApiModelProperty(value = "商品条码")
    private String upcCode;

    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String skuName;

    /**
     * 应收数量
     */
    @ApiModelProperty(value = "应收数量")
    private BigDecimal expectQty;

    /**
     * 实收正品数量
     */
    @ApiModelProperty(value = "实收正品数量")
    private BigDecimal avlQty;

    /**
     * 实收次品数量
     */
    @ApiModelProperty(value = "实收次品数量")
    private BigDecimal damageQty;

    /**
     * 批次商品属性
     */
    @ApiModelProperty(value = "批次商品属性")
    private String skuQuality;

    /**
     * 生产日期
     */
    @ApiModelProperty(value = "生产日期")
    private Long manufDate;

    /**
     * 失效日期
     */
    @ApiModelProperty(value = "失效日期")
    private Long expireDate;

   /**
    * 创建人
    */
    @ApiModelProperty(value = "创建人")
    private String createdBy;

   /**
    * 修改人
    */
    @ApiModelProperty(value = "修改人")
    private String updatedBy;
}