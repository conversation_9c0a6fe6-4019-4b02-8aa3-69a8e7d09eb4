package com.danding.exporter.transfer.dto;

import com.danding.cola.dto.BaseSearchParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 库存转移
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="Transfer对象", description="库存转移")
public class TransferQry extends BaseSearchParam  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;


    /**
     * 货主编码
     */
    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    /**
     * 转移单编码
     */
    @ApiModelProperty(value = "转移单编码")
    private String code;

    /**
     * 单据状态
     */
    @ApiModelProperty(value = "单据状态")
    private String status;

    /**
     * 转移原因
     */
    @ApiModelProperty(value = "转移原因")
    private String reason;

    /**
     * 转移描叙
     */
    @ApiModelProperty(value = "转移描叙")
    private String note;

    /**
     * 完成日期 (时间戳)
     */
    @ApiModelProperty(value = "完成日期 (时间戳)")
    private Long completeDate;

    /**
     * 操作人
     */
    @ApiModelProperty(value = "操作人")
    private String opBy;

    /**
     * ERP系统对应单号
     */
    @ApiModelProperty(value = "ERP系统对应单号")
    private String erpCode;

    /**
     * 审核说明
     */
    @ApiModelProperty(value = "审核说明")
    private String remark;

   /**
    * 创建人
    */
    @ApiModelProperty(value = "创建人")
    private String createdBy;

   /**
    * 修改人
    */
    @ApiModelProperty(value = "修改人")
    private String updatedBy;

    private List<String> codeList;
    
    private List<String> cargoCodeList;
    private List<String> originSkuCodeList;
    private List<String> originUpcCodeList;
    private String originSkuLotNo;
    private String originZoneCode;
    private String originLocationCode;
    private List<String> originLocationCodeList;
    
    private List<String> targetSkuCodeList;
    private List<String> targetUpcCodeList;
    private String targetZoneCode;
    private String targetLocationCode;
    private List<String> targetLocationCodeList;
    @ApiModelProperty(value = "完成时间start")
    private Long completeDateStart;

    @ApiModelProperty(value = "完成时间end")
    private Long completeDateEnd;
}