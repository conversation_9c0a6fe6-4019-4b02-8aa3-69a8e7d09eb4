package com.danding.exporter.shelf.dto;

import com.danding.cola.dto.BaseSearchParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 下架明细
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="OffShelfDetail对象", description="下架明细")
public class OffShelfDetailQry extends BaseSearchParam  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;


    /**
     * 货主编码
     */
    @ApiModelProperty(value = "货主编码")
    private String cargoCode;
    private List<String> cargoCodeList;

    /**
     * 商品编码
     */
    @ApiModelProperty(value = "商品编码")
    private String skuCode;

    /**
     * 下架库位
     */
    @ApiModelProperty(value = "下架库位")
    private String locationCode;

    /**
     * 批次ID
     */
    @ApiModelProperty(value = "批次ID")
    private String skuLotNo;

    /**
     * 下架单号
     */
    @ApiModelProperty(value = "下架单号")
    private String offShelfCode;
    private List<String> offShelfCodeList;

    /**
     * 下架人
     */
    @ApiModelProperty(value = "下架人")
    private String offShelfBy;

    /**
     * 状态码
     */
    @ApiModelProperty(value = "状态码")
    private String status;

    /**
     * batch serial no
     */
    @ApiModelProperty(value = "batch serial no")
    private String batchSerialNo;

    /**
     * 下架数量
     */
    @ApiModelProperty(value = "下架数量")
    private BigDecimal offQty;

   /**
    * 创建人
    */
    @ApiModelProperty(value = "创建人")
    private String createdBy;

   /**
    * 修改人
    */
    @ApiModelProperty(value = "修改人")
    private String updatedBy;

    private List<String> skuCodeList;
    private List<String> skuLotNoList;
}