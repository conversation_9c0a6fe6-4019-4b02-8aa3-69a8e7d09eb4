package com.danding.exporter.database.stock.util;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.danding.cola.mp.query.QueryWrapper;
import com.danding.exporter.database.stock.entity.StockTransaction;
import com.danding.exporter.stock.dto.StockTransactionQry;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

/**
* <p>
    * 库存交易请求
    * </p>
*
* <AUTHOR>
* @since 2022-09-22
*/
@Component
public class StockTransactionUtil extends QueryWrapper<StockTransaction,StockTransactionQry> {

    @Override
    public LambdaQueryWrapper<StockTransaction> getQueryWrapper(StockTransactionQry param) {
        LambdaQueryWrapper<StockTransaction> lambdaQueryWrapper = getSortWrapper(param).lambda();
        lambdaQueryWrapper
                .eq(!StringUtils.isEmpty(param.getId()), StockTransaction::getId, param.getId())
                .gt(ObjectUtil.isNotEmpty(param.getStartId()),StockTransaction::getId,param.getStartId())
                .in(!CollectionUtils.isEmpty(param.getIdList()), StockTransaction::getId, param.getIdList())
                .gt(!StringUtils.isEmpty(param.getCreatedTimeStart()), StockTransaction::getCreatedTime, param.getCreatedTimeStart())
                .lt(!StringUtils.isEmpty(param.getCreatedTimeEnd()), StockTransaction::getCreatedTime, param.getCreatedTimeEnd())
                .gt(!StringUtils.isEmpty(param.getUpdatedTimeStart()), StockTransaction::getUpdatedTime, param.getUpdatedTimeStart())
                .lt(!StringUtils.isEmpty(param.getUpdatedTimeEnd()), StockTransaction::getUpdatedTime, param.getUpdatedTimeEnd())

                //仓库编码
                .eq(!ObjectUtils.isEmpty(param.getWarehouseCode()), StockTransaction::getWarehouseCode, param.getWarehouseCode())
                //货主编码
                .eq(!ObjectUtils.isEmpty(param.getCargoCode()), StockTransaction::getCargoCode, param.getCargoCode())
                .in(!CollectionUtil.isEmpty(param.getCargoCodeList()), StockTransaction::getCargoCode, param.getCargoCodeList())
                //batch serial no
                .eq(!ObjectUtils.isEmpty(param.getBatchSerialNo()), StockTransaction::getBatchSerialNo, param.getBatchSerialNo())
                .in(CollectionUtil.isNotEmpty(param.getBatchSerialNoList()),StockTransaction::getBatchSerialNo,param.getBatchSerialNoList())
                //transaction serial no
                .eq(!ObjectUtils.isEmpty(param.getTransactionSerialNo()), StockTransaction::getTransactionSerialNo, param.getTransactionSerialNo())
                .in(CollectionUtil.isNotEmpty(param.getTransactionSerialNoList()), StockTransaction::getTransactionSerialNo, param.getTransactionSerialNoList())
                //商品编码
                .eq(!ObjectUtils.isEmpty(param.getSkuCode()), StockTransaction::getSkuCode, param.getSkuCode())
                .in(!CollectionUtil.isEmpty(param.getSkuCodeList()), StockTransaction::getSkuCode, param.getSkuCodeList())
                //商品属性
                .eq(!ObjectUtils.isEmpty(param.getSkuQuality()), StockTransaction::getSkuQuality, param.getSkuQuality())
                //批次ID
                .eq(!ObjectUtils.isEmpty(param.getSkuLotNo()), StockTransaction::getSkuLotNo, param.getSkuLotNo())
                //商品条形码
                .eq(!ObjectUtils.isEmpty(param.getUpcCode()), StockTransaction::getUpcCode, param.getUpcCode())
                //单据类型 BillTypeEnum
                .eq(!ObjectUtils.isEmpty(param.getBillType()), StockTransaction::getBillType, param.getBillType())
                //单据编码
                .eq(!ObjectUtils.isEmpty(param.getBillNo()), StockTransaction::getBillNo, param.getBillNo())
                .in(!CollectionUtil.isEmpty(param.getBillNoList()), StockTransaction::getBillNo, param.getBillNoList())
                //操作类型 与业务类型对应
                .eq(!ObjectUtils.isEmpty(param.getOperationType()), StockTransaction::getOperationType, param.getOperationType())
                //业务类型 TradeTypeEnum
                .eq(!ObjectUtils.isEmpty(param.getTradeType()), StockTransaction::getTradeType, param.getTradeType())
                //库存层级
                .eq(!ObjectUtils.isEmpty(param.getStockLevel()), StockTransaction::getStockLevel, param.getStockLevel())
                //库区类型 ZoneTypeEnum
                .eq(!ObjectUtils.isEmpty(param.getZoneType()), StockTransaction::getZoneType, param.getZoneType())
                //库区编码
                .eq(!ObjectUtils.isEmpty(param.getZoneCode()), StockTransaction::getZoneCode, param.getZoneCode())
                //库位类型 LocationTypeEnum
                .eq(!ObjectUtils.isEmpty(param.getLocationType()), StockTransaction::getLocationType, param.getLocationType())
                //库位编码
                .eq(!ObjectUtils.isEmpty(param.getLocationCode()), StockTransaction::getLocationCode, param.getLocationCode())
                //操作时间
                .eq(!ObjectUtils.isEmpty(param.getTradeTime()), StockTransaction::getTradeTime, param.getTradeTime())
                //父单据编码
                .eq(!ObjectUtils.isEmpty(param.getParentBillNo()), StockTransaction::getParentBillNo, param.getParentBillNo())
                .in(!CollectionUtil.isEmpty(param.getParentBillNoList()), StockTransaction::getParentBillNo, param.getParentBillNoList())
                //状态
                .eq(!ObjectUtils.isEmpty(param.getStatus()), StockTransaction::getStatus, param.getStatus())
                //状态
                .ne(!ObjectUtils.isEmpty(param.getNoStatus()), StockTransaction::getStatus, param.getNoStatus())
                //状态
                .notIn(!ObjectUtils.isEmpty(param.getNoStatusList()), StockTransaction::getStatus, param.getNoStatusList())
        .in(CollectionUtil.isNotEmpty(param.getGlobalNoList()),StockTransaction::getGlobalNo,param.getGlobalNoList())
        ;

        // logic delete 
        lambdaQueryWrapper.eq(param.getNoDeletedData(),StockTransaction::getDeleted, 1);
        lambdaQueryWrapper.last(ObjectUtil.isNotEmpty(param.getForUpdate()) && param.getForUpdate(), " for update");
        return lambdaQueryWrapper;
    }
}
