package com.danding.exporter.rpc;

import com.dt.component.common.enums.cargo.CargoConfigParamEnum;
import com.dt.component.common.result.Result;
import com.dt.domain.base.client.ICargoConfigClient;
import com.dt.domain.base.client.IWarehouseClient;
import com.dt.domain.base.dto.CargoConfigDTO;
import com.dt.domain.base.dto.DecimalPlaceDTO;
import com.dt.domain.base.dto.WarehouseDTO;
import com.dt.domain.base.param.CargoConfigParam;
import com.dt.domain.base.param.WarehouseParam;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class DecimalPlaceRpcMapper {


    @DubboReference
    ICargoConfigClient iCargoConfigClient;

    public List<DecimalPlaceDTO> getList(List<String> cargoCodeList) {
        if (ObjectUtils.isEmpty(cargoCodeList)) {
            return new ArrayList<>();
        }
        List<DecimalPlaceDTO> decimalPlaceDTOList = new ArrayList<>();
        CargoConfigParam param = new CargoConfigParam();
        param.setCargoCodeList(cargoCodeList);
        List<CargoConfigDTO> cargoConfigDTOList = iCargoConfigClient.getList(param).getData();
        Map<String, List<CargoConfigDTO>> cargoConfigMap = cargoConfigDTOList.stream().collect(Collectors.groupingBy(CargoConfigDTO::getCargoCode));
        for (String cargoCode : cargoCodeList) {
            DecimalPlaceDTO decimalPlaceDTO = new DecimalPlaceDTO();
            decimalPlaceDTO.setCargoCode(cargoCode);
            List<CargoConfigDTO> mapList = cargoConfigMap.get(cargoCode);
            if (mapList != null) {
                for (CargoConfigDTO cargoConfigDTO : mapList) {
                    if (cargoConfigDTO.getPropKey().equals(CargoConfigParamEnum.WEIGHT_FORMAT.getCode())) {
                        decimalPlaceDTO.setWeight(Integer.valueOf(cargoConfigDTO.getPropValue()));
                    }
                    if (cargoConfigDTO.getPropKey().equals(CargoConfigParamEnum.VOLUME_FORMAT.getCode())) {
                        decimalPlaceDTO.setVolume(Integer.valueOf(cargoConfigDTO.getPropValue()));
                    }
                    if (cargoConfigDTO.getPropKey().equals(CargoConfigParamEnum.LENGTH_WIDTH_HEIGHT_FORMAT.getCode())) {
                        decimalPlaceDTO.setLengthWidthHeight(Integer.valueOf(cargoConfigDTO.getPropValue()));
                    }
                    if (cargoConfigDTO.getPropKey().equals(CargoConfigParamEnum.NUMBER_FORMAT.getCode())) {
                        decimalPlaceDTO.setNumber(Integer.valueOf(cargoConfigDTO.getPropValue()));
                    }
                }
            }
            decimalPlaceDTOList.add(decimalPlaceDTO);
        }
        return decimalPlaceDTOList;
    }
}
