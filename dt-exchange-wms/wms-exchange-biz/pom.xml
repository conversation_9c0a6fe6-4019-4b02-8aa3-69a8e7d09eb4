<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.dt</groupId>
        <artifactId>dt-exchange-wms</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <groupId>com.dt</groupId>
    <artifactId>wms-exchange-biz</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <name>wms-exchange-biz</name>
    <description>WMS业务逻辑</description>
    <properties>
        <java.version>1.8</java.version>
        <http.request.version>6.0</http.request.version>
        <exchange.wms.version>3.2.5-RELEASE</exchange.wms.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-spring-boot-starter</artifactId>
            <version>2.1.1</version>
        </dependency>
        <!--服务配置-->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        <!--数据传输对象-->
        <dependency>
            <groupId>com.dt</groupId>
            <artifactId>wms-exchange-client</artifactId>
            <version>${exchange.wms.version}</version>
        </dependency>
        <!--第三方集成-->
        <dependency>
            <groupId>com.dt</groupId>
            <artifactId>wms-platform-client</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>cds-out-rpc</artifactId>
            <version>3.1.4-RELEASE</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.dt</groupId>
            <artifactId>wms-exchange-integration</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dt</groupId>
            <artifactId>dt-component-utils</artifactId>
        </dependency>

        <dependency>
            <groupId>com.dt</groupId>
            <artifactId>dt-component-uid</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dt</groupId>
            <artifactId>dt-component-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dt</groupId>
            <artifactId>dt-component-canal</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dt</groupId>
            <artifactId>dt-component-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.kevinsawicki</groupId>
            <artifactId>http-request</artifactId>
            <version>${http.request.version}</version>
        </dependency>
        <!--test-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <!--测试数据库-->
        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>ares-config-rpc-client</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>dubbo</artifactId>
                    <groupId>org.apache.dubbo</groupId>
                </exclusion>
            </exclusions>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
