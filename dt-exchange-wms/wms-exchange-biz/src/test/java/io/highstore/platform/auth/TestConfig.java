//package io.highstore.platform.auth;
//
//import io.highstore.platform.auth.middleware.mq.process.BondedSink;
//import io.highstore.platform.auth.middleware.mq.process.BondedSource;
//import org.mybatis.spring.annotation.MapperScan;
//import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
//import org.springframework.cloud.openfeign.EnableFeignClients;
//import org.springframework.cloud.stream.annotation.EnableBinding;
//import org.springframework.cloud.stream.messaging.Source;
//import org.springframework.context.annotation.ComponentScan;
//import org.springframework.context.annotation.Configuration;
//
///**
// * 业务模块配置类
// *
// * <AUTHOR> yz
// * @return
// * @date 2019/10/16
// */
//@EnableAutoConfiguration
//@Configuration
//@ComponentScan(basePackageClasses = TestConfig.class)
//@MapperScan("io.highstore.**.mapper")
//@EnableFeignClients(basePackages = {"io.highstore.domain", "io.highstore.platform"})
//@EnableBinding({BondedSink.class, BondedSource.class})
//public class TestConfig {
//}
