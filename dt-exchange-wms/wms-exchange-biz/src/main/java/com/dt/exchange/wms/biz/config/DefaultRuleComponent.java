package com.dt.exchange.wms.biz.config;
import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.exceptions.BaseException;
import com.dt.domain.base.dto.AllocationRuleDTO;
import com.dt.domain.base.dto.LotRuleDTO;
import com.dt.domain.base.dto.TurnoverRuleDTO;
import com.dt.exchange.wms.integration.IRemoteAllocationRuleClient;
import com.dt.exchange.wms.integration.IRemoteLotRuleClient;
import com.dt.exchange.wms.integration.IRemoteTurnoverRuleClient;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;

@Component
public class DefaultRuleComponent {
    @Resource
    private IRemoteAllocationRuleClient remoteAllocationRuleClient;

    @Resource
    private IRemoteLotRuleClient remoteLotRuleClient;

    @Resource
    private IRemoteTurnoverRuleClient remoteTurnoverRuleClient;



    public DefaultRuleEntity getDefaultRuleEntity()
    {
        LotRuleDTO lotRuleDTO = null;
        try {
            lotRuleDTO = remoteLotRuleClient.getDefaultRule();
            if(lotRuleDTO == null)
            {
                throw new BaseException(BaseBizEnum.TIP, "默认批次规则查询为空");
            }
        }catch (Exception ex)
        {
            throw new BaseException(BaseBizEnum.TIP, "默认批次规则查询异常");
        }

        AllocationRuleDTO allocationRuleDTO = null;
        try {
            allocationRuleDTO = remoteAllocationRuleClient.getDefaultRule();
            if(allocationRuleDTO == null)
            {
                throw new BaseException(BaseBizEnum.TIP, "默认分配规则查询为空");
            }
        }catch (Exception ex)
        {
            throw new BaseException(BaseBizEnum.TIP, "默认分配规则查询异常");
        }

        TurnoverRuleDTO turnoverRuleDTO = null;
        try {
            turnoverRuleDTO = remoteTurnoverRuleClient.getDefaultRule();
            if(turnoverRuleDTO == null)
            {
                throw new BaseException(BaseBizEnum.TIP, "默认周转规则查询为空");
            }
        }catch (Exception ex)
        {
            throw new BaseException(BaseBizEnum.TIP, "默认周转规则查询异常");
        }
        DefaultRuleEntity defaultRuleEntity = new DefaultRuleEntity();
        defaultRuleEntity.setAllocationRuleDTO(allocationRuleDTO);
        defaultRuleEntity.setLotRuleDTO(lotRuleDTO);
        defaultRuleEntity.setTurnoverRuleDTO(turnoverRuleDTO);
        return defaultRuleEntity;
    }

}
