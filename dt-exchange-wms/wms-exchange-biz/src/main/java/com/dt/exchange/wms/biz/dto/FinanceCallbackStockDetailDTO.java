package com.dt.exchange.wms.biz.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/20 9:01
 */
@Data
public class FinanceCallbackStockDetailDTO implements Serializable {

    @ApiModelProperty(value = "外部批次号")
    private String batchCode;

    @ApiModelProperty(value = "wms批次号")
    private String internalBatchCode;

    @ApiModelProperty(value = "sku货品编码")
    private String sku;

    @ApiModelProperty(value = "数量")
    private BigDecimal qty;

    @ApiModelProperty(value = "1正品，2次品")
    private Integer inventoryType;

    @ApiModelProperty(value = "生产日期")
    private Long productionDate;

    @ApiModelProperty(value = "失效日期")
    private Long expireDate;

    @ApiModelProperty(value = "条码")
    private String barcode;

    @ApiModelProperty(value = "货品名称")
    private String goodsName;

    @ApiModelProperty(value = "生产批次号")
    private String productBatchCode;

    @ApiModelProperty(value = "入库日期")
    private Long inStockTime;


}
