package com.dt.exchange.wms.biz.config;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;

@Data
@Component
@ConfigurationProperties(prefix = "dt-wms.config.other")
@RefreshScope
public class WarehouseOtherConfig  implements java.io.Serializable  {

    @ApiModelProperty(value = "字节泡沫箱")
    private List<String> foamBoxCodeList;

    @ApiModelProperty(value = "淘天仓库")
    private List<String> taotainWarehouseCodeList;


    public Boolean isTaoTian(String warehouseCode) {
        if (!CollectionUtils.isEmpty(getTaotainWarehouseCodeList()) && getTaotainWarehouseCodeList().contains(warehouseCode)) {
            return true;
        }
        return false;
    }


}