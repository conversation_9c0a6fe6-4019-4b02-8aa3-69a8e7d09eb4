package com.dt.exchange.wms.biz.mq;

import cn.hutool.core.util.StrUtil;
import com.alibaba.otter.canal.protocol.FlatMessage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.dt.component.canal.mq.AbstractCanalMQService;
import com.dt.component.common.enums.asn.AsnStatusEnum;
import com.dt.component.common.exceptions.BaseException;
import com.dt.component.common.filter.RpcContextUtil;
import com.dt.component.common.result.Result;
import com.dt.domain.bill.dto.AsnDTO;
import com.dt.exchange.wms.biz.INotifyBiz;
import com.dt.exchange.wms.enums.NotifyCallBackStatusEnum;
import com.dt.exchange.wms.integration.IRemoteAsnClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 功能描述:  入库单回传
 * 创建时间:  2022/1/10 11:28 上午
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = "dt_wms_asn_topic", consumerGroup = "asn_order_notify")
public class AsnCallBackConsumer extends AbstractCanalMQService<AsnDTO> implements RocketMQListener<FlatMessage> {


    @Resource
    private INotifyBiz notifyBiz;

    @Resource
    private IRemoteAsnClient remoteAsnClient;


    @Override
    public void onMessage(FlatMessage message) {
        process(message);
    }

    /**
     * 新增操作
     *
     * @param asnDTO
     */
    @Override
    public void insert(AsnDTO asnDTO) {
        //新增增加入库单运单号识别。
        Pattern pattern = Pattern.compile("运单号[:|：]([A-Za-z]*\\d*\\w*|.{0,22})[,$|，$]?");
        Matcher m = pattern.matcher(asnDTO.getRemark());
        if (m.find()) {
            String result = m.group(1);
            if (StrUtil.isNotEmpty(result)) {
                RpcContextUtil.setWarehouseCode(asnDTO.getWarehouseCode());
                if (StrUtil.isEmpty(asnDTO.getExpressNo())) {
                    AsnDTO newAsnDTO = new AsnDTO();
                    newAsnDTO.setId(asnDTO.getId());
                    newAsnDTO.setExpressNo(result);
                    remoteAsnClient.updateById(newAsnDTO);
                }
                RpcContextUtil.clearAttachments();
            }
        }
    }


    /**
     * 对于更新操作来讲，before 中的属性只包含变更的属性，after 包含所有属性，通过对比可发现那些属性更新了
     *
     * @param before
     * @param after
     */
    @Override
    public void update(AsnDTO before, AsnDTO after) {
        if (ObjectUtils.isNotEmpty(before.getStatus()) && Objects.equals(after.getStatus(), AsnStatusEnum.COMPLETE.getCode())
                && (Objects.equals(after.getNotifyStatus(), NotifyCallBackStatusEnum.INIT.getCode()) ||Objects.equals(after.getNotifyStatus(), NotifyCallBackStatusEnum.PART_CALLBACK.getCode()))
                && !after.getWarehouseCode().equalsIgnoreCase("DT_HNWMS")) {
            RpcContextUtil.setWarehouseCode(after.getWarehouseCode());
            Result<String> result = notifyBiz.doNotifyAsn(after.getAsnId());
            //重复回传错误编码，直接忽略
            if (new Integer(-200001).equals(result.getCode())) {
                return;
            }
            if (!new Integer(200).equals(result.getCode())) {
                if (!System.getenv("SPRING_PROFILES_ACTIVE").equalsIgnoreCase("prod")) {
                    log.error(String.format("入库单单回传失败，失败原因：[%s] ，所属仓库：[%s] ，单据号：[%s]", result.getMessage(), after.getWarehouseCode(), after.getAsnId()));
                    return;
                }
                //抛出异常进行mq 重试消费。
                throw new BaseException(result);
            }
        }
    }


    /**
     * 删除操作
     *
     * @param shipmentOrderBizDTO
     */
    @Override
    public void delete(AsnDTO shipmentOrderBizDTO) {

    }


}
