package com.dt.exchange.wms.biz.mq;

import com.alibaba.otter.canal.protocol.FlatMessage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.dt.component.canal.mq.AbstractCanalMQService;
import com.dt.component.common.enums.tally.TallyStatusEnum;
import com.dt.domain.bill.dto.tally.TallyDTO;
import com.dt.exchange.wms.biz.INotifyBiz;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 功能描述:  出库单仓轨迹
 * 创建时间:  2021/1/12 5:06 下午
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = "dt_wms_tally_topic", consumerGroup = "tally_cancel_notify_ccs", consumeMode = ConsumeMode.ORDERLY)
public class TallyCancelNotifyCCSCallBackConsumer extends AbstractCanalMQService<TallyDTO> implements RocketMQListener<FlatMessage> {


    @Resource
    private INotifyBiz notifyBiz;


    @Override
    public void onMessage(FlatMessage message) {
        process(message);
    }

    /**
     * 新增操作
     *
     * @param tallyDTO
     */
    @Override
    public void insert(TallyDTO tallyDTO) {

    }


    /**
     * 对于更新操作来讲，before 中的属性只包含变更的属性，after 包含所有属性，通过对比可发现那些属性更新了
     *
     * @param before
     * @param after
     */
    @Override
    public void update(TallyDTO before, TallyDTO after) {
        //修改的状态为空时不做逻辑操作，必须是修改状态时才走逻辑
        if (ObjectUtils.isNull(before.getStatus()) || ObjectUtils.isNull(after.getStatus())) {
            return;
        }
        //状态相同时不做处理
        if (Objects.equals(before.getStatus(), after.getStatus())) {
            return;
        }
        //状态码从审核通过到取消(通知erp)
        if(!after.getWarehouseCode().equalsIgnoreCase("DT_HNWMS") &&
                Objects.equals(before.getStatus(), TallyStatusEnum.SUCCESS_AUTH.getCode()) && Objects.equals(after.getStatus(), TallyStatusEnum.CANCEL.getCode())){
            notifyBiz.tallyCancelNotifyCCS(after);
        }

    }


    /**
     * 删除操作
     *
     * @param tallyDTO
     */
    @Override
    public void delete(TallyDTO tallyDTO) {
        //你的逻辑
    }


}
