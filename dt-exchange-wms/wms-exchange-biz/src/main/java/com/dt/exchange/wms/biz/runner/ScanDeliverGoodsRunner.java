//package com.dt.exchange.wms.biz.runner;
//
//import com.dt.exchange.wms.biz.config.WarehouseConfig;
//import com.dt.exchange.wms.client.INotifyBizClient;
//import com.dt.exchange.wms.enums.NotifyCallBackStatusEnum;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.concurrent.BasicThreadFactory;
//import org.apache.dubbo.config.annotation.DubboReference;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.CommandLineRunner;
//import org.springframework.boot.context.properties.ConfigurationProperties;
//import org.springframework.context.annotation.Profile;
//import org.springframework.stereotype.Component;
//
//import java.util.List;
//import java.util.concurrent.ScheduledExecutorService;
//import java.util.concurrent.ScheduledThreadPoolExecutor;
//import java.util.concurrent.ThreadFactory;
//import java.util.concurrent.TimeUnit;
//
//@Slf4j
//@Component
//@Profile({"test", "prod"})
//public class ScanDeliverGoodsRunner implements CommandLineRunner {
//
//    @Autowired
//    private WarehouseConfig warehouseConfig;
//    @DubboReference
//    private INotifyBizClient notifyBizClient;
//    private static final ThreadFactory signThreadFactory = new BasicThreadFactory
//            .Builder()
//            .namingPattern("ScanDeliverGoodsRunner-process-%d")
//            .daemon(true)
//            .build();
//    private static final ScheduledExecutorService deliverExecutor = new ScheduledThreadPoolExecutor(5, signThreadFactory);
//
//    private static final ScheduledExecutorService tryFailExecutor = new ScheduledThreadPoolExecutor(5, signThreadFactory);
//
//    @Override
//    public void run(String... args) throws Exception {
//        if (warehouseConfig.getWarehouseCodeList().isEmpty()) {
//            log.error("该定时任务没有设置仓库编号!");
//            return;
//        }
//        for (String warehouseCode : warehouseConfig.getWarehouseCodeList()) {
//            Runnable scanPackageNotify = () -> {
//                try {
//                    notifyBizClient.scanShipmentOrder(NotifyCallBackStatusEnum.INIT.getCode(), warehouseCode);//出库单 dt_shipment_order
//                } catch (Exception e) {
//
//                }
//            };
//            Runnable tryPackageNotify = () -> {
//                try {
//                    notifyBizClient.scanShipmentOrder(NotifyCallBackStatusEnum.FAIL.getCode(), warehouseCode);
//                } catch (Exception e) {
//
//                }
//            };
//            /**
//             * 说明下：scheduleAtFixedRate，如果执行当前任务 耗时大于 delay,得小心，
//             *
//             * scheduleWithFixedDelay 比较复合需求
//             *
//             */
//            deliverExecutor.scheduleWithFixedDelay(scanPackageNotify, 0, 30, TimeUnit.SECONDS);
//            tryFailExecutor.scheduleWithFixedDelay(tryPackageNotify, 0, 1, TimeUnit.DAYS);
//        }
//    }
//}
