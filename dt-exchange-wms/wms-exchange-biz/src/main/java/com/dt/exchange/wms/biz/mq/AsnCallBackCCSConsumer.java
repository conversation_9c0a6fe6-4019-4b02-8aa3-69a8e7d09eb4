package com.dt.exchange.wms.biz.mq;

import com.alibaba.otter.canal.protocol.FlatMessage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.dt.component.canal.mq.AbstractCanalMQService;
import com.dt.component.common.enums.asn.AsnStatusEnum;
import com.dt.component.common.enums.asn.AsnTypeEnum;
import com.dt.component.common.exceptions.BaseException;
import com.dt.component.common.filter.RpcContextUtil;
import com.dt.component.common.result.Result;
import com.dt.domain.bill.dto.AsnDTO;
import com.dt.exchange.wms.biz.INotifyBiz;
import com.dt.exchange.wms.biz.config.WarehouseConfig;
import com.dt.exchange.wms.integration.IRemoteAsnClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Objects;

/**
 * 功能描述:  入库单回传
 * 创建时间:  2022/1/10 11:28 上午
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = "dt_wms_asn_topic", consumerGroup = "asn_order_notify_ccs")
public class AsnCallBackCCSConsumer extends AbstractCanalMQService<AsnDTO> implements RocketMQListener<FlatMessage> {


    @Resource
    private INotifyBiz notifyBiz;

    @Resource
    private IRemoteAsnClient remoteAsnClient;

    @Resource
    private WarehouseConfig warehouseConfig;


    @Override
    public void onMessage(FlatMessage message) {
        process(message);
    }

    /**
     * 新增操作
     *
     * @param asnDTO
     */
    @Override
    public void insert(AsnDTO asnDTO) {

    }


    /**
     * 对于更新操作来讲，before 中的属性只包含变更的属性，after 包含所有属性，通过对比可发现那些属性更新了
     *
     * @param before
     * @param after
     */
    @Override
    public void update(AsnDTO before, AsnDTO after) {
        if (ObjectUtils.isNotEmpty(before.getStatus()) && Objects.equals(after.getStatus(), AsnStatusEnum.COMPLETE.getCode())) {
            //只针对配置的仓库操作回传ccs
            if (CollectionUtils.isEmpty(warehouseConfig.getWarehouseCodeCallBackCcsList()) ||
                    !warehouseConfig.getWarehouseCodeCallBackCcsList().contains(after.getWarehouseCode())) {
                return;
            }
            //采购，调拨，其他入库回传ccs
            if (!Arrays.asList(AsnTypeEnum.OTHERS.getCode(), AsnTypeEnum.PURCHASE.getCode(), AsnTypeEnum.TRANSFER.getCode(),AsnTypeEnum.PURCHASE_SUPERVISE.getCode(),AsnTypeEnum.PURCHASE_REDEEM.getCode()).contains(after.getType())) {
                return;
            }
            RpcContextUtil.setWarehouseCode(after.getWarehouseCode());
            Result<String> result = notifyBiz.doNotifyCCSAsn(after.getAsnId());
            if (!new Integer(200).equals(result.getCode())) {
                if (!System.getenv("SPRING_PROFILES_ACTIVE").equalsIgnoreCase("prod")) {
                    log.error(String.format("入库单单特定仓库回传CCS失败，失败原因：[%s] ，所属仓库：[%s] ，单据号：[%s]", result.getMessage(), after.getWarehouseCode(), after.getAsnId()));
                    return;
                }
                //抛出异常进行mq 重试消费。TODO 目前线上调试阶段不做重试
//                throw new BaseException(result);
            }
        }
    }


    /**
     * 删除操作
     *
     * @param shipmentOrderBizDTO
     */
    @Override
    public void delete(AsnDTO shipmentOrderBizDTO) {

    }


}
