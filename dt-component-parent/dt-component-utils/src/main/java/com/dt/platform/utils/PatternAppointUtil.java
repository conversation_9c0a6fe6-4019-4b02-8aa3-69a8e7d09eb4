package com.dt.platform.utils;

import org.springframework.util.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 指定字段的正则校验
 *
 * <AUTHOR>
 * @date 2021/7/19 9:33
 */
public class PatternAppointUtil {

    /**
     * 支持中英文，数字，符号，中间空格
     */
//    public static final String ZH_EN_NUM_SYMBOL = "[0-9a-zA-Z|\u4e00-\u9fa5|\\s|\\pP|\\^|\\$|\\~|\\`]*";
    public static final String ZH_EN_NUM_SYMBOL = "^[0-9a-zA-Z\\u4e00-\\u9fa5-.。~/·=\\[【\\]】《|…—》 \\\\、`！!@#￥$%^&*（({})）_+:：；;“”’,，\"<>？?‘’…]+$";
    /**
     * 支持英文大小写,数字,横杠
     */
    public static final String EN_NUM = "[A-Za-z0-9-]*";
    /**
     * 中英文和·
     */
    public static final String ZH_EN_POINT = "[a-zA-Z|\u4e00-\u9fa5|·]*";
    /**
     * 手机号
     */
    public static final String PHONE = "\\d{11}";
    /**
     * 座机号
     */
    public static final String TEL = "^[0][1-9]{2,3}-[0-9]{7,8}$";
    public static final String PHONE_TEL = "^0\\d{2,3}-\\d{7,8}|\\d{11}$";

    /**
     * 支持中英文，数字，中间空格，中划线
     */
    public static final String ZH_EN_NUM_SYMBOL_ONE = "[0-9a-zA-Z|\u4e00-\u9fa5|\\s|\\-]*";
    /**
     * 表情
     */
    public static final String EMOJI = "[\\ud83c\\udc00-\\ud83c\\udfff]|[\\ud83d\\udc00-\\ud83d\\udfff]|[\\u2600-\\u27ff]";

    /**
     * @param str
     * @return java.lang.Boolean
     * @author: WuXian
     * description:  支持英文大小写,数字,横杠
     * create time: 2021/7/19 9:42
     */
    public static Boolean checkENAndNumber(String str) {
        if (!Pattern.matches(EN_NUM, StringUtils.isEmpty(str) ? "" : str.trim())) {
            return false;
        }
        return true;
    }

    /**
     * @param str
     * @return java.lang.Boolean
     * @author: WuXian
     * description:  支持中英文，数字，符号，中间空格
     * create time: 2021/7/19 9:42
     */
    public static Boolean checkZHAndENAndNumber(String str) {
        if (!Pattern.matches(ZH_EN_NUM_SYMBOL, StringUtils.isEmpty(str) ? "" : str.trim())) {
            return false;
        }
        return true;
    }

    /**
     * @param str
     * @return java.lang.Boolean
     * @author: WuXian
     * description:  支持中英文，数字，符号，中间空格
     * create time: 2021/7/19 9:42
     */
    public static Boolean checkZHAndENAndNumberOne(String str) {
        if (!Pattern.matches(ZH_EN_NUM_SYMBOL_ONE, StringUtils.isEmpty(str) ? "" : str.trim())) {
            return false;
        }
        return true;
    }

    /**
     * @param str
     * @return java.lang.Boolean
     * @author: WuXian
     * description:  校验手机号
     * create time: 2021/7/19 9:42
     */
    public static Boolean checkPhone(String str) {
        if (!Pattern.matches(PHONE, StringUtils.isEmpty(str) ? "" : str.trim())) {
            return false;
        }
        return true;
    }

    /**
     * @param str
     * @return java.lang.Boolean
     * @author: WuXian
     * description:  校验手机号和座机
     * create time: 2021/7/19 9:42
     */
    public static Boolean checkPhoneAndTel(String str) {
        if (!Pattern.matches(PHONE_TEL, StringUtils.isEmpty(str) ? "" : str.trim())) {
            return false;
        }
        return true;
    }

    /**
     * @param str
     * @return java.lang.Boolean
     * @author: WuXian
     * description:  校验座机号
     * create time: 2021/7/19 9:42
     */
    public static Boolean checkTel(String str) {
        if (!Pattern.matches(TEL, StringUtils.isEmpty(str) ? "" : str.trim())) {
            return false;
        }
        return true;
    }

    /**
     * @param str
     * @return java.lang.Boolean
     * @author: WuXian
     * description:  支持中英文和·
     * create time: 2021/7/19 9:42
     */
    public static Boolean checkZHAndENAndPoint(String str) {
        if (!Pattern.matches(ZH_EN_POINT, StringUtils.isEmpty(str) ? "" : str.trim())) {
            return false;
        }
        return true;
    }

    /**
     * @param nick_name
     * @return java.lang.String
     * @author: WuXian
     * description:  过滤表情
     * create time: 2021/8/12 11:13
     */
    public static String filterEmoji(String nick_name) {
        //nick_name 所获取的用户昵称
        if (nick_name == null) {
            return nick_name;
        }
        Pattern emoji = Pattern.compile(EMOJI, Pattern.UNICODE_CASE | Pattern.CASE_INSENSITIVE);
        Matcher emojiMatcher = emoji.matcher(nick_name);
        if (emojiMatcher.find()) {
            //将所获取的表情转换为*
            nick_name = emojiMatcher.replaceAll("");
            return nick_name;
        }
        return nick_name;
    }

    public static void main(String[] args) {
//        System.out.println(checkENAndNumber("SBcd12-"));
//        System.out.println(checkZHAndENAndNumber("SBcd    12-哈哈哈*&^%$#@"));
//        System.out.println(checkZHAndENAndNumberOne("哈哈哈 za"));
        //匹配固定电话  4位区号-7位号码 或者 3位区号-8位号码
//        String regex2="^0\\d{2,3}-\\d{7,8}|\\d{11}$";
//        System.out.println(Pattern.matches(regex2,"13945699856"));
//        System.out.println(Pattern.matches(regex2,"1571-12345678"));
        System.out.println(checkZHAndENAndNumber("~！我是谁哈哈哈哈啊！@#￥%……&*（）——+|}{}：“”？》《·-=、【；‘、，/.,’;][\\=-)(*&^%$#@!~`-=\\][;’/.,/*-+..’】》"));


    }

}
