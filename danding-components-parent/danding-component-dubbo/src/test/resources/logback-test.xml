<configuration>
    <include resource="org/springframework/boot/logging/logback/defaults.xml" />


    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
       <encoder>
           <pattern>%-4relative [%thread] %-5level %logger{35} - %msg%n</pattern>
           <charset>utf8</charset>
       </encoder>
   </appender>

   <!--rootLogger是默认的logger-->
   <root level="INFO">
       <!--定义了两个appender，日志会通过往这两个appender里面写-->
       <appender-ref ref="CONSOLE"/>
   </root>

   <!--应用日志-->
   <!--这个logger没有指定appender，它会继承root节点中定义的那些appender-->
   <logger name="com.danding.cola" level="DEBUG"/>

</configuration>
