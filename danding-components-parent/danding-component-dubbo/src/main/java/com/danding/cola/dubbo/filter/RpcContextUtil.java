package com.danding.cola.dubbo.filter;

import com.danding.cola.dubbo.holder.CurrentRouteHolder;
import org.apache.dubbo.rpc.RpcContext;

/**
 * 功能描述:
 * 创建时间:  2020/12/30 9:05 上午
 *
 * <AUTHOR>
 */
public class RpcContextUtil {

    public static String DT_ROUTE_WAREHOUSE_CODE = "DT_ROUTE_WAREHOUSE_CODE";

    public static RpcContext setWarehouseCode(String warehouseCode) {
        CurrentRouteHolder.setWarehouseCode(warehouseCode);
        return RpcContext.getContext().setAttachment(DT_ROUTE_WAREHOUSE_CODE, warehouseCode);
    }

//    public static RpcContext setAttachment(String name, String warehouseCode) {
//        return RpcContext.getContext().setAttachment(name, warehouseCode);
//    }

    public static void clearAttachments() {
        CurrentRouteHolder.clear();
        RpcContext.getContext().clearAttachments();
    }
}
