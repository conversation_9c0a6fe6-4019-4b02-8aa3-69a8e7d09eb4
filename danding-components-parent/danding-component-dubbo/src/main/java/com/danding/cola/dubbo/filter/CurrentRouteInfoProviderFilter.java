package com.danding.cola.dubbo.filter;

import com.danding.cola.dubbo.holder.CurrentRouteHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.constants.CommonConstants;
import org.apache.dubbo.common.extension.Activate;
import org.apache.dubbo.rpc.*;
import org.springframework.util.StringUtils;


/**
 * <p>
 * 仓库编码路由过滤
 * </p>
 * -
 *
 * <AUTHOR>
 * @since 2020-09-11
 */
@Slf4j
@Activate(group = CommonConstants.PROVIDER, order = 505)
public class CurrentRouteInfoProviderFilter implements Filter {


    public static String DT_ROUTE_WAREHOUSE_CODE = "DT_ROUTE_WAREHOUSE_CODE";

    public CurrentRouteInfoProviderFilter() {
        super();
    }

    @Override
    public Result invoke(Invoker<?> invoker, Invocation invocation) throws RpcException {
        RpcContext rpcContext = RpcContext.getContext();
//        log.error("PROVIDER  start");
//        log.error("PROVIDER rpcContext.isProviderSide() {}", rpcContext.isProviderSide());
//        log.error("PROVIDER rpcContext.getAttachment(DT_ROUTE_WAREHOUSE_CODE) {}", rpcContext.getAttachment(DT_ROUTE_WAREHOUSE_CODE));
//        log.error("PROVIDER CurrentRouteHolder.getWarehouseCode() {}", CurrentRouteHolder.getWarehouseCode());
        try {
            String warehouseCode = rpcContext.getAttachment(DT_ROUTE_WAREHOUSE_CODE);
            if (!StringUtils.isEmpty(warehouseCode)) {
                // *) 从RpcContext里获取traceId并保存
//                log.error("PROVIDER 111111");
                CurrentRouteHolder.setWarehouseCode(warehouseCode);
            } else {
                // *) 交互前重新设置traceId, 避免信息丢失
//                log.error("PROVIDER 2222222");
                rpcContext.setAttachment(DT_ROUTE_WAREHOUSE_CODE, CurrentRouteHolder.getWarehouseCode());
            }
            return invoker.invoke(invocation);
        } finally {
//            log.error("PROVIDER  clear");
//            log.error("PROVIDER rpcContext.isProviderSide() {}", rpcContext.isProviderSide());
//            log.error("PROVIDER rpcContext.getAttachment(DT_ROUTE_WAREHOUSE_CODE) {}", rpcContext.getAttachment(DT_ROUTE_WAREHOUSE_CODE));
//            log.error("PROVIDER CurrentRouteHolder.getWarehouseCode() {}", CurrentRouteHolder.getWarehouseCode());
            CurrentRouteHolder.clear();
            rpcContext.clearAttachments();
        }
    }

}

