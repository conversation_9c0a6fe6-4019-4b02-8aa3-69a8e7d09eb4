package com.danding.cola.dubbo.holder;

import com.alibaba.ttl.TransmittableThreadLocal;

public class CurrentRouteHolder {
    private static final ThreadLocal<String> ROUTE_CACHE = new TransmittableThreadLocal<>();

    public static String getWarehouseCode() {
        return ROUTE_CACHE.get();
    }

    public static void setWarehouseCode(String warehouseCode) {
        ROUTE_CACHE.set(warehouseCode);
    }

    public static void clear() {
        ROUTE_CACHE.remove();
    }
}
