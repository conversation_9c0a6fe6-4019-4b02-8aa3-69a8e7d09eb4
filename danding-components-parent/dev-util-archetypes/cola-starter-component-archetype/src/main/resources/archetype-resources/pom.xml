<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>${groupId}</groupId>
        <artifactId>danding-components-parent</artifactId>
        <version>${version}</version>
    </parent>

    <artifactId>${artifactId}</artifactId>
    <packaging>jar</packaging>
    <name>${project.artifactId}</name>
    <description>${project.artifactId}</description>
    <url>https://github.com/alibaba/COLA</url>

    <licenses>
        <license>
            <name>GNU Lesser General Public License v2.1</name>
            <url>https://github.com/alibaba/COLA/blob/master/LICENSE</url>
            <distribution>repo</distribution>
        </license>
    </licenses>
    <scm>
        <connection>scm:git:https://github.com/alibaba/COLA.git</connection>
        <developerConnection>scm:git:https://github.com/alibaba/COLA.git</developerConnection>
        <url>https://github.com/alibaba/COLA</url>
    </scm>
    <issueManagement>
        <url>https://github.com/alibaba/COLA/issues</url>
        <system>GitHub Issues</system>
    </issueManagement>
    <developers>
        <developer>
            <id>significantfrank</id>
            <name>Frank Zhang</name>
            <email>25216348(at)qq.com</email>
            <roles>
                <role>Developer</role>
                <role>Architect</role>
            </roles>
            <timezone>+8</timezone>
            <url>https://github.com/significantfrank</url>
        </developer>
        <developer>
            <id>oldratlee</id>
            <name>Jerry Lee</name>
            <email>oldratlee(at)gmail.com</email>
            <roles>
                <role>Developer</role>
                <role>CI/SCM Engineer</role>
            </roles>
            <timezone>+8</timezone>
            <url>https://github.com/oldratlee</url>
        </developer>
    </developers>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-autoconfigure</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
            <scope>provided</scope>
        </dependency>

        <!-- 工具包 -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <scope>provided</scope>
        </dependency>

        <!-- 日志包 -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>

        <!-- 测试包 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>
</project>
