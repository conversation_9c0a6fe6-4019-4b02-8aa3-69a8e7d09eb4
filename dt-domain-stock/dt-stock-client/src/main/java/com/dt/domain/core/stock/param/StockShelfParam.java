package com.dt.domain.core.stock.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Accessors(chain = true)
@ApiModel(value="上架完成容器明细DTO", description="上架完成容器")
public class StockShelfParam implements Serializable {

    @ApiModelProperty("仓库编码")
    private String warehouseCode;

    @ApiModelProperty("交易日期")
    private Long tradeDate;

    @ApiModelProperty("上架类型")
    private String shelfType;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    @ApiModelProperty(value = "行号")
    private String lineSeq;

    @ApiModelProperty(value = "商品编码")
    private String skuCode;

    @ApiModelProperty(value = "商品属性")
    private String skuQuality;

    @ApiModelProperty(value = "容器编码")
    private String contCode;

    @ApiModelProperty(value = "上架单号")
    private String shelfCode;

    @ApiModelProperty(value = "商品名称")
    private String skuName;

    @ApiModelProperty(value = "商品数量")
    private BigDecimal skuQty;

    @ApiModelProperty(value = "上架数量")
    private BigDecimal shelfSkuQty;

    @ApiModelProperty(value = "来源库区编码")
    private String originZoneCode;

    @ApiModelProperty(value = "来源库区类型")
    private String originZoneType;

    @ApiModelProperty(value = "来源库位")
    private String originLocationCode;

    @ApiModelProperty(value = "来源库位")
    private String originLocationType;

    @ApiModelProperty(value = "推荐目标库位")
    private String recLocationCode;

    @ApiModelProperty(value = "目标库区编码")
    private String targetZoneCode;

    @ApiModelProperty(value = "目标库区类型")
    private String targetZoneType;

    @ApiModelProperty(value = "实际目标库位")
    private String targetLocationCode;

    @ApiModelProperty(value = "实际目标库位")
    private String targetLocationType;

    @ApiModelProperty(value = "批次ID")
    private String skuLotNo;
}
