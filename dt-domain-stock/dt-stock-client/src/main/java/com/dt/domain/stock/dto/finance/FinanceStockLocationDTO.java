package com.dt.domain.stock.dto.finance;

import com.dt.component.common.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 * 监管库存
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "FinanceStockLocation对象", description = "监管库存")
public class FinanceStockLocationDTO extends BaseDTO  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    /**
     * 仓库编码
     */
    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    /**
     * 货主编码
     */
    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    /**
     * 商品编码
     */
    @ApiModelProperty(value = "商品编码")
    private String skuCode;

    /**
     * 库位编码
     */
    @ApiModelProperty(value = "库位编码")
    private String locationCode;

    /**
     * 实物数量
     */
    @ApiModelProperty(value = "实物数量")
    private BigDecimal physicalQty;

    /**
     * 冻结数量
     */
    @ApiModelProperty(value = "冻结数量")
    private BigDecimal frozenQty;

    /**
     * 可用数量 实物库存-冻结=可用数
     */
    @ApiModelProperty(value = "可用数量 实物库存-冻结=可用数")
    private BigDecimal availableQty;

    /**
     * 批次ID
     */
    @ApiModelProperty(value = "批次ID")
    private String skuLotNo;

    /**
     * 失效日期
     */
    @ApiModelProperty(value = "失效日期")
    private Long expireDate;

    /**
     * 生产批次号
     */
    @ApiModelProperty(value = "生产批次号")
    private String productionNo;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "修改人")
    private String updatedBy;
}