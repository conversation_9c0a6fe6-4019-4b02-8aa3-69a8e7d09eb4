package com.dt.domain.core.stock.param;

import com.dt.component.common.param.BaseSearchParam;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 一级库存
 * </p>
 *
 * <AUTHOR> xy
 * @since 2020-09-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="StockLot对象", description="一级库存")
public class StockParam extends BaseSearchParam  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    @ApiModelProperty(value = "商品编码")
    private String skuCode;

    @ApiModelProperty(value = "商品属性")
    private String skuQuality;

    /**
     * 用于判断是否是真实的商品 RealOrUnrealGoodsEnum
     */
    private String realGoods;

    @ApiModelProperty(value = "实物数量")
    private BigDecimal physicalQty;

    @ApiModelProperty(value = "冻结数量")
    private BigDecimal frozenQty;

    @ApiModelProperty(value = "占用数量")
    private BigDecimal occupyQty;

    @ApiModelProperty(value = "可用数量 实物库存-冻结-占用=可用数")
    private BigDecimal availableQty;

    @ApiModelProperty(value = "状态码 ")
    private Integer status;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "更新人")
    private String updatedBy;

    @ApiModelProperty(value = "Sku编码列表")
    private List<String> skuCodeList;

    @ApiModelProperty(value = "Sku编码列表")
    private List<Map<String,String>> skuCodePairQueryList;

    @ApiModelProperty(value = "货主编码列表")
    private List<String> cargoCodeList;
    @ApiModelProperty(value = "正残属性列表")
    private List<String> skuQualityList;

    @ApiModelProperty(value = "库存商品类型")
    private String stockSkuType;

    private Long latestUpdateTimeStart;
    private Long latestUpdateTimeEnd;

    private Boolean hasPhysicalQty;
    private Boolean hasAvailableQty;
    private Boolean hasOccupyQty;
    private Long startId;
    private Long endId;

    private List<String> tableFieldList;
}