package com.dt.domain.core.stock.param;

import com.dt.component.common.param.BaseSearchParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;


/**
 * <p>
 * 库位库存交易流水
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "StockTransaction对象", description = "库位库存交易流水")
public class StockTransactionParam extends BaseSearchParam  implements java.io.Serializable  {

    @ApiModelProperty(value = "全局单号-在但丁云系统中唯一单号")
    private String globalNo;
    private List<String> globalNoList;

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;
    private List<String> cargoCodeList;

    @ApiModelProperty(value = "batch serial no")
    private String batchSerialNo;
    private List<String> batchSerialNoList;

    @ApiModelProperty(value = "transaction serial no")
    private String transactionSerialNo;
    private List<String> transactionSerialNoList;

    @ApiModelProperty(value = "商品编码")
    private String skuCode;
    private List<String> skuCodeList;

    @ApiModelProperty(value = "商品属性")
    private String skuQuality;

    /**
     * 用于判断是否是真实的商品 RealOrUnrealGoodsEnum
     */
    private String realGoods;

    @ApiModelProperty(value = "批次ID")
    private String skuLotNo;

    @ApiModelProperty(value = "商品条形码")
    private String upcCode;

    @ApiModelProperty(value = "单据类型 BillTypeEnum")
    private String billType;

    @ApiModelProperty(value = "单据编码")
    private String billNo;
    private List<String> billNoList;

    @ApiModelProperty(value = "父单据编码")
    private String parentBillNo;
    private List<String> parentBillNoList;

    @ApiModelProperty(value = "操作类型 与业务类型对应")
    private String operationType;
    private List<String> operationTypeList;

    @ApiModelProperty(value = "业务类型 TradeTypeEnum")
    private String tradeType;

    @ApiModelProperty(value = "库存层级")
    private String stockLevel;

    @ApiModelProperty(value = "库区类型 ZoneTypeEnum")
    private String zoneType;

    @ApiModelProperty(value = "库区编码")
    private String zoneCode;

    @ApiModelProperty(value = "库位类型 LocationTypeEnum")
    private String locationType;

    @ApiModelProperty(value = "库位编码")
    private String locationCode;

    @ApiModelProperty(value = "操作时间")
    private Long tradeTime;

    @ApiModelProperty(value = "需变动数量")
    private BigDecimal changeQty;

    @ApiModelProperty(value = "已处理数量")
    private BigDecimal doneQty;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "更新人")
    private String updatedBy;

    @ApiModelProperty(value = "状态")
    private String status;

    private String remark;

    @ApiModelProperty(value = "状态")
    private String noStatus;

    @ApiModelProperty(value = "状态")
    private List<String> noStatusList;

    private Boolean forUpdate;

    private Boolean searchArchive;

    private Long startId;

    /**
     * 不查询逻辑删除的数据
     */
    private Boolean noDeletedData = true;
}