package com.dt.domain.core.stock.param;

import com.dt.component.common.param.BaseSearchParam;
import com.dt.domain.core.stock.dto.StockZoneOperateDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * 二级库存
 * </p>
 *
 * <AUTHOR> xy
 * @since 2020-09-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="StockZoneBatch对象", description="二级库存")
public class StockZoneBatchReleaseParam extends BaseSearchParam  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "库区库存批量操作列表")
    private List<StockZoneOperateDTO> stockReleaseList;


}