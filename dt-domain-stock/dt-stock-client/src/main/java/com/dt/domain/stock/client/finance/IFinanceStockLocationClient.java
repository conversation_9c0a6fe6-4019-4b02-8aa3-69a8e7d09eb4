package com.dt.domain.stock.client.finance;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.domain.stock.bo.finance.FinanceStockLocationBO;
import com.dt.domain.stock.dto.finance.FinanceStockLocationDTO;
import com.dt.domain.stock.param.finance.FinanceStockLocationParam;

import java.util.List;

/**
 * <p>
 * 监管库存 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-19
 */
public interface IFinanceStockLocationClient {

    /**
     * 新增监管库存
     *
     * @param financeStockLocationDTO
     * @return
     */
    Result<Boolean> save(FinanceStockLocationDTO financeStockLocationDTO);

    /**
     * 批量新增监管库存
     *
     * @param financeStockLocationDTOList
     * @return
     */
    Result<Boolean> saveBatch(List<FinanceStockLocationDTO> financeStockLocationDTOList);

    /**
     * 修改监管库存
     *
     * ID | Code 二选一
     * @param financeStockLocationDTO
     * @return
     */
    Result<Boolean> modify(FinanceStockLocationDTO financeStockLocationDTO);

    /**
     * 批量修改监管库存
     *
     * @param financeStockLocationDTOList
     * @return
     */
    Result<Boolean> modifyBatch(List<FinanceStockLocationDTO> financeStockLocationDTOList);

    /**
     * 根据条件查询是否存在
     *
     * @param param
     * @return
     */
    Result<Boolean> checkExits(FinanceStockLocationParam param);

    /**
     * 获取监管库存
     *
     * @param param
     * @return
     */
    Result<FinanceStockLocationDTO> get(FinanceStockLocationParam param);

    /**
     * 获取监管库存列表
     * @param param
     * @return
     */
    Result<List<FinanceStockLocationDTO>> getList(FinanceStockLocationParam param);

    /**
     * 分页获取监管库存
     *
     * @param param
     * @return
     */
    Result<Page<FinanceStockLocationDTO>> getPage(FinanceStockLocationParam param);

    /**
     * 功能描述:  删除
     * 创建时间:  2021/1/8 11:25 上午
     *
     * @param param:
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     */
    Result<Boolean> remove(FinanceStockLocationParam param);

    Result<Boolean> commit(FinanceStockLocationBO stockLocationBO);
}

