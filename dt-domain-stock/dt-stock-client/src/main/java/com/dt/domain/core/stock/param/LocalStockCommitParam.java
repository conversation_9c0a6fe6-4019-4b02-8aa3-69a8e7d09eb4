package com.dt.domain.core.stock.param;

import com.dt.domain.core.stock.dto.StockDTO;
import com.dt.domain.core.stock.dto.StockLocationDTO;
import com.dt.domain.core.stock.dto.StockZoneDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

@Data
@Accessors(chain = true)
@ApiModel(value = "本地事务提交对象", description = "")
public class LocalStockCommitParam implements Serializable {

    @ApiModelProperty("批次库存调整列表")
    private List<StockDTO> stockLotSaveOrUpdateList;
    @ApiModelProperty("库区库存调整列表")
    private List<StockZoneDTO> stockZoneSaveOrUpdateList;
    @ApiModelProperty("库位库存调整列表")
    private List<StockLocationDTO> stockLocationSaveOrUpdateList;

}
