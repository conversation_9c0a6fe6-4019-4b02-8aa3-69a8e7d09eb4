package com.dt.domain.stock.param.sn;

import com.dt.component.common.param.BaseSearchParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import java.math.BigDecimal;
import java.util.List;

/**
* <p>
    * SN库存流水
    * </p>
*
* <AUTHOR>
* @since 2024-05-09
*/
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="SnStockSerial对象", description="SN库存流水")
public class SnStockSerialParam extends BaseSearchParam  implements java.io.Serializable  {

private static final long serialVersionUID = 1L;

    /**
    * SN
    */
    @ApiModelProperty(value = "SN")
    private String sn;
    private List<String> snList;

    /**
    * 货主编码
    */
    @ApiModelProperty(value = "货主编码")
    private String cargoCode;
    private List<String> cargoCodeList;

    /**
    * 商品编码
    */
    @ApiModelProperty(value = "商品编码")
    private String skuCode;
    private List<String> skuCodeList;

    /**
    * 商品条形码
    */
    @ApiModelProperty(value = "商品条形码")
    private String upcCode;
    private List<String> upcCodeList;

    /**
    * 商品名称
    */
    @ApiModelProperty(value = "商品名称")
    private String skuName;

    /**
    * 操作类型 与业务类型对应
    */
    @ApiModelProperty(value = "操作类型 与业务类型对应")
    private String operationType;

    /**
    * 单据编码
    */
    @ApiModelProperty(value = "单据编码")
    private String billNo;
    private List<String> billNoList;

    /**
    * 客户单号
    */
    @ApiModelProperty(value = "客户单号")
    private String poNo;
    private List<String> poNoList;

    /**
    * 数量
    */
    @ApiModelProperty(value = "数量")
    private BigDecimal qty;

@ApiModelProperty(value = "创建人")
private String createdBy;

@ApiModelProperty(value = "修改人")
private String updatedBy;
}