package com.dt.domain.core.stock.param;

import java.util.List;

import com.dt.component.common.param.BaseSearchParam;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created by nobody on 2021/1/7 18:09
 */
@Data
public class ValidityPeriodWarnParam extends BaseSearchParam  implements java.io.Serializable  {

    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;
    private List<String> cargoCodeList;

    @ApiModelProperty(value = "外部批次编码")
    private String externalSkuLotNo;
    private List<String> externalSkuLotNoList;

    @ApiModelProperty(value = "商品编码")
    private String skuCode;
    private List<String> skuCodeList;

    @ApiModelProperty(value = "库位编码")
    private String locationCode;
    private String locationType;

    @ApiModelProperty(value = "批次ID")
    private String skuLotNo;
    private List<String> skuLotNoList;

    @ApiModelProperty(value = "商品属性")
    private String skuQuality;

    @ApiModelProperty(value = "状态 预警或禁售")
    private String status;

    private Long warnDateEnd;

    private Long withdrawDateStart;
    private Long withdrawDateEnd;

    private List<Long> withdrawDateList;
    private Boolean hasAvailableQty;

    private boolean hasAvl;

    /**
     * 初始化数据时使用
     */
    private String appSecret;
}