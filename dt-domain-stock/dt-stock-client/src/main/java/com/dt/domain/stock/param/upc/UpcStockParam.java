package com.dt.domain.stock.param.upc;

import com.dt.component.common.param.BaseSearchParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 包耗材库存
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "UpcStock对象", description = "包耗材库存")
public class UpcStockParam extends BaseSearchParam  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    /**
     * 仓库编码
     */
    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    /**
     * 包耗材条码
     */
    @ApiModelProperty(value = "包耗材条码")
    private String upcCode;
    private List<String> upcCodeList;

    /**
     * 实物数量
     */
    @ApiModelProperty(value = "实物数量")
    private BigDecimal physicalQty;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "修改人")
    private String updatedBy;
    
}