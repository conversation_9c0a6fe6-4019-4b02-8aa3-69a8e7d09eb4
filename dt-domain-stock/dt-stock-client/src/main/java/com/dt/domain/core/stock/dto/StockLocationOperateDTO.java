package com.dt.domain.core.stock.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 二级库存
 * </p>
 *
 * <AUTHOR> xy
 * @since 2020-09-11
 */
@Data
@Accessors(chain = true)
@ApiModel(value="StockLocationOperateDTO对象", description="库存操作对象")
public class StockLocationOperateDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    @ApiModelProperty(value = "单据编码")
    private String billCode;

    @ApiModelProperty(value = "明细序号")
    private String lineSeq;

    @ApiModelProperty(value = "库区编码")
    private String zoneCode;

    @ApiModelProperty(value = "库区类型")
    private String zoneType;

    @ApiModelProperty(value = "库区编码")
    private String locationCode;

    @ApiModelProperty(value = "库区类型")
    private String locationType;

    @ApiModelProperty(value = "商品编码")
    private String skuCode;

    @ApiModelProperty(value = "质量等级")
    private String skuQuality;

    @ApiModelProperty(value = "操作类型")
    private String opType;

    @ApiModelProperty(value = "操作数量")
    private BigDecimal opQty;

}
