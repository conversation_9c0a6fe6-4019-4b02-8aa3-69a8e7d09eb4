package com.dt.domain.stock.client.sn;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.result.Result;
import com.dt.domain.stock.dto.sn.SnStockSerialDTO;
import com.dt.domain.stock.param.sn.SnStockSerialParam;

import java.util.List;

/**
 * <p>
 * SN库存流水 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-09
 */
public interface ISnStockSerialClient {

    /**
     * 新增SN库存流水
     *
     * @param snStockSerialDTO
     * @return
     */
    Result<Boolean> save(SnStockSerialDTO snStockSerialDTO);

    /**
     * 批量新增SN库存流水
     *
     * @param snStockSerialDTOList
     * @return
     */
    Result<Boolean> saveBatch(List<SnStockSerialDTO> snStockSerialDTOList);

    /**
     * 修改SN库存流水
     *
     * ID | Code 二选一
     * @param snStockSerialDTO
     * @return
     */
    Result<Boolean> modify(SnStockSerialDTO snStockSerialDTO);

    /**
     * 批量修改SN库存流水
     *
     * @param snStockSerialDTOList
     * @return
     */
    Result<Boolean> modifyBatch(List<SnStockSerialDTO> snStockSerialDTOList);

    /**
     * 根据条件查询是否存在
     *
     * @param param
     * @return
     */
    Result<Boolean> checkExits(SnStockSerialParam param);

    /**
     * 获取SN库存流水
     *
     * @param param
     * @return
     */
    Result<SnStockSerialDTO> get(SnStockSerialParam param);

    /**
     * 获取SN库存流水列表
     * @param param
     * @return
     */
    Result<List<SnStockSerialDTO>> getList(SnStockSerialParam param);

    /**
     * 分页获取SN库存流水
     *
     * @param param
     * @return
     */
    Result<Page<SnStockSerialDTO>> getPage(SnStockSerialParam param);

    /**
     * 功能描述:  删除
     * 创建时间:  2021/1/8 11:25 上午
     *
     * @param param:
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * <AUTHOR>
     */
    Result<Boolean> remove(SnStockSerialParam param);

}

