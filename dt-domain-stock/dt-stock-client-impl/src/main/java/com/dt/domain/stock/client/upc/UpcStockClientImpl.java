package com.dt.domain.stock.client.upc;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.exceptions.BaseException;
import com.dt.component.common.result.Result;
import com.dt.domain.stock.bo.upc.UpcStockOperationBO;
import com.dt.domain.stock.dto.upc.UpcStockDTO;
import com.dt.domain.stock.param.upc.UpcStockParam;
import com.dt.domain.stock.upc.entity.UpcStock;
import com.dt.domain.stock.upc.entity.UpcStockDifference;
import com.dt.domain.stock.upc.entity.UpcStockSerial;
import com.dt.domain.stock.upc.entity.UpcStockTransaction;
import com.dt.domain.stock.upc.service.IUpcStockDifferenceService;
import com.dt.domain.stock.upc.service.IUpcStockSerialService;
import com.dt.domain.stock.upc.service.IUpcStockService;
import com.dt.domain.stock.upc.service.IUpcStockTransactionService;
import com.dt.domain.stock.util.upc.UpcStockUtil;
import com.dt.platform.utils.ConverterUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;


/**
 * <p>
 * 包耗材库存 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-09
 */
@Slf4j
@DubboService(version = "${dubbo.service.version}")
@DS("#DTWMS")
public class UpcStockClientImpl implements IUpcStockClient {

    @Resource
    private IUpcStockService upcStockService;

    @Resource
    private IUpcStockSerialService upcStockSerialService;

    @Resource
    private IUpcStockTransactionService upcStockTransactionService;

    @Resource
    private IUpcStockDifferenceService upcStockDifferenceService;

    @Resource
    private UpcStockUtil upcStockUtil;

    @Override
    @Transactional
    public void stockOperation(UpcStockOperationBO operationBO) {
        saveStock(ConverterUtil.convertList(operationBO.getUpcStockDTOList(), UpcStock.class));
        saveStockSerial(ConverterUtil.convertList(operationBO.getUpcStockSerialDTOList(), UpcStockSerial.class));
        saveStockTransaction(ConverterUtil.convertList(operationBO.getUpcStockTransactionDTOList(), UpcStockTransaction.class));
        saveStockDifference(ConverterUtil.convertList(operationBO.getUpcStockDifferenceDTOList(), UpcStockDifference.class));
    }

    private void saveStock(List<UpcStock> upcStockList) {
        if (CollectionUtil.isEmpty(upcStockList)) return;
        List<UpcStock> saveList = upcStockList.stream()
                .filter(upcStock -> ObjectUtil.isEmpty(upcStock.getId()))
                .collect(Collectors.toList());
        List<UpcStock> updateList = upcStockList.stream()
                .filter(upcStock -> ObjectUtil.isNotEmpty(upcStock.getId()))
                .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(saveList)) {
            if (!upcStockService.saveBatch(saveList)) {
                throw new BaseException(BaseBizEnum.TIP, "保存包耗材库存异常");
            }
        }
        for (UpcStock upcStock : updateList) {
            if (!upcStockService.updateById(upcStock)) {
                throw new BaseException(BaseBizEnum.TIP, "修改包耗材库存异常");
            }
        }
    }

    private void saveStockSerial(List<UpcStockSerial> serialList) {
        if (CollectionUtil.isEmpty(serialList)) return;
        if (CollectionUtil.isNotEmpty(serialList)) {
            if (!upcStockSerialService.saveBatch(serialList)) {
                throw new BaseException(BaseBizEnum.TIP, "保存包耗材库存流水异常");
            }
        }
    }

    private void saveStockTransaction(List<UpcStockTransaction> transactionList) {
        if (CollectionUtil.isEmpty(transactionList)) return;
        List<UpcStockTransaction> saveList = transactionList.stream()
                .filter(upcStock -> ObjectUtil.isEmpty(upcStock.getId()))
                .collect(Collectors.toList());
        List<UpcStockTransaction> updateList = transactionList.stream()
                .filter(upcStock -> ObjectUtil.isNotEmpty(upcStock.getId()))
                .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(saveList)) {
            if (!upcStockTransactionService.saveBatch(saveList)) {
                throw new BaseException(BaseBizEnum.TIP, "保存包耗材库存核销异常");
            }
        }
        for (UpcStockTransaction upcStock : updateList) {
            if (!upcStockTransactionService.updateById(upcStock)) {
                throw new BaseException(BaseBizEnum.TIP, "修改包耗材库存核销异常");
            }
        }
    }

    private void saveStockDifference(List<UpcStockDifference> differenceList) {
        if (CollectionUtil.isEmpty(differenceList)) return;
        List<UpcStockDifference> saveList = differenceList.stream()
                .filter(upcStock -> ObjectUtil.isEmpty(upcStock.getId()))
                .collect(Collectors.toList());
        List<UpcStockDifference> updateList = differenceList.stream()
                .filter(upcStock -> ObjectUtil.isNotEmpty(upcStock.getId()))
                .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(saveList)) {
            if (!upcStockDifferenceService.saveBatch(saveList)) {
                throw new BaseException(BaseBizEnum.TIP, "保存包耗材差异异常");
            }
        }
        for (UpcStockDifference upcStock : updateList) {
            if (!upcStockDifferenceService.updateById(upcStock)) {
                throw new BaseException(BaseBizEnum.TIP, "修改包耗材差异异常");
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> stockOperation(UpcStockDTO upcStockDTO) {
        UpcStock upcStock = ConverterUtil.convert(upcStockDTO, UpcStock.class);
        if (ObjectUtils.isEmpty(upcStock)) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        Boolean result = upcStockService.save(upcStock);
        if (!result) {
            throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
        }
        return Result.success(result);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> saveBatch(List<UpcStockDTO> upcStockDTOList) {
        if (CollectionUtils.isEmpty(upcStockDTOList)) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        List<UpcStock> list = ConverterUtil.convertList(upcStockDTOList, UpcStock.class);
        Boolean result = upcStockService.saveBatch(list);
        if (!result) {
            throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
        }
        return Result.success(result);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> modify(UpcStockDTO upcStockDTO) {
        if (ObjectUtils.isEmpty(upcStockDTO.getId())) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        LambdaQueryWrapper<UpcStock> wrapper = new LambdaQueryWrapper<>();
        wrapper
                .eq(!StringUtils.isEmpty(upcStockDTO.getId()), UpcStock::getId, upcStockDTO.getId())
        ;
        UpcStock upcStock = ConverterUtil.convert(upcStockDTO, UpcStock.class);
        if (ObjectUtils.isEmpty(upcStock)) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        Boolean result = upcStockService.update(upcStock, wrapper);
        if (!result) {
            throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
        }
        return Result.success(result);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> modifyBatch(List<UpcStockDTO> upcStockDTOList) {
        if (CollectionUtils.isEmpty(upcStockDTOList)) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        List<UpcStock> list = ConverterUtil.convertList(upcStockDTOList, UpcStock.class);
        list.forEach(entity -> {
            Boolean result = upcStockService.updateById(entity);
            if (!result) {
                throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
            }
        });
        return Result.success(true);

    }

    @Override
    public Result<Boolean> checkExits(UpcStockParam param) {
        LambdaQueryWrapper<UpcStock> wrapper = upcStockUtil.getQueryWrapper(param);
        Integer count = upcStockService.count(wrapper);
        return Result.success(count != 0);
    }

    @Override
    public Result<UpcStockDTO> get(UpcStockParam param) {
        LambdaQueryWrapper<UpcStock> queryWrapper = upcStockUtil.getQueryWrapper(param);
        UpcStock upcStock = upcStockService.getOne(queryWrapper);
        UpcStockDTO result = ConverterUtil.convert(upcStock, UpcStockDTO.class);
        return Result.success(result);
    }

    @Override
    public Result<List<UpcStockDTO>> getList(UpcStockParam param) {
        LambdaQueryWrapper<UpcStock> queryWrapper = upcStockUtil.getQueryWrapper(param);
        List<UpcStock> upcStockList = upcStockService.list(queryWrapper);
        List<UpcStockDTO> result = ConverterUtil.convertList(upcStockList, UpcStockDTO.class);
        return Result.success(result);
    }

    @Override
    public Result<Page<UpcStockDTO>> getPage(UpcStockParam param) {
        Page<UpcStock> page = new Page<>(param.getCurrent(), param.getSize(), param.getSearchCount());
        LambdaQueryWrapper<UpcStock> queryWrapper = upcStockUtil.getQueryWrapper(param);
        IPage<UpcStock> UpcStockPage = upcStockService.page(page, queryWrapper);
        Page<UpcStockDTO> result = ConverterUtil.convertPage(UpcStockPage, UpcStockDTO.class);
        return Result.success(result);
    }

    @Override
    public Result<Boolean> remove(UpcStockParam param) {
        LambdaQueryWrapper<UpcStock> wrapper = upcStockUtil.getQueryWrapper(param);
        Boolean result = upcStockService.remove(wrapper);
        if (!result) {
            throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
        }
        return Result.success(result);
    }
}
