package com.dt.domain.core.stock.client;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.enums.bill.BillTypeEnum;
import com.dt.component.common.enums.sku.SkuQualityEnum;
import com.dt.component.common.enums.stock.StockLevelEnum;
import com.dt.component.common.enums.stock.TradeTypeEnum;
import com.dt.component.common.exceptions.BaseException;
import com.dt.component.common.result.Result;
import com.dt.domain.core.stock.dto.StockDTO;
import com.dt.domain.core.stock.dto.StockOperateDTO;
import com.dt.domain.core.stock.dto.StockStatisticDTO;
import com.dt.domain.core.stock.entity.Stock;
import com.dt.domain.core.stock.entity.StockLog;
import com.dt.domain.core.stock.mapper.StockMapper;
import com.dt.domain.core.stock.param.StockBatchParam;
import com.dt.domain.core.stock.param.StockBatchReleaseParam;
import com.dt.domain.core.stock.param.StockParam;
import com.dt.domain.core.stock.service.IStockLogService;
import com.dt.domain.core.stock.service.IStockService;
import com.dt.domain.core.stock.util.StockUtil;
import com.dt.platform.utils.ConverterUtil;
import com.dt.platform.utils.DefaultSortUtil;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@DubboService(version = "${dubbo.service.version}")
@DS("#DTWMS")
public class StockClient implements IStockClient {


    @Resource
    private IStockService stockService;

    @Resource
    private StockUtil stockUtil;

    @Resource
    private IStockLogService stockLogService;

    @Resource
    private StockMapper stockMapper;

    @Override
    public Result<Boolean> modify(StockDTO stockDTO) {
        Stock stock = BeanUtil.toBean(stockDTO, Stock.class);
        if (!stockService.updateById(stock)) {
            throw new BaseException(BaseBizEnum.DATA_ERROR);
        }
        return Result.success(true);
    }

    @Override
    public Result<Boolean> remove(StockDTO stockDTO) {
        Stock stock = BeanUtil.toBean(stockDTO, Stock.class);
        if (!stockService.removeById(stock)) {
            throw new BaseException(BaseBizEnum.DATA_ERROR);
        }
        return Result.success(true);
    }

    @Override
    public Result<Boolean> save(StockParam stockParam) {
        Stock stock = BeanUtil.toBean(stockParam, Stock.class);
        if (!stockService.save(stock)) {
            throw new BaseException(BaseBizEnum.DATA_ERROR);
        }
        return Result.success(true);
    }

    @Override
    public Result<Boolean> saveOrUpdate(StockBatchParam param) {
        if (ObjectUtils.isEmpty(param) || CollectionUtils.isEmpty(param.getStockLotList())) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        List<Stock> stockList = ConverterUtil.convertList(param.getStockLotList(), Stock.class);
        if (CollectionUtils.isEmpty(stockList)) {
            throw new BaseException(BaseBizEnum.DATA_ERROR);
        }
        List<Stock> createStockList = stockList.stream()
                .filter(a -> StringUtils.isEmpty(a.getId()))
                .collect(Collectors.toList());
        List<Stock> updateStockList = stockList.stream()
                .filter(a -> !ObjectUtils.isEmpty(a.getId()))
                .filter(a -> !ObjectUtils.isEmpty(a.getVersion()))
                .collect(Collectors.toList());
        if (stockList.size() != createStockList.size() + updateStockList.size()) {
            throw new BaseException(BaseBizEnum.DATA_ERROR);
        }
        for (Stock stock : updateStockList) {
            Boolean result = stockService.updateById(stock);
            if (!result) {
                throw new BaseException(BaseBizEnum.TIP, "修改一级库存失败");
            }
        }
        Boolean result = stockService.saveBatch(createStockList);
        if (!result) {
            throw new BaseException(BaseBizEnum.TIP, "保存一级库存失败");
        }
        return Result.success(true);
    }

    @Override
    @Transactional
    public Result<Boolean> lockStock(StockBatchReleaseParam param) {
        if (ObjectUtils.isEmpty(param) || CollectionUtils.isEmpty(param.getStockReleaseList())) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        List<String> cargoCodeList = param.getStockReleaseList()
                .stream()
                .flatMap(a -> Stream.of(a.getCargoCode()))
                .distinct()
                .collect(Collectors.toList());
        List<String> skuCodeList = param.getStockReleaseList()
                .stream()
                .flatMap(a -> Stream.of(a.getSkuCode()))
                .distinct()
                .collect(Collectors.toList());
        List<String> skuQualityList = param.getStockReleaseList()
                .stream()
                .flatMap(a -> Stream.of(a.getSkuQuality()))
                .distinct()
                .collect(Collectors.toList());
        StockParam stockParam = new StockParam();
        stockParam.setCargoCodeList(cargoCodeList);
        stockParam.setSkuCodeList(skuCodeList);
        stockParam.setSkuQualityList(skuQualityList);
        List<Stock> stockList = getLockStockList(stockParam);

        List<StockLog> stockLogList = new ArrayList<>();
        List<Stock> saveOrUpdateStockZoneList = new ArrayList<>();
        for (StockOperateDTO stockLock : param.getStockReleaseList()) {
            Stock stock = saveOrUpdateStockZoneList
                    .stream()
                    .filter(a -> a.getCargoCode().equals(stockLock.getCargoCode()))
                    .filter(a -> a.getSkuCode().equals(stockLock.getSkuCode()))
                    .filter(a -> a.getSkuQuality().equals(stockLock.getSkuQuality()))
                    .findAny().orElse(null);
            if (ObjectUtils.isEmpty(stock)) {
                stock = stockList
                        .stream()
                        .filter(a -> a.getCargoCode().equals(stockLock.getCargoCode()))
                        .filter(a -> a.getSkuCode().equals(stockLock.getSkuCode()))
                        .filter(a -> a.getSkuQuality().equals(stockLock.getSkuQuality()))
                        .findAny().get();
            }
            StockLog stockLog = StockLog.log(stock.getWarehouseCode(), stock.getCargoCode(),
                    TradeTypeEnum.TRADE_TYPE_INTERCEPTION.getType(), BillTypeEnum.BILL_TYPE_SHIPMENT.getType(),
                    stockLock.getBillCode(), stockLock.getLineSeq(), StockLevelEnum.LEVEL_STOCK.getLevel(),
                    stockLock.getSkuCode(), "", "", stockLock.getOpQty(), "",
                    stockLock.getSkuCode(), "", "", stockLock.getOpQty(), "",
                    stockLock.getTradeDate(),
                    "", "", "", "", 0, ""
            );

            stock.setAvailableQty(stock.getAvailableQty().subtract(stockLock.getOpQty()));
            stock.setOccupyQty(stock.getOccupyQty().add(stockLock.getOpQty()));

            stockLogList.add(stockLog);
            saveOrUpdateStockZoneList.add(stock);
        }
        for (Stock stock : saveOrUpdateStockZoneList) {
            stockService.updateById(stock);
        }
        stockLogService.saveBatch(stockLogList);
        return Result.success(true);
    }

    @Override
    @Transactional
    public Result<Boolean> releaseStock(StockBatchReleaseParam param) {
        if (ObjectUtils.isEmpty(param) || CollectionUtils.isEmpty(param.getStockReleaseList())) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        List<String> cargoCodeList = param.getStockReleaseList()
                .stream()
                .flatMap(a -> Stream.of(a.getCargoCode()))
                .distinct()
                .collect(Collectors.toList());
        List<String> skuCodeList = param.getStockReleaseList()
                .stream()
                .flatMap(a -> Stream.of(a.getSkuCode()))
                .distinct()
                .collect(Collectors.toList());
        List<String> skuQualityList = param.getStockReleaseList()
                .stream()
                .flatMap(a -> Stream.of(a.getSkuQuality()))
                .distinct()
                .collect(Collectors.toList());
        StockParam stockParam = new StockParam();
        stockParam.setCargoCodeList(cargoCodeList);
        stockParam.setSkuCodeList(skuCodeList);
        stockParam.setSkuQualityList(skuQualityList);
        List<Stock> stockList = getLockStockList(stockParam);

        List<StockLog> stockLogList = new ArrayList<>();
        List<Stock> saveOrUpdateStockZoneList = new ArrayList<>();
        for (StockOperateDTO stockLock : param.getStockReleaseList()) {
            Stock stock = saveOrUpdateStockZoneList
                    .stream()
                    .filter(a -> a.getCargoCode().equals(stockLock.getCargoCode()))
                    .filter(a -> a.getSkuCode().equals(stockLock.getSkuCode()))
                    .filter(a -> a.getSkuQuality().equals(stockLock.getSkuQuality()))
                    .findAny().orElse(null);
            if (ObjectUtils.isEmpty(stock)) {
                stock = stockList
                        .stream()
                        .filter(a -> a.getCargoCode().equals(stockLock.getCargoCode()))
                        .filter(a -> a.getSkuCode().equals(stockLock.getSkuCode()))
                        .filter(a -> a.getSkuQuality().equals(stockLock.getSkuQuality()))
                        .findAny().get();
            }
            StockLog stockLog = StockLog.log(stock.getWarehouseCode(), stock.getCargoCode(),
                    TradeTypeEnum.TRADE_TYPE_INTERCEPTION.getType(), BillTypeEnum.BILL_TYPE_SHIPMENT.getType(),
                    stockLock.getBillCode(), stockLock.getLineSeq(), StockLevelEnum.LEVEL_STOCK.getLevel(),
                    stockLock.getSkuCode(), "", "", stockLock.getOpQty(), "",
                    stockLock.getSkuCode(), "", "", stockLock.getOpQty(), "",
                    stockLock.getTradeDate(),
                    "", "", "", "", 0, ""
            );

            stock.setAvailableQty(stock.getAvailableQty().add(stockLock.getOpQty()));
            stock.setOccupyQty(stock.getOccupyQty().subtract(stockLock.getOpQty()));

            stockLogList.add(stockLog);
            saveOrUpdateStockZoneList.add(stock);
        }
        for (Stock stock : saveOrUpdateStockZoneList) {
            stockService.updateById(stock);
        }
        stockLogService.saveBatch(stockLogList);
        return Result.success(true);
    }

    private List<Stock> getLockStockList(StockParam stockParam) {
        LambdaQueryWrapper<Stock> queryWrapper = stockUtil.getQueryWrapper(stockParam);
        queryWrapper.select(Stock::getId);
        List<Stock> queryStockList = stockService.list(queryWrapper);
        if (CollectionUtils.isEmpty(queryStockList)) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        List<Long> stockIdList = queryStockList
                .stream()
                .flatMap(a -> Stream.of(a.getId()))
                .collect(Collectors.toList());
        LambdaQueryWrapper<Stock> lockQueryWrapper = new LambdaQueryWrapper<>();
        lockQueryWrapper.in(Stock::getId, stockIdList);
        lockQueryWrapper.last(" for update ");
        return stockService.list(lockQueryWrapper);
    }

    @Override
    public Result<Boolean> checkExits(StockParam param) {
        LambdaQueryWrapper<Stock> wrapper = stockUtil.getQueryWrapper(param);
        Integer count = stockService.count(wrapper);
        return Result.success(count != 0);
    }

    @Override
    public Result<StockDTO> get(StockParam param) {
        LambdaQueryWrapper<Stock> queryWrapper = stockUtil.getQueryWrapper(param);
        Stock stock = stockService.getOne(queryWrapper);
        StockDTO result = ConverterUtil.convert(stock, StockDTO.class);
        return Result.success(result);
    }

    @Override
    public Result<List<StockDTO>> getList(StockParam param) {
        LambdaQueryWrapper<Stock> queryWrapper = stockUtil.getQueryWrapper(param);
        List<Stock> stockList = stockService.list(queryWrapper);
        List<StockDTO> result = ConverterUtil.convertList(stockList, StockDTO.class);
        return Result.success(result);
    }

    @Override
    public Result<List<StockDTO>> getListAccount(StockParam param) {
        DefaultSortUtil.formatSortSort(param);
        LambdaQueryWrapper<Stock> queryWrapper = stockUtil.getQueryWrapper(param);
        queryWrapper.gt(Stock::getPhysicalQty, new BigDecimal("0.000"));
        List<Stock> stockList = stockService.list(queryWrapper);
        List<StockDTO> result = ConverterUtil.convertList(stockList, StockDTO.class);
        return Result.success(result);
    }

    @Override
    public Result<Page<StockDTO>> getPage(StockParam param) {
        DefaultSortUtil.formatSortSort(param);

        Page<Stock> page = new Page<>(param.getCurrent(), param.getSize(), param.getSearchCount());
        LambdaQueryWrapper<Stock> queryWrapper = stockUtil.getQueryWrapper(param);
        if (!CollectionUtils.isEmpty(param.getSkuCodePairQueryList())) {
            List<Map<String, String>> pair = param.getSkuCodePairQueryList();
            queryWrapper.and(s -> {
                for (Map<String, String> map : pair) {
                    String skuCode = map.keySet().stream().findFirst().get();
                    String cargoCode = map.values().stream().findFirst().get();
                    s.or(ss -> {
                        ss.eq(Stock::getSkuCode, skuCode).eq(Stock::getCargoCode, cargoCode);
                    });
                }
            });
        }

        IPage<Stock> stockLotPage = stockService.page(page, queryWrapper);
        Page<StockDTO> result = ConverterUtil.convertPage(stockLotPage, StockDTO.class);
        return Result.success(result);
    }

    @Override
    public Result<StockStatisticDTO> getStatistic(StockParam param) {
        param.setHasPhysicalQty(true);
        LambdaQueryWrapper<Stock> queryWrapper = stockUtil.getQueryWrapper(param);
        queryWrapper.select(Stock::getSkuQuality, Stock::getPhySum, Stock::getAvlSum, Stock::getOccupySum, Stock::getFrozenSum);
        queryWrapper.groupBy(Stock::getSkuQuality);
        List<Stock> stockList = stockService.list(queryWrapper);
        StockStatisticDTO stockStatisticDTO = new StockStatisticDTO();
        stockStatisticDTO.setTotalPhysicalQty(stockList.stream().map(Stock::getPhySum).reduce(BigDecimal.ZERO, BigDecimal::add));
        stockStatisticDTO.setTotalAvlAvailableQty(stockList.stream()
                .filter(it ->it.getSkuQuality().equalsIgnoreCase(SkuQualityEnum.SKU_QUALITY_AVL.getLevel()))
                .map(Stock::getAvlSum).reduce(BigDecimal.ZERO, BigDecimal::add));
        stockStatisticDTO.setTotalDamageAvailableQty(stockList.stream()
                .filter(it ->it.getSkuQuality().equalsIgnoreCase(SkuQualityEnum.SKU_QUALITY_DAMAGE.getLevel()))
                .map(Stock::getAvlSum).reduce(BigDecimal.ZERO, BigDecimal::add));
        stockStatisticDTO.setTotalOccupyQty(stockList.stream().map(Stock::getOccupySum).reduce(BigDecimal.ZERO, BigDecimal::add));
        stockStatisticDTO.setTotalFrozenQty(stockList.stream().map(Stock::getFrozenSum).reduce(BigDecimal.ZERO, BigDecimal::add));
        // 商品种类数
        queryWrapper = stockUtil.getQueryWrapper(param);
        queryWrapper.select(Stock::getSkuCode);
        queryWrapper.groupBy(Stock::getCargoCode,Stock::getSkuCode);
        List<Stock> count = stockService.list(queryWrapper);
        stockStatisticDTO.setTotalSku(count.size());
        return Result.success(stockStatisticDTO);
    }

    @Override
    public List<HashMap<String, String>> stockBalanceWarehouseAndZone() {
        return stockService.stockBalanceWarehouseAndZone();
    }

    @Override
    public List<HashMap<String, String>> stockBalanceWarehouseAndLocation() {
        return stockService.stockBalanceWarehouseAndLocation();
    }

    @Override
    public void  cleanStock(long updateEnd) {
        stockMapper.cleanStock(updateEnd);
    }
}
