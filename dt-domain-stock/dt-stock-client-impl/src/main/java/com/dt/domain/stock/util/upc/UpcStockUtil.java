package com.dt.domain.stock.util.upc;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dt.component.mp.query.QueryWrapper;
import com.dt.domain.stock.upc.entity.UpcStock;
import com.dt.domain.stock.param.upc.UpcStockParam;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

/**
 * <p>
 * 包耗材库存
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-09
 */
@Component
public class UpcStockUtil extends QueryWrapper<UpcStock, UpcStockParam> {
    @Override
    public LambdaQueryWrapper<UpcStock> getQueryWrapper(UpcStockParam param) {
        LambdaQueryWrapper<UpcStock> lambdaQueryWrapper = super.getQueryWrapper(param);
        lambdaQueryWrapper
                //仓库编码
                .eq(!ObjectUtils.isEmpty(param.getWarehouseCode()), UpcStock::getWarehouseCode, param.getWarehouseCode())
                //包耗材条码
                .eq(!ObjectUtils.isEmpty(param.getUpcCode()), UpcStock::getUpcCode, param.getUpcCode())
                .in(CollectionUtil.isNotEmpty(param.getUpcCodeList()), UpcStock::getUpcCode, param.getUpcCodeList())
                //实物数量
                .eq(!ObjectUtils.isEmpty(param.getPhysicalQty()), UpcStock::getPhysicalQty, param.getPhysicalQty())
        ;
        return lambdaQueryWrapper;
    }
}
