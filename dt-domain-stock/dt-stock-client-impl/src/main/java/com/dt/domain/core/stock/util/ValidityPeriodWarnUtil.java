package com.dt.domain.core.stock.util;

import java.math.BigDecimal;

import com.dt.domain.core.stock.entity.StockLocation;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dt.component.mp.query.QueryWrapper;
import com.dt.domain.core.stock.entity.ValidityPeriodWarn;
import com.dt.domain.core.stock.param.ValidityPeriodWarnParam;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import org.springframework.util.ObjectUtils;

/**
 * Created by nobody on 2021/1/7 18:09
 */
@Component
public class ValidityPeriodWarnUtil extends QueryWrapper<ValidityPeriodWarn, ValidityPeriodWarnParam> {

    @Override
    public LambdaQueryWrapper<ValidityPeriodWarn> getQueryWrapper(ValidityPeriodWarnParam param) {
        LambdaQueryWrapper<ValidityPeriodWarn> lambdaQueryWrapper = super.getQueryWrapper(param);
        lambdaQueryWrapper.eq(StrUtil.isNotBlank(param.getWarehouseCode()), ValidityPeriodWarn::getWarehouseCode,
            param.getWarehouseCode());
        lambdaQueryWrapper.eq(StrUtil.isNotBlank(param.getCargoCode()), ValidityPeriodWarn::getCargoCode,
            param.getCargoCode());
        lambdaQueryWrapper.eq(StrUtil.isNotBlank(param.getSkuCode()), ValidityPeriodWarn::getSkuCode,
            param.getSkuCode());
        lambdaQueryWrapper.in(ObjectUtil.isNotEmpty(param.getSkuCodeList()), ValidityPeriodWarn::getSkuCode,
                param.getSkuCodeList());
        lambdaQueryWrapper.eq(StrUtil.isNotBlank(param.getLocationCode()), ValidityPeriodWarn::getLocationCode,
            param.getLocationCode());
        lambdaQueryWrapper.eq(StrUtil.isNotBlank(param.getSkuLotNo()), ValidityPeriodWarn::getSkuLotNo,
            param.getSkuLotNo());
        lambdaQueryWrapper.in(!CollectionUtils.isEmpty(param.getSkuLotNoList()), ValidityPeriodWarn::getSkuLotNo,
            param.getSkuLotNoList());

        lambdaQueryWrapper.eq(StrUtil.isNotBlank(param.getSkuQuality()), ValidityPeriodWarn::getSkuQuality,
            param.getSkuQuality());

        lambdaQueryWrapper.lt(ObjectUtil.isNotEmpty(param.getWarnDateEnd()), ValidityPeriodWarn::getWarnDate,
            param.getWarnDateEnd());
        lambdaQueryWrapper.lt(ObjectUtil.isNotEmpty(param.getWithdrawDateEnd()), ValidityPeriodWarn::getWithdrawDate,
            param.getWithdrawDateEnd());

        lambdaQueryWrapper.gt(ObjectUtil.isNotEmpty(param.getWithdrawDateStart()), ValidityPeriodWarn::getWithdrawDate,
            param.getWithdrawDateStart());

        lambdaQueryWrapper.in(!CollectionUtils.isEmpty(param.getCargoCodeList()), ValidityPeriodWarn::getCargoCode,
            param.getCargoCodeList());

        lambdaQueryWrapper.in(!CollectionUtils.isEmpty(param.getWithdrawDateList()), ValidityPeriodWarn::getWithdrawDate,
                param.getWithdrawDateList());

        // 只有物理数大于0才需要显示
        lambdaQueryWrapper.gt(ValidityPeriodWarn::getPhysicalQty, BigDecimal.ZERO);

        //
        lambdaQueryWrapper.gt(!ObjectUtils.isEmpty(param.getHasAvailableQty()) && param.getHasAvailableQty(),
                ValidityPeriodWarn::getAvailableQty, new BigDecimal("0.000"));

        lambdaQueryWrapper.gt(param.isHasAvl(), ValidityPeriodWarn::getAvailableQty, BigDecimal.ZERO);
        return lambdaQueryWrapper;
    }
}
