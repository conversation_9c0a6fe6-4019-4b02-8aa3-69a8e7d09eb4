package com.dt.elasticsearch.wms.client;

import com.dt.component.common.result.Result;
import com.dt.component.common.vo.PageVO;
import com.dt.elasticsearch.wms.dto.PackageIndexDTO;
import com.dt.elasticsearch.wms.dto.PackageScrollResultDTO;
import com.dt.elasticsearch.wms.param.PackageEsParam;

/**
 * <AUTHOR>
 * @date 2021/9/9 11:34
 */
public interface IPackageEsClient {
    /**
     * 包裹分页查询
     * @param param
     * @return
     */
    Result<PageVO<PackageIndexDTO>> getPage(PackageEsParam param);

    Result<PackageScrollResultDTO> getByScroll(PackageEsParam param);
}
