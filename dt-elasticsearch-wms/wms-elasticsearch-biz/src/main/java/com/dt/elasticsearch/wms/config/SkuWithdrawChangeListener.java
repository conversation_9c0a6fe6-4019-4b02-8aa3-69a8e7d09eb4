package com.dt.elasticsearch.wms.config;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.otter.canal.protocol.FlatMessage;
import com.dt.component.canal.mq.AbstractCanalMQService;
import com.dt.component.common.filter.RpcContextUtil;
import com.dt.component.common.result.Result;
import com.dt.domain.base.dto.SkuDTO;
import com.dt.domain.stock.client.lot.IStockLotClient;
import com.dt.domain.stock.dto.lot.StockLotDTO;
import com.dt.domain.stock.param.lot.StockLotParam;
import com.dt.elasticsearch.wms.biz.IStockLotEsBizClient;
import com.dt.elasticsearch.wms.integration.DefaultWarehouseCodeConfig;
import com.dt.elasticsearch.wms.integration.common.IRemoteSkuESClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RocketMQMessageListener(topic = "dt_wms_sku_modify_topic", consumerGroup = "sku_withdraw_modify_consumer_group", consumeMode = ConsumeMode.CONCURRENTLY)
@Slf4j
@Profile({"dev", "test", "pre", "prod"})
public class SkuWithdrawChangeListener extends AbstractCanalMQService<SkuDTO> implements RocketMQListener<FlatMessage> {

    @Autowired
    private DefaultWarehouseCodeConfig defaultWarehouseCodeConfig;

    @Autowired
    private IRemoteSkuESClient remoteSkuESClient;

    @DubboReference
    private IStockLotClient stockLotClient;

    @Autowired
    private IStockLotEsBizClient stockLotEsBizClient;

    @Override
    protected void insert(SkuDTO skuDTO) {
    }

    @Override
    protected void update(SkuDTO before, SkuDTO after) {
        process(after);
    }

    @Override
    protected void delete(SkuDTO skuDTO) {
    }

    /**
     * 统一处理逻辑
     */
    private void process(SkuDTO skuDTO) {
        if (!defaultWarehouseCodeConfig.getWarehouseCodeList().contains(skuDTO.getWarehouseCode())) {
            return;
        }
        // 上下文设置
        RpcContextUtil.setWarehouseCode(skuDTO.getWarehouseCode());
        remoteSkuESClient.refresh(skuDTO.getWarehouseCode(), skuDTO.getCargoCode(), skuDTO.getCode());
        StockLotParam stockLotParam = new StockLotParam();
        stockLotParam.setCargoCode(skuDTO.getCargoCode());
        stockLotParam.setSkuCode(skuDTO.getCode());
        Result<List<StockLotDTO>> result = stockLotClient.getList(stockLotParam);
        if (result.checkSuccess() && CollectionUtil.isNotEmpty(result.getData())) {
            stockLotEsBizClient.handleDataBatch(result.getData());
        }
    }

    @Override
    public void onMessage(FlatMessage message) {
        process(message);
    }
}
