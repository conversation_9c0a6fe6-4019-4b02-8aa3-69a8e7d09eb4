package com.dt.elasticsearch.wms.mq;

import cn.hutool.json.JSONUtil;
import com.alibaba.otter.canal.protocol.CanalEntry;
import com.alibaba.otter.canal.protocol.FlatMessage;
import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.exceptions.BaseException;
import com.dt.domain.bill.dto.ShipmentOrderDTO;
import com.dt.elasticsearch.wms.biz.IShipmentOrderEsBizClient;
import com.dt.elasticsearch.wms.integration.DefaultWarehouseCodeConfig;
import com.dt.platform.utils.ConvertMessageUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.client.consumer.listener.ConsumeOrderlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerOrderly;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.common.consumer.ConsumeFromWhere;
import org.apache.rocketmq.common.protocol.heartbeat.MessageModel;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/10/14 17:20
 * 出库单消息批量消费
 */
@Slf4j
@Configuration
public class BatchShipmentConsumerConfiguration {

    @Resource
    private IShipmentOrderEsBizClient shipmentOrderEsBizClient;

    @Resource
    private DefaultWarehouseCodeConfig defaultWarehouseCodeConfig;

    @Value("${rocketmq.name-server}")
    public String nameServer;

    @Value("${rocketmq.topic.shipment-es-topic}")
    public String topic;

    private static final String CONSUME_GROUP = "shipment_order_es";

    @Bean("shipmentPushConsumer")
    public DefaultMQPushConsumer consumer() throws MQClientException {
        DefaultMQPushConsumer consumer = new DefaultMQPushConsumer(CONSUME_GROUP);
        consumer.setNamesrvAddr(nameServer);
        consumer.setConsumeFromWhere(ConsumeFromWhere.CONSUME_FROM_FIRST_OFFSET);
        consumer.setMessageModel(MessageModel.CLUSTERING);
        consumer.setConsumeMessageBatchMaxSize(1024);
        consumer.setConsumeThreadMax(64);
        consumer.setConsumeThreadMin(64);
        consumer.subscribe(topic, "*");
        // 顺序消费
        consumer.registerMessageListener((MessageListenerOrderly) (msgs, consumeOrderlyContext) -> {
            List<ShipmentOrderDTO> list = new ArrayList<>();
            msgs.forEach(it -> {
                String str = new String(it.getBody(), StandardCharsets.UTF_8);
                FlatMessage message = JSONUtil.toBean(str, FlatMessage.class);
                process(message, list);
            });
            shipmentOrderEsBizClient.handleDataBatch(list);
            return ConsumeOrderlyStatus.SUCCESS;
        });
        consumer.start();
        return consumer;
    }

    /**
     * 处理canal消息 生成出库单DTO对象
     *
     * @param message
     * @param list
     */
    private void process(FlatMessage message, List<ShipmentOrderDTO> list) {
        if (Boolean.TRUE.equals(message.getIsDdl())) {
            return;
        }
        CanalEntry.EventType eventType = CanalEntry.EventType.valueOf(message.getType());
        if (eventType.equals(CanalEntry.EventType.DELETE)) {
            return;
        }
        List<Map<String, String>> data = message.getData();
        data.forEach(it -> {
            try {
                ShipmentOrderDTO shipmentOrderDTO = ConvertMessageUtil.newInstance(ShipmentOrderDTO.class, it);
                if (!defaultWarehouseCodeConfig.getWarehouseCodeList().contains(shipmentOrderDTO.getWarehouseCode())) {
                    return;
                }

                if (shipmentOrderDTO.getDeleted().equals(-1)) {
                    shipmentOrderEsBizClient.remove(shipmentOrderDTO);
                } else {
                    list.add(shipmentOrderDTO);
                }
            } catch (Exception e) {
                e.printStackTrace();
                throw new BaseException(BaseBizEnum.TIP,"转换消息异常");
            }
        });
    }

}
