package com.dt.elasticsearch.wms.biz.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.danding.business.client.rpc.config.result.WarehouseInfoResult;
import com.danding.business.client.rpc.goods.center.result.GoodsManagementRpcResult;
import com.danding.business.client.rpc.goods.result.GoodsRpcResult;
import com.dt.component.common.enums.sku.SkuLifeCtrlEnum;
import com.dt.component.common.filter.RpcContextUtil;
import com.dt.domain.base.dto.SkuDTO;
import com.dt.domain.base.dto.SkuLotDTO;
import com.dt.domain.stock.dto.lot.StockLotDTO;
import com.dt.elasticsearch.wms.biz.IStockLotEsBizClient;
import com.dt.elasticsearch.wms.config.IndexNameConfig;
import com.dt.elasticsearch.wms.integration.common.ICargoUserRelation;
import com.dt.elasticsearch.wms.integration.common.IRemoteSkuESClient;
import com.dt.elasticsearch.wms.integration.common.IRemoteSkuGoodsRelationClient;
import com.dt.elasticsearch.wms.integration.common.IRemoteSkuLotESClient;
import com.dt.elasticsearch.wms.integration.domain.StockLotIndex;
import com.dt.platform.wms.client.config.WmsTenantHelper;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.DocWriteResponse;
import org.elasticsearch.action.delete.DeleteRequest;
import org.elasticsearch.action.delete.DeleteResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.query.IndexQuery;
import org.springframework.data.elasticsearch.core.query.IndexQueryBuilder;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.RoundingMode;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
public class StockLotEsBizClientImpl implements IStockLotEsBizClient {

    @Resource
    private ElasticsearchRestTemplate elasticsearchRestTemplate;

    @Resource
    private IndexNameConfig indexNameConfig;

    @Resource
    private ICargoUserRelation cargoUserRelation;

    @Resource
    private IRemoteSkuGoodsRelationClient skuGoodsRelation;

    @Resource
    private IRemoteSkuLotESClient remoteSkuLotESClient;

    @Resource
    private IRemoteSkuESClient remoteSkuESClient;

    @Resource
    private RestHighLevelClient restHighLevelClient;

    @Resource
    private WmsTenantHelper wmsTenantHelper;

    @Override
    public void remove(StockLotDTO stockLotDTO) {
        remove(getId(stockLotDTO));
    }

    private String getId(StockLotDTO stockLotDTO) {
        return StrUtil.join(StrUtil.COLON, stockLotDTO.getWarehouseCode(), stockLotDTO.getCargoCode(), stockLotDTO.getSkuCode(), stockLotDTO.getSkuLotNo());
    }

    private void remove(String id) {
        DeleteRequest deleteRequest = new DeleteRequest(indexNameConfig.getStockLotIndexName());
        deleteRequest.type("_doc");
        deleteRequest.id(id);
        try {
            DeleteResponse delete = restHighLevelClient.delete(deleteRequest, RequestOptions.DEFAULT);
            log.info(JSONUtil.toJsonStr(delete));
            if (delete.getResult() != DocWriteResponse.Result.DELETED && delete.getResult() != DocWriteResponse.Result.NOT_FOUND) {
                throw new RuntimeException("stock lot es delete fail");
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void handleDataBatch(List<StockLotDTO> stockLotDTOList) {
        if (CollectionUtil.isEmpty(stockLotDTOList)) return;
        try {
            List<StockLotIndex> stockLotIndexList = stockLotIndexList(stockLotDTOList);
            List<IndexQuery> indexQueryList = stockLotIndexList.stream()
                    .map(stockLotIndex -> new IndexQueryBuilder()
                            .withIndexName(indexNameConfig.getStockLotIndexName())
                            .withObject(stockLotIndex)
                            .withId(stockLotIndex.getIndexId())
                            .withType("_doc")
                            .build()
                    ).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(indexQueryList)) {
                elasticsearchRestTemplate.bulkIndex(indexQueryList);
            }
        } catch (Exception exception) {
            log.error(exception.getMessage(), exception);
            throw exception;
        }
    }

    private List<StockLotIndex> stockLotIndexList(List<StockLotDTO> stockLotDTOList) {
        return stockLotDTOList.stream().map(stockLotDTO -> {
            RpcContextUtil.setWarehouseCode(stockLotDTO.getWarehouseCode());
            StockLotIndex stockLotIndex = new StockLotIndex();
            stockLotIndex.setIndexId(getId(stockLotDTO));
            stockLotIndex.setKey(stockLotDTO.getId());
            wmsTenantHelper.setTenantId(stockLotDTO.getWarehouseCode(), stockLotDTO.getCargoCode());
            WarehouseInfoResult warehouseInfoResult = cargoUserRelation.userId(stockLotDTO.getWarehouseCode(), stockLotDTO.getCargoCode());
            if (warehouseInfoResult != null) {
                stockLotIndex.setUserId(String.valueOf(warehouseInfoResult.getUserId()));
                stockLotIndex.setCargoName(warehouseInfoResult.getOwnerName());
                stockLotIndex.setWarehouseName(warehouseInfoResult.getWarehouseName());
            }
            stockLotIndex.setCargoCode(stockLotDTO.getCargoCode());
            GoodsManagementRpcResult goodsRpcResult = skuGoodsRelation.goodsId(stockLotDTO.getWarehouseCode(), stockLotDTO.getCargoCode(), stockLotDTO.getSkuCode());
            stockLotIndex.setGoodsId(StrUtil.EMPTY);
            stockLotIndex.setUpcCode(StrUtil.EMPTY);
            if (null != goodsRpcResult) {
                stockLotIndex.setGoodsId(goodsRpcResult.getGoodsCode());
                if (goodsRpcResult.getType() != null) {
                    stockLotIndex.setGoodsType(goodsRpcResult.getType().getValue());
                }
                stockLotIndex.setUpcCode(goodsRpcResult.getBarcode());
                stockLotIndex.setGoodsName(goodsRpcResult.getGoodsName());
            }
            stockLotIndex.setSkuLotNo(stockLotDTO.getSkuLotNo());
            stockLotIndex.setSkuCode(stockLotDTO.getSkuCode());
            stockLotIndex.setWarehouseCode(stockLotDTO.getWarehouseCode());
            stockLotIndex.setPhysicalQty(stockLotDTO.getPhysicalQty().setScale(0, RoundingMode.HALF_UP));
            stockLotIndex.setOccupyQty(stockLotDTO.getOccupyQty().setScale(0, RoundingMode.HALF_UP));
            stockLotIndex.setAvailableQty(stockLotDTO.getAvailableQty().setScale(0, RoundingMode.HALF_UP));
            stockLotIndex.setFrozenQty(stockLotDTO.getFrozenQty().setScale(0, RoundingMode.HALF_UP));
            stockLotIndex.setSkuQuality(stockLotDTO.getSkuQuality());
            SkuLotDTO skuLotDTO = remoteSkuLotESClient.skuLotDTO(stockLotDTO.getWarehouseCode(), stockLotDTO.getSkuLotNo());
            SkuDTO skuDTO = remoteSkuESClient.skuDTO(stockLotDTO.getWarehouseCode(), stockLotDTO.getCargoCode(), stockLotDTO.getSkuCode());
            if (null != skuLotDTO) {
                stockLotIndex.setManufDate(skuLotDTO.getManufDate());
                stockLotIndex.setReceiveDate(skuLotDTO.getReceiveDate());
                stockLotIndex.setExpireDate(skuLotDTO.getExpireDate());
                stockLotIndex.setExternalSkuLotNo(skuLotDTO.getExternalSkuLotNo());
                stockLotIndex.setProductionNo(skuLotDTO.getProductionNo());
                stockLotIndex.setExternalLinkBillNo(skuLotDTO.getExternalLinkBillNo());
                stockLotIndex.setInventoryType(skuLotDTO.getInventoryType());
                if (null != skuDTO) {
                    stockLotIndex.setWarnCycle(skuDTO.getWarnCycle());
                    stockLotIndex.setRejectCycle(skuDTO.getRejectCycle());
                    stockLotIndex.setWithdrawCycle(skuDTO.getWithdrawCycle());
                    stockLotIndex.setLifeCycle(skuDTO.getLifeCycle());
                    stockLotIndex.setIsLifeMgt(skuDTO.getIsLifeMgt());
                    if (SkuLifeCtrlEnum.SKU_LIFE_CTRL_YES.getCode().equals(skuDTO.getIsLifeMgt())) {
                        if (stockLotIndex.getExpireDate() == 0) {
                            stockLotIndex.setExpireDate(Long.MAX_VALUE);
                        }
                        try {
                            stockLotIndex.setWithdrawDate(DateTime.of(stockLotIndex.getExpireDate()).offset(DateField.DAY_OF_YEAR, -skuDTO.getWithdrawCycle()).getTime());
                            stockLotIndex.setWarnDate(DateTime.of(stockLotIndex.getExpireDate()).offset(DateField.DAY_OF_YEAR, -skuDTO.getWarnCycle()).getTime());
                        } catch (Exception exception) {
                            log.info("ddebug {} {}", skuDTO, stockLotIndex);
                        }
                    } else {
                        stockLotIndex.setWithdrawDate(Long.MAX_VALUE);
                        stockLotIndex.setWarnDate(Long.MAX_VALUE);
                        stockLotIndex.setExpireDate(Long.MAX_VALUE);
                    }
                } else {
                    log.error("sku not found {} {} {}", stockLotDTO.getWarehouseCode(), stockLotDTO.getCargoCode(), stockLotDTO.getSkuCode());
                    stockLotIndex.setWithdrawDate(Long.MAX_VALUE);
                    stockLotIndex.setWarnDate(Long.MAX_VALUE);
                    stockLotIndex.setExpireDate(Long.MAX_VALUE);
                }
            } else {
                log.error("lot not found {}", stockLotDTO.getSkuLotNo());
                stockLotIndex.setWithdrawDate(Long.MAX_VALUE);
                stockLotIndex.setWarnDate(Long.MAX_VALUE);
                stockLotIndex.setExpireDate(Long.MAX_VALUE);
            }
            stockLotIndex.setTtPhysical(stockLotDTO.getTtPhysical());
            stockLotIndex.setTtAvl(stockLotDTO.getTtAvl());
            stockLotIndex.setTtOccupy(stockLotDTO.getTtOccupy());
            return stockLotIndex;
        }).collect(Collectors.toList());
    }
}
