package com.dt.domain.statistics.util.snapshot;

import java.math.BigDecimal;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dt.component.mp.query.QueryWrapper;
import com.dt.domain.statistics.param.snapshot.StockLocationSnapshotParam;
import com.dt.domain.statistics.snapshot.entity.StockLocationSnapshot;

import cn.hutool.core.util.ObjectUtil;

/**
 * <p>
 * 三级库存快照
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-07
 */
@Component
public class StockLocationSnapshotUtil extends QueryWrapper<StockLocationSnapshot, StockLocationSnapshotParam> {
    @Override
    public LambdaQueryWrapper<StockLocationSnapshot> getQueryWrapper(StockLocationSnapshotParam param) {
        LambdaQueryWrapper<StockLocationSnapshot> lambdaQueryWrapper = super.getQueryWrapper(param);
        lambdaQueryWrapper
                // 主键id
                .eq(!ObjectUtils.isEmpty(param.getHid()), StockLocationSnapshot::getHid, param.getHid())
                .eq(StrUtil.isNotBlank(param.getRealGoods()), StockLocationSnapshot::getRealGoods, param.getRealGoods())
                // 快照日期
                .eq(!ObjectUtils.isEmpty(param.getSnapshotTime()), StockLocationSnapshot::getSnapshotTime,
                        param.getSnapshotTime())
                // 仓库编码
                .eq(!ObjectUtils.isEmpty(param.getWarehouseCode()), StockLocationSnapshot::getWarehouseCode,
                        param.getWarehouseCode())
                // 货主编码
                .eq(!ObjectUtils.isEmpty(param.getCargoCode()), StockLocationSnapshot::getCargoCode, param.getCargoCode())
                // 商品编码
                .eq(!ObjectUtils.isEmpty(param.getSkuCode()), StockLocationSnapshot::getSkuCode, param.getSkuCode())
                // 库区编码
                .eq(!ObjectUtils.isEmpty(param.getZoneCode()), StockLocationSnapshot::getZoneCode, param.getZoneCode())
                // 库区类型
                .eq(!ObjectUtils.isEmpty(param.getZoneType()), StockLocationSnapshot::getZoneType, param.getZoneType())
                // 库位编码
                .eq(!ObjectUtils.isEmpty(param.getLocationCode()), StockLocationSnapshot::getLocationCode,
                        param.getLocationCode())
                // 库位类型
                .eq(!ObjectUtils.isEmpty(param.getLocationType()), StockLocationSnapshot::getLocationType,
                        param.getLocationType())
                // 库位用途
                .eq(!ObjectUtils.isEmpty(param.getLocationUseMode()), StockLocationSnapshot::getLocationUseMode,
                        param.getLocationUseMode())
                // 批次ID
                .eq(!ObjectUtils.isEmpty(param.getSkuLotNo()), StockLocationSnapshot::getSkuLotNo, param.getSkuLotNo())
                // 实物数量
                .eq(!ObjectUtils.isEmpty(param.getPhysicalQty()), StockLocationSnapshot::getPhysicalQty,
                        param.getPhysicalQty())
                // 冻结数量
                .eq(!ObjectUtils.isEmpty(param.getFrozenQty()), StockLocationSnapshot::getFrozenQty, param.getFrozenQty())
                // 占用数量
                .eq(!ObjectUtils.isEmpty(param.getOccupyQty()), StockLocationSnapshot::getOccupyQty, param.getOccupyQty())
                // 可用数量 实物库存-冻结-占用=可用数
                .eq(!ObjectUtils.isEmpty(param.getAvailableQty()), StockLocationSnapshot::getAvailableQty,
                        param.getAvailableQty())
                // 状态码
                .eq(!ObjectUtils.isEmpty(param.getStatus()), StockLocationSnapshot::getStatus, param.getStatus())
                // 待上架数量
                .eq(!ObjectUtils.isEmpty(param.getWaitShelfQty()), StockLocationSnapshot::getWaitShelfQty,
                        param.getWaitShelfQty())
                // 商品属性
                .eq(!ObjectUtils.isEmpty(param.getSkuQuality()), StockLocationSnapshot::getSkuQuality,
                        param.getSkuQuality());
        lambdaQueryWrapper.ge(ObjectUtil.isNotEmpty(param.getSnapshotTimeBegin()),
            StockLocationSnapshot::getSnapshotTime, param.getSnapshotTimeBegin());
        lambdaQueryWrapper.le(ObjectUtil.isNotEmpty(param.getSnapshotTimeEnd()), StockLocationSnapshot::getSnapshotTime,
            param.getSnapshotTimeEnd());
        lambdaQueryWrapper.lt(ObjectUtil.isNotEmpty(param.getEndId()), StockLocationSnapshot::getId, param.getEndId());
        lambdaQueryWrapper.gt(ObjectUtil.isNotEmpty(param.getHasPhysicalQty()), StockLocationSnapshot::getPhysicalQty,
            BigDecimal.ZERO);
        

        if (CollectionUtil.isNotEmpty(param.getTableFieldList())) {
            lambdaQueryWrapper.select(StockLocationSnapshot.class, i -> param.getTableFieldList().contains(i.getColumn()));
        }
        return lambdaQueryWrapper;
    }
}
