package com.dt.domain.statistics.client.shipmentStatistics;

import java.util.List;

import javax.annotation.Resource;

import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.exceptions.BaseException;
import com.dt.component.common.result.Result;
import com.dt.domain.statistics.dto.shipmentStatistics.ShipmentOrderStatisticsDTO;
import com.dt.domain.statistics.param.shipmentStatistics.ShipmentOrderStatisticsParam;
import com.dt.domain.statistics.shipmentStatistics.entity.ShipmentOrderStatistics;
import com.dt.domain.statistics.shipmentStatistics.service.IShipmentOrderStatisticsService;
import com.dt.domain.statistics.util.shipmentStatistics.ShipmentOrderStatisticsUtil;
import com.dt.platform.utils.ConverterUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 出库单发货明细表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-06
 */
@Slf4j
@DubboService(version = "${dubbo.service.version}")
@DS("#DTWMS")
public class ShipmentOrderStatisticsClientImpl implements IShipmentOrderStatisticsClient {

    @Resource
    private IShipmentOrderStatisticsService shipmentOrderStatisticsService;

    @Resource
    private ShipmentOrderStatisticsUtil shipmentOrderStatisticsUtil;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> save(ShipmentOrderStatisticsDTO shipmentOrderStatisticsDTO) {
        ShipmentOrderStatistics shipmentOrderStatistics =
            ConverterUtil.convert(shipmentOrderStatisticsDTO, ShipmentOrderStatistics.class);
        if (ObjectUtils.isEmpty(shipmentOrderStatistics)) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        Boolean result = shipmentOrderStatisticsService.save(shipmentOrderStatistics);
        if (!result) {
            throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
        }
        return Result.success(result);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> saveBatch(List<ShipmentOrderStatisticsDTO> shipmentOrderStatisticsDTOList) {
        if (CollectionUtils.isEmpty(shipmentOrderStatisticsDTOList)) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        List<ShipmentOrderStatistics> list =
            ConverterUtil.convertList(shipmentOrderStatisticsDTOList, ShipmentOrderStatistics.class);
        Boolean result = shipmentOrderStatisticsService.saveBatch(list);
        if (!result) {
            throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
        }
        return Result.success(result);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> modify(ShipmentOrderStatisticsDTO shipmentOrderStatisticsDTO) {
        if (ObjectUtils.isEmpty(shipmentOrderStatisticsDTO.getId())) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        LambdaQueryWrapper<ShipmentOrderStatistics> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(!StringUtils.isEmpty(shipmentOrderStatisticsDTO.getId()), ShipmentOrderStatistics::getId,
            shipmentOrderStatisticsDTO.getId());
        ShipmentOrderStatistics shipmentOrderStatistics =
            ConverterUtil.convert(shipmentOrderStatisticsDTO, ShipmentOrderStatistics.class);
        if (ObjectUtils.isEmpty(shipmentOrderStatistics)) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        Boolean result = shipmentOrderStatisticsService.update(shipmentOrderStatistics, wrapper);
        if (!result) {
            throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
        }
        return Result.success(result);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> modifyBatch(List<ShipmentOrderStatisticsDTO> shipmentOrderStatisticsDTOList) {
        if (CollectionUtils.isEmpty(shipmentOrderStatisticsDTOList)) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        List<ShipmentOrderStatistics> list =
            ConverterUtil.convertList(shipmentOrderStatisticsDTOList, ShipmentOrderStatistics.class);
        list.stream().forEach(entity ->{
            Boolean result = shipmentOrderStatisticsService.updateById(entity);
            if (!result) {
                throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
            }
        });
        return Result.success(true);
    }

    @Override
    public Result<Boolean> checkExits(ShipmentOrderStatisticsParam param) {
        LambdaQueryWrapper<ShipmentOrderStatistics> wrapper = shipmentOrderStatisticsUtil.getQueryWrapper(param);
        Integer count = shipmentOrderStatisticsService.count(wrapper);
        return Result.success(count != 0);
    }

    @Override
    public Result<ShipmentOrderStatisticsDTO> get(ShipmentOrderStatisticsParam param) {
        LambdaQueryWrapper<ShipmentOrderStatistics> queryWrapper = shipmentOrderStatisticsUtil.getQueryWrapper(param);
        ShipmentOrderStatistics shipmentOrderStatistics = shipmentOrderStatisticsService.getOne(queryWrapper);
        ShipmentOrderStatisticsDTO result =
            ConverterUtil.convert(shipmentOrderStatistics, ShipmentOrderStatisticsDTO.class);
        return Result.success(result);
    }

    @Override
    public Result<List<ShipmentOrderStatisticsDTO>> getList(ShipmentOrderStatisticsParam param) {
        LambdaQueryWrapper<ShipmentOrderStatistics> queryWrapper = shipmentOrderStatisticsUtil.getQueryWrapper(param);
        List<ShipmentOrderStatistics> shipmentOrderStatisticsList = shipmentOrderStatisticsService.list(queryWrapper);
        List<ShipmentOrderStatisticsDTO> result =
            ConverterUtil.convertList(shipmentOrderStatisticsList, ShipmentOrderStatisticsDTO.class);
        return Result.success(result);
    }

    @Override
    public Result<Page<ShipmentOrderStatisticsDTO>> getPage(ShipmentOrderStatisticsParam param) {
        Page<ShipmentOrderStatistics> page = new Page<>(param.getCurrent(), param.getSize(), param.getSearchCount());
        LambdaQueryWrapper<ShipmentOrderStatistics> queryWrapper = shipmentOrderStatisticsUtil.getQueryWrapper(param);
        if (!CollectionUtils.isEmpty(param.getTableFieldList())) {
            queryWrapper.select(ShipmentOrderStatistics.class, i -> param.getTableFieldList().contains(i.getColumn()));
        }
        IPage<ShipmentOrderStatistics> ShipmentOrderStatisticsPage = shipmentOrderStatisticsService.page(page, queryWrapper);
        Page<ShipmentOrderStatisticsDTO> result = ConverterUtil.convertPage(ShipmentOrderStatisticsPage, ShipmentOrderStatisticsDTO.class);
        return Result.success(result);
    }

    @Override
    public Result<Boolean> remove(ShipmentOrderStatisticsParam param) {
        LambdaQueryWrapper<ShipmentOrderStatistics> wrapper = shipmentOrderStatisticsUtil.getQueryWrapper(param);
        int result = shipmentOrderStatisticsService.removeNotLogic(wrapper);
        if (result == 0) {
            throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
        }
        return Result.success();
    }

    @Override
    public Result<Integer> getExportNum(ShipmentOrderStatisticsParam param) {
        LambdaQueryWrapper<ShipmentOrderStatistics> wrapper = shipmentOrderStatisticsUtil.getQueryWrapper(param);
        return Result.success(shipmentOrderStatisticsService.count(wrapper));
    }
}
