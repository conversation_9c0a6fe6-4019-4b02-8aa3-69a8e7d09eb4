package com.dt.domain.statistics.dto.dateSummary;

import com.dt.component.common.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;


/**
 * <p>
 * 包裹日汇总
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="PackageDateSummary对象", description="包裹日汇总")
public class PackageDateSummaryDTO extends BaseDTO  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    /**
     * 仓库编码
     */
    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    /**
     * 货主编码
     */
    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型")
    private String businessType;

    /**
     * 创建日期
     */
    @ApiModelProperty(value = "创建日期")
    private Long summaryTime;

    /**
     * 快递公司编码
     */
    @ApiModelProperty(value = "快递公司编码")
    private String carrierCode;

    /**
     * 快递公司名称
     */
    @ApiModelProperty(value = "快递公司名称")
    private String carrierName;

    /**
     * 总包裹数
     */
    @ApiModelProperty(value = "总包裹数")
    private Integer packageTotalQty;

    /**
     * 创建状态包裹数
     */
    @ApiModelProperty(value = "创建状态包裹数")
    private Integer packageStatusQty;

    /**
     * 预处理失败包裹数
     */
    @ApiModelProperty(value = "预处理失败包裹数")
    private Integer failPackageQty;

    /**
     * 预处理完成包裹数
     */
    @ApiModelProperty(value = "预处理完成包裹数")
    private Integer completePackageQty;

    /**
     * 分配失败包裹数
     */
    @ApiModelProperty(value = "分配失败包裹数")
    private Integer allocationFailQty;

    /**
     * 分配包裹数
     */
    @ApiModelProperty(value = "分配包裹数")
    private Integer allocationPackageQty;

    /**
     * 拣选开始包裹数
     */
    @ApiModelProperty(value = "拣选开始包裹数")
    private Integer pickStartQty;

    /**
     * 拣选完成包裹数
     */
    @ApiModelProperty(value = "拣选完成包裹数")
    private Integer pickEndQty;

    /**
     * 复核开始包裹数
     */
    @ApiModelProperty(value = "复核开始包裹数")
    private Integer checkStartQty;

    /**
     * 复核完成包裹数
     */
    @ApiModelProperty(value = "复核完成包裹数")
    private Integer checkEndQty;

    /**
     * 已出库包裹数
     */
    @ApiModelProperty(value = "已出库包裹数")
    private Integer outPackageQty;

    /**
     * 拦截包裹数
     */
    @ApiModelProperty(value = "拦截包裹数")
    private Integer interceptPackageQty;

    /**
     * 拦截取消包裹数
     */
    @ApiModelProperty(value = "拦截取消包裹数")
    private Integer cancelPackageQty;

    /**
     * 缺货出库包裹数
     */
    @ApiModelProperty(value = "缺货出库包裹数")
    private Integer lackOutQty;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "修改人")
    private String updatedBy;
}