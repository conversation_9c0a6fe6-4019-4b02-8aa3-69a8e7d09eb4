package com.dt.domain.statistics.dto.tallyGuide;

import com.dt.component.common.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;


/**
 * <p>
 * 理库指引
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="TallyGuide对象", description="理库指引")
public class TallyGuideDTO extends BaseDTO  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    /**
     * 仓库编码
     */
    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    /**
     * 货主编码
     */
    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    /**
     * 商品编码
     */
    @ApiModelProperty(value = "商品编码")
    private String skuCode;

    /**
     * 商品条码
     */
    @ApiModelProperty(value = "商品条码")
    private String upcCode;

    /**
     * 库区编码
     */
    @ApiModelProperty(value = "库区编码")
    private String zoneCode;

    /**
     * 库区类型
     */
    @ApiModelProperty(value = "库区类型")
    private String zoneType;

    /**
     * 库位编码
     */
    @ApiModelProperty(value = "库位编码")
    private String locationCode;

    /**
     * 库位类型
     */
    @ApiModelProperty(value = "库位类型")
    private String locationType;

    /**
     * 批次编码
     */
    @ApiModelProperty(value = "批次编码")
    private String skuLotNo;

    /**
     * 异常明细
     */
    @ApiModelProperty(value = "异常明细")
    private String exceptionContent;

    @ApiModelProperty(value = "可用数量")
    private BigDecimal availableQty;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "修改人")
    private String updatedBy;
}