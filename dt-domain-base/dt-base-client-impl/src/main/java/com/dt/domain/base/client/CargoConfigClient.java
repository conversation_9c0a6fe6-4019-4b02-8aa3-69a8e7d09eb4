package com.dt.domain.base.client;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.exceptions.BaseException;
import com.dt.component.common.result.Result;
import com.dt.domain.base.cargo.entity.CargoConfig;
import com.dt.domain.base.cargo.service.ICargoConfigService;
import com.dt.domain.base.dto.CargoConfigDTO;
import com.dt.domain.base.param.CargoConfigBatchParam;
import com.dt.domain.base.param.CargoConfigParam;
import com.dt.domain.base.util.CargoConfigUtil;
import com.dt.platform.utils.ConverterUtil;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/9/15 17:53
 */
@DubboService(version = "${dubbo.service.version}")
@DS("#DTWMS")
public class CargoConfigClient implements ICargoConfigClient {

    @Resource
    ICargoConfigService iCargoConfigService;

    @Resource
    private CargoConfigUtil cargoConfigUtil;

    @Override
    @Transactional
    public Result<Boolean> save(CargoConfigParam param) {
        CargoConfig CargoConfig = ConverterUtil.convert(param, CargoConfig.class);
        if (ObjectUtils.isEmpty(CargoConfig)) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        Boolean result = iCargoConfigService.save(CargoConfig);
        if (!result){
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        return Result.success(true);
    }

    @Override
    @Transactional
    public Result<Boolean> saveBatch(CargoConfigBatchParam param) {
        List<CargoConfigParam> paramList = param.getCargoConfigList();
        List<CargoConfig> CargoConfigList = ConverterUtil.convertList(paramList, CargoConfig.class);
        if (CollectionUtils.isEmpty(CargoConfigList)) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        Boolean result = iCargoConfigService.saveBatch(CargoConfigList);
        if (!result){
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        return Result.success(true);
    }

    @Override
    @Transactional
    public Result<Boolean> saveBatch(List<CargoConfigDTO> configDTOList) {
        List<CargoConfig> CargoConfigList = ConverterUtil.convertList(configDTOList, CargoConfig.class);
        if (CollectionUtils.isEmpty(CargoConfigList)) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        Boolean result = iCargoConfigService.saveBatch(CargoConfigList);
        if (!result){
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        return Result.success(true);
    }

    @Override
    @Transactional
    public Result<Boolean> modify(CargoConfigParam param) {
        if (StringUtils.isEmpty(param.getId())
                && CollectionUtils.isEmpty(param.getIdList())) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        LambdaQueryWrapper<CargoConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(!StringUtils.isEmpty(param.getId()), CargoConfig::getId, param.getId())
                .in(!StringUtils.isEmpty(param.getIdList()), CargoConfig::getId, param.getIdList());
        CargoConfig CargoConfig = ConverterUtil.convert(param, CargoConfig.class);
        if (ObjectUtils.isEmpty(CargoConfig)) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        Boolean result = iCargoConfigService.update(CargoConfig, wrapper);
        if (!result) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        return Result.success(true);
    }

    @Override
    public Result<Boolean> checkExits(CargoConfigParam param) {
        LambdaQueryWrapper<CargoConfig> wrapper = cargoConfigUtil.getQueryWrapper(param);
        Integer count = iCargoConfigService.count(wrapper);
        return Result.success(count != 0);
    }

    @Override
    public Result<CargoConfigDTO> get(CargoConfigParam param) {
        LambdaQueryWrapper<CargoConfig> queryWrapper = cargoConfigUtil.getQueryWrapper(param);
        CargoConfig CargoConfig = iCargoConfigService.getOne(queryWrapper);
        CargoConfigDTO result = ConverterUtil.convert(CargoConfig, CargoConfigDTO.class);
        return Result.success(result);
    }

    @Override
    public Result<List<CargoConfigDTO>> getList(CargoConfigParam param) {
        LambdaQueryWrapper<CargoConfig> queryWrapper = cargoConfigUtil.getQueryWrapper(param);
        List<CargoConfig> CargoConfigList = iCargoConfigService.list(queryWrapper);
        List<CargoConfigDTO> result = ConverterUtil.convertList(CargoConfigList, CargoConfigDTO.class);
        return Result.success(result);
    }

    @Override
    public Result<Page<CargoConfigDTO>> getPage(CargoConfigParam param) {
//        if (!Objects.isNull(param.getCurrentPage())) {
//            param.setCurrent(param.getCurrentPage());
//        }
//        if (!Objects.isNull(param.getPageSize())) {
//            param.setSize(param.getPageSize());
//        }
        Page<CargoConfig> page = new Page<>(param.getCurrent(), param.getSize(), param.getSearchCount());
        LambdaQueryWrapper<CargoConfig> queryWrapper = cargoConfigUtil.getQueryWrapper(param);
        IPage<CargoConfig> CargoConfigPage = iCargoConfigService.page(page, queryWrapper);
        Page<CargoConfigDTO> result = ConverterUtil.convertPage(CargoConfigPage, CargoConfigDTO.class);
        return Result.success(result);
    }

}
