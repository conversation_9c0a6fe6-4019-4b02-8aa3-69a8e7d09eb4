package com.dt.domain.base.client;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.enums.base.ContainerStatusEnum;
import com.dt.component.common.exceptions.BaseException;
import com.dt.component.common.result.Result;
import com.dt.domain.base.dto.ContainerDTO;
import com.dt.domain.base.param.ContainerBatchParam;
import com.dt.domain.base.param.ContainerParam;
import com.dt.domain.base.util.ContainerUtil;
import com.dt.domain.base.warehouse.entity.Container;
import com.dt.domain.base.warehouse.service.IContainerService;
import com.dt.platform.utils.ConverterUtil;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/9/16 13:43
 */
@DubboService(version = "${dubbo.service.version}")
@DS("#DTWMS")
public class ContainerClient implements IContainerClient {

    @Resource
    IContainerService iContainerService;

    @Resource
    private ContainerUtil containerUtil;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> save(ContainerParam param) {
        Container entity = ConverterUtil.convert(param, Container.class);
        if (ObjectUtils.isEmpty(entity)) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        Boolean result = iContainerService.save(entity);

        if (!result) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        return Result.success(true);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> saveBatch(ContainerBatchParam param) {
        if (CollectionUtils.isEmpty(param.getContainerList())) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        List<Container> list = ConverterUtil.convertList(param.getContainerList(), Container.class);
        Boolean result = iContainerService.saveBatch(list);
        if (!result) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        return Result.success(true);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> modify(ContainerParam param) {
        if (StringUtils.isEmpty(param.getId()) && StringUtils.isEmpty(param.getCode())
                && CollectionUtils.isEmpty(param.getCodeList())
                && CollectionUtils.isEmpty(param.getIdList())) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        LambdaQueryWrapper<Container> wrapper = new LambdaQueryWrapper<>();
        wrapper
                .eq(!StringUtils.isEmpty(param.getId()), Container::getId, param.getId())
                .eq(!StringUtils.isEmpty(param.getCode()), Container::getCode, param.getCode())
                .in(!CollectionUtils.isEmpty(param.getIdList()), Container::getId, param.getIdList())
                .in(!CollectionUtils.isEmpty(param.getCodeList()), Container::getCode, param.getCodeList())
        ;
        Container Container = ConverterUtil.convert(param, Container.class);
        if (ObjectUtils.isEmpty(Container)) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        Boolean result = iContainerService.update(Container, wrapper);
        if (!result) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        return Result.success(true);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> modifyBatch(ContainerBatchParam param) {
        if (CollectionUtils.isEmpty(param.getContainerList())) {
            throw new BaseException(BaseBizEnum.TIP, "参数不能为空");
        }
        for (ContainerDTO container : param.getContainerList()) {
            if (StringUtils.isEmpty(container.getId())) {
                throw new BaseException(BaseBizEnum.TIP, "系统事务异常");
            }
        }
        List<Container> containerList = ConverterUtil.convertList(param.getContainerList(), Container.class);
        containerList.forEach(entity -> {
            Boolean result = iContainerService.updateById(entity);
            if (!result) {
                throw new BaseException(BaseBizEnum.TIP, "系统事务异常");
            }
        });
        return Result.success(true);
    }

    @Override
    public Result<Boolean> checkExits(ContainerParam param) {
        LambdaQueryWrapper<Container> wrapper = containerUtil.getQueryWrapper(param);
        Integer count = iContainerService.count(wrapper);
        return Result.success(count != 0);
    }

    @Override
    public Result<ContainerDTO> get(ContainerParam param) {
        LambdaQueryWrapper<Container> queryWrapper = containerUtil.getQueryWrapper(param);
        Container entity = iContainerService.getOne(queryWrapper);
        ContainerDTO result = ConverterUtil.convert(entity, ContainerDTO.class);
        return Result.success(result);
    }

    @Override
    public Result<List<ContainerDTO>> getList(ContainerParam param) {
        LambdaQueryWrapper<Container> queryWrapper = containerUtil.getQueryWrapper(param);
        List<Container> list = iContainerService.list(queryWrapper);
        List<ContainerDTO> result = ConverterUtil.convertList(list, ContainerDTO.class);
        return Result.success(result);
    }

    @Override
    public Result<Page<ContainerDTO>> getPage(ContainerParam param) {
//        if (!Objects.isNull(param.getCurrentPage())) {
//            param.setCurrent(param.getCurrentPage());
//        }
//        if (!Objects.isNull(param.getPageSize())) {
//            param.setSize(param.getPageSize());
//        }
        Page<Container> page = new Page<>(param.getCurrent(), param.getSize(), param.getSearchCount());
        LambdaQueryWrapper<Container> queryWrapper = containerUtil.getQueryWrapper(param);
        IPage<Container> entity = iContainerService.page(page, queryWrapper);
        Page<ContainerDTO> result = ConverterUtil.convertPage(entity, ContainerDTO.class);
        return Result.success(result);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> saveBatch(List<ContainerDTO> containerDTOS) {
        if (CollectionUtils.isEmpty(containerDTOS)) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        List<Container> list = ConverterUtil.convertList(containerDTOS, Container.class);
        Boolean result = iContainerService.saveBatch(list);
        if (!result) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        return Result.success(true);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> modifyBatch(List<ContainerDTO> containerDTOList) {
        List<Container> containerList = ConverterUtil.convertList(containerDTOList, Container.class);
        containerList.forEach(entity -> {
            Boolean result = iContainerService.updateById(entity);
            if (!result) {
                throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
            }
        });
        return Result.success(true);
    }

    @Override
    public Result<String> queryRecommendShContCode() {
        LambdaQueryWrapper<Container> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(Container::getCode);
        queryWrapper.eq(Container::getStatus, ContainerStatusEnum.ENABLE.getValue());
        queryWrapper.like(Container::getCode, "SH");
        List<Container> list = iContainerService.list(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return Result.success("");
        }
        return Result.success(list.get(0).getCode());
    }

    @Override
    public Result<ContainerDTO> getAssemblingContCode(ContainerParam containerParam) {
        LambdaQueryWrapper<Container> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Container::getStatus, ContainerStatusEnum.ENABLE.getValue());
        queryWrapper.likeRight(Container::getCode, "SH");
        queryWrapper.last(" limit 1 ");
        List<Container> containerList = iContainerService.list(queryWrapper);
        if (CollectionUtils.isEmpty(containerList)) {
            LambdaQueryWrapper<Container> queryWrapperOther = new LambdaQueryWrapper<>();
            queryWrapperOther.eq(Container::getStatus, ContainerStatusEnum.ENABLE.getValue());
            queryWrapperOther.last(" limit 1 ");
            List<Container> list = iContainerService.list(queryWrapperOther);
            if (CollectionUtils.isEmpty(list)) {
                return Result.success(null);
            }
            return Result.success(ConverterUtil.convert(list.get(0), ContainerDTO.class));
        }
        return Result.success(ConverterUtil.convert(containerList.get(0), ContainerDTO.class));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> modifyContainerOccupyDTO(ContainerDTO containerDTO) {
        Container entity = ConverterUtil.convert(containerDTO, Container.class);
        if (ObjectUtils.isEmpty(entity)) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        Boolean result = iContainerService.updateById(entity);
        if (!result) {
            throw new RuntimeException("更新异常");
        }
        return Result.success(true);
    }
}
