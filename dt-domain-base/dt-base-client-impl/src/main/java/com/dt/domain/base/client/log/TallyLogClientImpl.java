package com.dt.domain.base.client.log;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.exceptions.BaseException;
import com.dt.component.common.result.Result;
import com.dt.domain.base.dto.log.TallyLogDTO;
import com.dt.domain.base.param.log.TallyLogParam;
import com.dt.domain.base.log.entity.TallyLog;
import com.dt.domain.base.log.service.ITallyLogService;
import com.dt.domain.base.util.log.TallyLogUtil;
import com.dt.platform.utils.ConverterUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;


/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-30
 */
@Slf4j
@DubboService(version = "${dubbo.service.version}")
@DS("#DTWMS")
public class TallyLogClientImpl implements ITallyLogClient {

    @Resource
    private ITallyLogService tallyLogService;

    @Resource
    private TallyLogUtil tallyLogUtil;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> save(TallyLogDTO tallyLogDTO) {
        TallyLog tallyLog = ConverterUtil.convert(tallyLogDTO, TallyLog.class);
        if (ObjectUtils.isEmpty(tallyLog)) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        Boolean result = tallyLogService.save(tallyLog);
        if (!result) {
            throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
        }
        return Result.success(result);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> saveBatch(List<TallyLogDTO> tallyLogDTOList) {
        if (CollectionUtils.isEmpty(tallyLogDTOList)){
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        List<TallyLog> list = ConverterUtil.convertList(tallyLogDTOList, TallyLog.class);
        Boolean result = tallyLogService.saveBatch(list);
        if (!result) {
            throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
        }
        return Result.success(result);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> modify(TallyLogDTO tallyLogDTO) {
        if (ObjectUtils.isEmpty(tallyLogDTO.getId())){
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        LambdaQueryWrapper<TallyLog> wrapper = new LambdaQueryWrapper<>();
        wrapper
                .eq(!StringUtils.isEmpty(tallyLogDTO.getId()), TallyLog::getId, tallyLogDTO.getId())
        ;
        TallyLog tallyLog = ConverterUtil.convert(tallyLogDTO, TallyLog.class);
        if (ObjectUtils.isEmpty(tallyLog)) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        Boolean result = tallyLogService.update(tallyLog, wrapper);
        if (!result) {
            throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
        }
        return Result.success(result);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> modifyBatch(List<TallyLogDTO> tallyLogDTOList) {
        if (CollectionUtils.isEmpty(tallyLogDTOList)){
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        List<TallyLog> list = ConverterUtil.convertList(tallyLogDTOList, TallyLog.class);
        Boolean result = tallyLogService.updateBatchById(list);
        if (!result) {
            throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
        }
        return Result.success(result);
    }

    @Override
    public Result<Boolean> checkExits(TallyLogParam param) {
        LambdaQueryWrapper<TallyLog> wrapper = tallyLogUtil.getQueryWrapper(param);
        Integer count = tallyLogService.count(wrapper);
        return Result.success(count != 0);
    }

    @Override
    public Result<TallyLogDTO> get(TallyLogParam param) {
        LambdaQueryWrapper<TallyLog> queryWrapper = tallyLogUtil.getQueryWrapper(param);
        TallyLog tallyLog = tallyLogService.getOne(queryWrapper);
        TallyLogDTO result = ConverterUtil.convert(tallyLog, TallyLogDTO.class);
        return Result.success(result);
    }

    @Override
    public Result<List<TallyLogDTO>> getList(TallyLogParam param) {
        LambdaQueryWrapper<TallyLog> queryWrapper = tallyLogUtil.getQueryWrapper(param);
        List<TallyLog> tallyLogList = tallyLogService.list(queryWrapper);
        List<TallyLogDTO> result = ConverterUtil.convertList(tallyLogList, TallyLogDTO.class);
        return Result.success(result);
    }

    @Override
    public Result<Page<TallyLogDTO>> getPage(TallyLogParam param) {
        Page<TallyLog> page = new Page<>(param.getCurrent(), param.getSize(), param.getSearchCount());
        LambdaQueryWrapper<TallyLog> queryWrapper = tallyLogUtil.getQueryWrapper(param);
        IPage<TallyLog> TallyLogPage = tallyLogService.page(page, queryWrapper);
        Page<TallyLogDTO> result = ConverterUtil.convertPage(TallyLogPage, TallyLogDTO.class);
        return Result.success(result);
    }

    @Override
    public Result<Boolean> remove(TallyLogParam param) {
        LambdaQueryWrapper<TallyLog> wrapper = tallyLogUtil.getQueryWrapper(param);
        Boolean result = tallyLogService.remove(wrapper);
        if (!result) {
            throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
        }
        return Result.success(result);
    }
}
