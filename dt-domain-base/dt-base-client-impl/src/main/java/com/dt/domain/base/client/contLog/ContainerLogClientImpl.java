package com.dt.domain.base.client.contLog;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.exceptions.BaseException;
import com.dt.component.common.result.Result;
import com.dt.domain.base.contLog.entity.ContainerLog;
import com.dt.domain.base.contLog.service.IContainerLogService;
import com.dt.domain.base.dto.contLog.ContainerLogDTO;
import com.dt.domain.base.param.contLog.ContainerLogParam;
import com.dt.domain.base.util.contLog.ContainerLogUtil;
import com.dt.platform.utils.ConverterUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;


/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-30
 */
@Slf4j
@DubboService(version = "${dubbo.service.version}")
@DS("#DTWMS")
public class ContainerLogClientImpl implements IContainerLogClient {

    @Resource
    private IContainerLogService containerLogService;

    @Resource
    private ContainerLogUtil containerLogUtil;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> save(ContainerLogDTO containerLogDTO) {
        ContainerLog containerLog = ConverterUtil.convert(containerLogDTO, ContainerLog.class);
        if (ObjectUtils.isEmpty(containerLog)) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        Boolean result = containerLogService.save(containerLog);
        if (!result) {
            throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
        }
        return Result.success(result);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> saveBatch(List<ContainerLogDTO> containerLogDTOList) {
        if (CollectionUtils.isEmpty(containerLogDTOList)) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        List<ContainerLog> list = ConverterUtil.convertList(containerLogDTOList, ContainerLog.class);
        Boolean result = containerLogService.saveBatch(list);
        if (!result) {
            throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
        }
        return Result.success(result);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> modify(ContainerLogDTO containerLogDTO) {
        if (ObjectUtils.isEmpty(containerLogDTO.getId())) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        LambdaQueryWrapper<ContainerLog> wrapper = new LambdaQueryWrapper<>();
        wrapper
                .eq(!StringUtils.isEmpty(containerLogDTO.getId()), ContainerLog::getId, containerLogDTO.getId())
        ;
        ContainerLog containerLog = ConverterUtil.convert(containerLogDTO, ContainerLog.class);
        if (ObjectUtils.isEmpty(containerLog)) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        Boolean result = containerLogService.update(containerLog, wrapper);
        if (!result) {
            throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
        }
        return Result.success(result);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> modifyBatch(List<ContainerLogDTO> containerLogDTOList) {
        if (CollectionUtils.isEmpty(containerLogDTOList)) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        List<ContainerLog> list = ConverterUtil.convertList(containerLogDTOList, ContainerLog.class);
        list.forEach(entity -> {
            Boolean result = containerLogService.updateById(entity);
            if (!result) {
                throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
            }
        });
        return Result.success(true);
    }

    @Override
    public Result<Boolean> checkExits(ContainerLogParam param) {
        LambdaQueryWrapper<ContainerLog> wrapper = containerLogUtil.getQueryWrapper(param);
        Integer count = containerLogService.count(wrapper);
        return Result.success(count != 0);
    }

    @Override
    public Result<ContainerLogDTO> get(ContainerLogParam param) {
        LambdaQueryWrapper<ContainerLog> queryWrapper = containerLogUtil.getQueryWrapper(param);
        ContainerLog containerLog = containerLogService.getOne(queryWrapper);
        ContainerLogDTO result = ConverterUtil.convert(containerLog, ContainerLogDTO.class);
        return Result.success(result);
    }

    @Override
    public Result<List<ContainerLogDTO>> getList(ContainerLogParam param) {
        LambdaQueryWrapper<ContainerLog> queryWrapper = containerLogUtil.getQueryWrapper(param);
        List<ContainerLog> containerLogList = containerLogService.list(queryWrapper);
        List<ContainerLogDTO> result = ConverterUtil.convertList(containerLogList, ContainerLogDTO.class);
        return Result.success(result);
    }

    @Override
    public Result<Page<ContainerLogDTO>> getPage(ContainerLogParam param) {
        Page<ContainerLog> page = new Page<>(param.getCurrent(), param.getSize(), param.getSearchCount());
        LambdaQueryWrapper<ContainerLog> queryWrapper = containerLogUtil.getQueryWrapper(param);
        IPage<ContainerLog> ContainerLogPage = containerLogService.page(page, queryWrapper);
        Page<ContainerLogDTO> result = ConverterUtil.convertPage(ContainerLogPage, ContainerLogDTO.class);
        return Result.success(result);
    }

    @Override
    public Result<Boolean> remove(ContainerLogParam param) {
        LambdaQueryWrapper<ContainerLog> wrapper = containerLogUtil.getQueryWrapper(param);
        Boolean result = containerLogService.remove(wrapper);
        if (!result) {
            throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
        }
        return Result.success(result);
    }

    @Override
    public Result<List<ContainerLogDTO>> getListLimitNum(ContainerLogParam param) {
        LambdaQueryWrapper<ContainerLog> queryWrapper = containerLogUtil.getQueryWrapper(param);
        queryWrapper.orderByDesc(ContainerLog::getId).last("limit 0,10");
        List<ContainerLog> containerLogList = containerLogService.list(queryWrapper);
        List<ContainerLogDTO> result = ConverterUtil.convertList(containerLogList, ContainerLogDTO.class);
        return Result.success(result);
    }
}
