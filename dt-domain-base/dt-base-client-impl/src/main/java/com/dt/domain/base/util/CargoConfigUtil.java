package com.dt.domain.base.util;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dt.component.mp.query.QueryWrapper;
import com.dt.domain.base.cargo.entity.CargoConfig;
import com.dt.domain.base.param.CargoConfigParam;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @date 2020/9/16 11:34
 */
@Component
public class CargoConfigUtil extends QueryWrapper<CargoConfig, CargoConfigParam> {

    @Override
    public LambdaQueryWrapper<CargoConfig> getQueryWrapper(CargoConfigParam param) {

        LambdaQueryWrapper<CargoConfig> lambdaQueryWrapper = super.getQueryWrapper(param);
        lambdaQueryWrapper
                .eq(!StringUtils.isEmpty(param.getCargoCode()), CargoConfig::getCargoCode, param.getCargoCode())
                .eq(!StringUtils.isEmpty(param.getWarehouseCode()), CargoConfig::getWarehouseCode, param.getWarehouseCode())
                .eq(!StringUtils.isEmpty(param.getPropGroup()), CargoConfig::getPropGroup, param.getPropGroup())
                .like(!StringUtils.isEmpty(param.getPropName()), CargoConfig::getPropName, param.getPropName())
                .eq(!StringUtils.isEmpty(param.getPropKey()), CargoConfig::getPropKey, param.getPropKey())
                .eq(!StringUtils.isEmpty(param.getPropValue()), CargoConfig::getPropValue, param.getPropValue())
                .eq(!StringUtils.isEmpty(param.getWarehouseCode()), CargoConfig::getWarehouseCode, param.getWarehouseCode())
                .eq(!StringUtils.isEmpty(param.getStatus()), CargoConfig::getStatus, param.getStatus())
                .in(!CollectionUtils.isEmpty(param.getStatusList()), CargoConfig::getStatus, param.getStatusList())
                .in(!CollectionUtils.isEmpty(param.getCargoCodeList()), CargoConfig::getCargoCode, param.getCargoCodeList())
        ;

        return lambdaQueryWrapper;

    }


}
