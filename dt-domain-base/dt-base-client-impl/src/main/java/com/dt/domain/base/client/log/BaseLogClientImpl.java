package com.dt.domain.base.client.log;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.exceptions.BaseException;
import com.dt.component.common.result.Result;
import com.dt.domain.base.dto.log.BaseLogDTO;
import com.dt.domain.base.param.log.BaseLogParam;
import com.dt.domain.base.log.entity.BaseLog;
import com.dt.domain.base.log.service.IBaseLogService;
import com.dt.domain.base.util.log.BaseLogUtil;
import com.dt.platform.utils.ConverterUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;


/**
 * <p>
 * 基础模块日志 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-18
 */
@Slf4j
@DubboService(version = "${dubbo.service.version}")
@DS("#DTWMS")
public class BaseLogClientImpl implements IBaseLogClient {

    @Resource
    private IBaseLogService baseLogService;

    @Resource
    private BaseLogUtil baseLogUtil;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> save(BaseLogDTO baseLogDTO) {
        BaseLog baseLog = ConverterUtil.convert(baseLogDTO, BaseLog.class);
        if (ObjectUtils.isEmpty(baseLog)) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        Boolean result = baseLogService.save(baseLog);
        if (!result) {
            throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
        }
        return Result.success(result);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> saveBatch(List<BaseLogDTO> baseLogDTOList) {
        if (CollectionUtils.isEmpty(baseLogDTOList)){
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        List<BaseLog> list = ConverterUtil.convertList(baseLogDTOList, BaseLog.class);
        Boolean result = baseLogService.saveBatch(list);
        if (!result) {
            throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
        }
        return Result.success(result);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> modify(BaseLogDTO baseLogDTO) {
        if (ObjectUtils.isEmpty(baseLogDTO.getId())){
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        LambdaQueryWrapper<BaseLog> wrapper = new LambdaQueryWrapper<>();
        wrapper
                .eq(!StringUtils.isEmpty(baseLogDTO.getId()), BaseLog::getId, baseLogDTO.getId())
        ;
        BaseLog baseLog = ConverterUtil.convert(baseLogDTO, BaseLog.class);
        if (ObjectUtils.isEmpty(baseLog)) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        Boolean result = baseLogService.update(baseLog, wrapper);
        if (!result) {
            throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
        }
        return Result.success(result);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> modifyBatch(List<BaseLogDTO> baseLogDTOList) {
        if (CollectionUtils.isEmpty(baseLogDTOList)){
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        List<BaseLog> list = ConverterUtil.convertList(baseLogDTOList, BaseLog.class);
        list.forEach(entity -> {
            Boolean result = baseLogService.updateById(entity);
            if (!result) {
                throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
            }
        });
        return Result.success(true);
    }

    @Override
    public Result<Boolean> checkExits(BaseLogParam param) {
        LambdaQueryWrapper<BaseLog> wrapper = baseLogUtil.getQueryWrapper(param);
        Integer count = baseLogService.count(wrapper);
        return Result.success(count != 0);
    }

    @Override
    public Result<BaseLogDTO> get(BaseLogParam param) {
        LambdaQueryWrapper<BaseLog> queryWrapper = baseLogUtil.getQueryWrapper(param);
        BaseLog baseLog = baseLogService.getOne(queryWrapper);
        BaseLogDTO result = ConverterUtil.convert(baseLog, BaseLogDTO.class);
        return Result.success(result);
    }

    @Override
    public Result<List<BaseLogDTO>> getList(BaseLogParam param) {
        LambdaQueryWrapper<BaseLog> queryWrapper = baseLogUtil.getQueryWrapper(param);
        List<BaseLog> baseLogList = baseLogService.list(queryWrapper);
        List<BaseLogDTO> result = ConverterUtil.convertList(baseLogList, BaseLogDTO.class);
        return Result.success(result);
    }

    @Override
    public Result<Page<BaseLogDTO>> getPage(BaseLogParam param) {
        Page<BaseLog> page = new Page<>(param.getCurrent(), param.getSize(), param.getSearchCount());
        LambdaQueryWrapper<BaseLog> queryWrapper = baseLogUtil.getQueryWrapper(param);
        IPage<BaseLog> BaseLogPage = baseLogService.page(page, queryWrapper);
        Page<BaseLogDTO> result = ConverterUtil.convertPage(BaseLogPage, BaseLogDTO.class);
        return Result.success(result);
    }

    @Override
    public Result<Boolean> remove(BaseLogParam param) {
        LambdaQueryWrapper<BaseLog> wrapper = baseLogUtil.getQueryWrapper(param);
        Boolean result = baseLogService.remove(wrapper);
        if (!result) {
            throw new BaseException(BaseBizEnum.DB_TRANSACTION_ERROR);
        }
        return Result.success(result);
    }
}
