package com.dt.domain.base.client;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.exceptions.BaseException;
import com.dt.component.common.result.Result;
import com.dt.domain.base.carrier.entity.Carrier;
import com.dt.domain.base.carrier.service.ICarrierService;
import com.dt.domain.base.dto.CarrierDTO;
import com.dt.domain.base.param.CarrierBatchParam;
import com.dt.domain.base.param.CarrierParam;
import com.dt.domain.base.util.CarrierUtil;
import com.dt.platform.utils.ConverterUtil;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/9/16 13:24
 */
@DubboService(version = "${dubbo.service.version}")
@DS("#DTWMS")
public class CarrierClient implements ICarrierClient {

    @Resource
    ICarrierService iCarrierService;

    @Resource
    private CarrierUtil carrierUtil;

    @Override
    @Transactional
    public Result<Boolean> save(CarrierParam param) {
        Carrier entity = ConverterUtil.convert(param, Carrier.class);
        if (ObjectUtils.isEmpty(entity)) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        Boolean result = iCarrierService.save(entity);

        return Result.success(result);
    }

    @Override
    @Transactional
    public Result<Boolean> saveBatch(CarrierBatchParam param) {
        List<CarrierParam> paramList = param.getCarrierList();
        List<Carrier> list = ConverterUtil.convertList(paramList, Carrier.class);
        if (CollectionUtils.isEmpty(list)) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        Boolean result = iCarrierService.saveBatch(list);
        if (!result){
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        return Result.success(true);
    }

    @Override
    @Transactional
    public Result<Boolean> modify(CarrierParam param) {
        if (StringUtils.isEmpty(param.getId()) && StringUtils.isEmpty(param.getCode())
                && CollectionUtils.isEmpty(param.getCodeList())
                && CollectionUtils.isEmpty(param.getIdList())) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        LambdaQueryWrapper<Carrier> wrapper = new LambdaQueryWrapper<>();
        wrapper
                .eq(!StringUtils.isEmpty(param.getId()), Carrier::getId, param.getId())
                .eq(!StringUtils.isEmpty(param.getCode()), Carrier::getCode, param.getCode())
                .in(!CollectionUtils.isEmpty(param.getIdList()), Carrier::getId, param.getIdList())
                .in(!CollectionUtils.isEmpty(param.getCodeList()), Carrier::getCode, param.getCodeList())
        ;
        Carrier Carrier = ConverterUtil.convert(param, Carrier.class);
        if (ObjectUtils.isEmpty(Carrier)) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        Boolean result = iCarrierService.update(Carrier, wrapper);
        return Result.success(result);
    }

    @Override
    public Result<Boolean> checkExits(CarrierParam param) {
        LambdaQueryWrapper<Carrier> wrapper = carrierUtil.getQueryWrapper(param);
        Integer count = iCarrierService.count(wrapper);
        return Result.success(count != 0);
    }

    @Override
    public Result<CarrierDTO> get(CarrierParam param) {
        LambdaQueryWrapper<Carrier> queryWrapper = carrierUtil.getQueryWrapper(param);
        Carrier entity = iCarrierService.getOne(queryWrapper);
        CarrierDTO result = ConverterUtil.convert(entity, CarrierDTO.class);
        return Result.success(result);
    }

    @Override
    public Result<List<CarrierDTO>> getList(CarrierParam param) {
        LambdaQueryWrapper<Carrier> queryWrapper = carrierUtil.getQueryWrapper(param);
        List<Carrier> list = iCarrierService.list(queryWrapper);
        List<CarrierDTO> result = ConverterUtil.convertList(list, CarrierDTO.class);
        return Result.success(result);
    }

    @Override
    public Result<Page<CarrierDTO>> getPage(CarrierParam param) {
//        if (!Objects.isNull(param.getCurrentPage())) {
//            param.setCurrent(param.getCurrentPage());
//        }
//        if (!Objects.isNull(param.getPageSize())) {
//            param.setSize(param.getPageSize());
//        }
        Page<Carrier> page = new Page<>(param.getCurrent(), param.getSize(), param.getSearchCount());
        LambdaQueryWrapper<Carrier> queryWrapper = carrierUtil.getQueryWrapper(param);
        IPage<Carrier> entity = iCarrierService.page(page, queryWrapper);
        Page<CarrierDTO> result = ConverterUtil.convertPage(entity, CarrierDTO.class);
        return Result.success(result);
    }
}
