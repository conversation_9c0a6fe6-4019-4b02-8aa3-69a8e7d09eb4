package com.dt.domain.base.client;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.exceptions.BaseException;
import com.dt.component.common.result.Result;
import com.dt.domain.base.dto.WarehouseDTO;
import com.dt.domain.base.param.WarehouseBatchParam;
import com.dt.domain.base.param.WarehouseParam;
import com.dt.domain.base.util.WarehouseUtil;
import com.dt.domain.base.warehouse.entity.Warehouse;
import com.dt.domain.base.warehouse.service.IWarehouseService;
import com.dt.platform.utils.ConverterUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;

@DubboService(version = "${dubbo.service.version}",timeout = 600000)
@DS("#DTWMS")
@Slf4j
public class WarehouseClient implements IWarehouseClient {

    @Resource
    private IWarehouseService warehouseService;

    @Resource
    private WarehouseUtil warehouseUtil;

    @Override
    @Transactional
    public Result<Boolean> save(WarehouseParam param) {
        Warehouse warehouse = ConverterUtil.convert(param, Warehouse.class);
        if (ObjectUtils.isEmpty(warehouse)) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        Boolean result = warehouseService.save(warehouse);

        if (!result) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        return Result.success(true);
    }

    @Override
    @Transactional
    public Result<Boolean> saveBatch(WarehouseBatchParam param) {
        List<WarehouseParam> paramList = param.getWarehouseList();
        List<Warehouse> warehouseList = ConverterUtil.convertList(paramList, Warehouse.class);
        if (CollectionUtils.isEmpty(warehouseList)) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        Boolean result = warehouseService.saveBatch(warehouseList);
        if (!result) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        return Result.success(true);
    }

    @Override
    @Transactional
    public Result<Boolean> modify(WarehouseParam param) {
        if (StringUtils.isEmpty(param.getId()) && StringUtils.isEmpty(param.getCode())
                && CollectionUtils.isEmpty(param.getCodeList())
                && CollectionUtils.isEmpty(param.getIdList())) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        LambdaQueryWrapper<Warehouse> wrapper = new LambdaQueryWrapper<>();
        wrapper
                .eq(!StringUtils.isEmpty(param.getId()), Warehouse::getId, param.getId())
                .eq(!StringUtils.isEmpty(param.getCode()), Warehouse::getCode, param.getCode())
                .in(!CollectionUtils.isEmpty(param.getIdList()), Warehouse::getId, param.getIdList())
                .in(!CollectionUtils.isEmpty(param.getCodeList()), Warehouse::getCode, param.getCodeList())
        ;
        Warehouse warehouse = ConverterUtil.convert(param, Warehouse.class);
        if (ObjectUtils.isEmpty(warehouse)) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        Boolean result = warehouseService.update(warehouse, wrapper);
        if (!result) {
            throw new BaseException(BaseBizEnum.ILLEGAL_ARGUMENT);
        }
        return Result.success(true);
    }

    @Override
    public Result<Boolean> checkExits(WarehouseParam param) {
        LambdaQueryWrapper<Warehouse> wrapper = warehouseUtil.getQueryWrapper(param);
        Integer count = warehouseService.count(wrapper);
        return Result.success(count != 0);
    }

    @Override
    public Result<WarehouseDTO> get(WarehouseParam param) {
        LambdaQueryWrapper<Warehouse> queryWrapper = warehouseUtil.getQueryWrapper(param);
        Warehouse warehouse = warehouseService.getOne(queryWrapper);
        WarehouseDTO result = ConverterUtil.convert(warehouse, WarehouseDTO.class);
        return Result.success(result);
    }

    @Override
    public Result<List<WarehouseDTO>> getList(WarehouseParam param) {
        LambdaQueryWrapper<Warehouse> queryWrapper = warehouseUtil.getQueryWrapper(param);
        List<Warehouse> warehouseList = warehouseService.list(queryWrapper);
        List<WarehouseDTO> result = ConverterUtil.convertList(warehouseList, WarehouseDTO.class);
        return Result.success(result);
    }

    @Override
    public Result<Page<WarehouseDTO>> getPage(WarehouseParam param) {
//        if (!Objects.isNull(param.getCurrentPage())) {
//            param.setCurrent(param.getCurrentPage());
//        }
//        if (!Objects.isNull(param.getPageSize())) {
//            param.setSize(param.getPageSize());
//        }
        Page<Warehouse> page = new Page<>(param.getCurrent(), param.getSize(), param.getSearchCount());
        LambdaQueryWrapper<Warehouse> queryWrapper = warehouseUtil.getQueryWrapper(param);
        IPage<Warehouse> warehousePage = warehouseService.page(page, queryWrapper);
        Page<WarehouseDTO> result = ConverterUtil.convertPage(warehousePage, WarehouseDTO.class);
        return Result.success(result);
    }


    @Override
    public Result<Integer> createOrUpdateTable(String sql) {
        int orUpdateTable = warehouseService.createOrUpdateTable(sql);
        return Result.success(orUpdateTable);
    }


}
