<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dt.domain.base.warehouseManage.mapper.TableDDLMapper">
    <update id="doCreateTable">
       ${ddl};
    </update>
    <update id="doCreateDB">
        create database ${dbName} character set utf8mb4 COLLATE utf8mb4_general_ci;
    </update>

    <select id="getTableNames" resultType="java.lang.String">
        show tables;
    </select>
    <select id="getCreateTableDDL" resultType="map" parameterType="java.lang.String">
        show create table ${tableName};
    </select>
</mapper>
