package com.dt.domain.base.rule.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 序列号规则详情
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_seq_rule_detail")
@ApiModel(value="SeqRuleDetail对象", description="序列号规则详情")
public class SeqRuleDetail extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "规则编码")
    private String ruleCode;

//    @ApiModelProperty(value = "仓库编码")
//    private String warehouseCode;

    @ApiModelProperty(value = "规则属性")
    private String ruleName;

    @ApiModelProperty(value = "属性键值")
    private String propKey;

    @ApiModelProperty(value = "属性名称")
    private String propName;

    @ApiModelProperty(value = "属性值")
    private String propValue;

    @ApiModelProperty(value = "日期格式")
    private String dataFormat;

    @ApiModelProperty(value = "序列起始值")
    private String startValue;

    @ApiModelProperty(value = "序列字符步长")
    private String step;

    @ApiModelProperty(value = "序列字符长度")
    private String digit;

    @ApiModelProperty(value = "状态码 字典组：ABLE_STATUS")
    private Integer status;

    @ApiModelProperty(value = "备注")
    private String remark;


}