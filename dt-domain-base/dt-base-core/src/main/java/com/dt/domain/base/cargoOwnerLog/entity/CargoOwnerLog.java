package com.dt.domain.base.cargoOwnerLog.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dt.component.mp.entity.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 货主档案日志
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_cargo_owner_log")
@ApiModel(value = "CargoOwnerLog对象", description = "货主档案日志")
public class CargoOwnerLog extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    @ApiModelProperty(value = "操作说明")
    private String opContent;

    @ApiModelProperty(value = "备注")
    private String opRemark;

    @ApiModelProperty(value = "操作人")
    private String opBy;

    private Long opDate;

}