package com.dt.domain.base.pre.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 * 多预包解析
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_multi_pre_def")
@ApiModel(value="MultiPreDef对象", description="多预包解析")
public class MultiPreDef extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    @ApiModelProperty(value = "多预包解析定义编码")
    private String multiPreDefCode;

    @ApiModelProperty(value = "多预包解析结构签名")
    private String multiPreDefSign;

    @ApiModelProperty(value = "商品代码【包括预包】")
    private String skuCode;

    @ApiModelProperty(value = "类型")
    private String type;

    @ApiModelProperty(value = "状态")
    private String status;

    @ApiModelProperty(value = "数量")
    private BigDecimal qty;


}