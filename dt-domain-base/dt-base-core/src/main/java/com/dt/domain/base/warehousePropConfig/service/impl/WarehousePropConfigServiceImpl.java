package com.dt.domain.base.warehousePropConfig.service.impl;

import com.dt.domain.base.warehousePropConfig.entity.WarehousePropConfig;
import com.dt.domain.base.warehousePropConfig.mapper.WarehousePropConfigMapper;
import com.dt.domain.base.warehousePropConfig.service.IWarehousePropConfigService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 仓库参数配置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-20
 */
@Service
public class WarehousePropConfigServiceImpl extends ServiceImpl<WarehousePropConfigMapper, WarehousePropConfig> implements IWarehousePropConfigService {

}
