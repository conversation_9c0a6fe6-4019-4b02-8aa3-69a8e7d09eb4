package com.dt.domain.base.carrier.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 承运商管理
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_carrier")
@ApiModel(value="Carrier对象", description="承运商管理")
public class Carrier extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "承运商编码 不允许修改,唯一")
    private String code;

//    @ApiModelProperty(value = "仓库编码")
//    private String warehouseCode;

    @ApiModelProperty(value = "承运商名称")
    private String name;

    @ApiModelProperty(value = "快递单类型")
    private String type;

    @ApiModelProperty(value = "是否接口获取，取值启用、停用，默认为是(1:是，-1:否)")
    private Integer isHttp;

    @ApiModelProperty(value = "是否子母单，取值启用、停用，默认为否(1:是，-1:否)")
    private Integer hasChild;

    @ApiModelProperty(value = "状态码，取值启用、停用，默认为启用状态(1:启用，-1:停用)")
    private Integer status;

    @ApiModelProperty(value = "优先权重")
    private Integer priority;

    @ApiModelProperty(value = "结算账号")
    private String settlementAccount;
}