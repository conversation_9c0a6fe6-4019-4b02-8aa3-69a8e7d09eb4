package com.dt.domain.base.pick.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dt.domain.base.pick.entity.PickRuleDetail;
import org.apache.ibatis.annotations.Delete;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR> xy
 * @since 2020-10-14
 */
public interface PickRuleDetailMapper extends BaseMapper<PickRuleDetail> {

    @Delete("delete from dt_pick_rule_detail where id = #{id}")
    boolean deleteDetail(Long id);
}
