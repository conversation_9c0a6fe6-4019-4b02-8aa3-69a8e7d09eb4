package com.dt.domain.base.cargoOwnerLog.service.impl;

import com.dt.domain.base.cargoOwnerLog.entity.CargoOwnerLog;
import com.dt.domain.base.cargoOwnerLog.mapper.CargoOwnerLogMapper;
import com.dt.domain.base.cargoOwnerLog.service.ICargoOwnerLogService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 货主档案日志 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-09
 */
@Service
public class CargoOwnerLogServiceImpl extends ServiceImpl<CargoOwnerLogMapper, CargoOwnerLog> implements ICargoOwnerLogService {

}
