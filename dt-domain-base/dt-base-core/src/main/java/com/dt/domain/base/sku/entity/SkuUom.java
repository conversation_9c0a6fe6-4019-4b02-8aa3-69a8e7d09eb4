package com.dt.domain.base.sku.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 * 商品规格
 * </p>
 *
 * <AUTHOR> xy
 * @since 2020-09-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_sku_uom")
@ApiModel(value="SkuUom对象", description="商品规格")
public class SkuUom extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

//    @ApiModelProperty(value = "仓库编码")
//    private String warehouseCode;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    @ApiModelProperty(value = "商品编码")
    private String skuCode;

    @ApiModelProperty(value = "包装单位 字典组：PACKAGE_UNIT")
    private String packageUnitCode;

    @ApiModelProperty(value = "包装单位数量 包装单位的数量")
    private BigDecimal packageQty;

    @ApiModelProperty(value = "状态码 ")
    private Integer status;


}