package com.dt.domain.base.warehouse.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 * 库位管理
 * </p>
 *
 * <AUTHOR> xy
 * @since 2020-09-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_location")
@ApiModel(value="Location对象", description="库位管理")
public class Location extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

//    @ApiModelProperty(value = "仓库编码 不能为空，取值仓库档案")
//    private String warehouseCode;

    @ApiModelProperty(value = "编码 不允许修改,唯一")
    private String code;

    @ApiModelProperty(value = "库区编码 不能为空，取值库区档案")
    private String zoneCode;
    private String zoneType;
    @ApiModelProperty(value = "巷道编码 不能为空，取值巷道档案")
    private String tunnelCode;

    @ApiModelProperty(value = "拣货路由序号")
    private Long pickSeq;

    @ApiModelProperty(value = "上架路由序号")
    private Long shelfSeq;

    @ApiModelProperty(value = "库位类型")
    private String type;

    @ApiModelProperty(value = "库位用途")
    private String useMode;

    @ApiModelProperty(value = "高（cm）")
    private BigDecimal height;

    @ApiModelProperty(value = "宽（cm）")
    private BigDecimal width;

    @ApiModelProperty(value = "长（cm）")
    private BigDecimal length;

    @ApiModelProperty(value = "最大允许体积(cm³)")
    private BigDecimal volume;

    @ApiModelProperty(value = "最大允许重量(kg)")
    private BigDecimal weight;

    @ApiModelProperty(value = "存放规则")
    private String storageRule;

    @ApiModelProperty(value = "混放规则")
    private String mixRuleCode;

    @ApiModelProperty(value = "最大商品数")
    private Integer maxSkuNum;

    @ApiModelProperty(value = "最大种类数")
    private Integer maxTypeNum;

    @ApiModelProperty(value = "库位货架类型")
    private String shelfType;

    @ApiModelProperty(value = "库位计费规格")
    private String chargingModel;

    @ApiModelProperty(value = "状态码 ")
    private Integer status;

    @ApiModelProperty(value = "库位标记")
    private Integer locationTag;

    @ApiModelProperty(value = "恒温仓")
    private String thermostatic;
}