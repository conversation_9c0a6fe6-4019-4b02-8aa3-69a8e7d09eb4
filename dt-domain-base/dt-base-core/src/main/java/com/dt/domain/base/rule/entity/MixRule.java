package com.dt.domain.base.rule.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 混放规则
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_mix_rule")
@ApiModel(value="MixRule对象", description="混放规则")
public class MixRule extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

//    @ApiModelProperty(value = "仓库编码")
//    private String warehouseCode;

    @ApiModelProperty(value = "混放编码")
    private String code;

    @ApiModelProperty(value = "策略名称")
    private String name;

    @ApiModelProperty(value = "策略描叙")
    private String note;

    @ApiModelProperty(value = "状态码 ")
    private Integer status;

    @ApiModelProperty(value = "默认混放规则")
    private Integer isDefault;




}