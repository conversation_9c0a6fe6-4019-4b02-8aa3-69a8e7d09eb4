package com.dt.domain.base.log.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dt.component.mp.entity.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_adjust_log")
@ApiModel(value = "AdjustLog对象", description = "")
public class AdjustLog extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    private String cargoCode;

    private String adjustCode;

    @ApiModelProperty(value = "操作说明")
    private String opContent;

    @ApiModelProperty(value = "备注")
    private String opRemark;

    @ApiModelProperty(value = "操作人")
    private String opBy;

    private Long opDate;

}