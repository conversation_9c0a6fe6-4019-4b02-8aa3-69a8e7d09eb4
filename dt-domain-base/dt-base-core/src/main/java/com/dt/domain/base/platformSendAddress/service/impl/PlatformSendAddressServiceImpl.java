package com.dt.domain.base.platformSendAddress.service.impl;

import com.dt.domain.base.platformSendAddress.entity.PlatformSendAddress;
import com.dt.domain.base.platformSendAddress.mapper.PlatformSendAddressMapper;
import com.dt.domain.base.platformSendAddress.service.IPlatformSendAddressService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 平台快递发件地址 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-27
 */
@Service
public class PlatformSendAddressServiceImpl extends ServiceImpl<PlatformSendAddressMapper, PlatformSendAddress> implements IPlatformSendAddressService {

}
