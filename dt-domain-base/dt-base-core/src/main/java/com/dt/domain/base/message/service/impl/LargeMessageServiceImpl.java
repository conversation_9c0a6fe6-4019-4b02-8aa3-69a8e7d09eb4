package com.dt.domain.base.message.service.impl;

import com.dt.domain.base.message.entity.LargeMessage;
import com.dt.domain.base.message.mapper.LargeMessageMapper;
import com.dt.domain.base.message.service.ILargeMessageService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 超大字段记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-13
 */
@Service
public class LargeMessageServiceImpl extends ServiceImpl<LargeMessageMapper, LargeMessage> implements ILargeMessageService {

}
