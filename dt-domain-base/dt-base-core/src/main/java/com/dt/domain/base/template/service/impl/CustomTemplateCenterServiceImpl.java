package com.dt.domain.base.template.service.impl;

import com.dt.domain.base.template.entity.CustomTemplateCenter;
import com.dt.domain.base.template.mapper.CustomTemplateCenterMapper;
import com.dt.domain.base.template.service.ICustomTemplateCenterService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 自定义打印模板配置中心 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-09
 */
@Service
public class CustomTemplateCenterServiceImpl extends ServiceImpl<CustomTemplateCenterMapper, CustomTemplateCenter> implements ICustomTemplateCenterService {

}
