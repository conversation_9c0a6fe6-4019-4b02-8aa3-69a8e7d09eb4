package com.dt.domain.base.rule.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dt.domain.base.rule.entity.MixRule;
import com.dt.domain.base.rule.mapper.MixRuleMapper;
import com.dt.domain.base.rule.service.IMixRuleService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 混放规则 服务实现类
 * </p>
 *
 * <AUTHOR> xy
 * @since 2020-09-11
 */
@Service
public class MixRuleServiceImpl extends ServiceImpl<MixRuleMapper, MixRule> implements IMixRuleService {

}
