package com.dt.domain.base.seq.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_sequence")
@ApiModel(value="Sequence对象", description="生成序列")
public class Sequence extends BaseEntity
{
    @ApiModelProperty(value = "生成序列代码")
    private String seqCode;
    @ApiModelProperty(value = "序列名称")
    private String seqName;
    @ApiModelProperty(value = "序列数值")
    private Long seqValue;

}