package com.dt.domain.base.location.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 库位日志
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_location_log")
@ApiModel(value="LocationLog对象", description="库位日志")
public class LocationLog extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;
 

    @ApiModelProperty(value = "编码 不允许修改,唯一")
    private String locationCode;

    @ApiModelProperty(value = "操作说明")
    private String opContent;

    private String opRemark;

    @ApiModelProperty(value = "操作人")
    private String opBy;

    private Long opDate;


}