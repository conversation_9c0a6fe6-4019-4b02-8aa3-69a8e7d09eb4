package com.dt.domain.base.config.service.impl;

import com.dt.domain.base.config.entity.CargoTenant;
import com.dt.domain.base.config.mapper.CargoTenantMapper;
import com.dt.domain.base.config.service.ICargoTenantService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 仓库货主租户映射 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-17
 */
@Service
public class CargoTenantServiceImpl extends ServiceImpl<CargoTenantMapper, CargoTenant> implements ICargoTenantService {

}
