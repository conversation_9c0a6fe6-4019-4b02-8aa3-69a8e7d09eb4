package com.dt.domain.base.allocation.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> xy
 * @since 2020-10-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_allocation_rule")
@ApiModel(value="AllocationRule对象", description="")
public class AllocationRule extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "编码")
    private String code;

//    @ApiModelProperty(value = "仓库编码")
//    private String warehouseCode;

    @ApiModelProperty(value = "分配规则名称")
    private String name;

    @ApiModelProperty(value = "清空库位优先")
    private String removeLocation;

    @ApiModelProperty(value = "满足订单优先")
    private String satisfyOrder;

    @ApiModelProperty(value = "状态码")
    private Integer status;

    @ApiModelProperty(value = "默认")
    private Integer isDefault;


}