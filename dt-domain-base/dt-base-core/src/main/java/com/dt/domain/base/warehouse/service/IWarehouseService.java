package com.dt.domain.base.warehouse.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dt.domain.base.warehouse.entity.Warehouse;

/**
 * <p>
 * 仓库档案 服务类
 * </p>
 *
 * <AUTHOR> xy
 * @since 2020-09-11
 */
public interface IWarehouseService extends IService<Warehouse> {


    /**
     * 功能描述:  sql 执行调用的方法
     * 创建时间:  2021/5/10 10:08 上午
     *
     * @param sql:
     * @return void
     * <AUTHOR>
     */
    int createOrUpdateTable(String sql);

}
