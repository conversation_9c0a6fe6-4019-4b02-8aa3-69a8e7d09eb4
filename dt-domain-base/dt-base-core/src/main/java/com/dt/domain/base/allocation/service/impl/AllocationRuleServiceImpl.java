package com.dt.domain.base.allocation.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dt.domain.base.allocation.entity.AllocationRule;
import com.dt.domain.base.allocation.mapper.AllocationRuleMapper;
import com.dt.domain.base.allocation.service.IAllocationRuleService;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR> xy
 * @since 2020-10-12
 */
@Service
public class AllocationRuleServiceImpl extends ServiceImpl<AllocationRuleMapper, AllocationRule> implements IAllocationRuleService {

}
