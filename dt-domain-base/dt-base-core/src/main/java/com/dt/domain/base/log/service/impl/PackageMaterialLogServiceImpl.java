package com.dt.domain.base.log.service.impl;

import com.dt.domain.base.log.entity.PackageMaterialLog;
import com.dt.domain.base.log.mapper.PackageMaterialLogMapper;
import com.dt.domain.base.log.service.IPackageMaterialLogService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 包材操作日志 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-30
 */
@Service
public class PackageMaterialLogServiceImpl extends ServiceImpl<PackageMaterialLogMapper, PackageMaterialLog> implements IPackageMaterialLogService {

}
