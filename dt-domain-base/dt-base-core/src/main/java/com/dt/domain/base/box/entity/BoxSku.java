package com.dt.domain.base.box.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 套盒商品绑定表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_box_sku")
@ApiModel(value="BoxSku对象", description="套盒商品绑定表")
public class BoxSku extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "套盒编码")
    private String boxCode;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    @ApiModelProperty(value = "套盒商品编码")
    private String boxSkuCode;

    @ApiModelProperty(value = "商品代码+数量(拼接升序MD5转小写)")
    private String boxSkuStructure;

    @ApiModelProperty(value = "审核人")
    private String authBy;

    @ApiModelProperty(value = "审核备注")
    private String authRemark;

    @ApiModelProperty(value = "审核时间 (时间戳)")
    private Long authTime;

    @ApiModelProperty(value = "状态")
    private String status;

    @ApiModelProperty(value = "绑定状态")
    private String docStatus;

    @ApiModelProperty(value = "备注")
    private String remark;


}