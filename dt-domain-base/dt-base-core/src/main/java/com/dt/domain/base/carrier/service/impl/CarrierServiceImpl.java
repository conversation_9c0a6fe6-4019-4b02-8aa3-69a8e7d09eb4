package com.dt.domain.base.carrier.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dt.domain.base.carrier.entity.Carrier;
import com.dt.domain.base.carrier.mapper.CarrierMapper;
import com.dt.domain.base.carrier.service.ICarrierService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 承运商管理 服务实现类
 * </p>
 *
 * <AUTHOR> xy
 * @since 2020-09-11
 */
@Service
public class CarrierServiceImpl extends ServiceImpl<CarrierMapper, Carrier> implements ICarrierService {

}
