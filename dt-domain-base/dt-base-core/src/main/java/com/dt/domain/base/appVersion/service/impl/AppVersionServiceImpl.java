package com.dt.domain.base.appVersion.service.impl;

import com.dt.domain.base.appVersion.entity.AppVersion;
import com.dt.domain.base.appVersion.mapper.AppVersionMapper;
import com.dt.domain.base.appVersion.service.IAppVersionService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * apk更新版本表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-08
 */
@Service
public class AppVersionServiceImpl extends ServiceImpl<AppVersionMapper, AppVersion> implements IAppVersionService {

}
