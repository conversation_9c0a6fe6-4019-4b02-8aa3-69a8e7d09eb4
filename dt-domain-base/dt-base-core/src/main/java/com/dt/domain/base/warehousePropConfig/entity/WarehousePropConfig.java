package com.dt.domain.base.warehousePropConfig.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 仓库参数配置
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_warehouse_prop_config")
@ApiModel(value="WarehousePropConfig对象", description="仓库参数配置")
public class WarehousePropConfig extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "参数分类")
    private String propGroup;

    @ApiModelProperty(value = "参数编码")
    private String propKey;

    @ApiModelProperty(value = "参数名称")
    private String propName;

    @ApiModelProperty(value = "参数说明")
    private String propNote;

    @ApiModelProperty(value = "对应参数属性值 对应字典属性值，实际启用的值")
    private String propValue;

    @ApiModelProperty(value = "状态码")
    private Integer status;

    @ApiModelProperty(value = "备注")
    private String remark;


}