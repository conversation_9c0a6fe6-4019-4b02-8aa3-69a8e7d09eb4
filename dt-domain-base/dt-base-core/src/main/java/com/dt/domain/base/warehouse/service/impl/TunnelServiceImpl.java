package com.dt.domain.base.warehouse.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dt.domain.base.warehouse.entity.Tunnel;
import com.dt.domain.base.warehouse.mapper.TunnelMapper;
import com.dt.domain.base.warehouse.service.ITunnelService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 巷道/通道管理 服务实现类
 * </p>
 *
 * <AUTHOR> xy
 * @since 2020-09-11
 */
@Service
public class TunnelServiceImpl extends ServiceImpl<TunnelMapper, Tunnel> implements ITunnelService {

}
