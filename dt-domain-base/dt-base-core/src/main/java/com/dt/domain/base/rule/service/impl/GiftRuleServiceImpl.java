package com.dt.domain.base.rule.service.impl;

import com.dt.domain.base.rule.entity.GiftRule;
import com.dt.domain.base.rule.mapper.GiftRuleMapper;
import com.dt.domain.base.rule.service.IGiftRuleService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 赠品规则 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-07
 */
@Service
public class GiftRuleServiceImpl extends ServiceImpl<GiftRuleMapper, GiftRule> implements IGiftRuleService {

}
