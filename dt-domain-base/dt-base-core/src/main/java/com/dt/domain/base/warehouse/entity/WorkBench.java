package com.dt.domain.base.warehouse.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 质检/工作台
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_work_bench")
@ApiModel(value = "WorkBench对象", description = "质检/工作台")
public class WorkBench extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

//    @ApiModelProperty(value = "仓库编码")
//    private String warehouseCode;

    @ApiModelProperty(value = "质检台编码 不允许修改，唯一")
    private String code;

    @ApiModelProperty(value = "质检台名称")
    private String name;

    @ApiModelProperty(value = "质检台类型 字典组:BENCH_GROUP")
    private String type;

    @ApiModelProperty(value = "状态码 字典组：ABLE_STATUS")
    private Integer status;

    /**
     * 拓展信息
     */
    @ApiModelProperty(value = "拓展信息")
    private String extraJson;
}