package com.dt.domain.base.warehouseManage.mapper;

import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/12/14 11:16
 */
public interface TableDDLMapper {
    List<String> getTableNames();

    Map<String,String> getCreateTableDDL(@Param("tableName") String tableName);

    Boolean doCreateTable(@Param("ddl") String ddl);

    Boolean doCreateDB(@Param("dbName") String dbName);
}
