package com.dt.domain.base.config.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 仓库货主租户映射
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_cargo_tenant")
@ApiModel(value="CargoTenant对象", description="仓库货主租户映射")
public class CargoTenant extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "仓库编码")
    private String warehouse;
    
    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    @ApiModelProperty(value = "租户ID")
    private String tenantId;


}