package com.dt.domain.base.location.service.impl;

import com.dt.domain.base.location.entity.LocationLog;
import com.dt.domain.base.location.mapper.LocationLogMapper;
import com.dt.domain.base.location.service.ILocationLogService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 库位日志 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-16
 */
@Service
public class LocationLogServiceImpl extends ServiceImpl<LocationLogMapper, LocationLog> implements ILocationLogService {

}
