package com.dt.domain.base.sku.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 货品批次信息
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_sku_lot")
@ApiModel(value="SkuLot对象", description="货品批次信息")
public class SkuLot extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "批次ID")
    private String code;

    @ApiModelProperty(value = "外部批次ID")
    private String externalSkuLotNo;

    @ApiModelProperty(value = "残次等级")
    private String inventoryType;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    @ApiModelProperty(value = "商品编码")
    private String skuCode;

    @ApiModelProperty(value = "入库日期")
    private Long receiveDate;
    private String receiveDateFormat;

    @ApiModelProperty(value = "生产日期")
    private Long manufDate;
    private String manufDateFormat;

    @ApiModelProperty(value = "失效/过期日期")
    private Long expireDate;
    private String expireDateFormat;

    @ApiModelProperty(value = "商品属性")
    private String skuQuality;

    @ApiModelProperty(value = "生产批次号")
    private String productionNo;

    @ApiModelProperty(value = "禁售日期")
    private Long withdrawDate;
    private String withdrawDateFormat;

    @ApiModelProperty(value = "状态码 ")
    private Integer status;

    @ApiModelProperty(value = "入库关联单号(拓展单号)")
    private String externalLinkBillNo;

    @ApiModelProperty(value = "关联批次号")
    private String linkSkuLotNo;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "托盘号")
    private String palletCode;

    @ApiModelProperty(value = "箱码")
    private String boxCode;

    @ApiModelProperty(value = "拓展字段")
    private String extraJson;



}