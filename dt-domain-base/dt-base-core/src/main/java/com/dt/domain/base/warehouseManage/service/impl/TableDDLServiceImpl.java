package com.dt.domain.base.warehouseManage.service.impl;

import com.dt.domain.base.warehouseManage.mapper.TableDDLMapper;
import com.dt.domain.base.warehouseManage.service.ITableDDLService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/12/14 11:13
 */
@Service
public class TableDDLServiceImpl implements ITableDDLService {

    @Resource
    private TableDDLMapper tableDDLMapper;

    @Override
    public List<String> getTableNames() {
        return tableDDLMapper.getTableNames();
    }

    @Override
    public String getCreateTableDDL(String tableName) {
        return tableDDLMapper.getCreateTableDDL(tableName).get("Create Table");
    }

    @Override
    public Boolean doCreateTable(String ddl) {
        return tableDDLMapper.doCreateTable(ddl);
    }

    @Override
    public Boolean doCreateDB(String dbName) {
        return tableDDLMapper.doCreateDB(dbName);
    }
}
