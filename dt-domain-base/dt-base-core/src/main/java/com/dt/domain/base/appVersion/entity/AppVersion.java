package com.dt.domain.base.appVersion.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.dt.component.mp.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * apk更新版本表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("dt_app_version")
@ApiModel(value="AppVersion对象", description="apk更新版本表")
public class AppVersion extends BaseEntity  implements java.io.Serializable  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "版本号")
    private Integer versionCode;

    @ApiModelProperty(value = "版本名称")
    private String versionName;

    @ApiModelProperty(value = "apk大小")
    private Integer apkSize;

    @ApiModelProperty(value = "md5校验码")
    private String apkMd5;

    @ApiModelProperty(value = "下载地址")
    private String downloadUrl;

    @ApiModelProperty(value = "更新说明")
    private String modifyContent;

    @ApiModelProperty(value = "更新状态")
    private Integer updateStatus;


}