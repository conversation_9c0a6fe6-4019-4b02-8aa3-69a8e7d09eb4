package com.dt.domain.base.rule.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dt.domain.base.rule.entity.MixRuleDetail;
import com.dt.domain.base.rule.mapper.MixRuleDetailMapper;
import com.dt.domain.base.rule.service.IMixRuleDetailService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 混放规则详情 服务实现类
 * </p>
 *
 * <AUTHOR> xy
 * @since 2020-09-11
 */
@Service
public class MixRuleDetailServiceImpl extends ServiceImpl<MixRuleDetailMapper, MixRuleDetail> implements IMixRuleDetailService {

}
