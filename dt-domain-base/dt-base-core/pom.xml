<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.dt</groupId>
        <artifactId>dt-domain-base</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.dt</groupId>
    <artifactId>dt-base-core</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <name>dt-base-core</name>
    <description>数据持久化层,数据库的增删该查,数据的缓存</description>


    <dependencies>
        <!--Mybatis plus-->
        <dependency>
            <groupId>com.dt</groupId>
            <artifactId>dt-component-mp</artifactId>
        </dependency>
        <!--redis、cached-->
        <dependency>
            <groupId>com.dt</groupId>
            <artifactId>dt-component-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dt</groupId>
            <artifactId>dt-component-uid</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dt</groupId>
            <artifactId>dt-component-common</artifactId>
        </dependency>
        <!--test-->
        <dependency>
            <groupId>com.github.gavlyukovskiy</groupId>
            <artifactId>p6spy-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>io.seata</groupId>
            <artifactId>seata-all</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>druid</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.seata</groupId>
            <artifactId>seata-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.danding.component</groupId>
            <artifactId>apolloclient-component</artifactId>
        </dependency>
        <!--test-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <!--测试h2数据库-->
        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
