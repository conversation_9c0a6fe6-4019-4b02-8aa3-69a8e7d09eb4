package com.dt.portal.wms.web.client;

import com.danding.soul.client.common.annotation.SoulClient;
import com.dt.component.common.result.Result;
import com.dt.component.common.vo.IdNameVO;
import com.dt.component.common.vo.PageVO;
import com.dt.platform.wms.dto.lot.SkuLotSourceBizDTO;
import com.dt.platform.wms.param.IdParam;
import com.dt.platform.wms.param.sku.SkuLotBizParam;
import com.dt.platform.wms.param.stock.StockLocationBizParam;
import com.dt.platform.wms.param.stock.StockLocationSkuLotParam;
import com.dt.platform.wms.param.stock.StockLocationStatisticsBizParam;
import com.dt.platform.wms.param.stock.StockLocationWithLotBizParam;
import com.dt.portal.wms.web.vo.lot.SkuLotSourceVO;
import com.dt.portal.wms.web.vo.stock.StockLocationVO;
import com.dt.portal.wms.web.vo.stock.StockLocationWithLotVO;
import com.dt.portal.wms.web.vo.stock.StockStatisticVO;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
 * <p>
 * 三级库存 管理 网关接口
 * </p>
 *
 * <AUTHOR> xy
 * @since 2020-09-14
 */
public interface IStockLocationPortalClient {


    /**
     * 获取三级库存
     *
     * @param param
     * @return
     */
    Result<StockLocationVO> getDetail(IdParam param);


    /**
     * 查询批次信息
     * @return
     */
    Result<List<IdNameVO>> getSkuLotList(StockLocationSkuLotParam param);



    /**
     * 三级级库存分页列表
     *
     * @param param
     * @return
     */
    Result<PageVO<StockLocationVO>> getPage(StockLocationBizParam param);

    @ApiOperation("库存汇总")
    Result<StockStatisticVO> getStatistic(StockLocationBizParam param);

    /**
     * 三级库存分页列表
     *
     * @param param
     * @return
     */
    Result<PageVO<StockLocationVO>> getStatisticsPage(StockLocationStatisticsBizParam param);

    /**
     * 三级库存分页列表
     *
     * @param param
     * @return
     */
    Result<PageVO<StockLocationWithLotVO>> getLocationWithLotPage(StockLocationWithLotBizParam param);


    @ApiOperation("货主货位批次汇总")
    Result<StockStatisticVO> getLocationWithLotPageStatistic(StockLocationWithLotBizParam param);

    /**
     * 货主、商品、批次查询
     * @param param
     * @return
     */
    Result<PageVO<StockLocationWithLotVO>> getStatisticsCargoLotPage(SkuLotBizParam param);


    Result<SkuLotSourceBizDTO> getSkuLotSource(StockLocationWithLotBizParam param);


}
