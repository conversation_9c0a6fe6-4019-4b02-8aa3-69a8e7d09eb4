package com.dt.portal.wms.web.client.orderStayStatistics.impl;

import com.danding.soul.client.common.annotation.SoulClient;
import com.dt.component.common.result.Result;
import com.dt.component.common.vo.IndexVO;
import com.dt.elasticsearch.wms.client.IOrderStayStatisticsClient;
import com.dt.elasticsearch.wms.param.OrderStayBizParam;
import com.dt.portal.wms.web.client.orderStayStatistics.IOrderStayStatisticsPortalClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;

/**
 * <AUTHOR>
 * @date 2021/9/30 9:23
 */
@Slf4j
@DubboService(version = "${dubbo.service.version}")
public class OrderStayStatisticsPortalClientImpl implements IOrderStayStatisticsPortalClient {
    @DubboReference
    private IOrderStayStatisticsClient orderStayStatisticsClient;

    @SoulClient(path = "/orderStay/getData",desc = "出库单滞留分析折线图")
    @Override
    public Result<IndexVO> getData(OrderStayBizParam param) {
        return orderStayStatisticsClient.getOrderStayData(param);
    }

}
