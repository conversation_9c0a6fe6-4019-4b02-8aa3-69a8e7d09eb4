package com.dt.portal.wms.web.client;

import com.dt.component.common.result.Result;
import com.dt.exchange.wms.param.AsnTrajectoryBizParam;
import com.dt.platform.wms.param.CodeListParam;
import com.dt.platform.wms.param.CodeParam;

public interface INotifyPortClient {
    /**
     * 手动回传到货通知单
     *
     * @param codeParam
     * @return
     */
    Result<String> notifyAsn(CodeParam codeParam);

    /**
     * 功能描述:  入库单轨迹回传
     * 创建时间:  2021/11/9 1:32 下午
     *
     * @param asnTrajectoryBizParam:
     * @return com.dt.component.common.result.Result<java.lang.String>
     * <AUTHOR>
     */
    Result<String> notifyAsnTrajectoryCallBack(AsnTrajectoryBizParam asnTrajectoryBizParam);

    /**
     * 手动回传出库单
     *
     * @param codeParam
     * @return
     */
    Result<String> notifyOutOrder(CodeParam codeParam);

    /**
     * @param param
     * @return com.dt.component.common.result.Result<java.lang.String>
     * @author: WuXian
     * description:
     * create time: 2022/4/19 15:46
     */
    Result<String> notifyReceiptPartCallBack(CodeListParam param) throws Exception;
}
