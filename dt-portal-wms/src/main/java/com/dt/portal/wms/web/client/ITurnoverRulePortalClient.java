package com.dt.portal.wms.web.client;

import com.dt.component.common.result.Result;
import com.dt.component.common.vo.IdNameVO;
import com.dt.component.common.vo.PageVO;
import com.dt.platform.wms.param.base.TurnoverRuleBizParam;
import com.dt.portal.wms.web.vo.base.TurnoverRuleVO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/10/14 13:35
 */
public interface ITurnoverRulePortalClient {

    /**
     * 周转规则分页查询
     * @param param
     * @return
     */
    Result<PageVO<TurnoverRuleVO>> queryPage(TurnoverRuleBizParam param);

    Result<TurnoverRuleVO> getDetail(TurnoverRuleBizParam param);
    Result<List<IdNameVO>> getOptions();

    /**
     *状态码
     * @return
     */
    Result<List<IdNameVO>> queryStatus();

    /**
     *状态码
     * @return
     */
    Result<List<IdNameVO>> queryStatusAll();

    /**
     * 修改
     * @param param
     * @return
     */
    Result<Boolean> modify(TurnoverRuleBizParam param);

    /**
     * 创建
     * @param param
     * @return
     */
    Result<Boolean> create(TurnoverRuleBizParam param);
    Result<Boolean> enable(TurnoverRuleBizParam param);
}
