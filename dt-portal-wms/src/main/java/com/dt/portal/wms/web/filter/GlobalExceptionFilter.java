package com.dt.portal.wms.web.filter;

import com.danding.soul.client.common.exception.BusinessException;
import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.exceptions.BaseException;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.constants.CommonConstants;
import org.apache.dubbo.common.extension.Activate;
import org.apache.dubbo.rpc.*;
import org.apache.dubbo.rpc.service.GenericService;

import javax.servlet.ServletException;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import javax.validation.ValidationException;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

@Slf4j
@Activate(group = CommonConstants.PROVIDER, order = -99)
public class GlobalExceptionFilter implements Filter {

    public Result invoke(Invoker<?> invoker, Invocation invocation) throws RpcException {
        try {
            //执行业务逻辑
            Result result = invoker.invoke(invocation);
            //异常处理
            if (result.hasException() && GenericService.class != invoker.getInterface()) {
                Throwable exception = result.getException();
                if(exception instanceof BusinessException)
                {
                    BusinessException soulException = (BusinessException) exception;
                    return AsyncRpcResult.newDefaultAsyncResult(com.dt.component.common.result.Result.fail(soulException.getCode(), soulException.getMessage(), null), invocation);
                }
                //业务异常捕捉
                if (exception instanceof BaseException) {

                    BaseException bizException = (BaseException) exception;
                    return AsyncRpcResult.newDefaultAsyncResult(com.dt.component.common.result.Result.fail(bizException.getCode(), bizException.getMessage(), bizException.getData()), invocation);
                }
                // 校验框架异常捕捉
                if (exception instanceof ServletException) {
                    ServletException methodException = (ServletException) exception;
                    Integer code = BaseBizEnum.ILLEGAL_ARGUMENT.getValue();
                    return AsyncRpcResult.newDefaultAsyncResult(com.dt.component.common.result.Result.fail(code, methodException.getMessage(), null), invocation);
                }
                //验证信息异常逻辑
                if (exception instanceof ValidationException) {
                    List<String> errorList = new ArrayList<>();
                    Set<ConstraintViolation<?>> violations = ((ConstraintViolationException) exception).getConstraintViolations();
                    for (ConstraintViolation<?> constraintViolation : violations) {
                        String errorMsg = constraintViolation.getMessage();
                        String property = constraintViolation.getPropertyPath().toString();
                        errorList.add(String.format("%s:%s;", property, errorMsg));
                    }
                    Integer code = BaseBizEnum.ILLEGAL_ARGUMENT.getValue();
                    return AsyncRpcResult.newDefaultAsyncResult(com.dt.component.common.result.Result.fail(code, String.join("\n", errorList), null), invocation);
                }
                return AsyncRpcResult.newDefaultAsyncResult(com.dt.component.common.result.Result.fail(BaseBizEnum.SYSTEM_ERROR), invocation);
            }
            return result;
        } catch (Exception e) {
            return AsyncRpcResult.newDefaultAsyncResult(com.dt.component.common.result.Result.fail(BaseBizEnum.SYSTEM_ERROR), invocation);
        }
    }
}