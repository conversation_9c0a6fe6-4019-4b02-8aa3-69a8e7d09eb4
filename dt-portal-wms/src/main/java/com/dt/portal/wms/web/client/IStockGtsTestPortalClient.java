package com.dt.portal.wms.web.client;

import com.dt.component.common.result.Result;
import com.dt.platform.wms.client.gts.*;

public interface IStockGtsTestPortalClient {
    Result<Boolean> shelfComplete(GtsReceiptStockContextDTO param);
    Result<Boolean> shelfCompleteDetail(GtsShelfDetailDTO param);
    Result<Boolean> moveComplete(GtsMoveDetailDTO param);
    Result<Boolean> shipmentPretreatmentStock(GtsPretreatmentDTO param);
    Result<Boolean> shipmentPretreatmentStockZone(GtsPretreatmentDTO param);
    Result<Boolean> pickDistribute(GtsStockDistributeDTO param);
    Result<Boolean> pickUp(GtsStockPickingDTO param);
    Result<Boolean> shipmentShipmentPackage(GtsShipmentPackageDTO param);
}
