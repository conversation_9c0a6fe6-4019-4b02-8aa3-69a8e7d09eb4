package com.dt.portal.wms.web.filter;

import com.danding.ucenter.client.secruity.helper.UserHelper;
import com.danding.ucenter.core.security.model.TokenUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.constants.CommonConstants;
import org.apache.dubbo.common.extension.Activate;
import org.apache.dubbo.rpc.*;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;


/**
 * <p>
 * 仓库编码路由过滤
 * </p>
 *
 * <AUTHOR> xy
 * @since 2020-09-11
 */
@Slf4j
@Activate(group = CommonConstants.PROVIDER, order = 500)
public class PortalRouteInfoFilter implements Filter {

    public static String DT_ROUTE_WAREHOUSE_CODE = "DT_ROUTE_WAREHOUSE_CODE";

    public PortalRouteInfoFilter() {
        super();
    }

    @Override
    public Result invoke(Invoker<?> invoker, Invocation invocation) throws RpcException {
        RpcContext rpcContext = RpcContext.getContext();
        try {
            TokenUser tokenUser = UserHelper.getTokenUser();
            if (!ObjectUtils.isEmpty(tokenUser)) {
                String warehouseCode = tokenUser.getUserName();
                if (!StringUtils.isEmpty(warehouseCode)) {
                    rpcContext.setAttachment(DT_ROUTE_WAREHOUSE_CODE, warehouseCode);
                }
            }
            return invoker.invoke(invocation);
        } finally {
            rpcContext.clearAttachments();
        }
    }

}

