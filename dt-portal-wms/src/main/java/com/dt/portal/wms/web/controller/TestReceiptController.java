package com.dt.portal.wms.web.controller;

import com.dt.component.common.filter.RpcContextUtil;
import com.dt.component.common.result.Result;
import com.dt.component.common.vo.PageVO;
import com.dt.platform.wms.param.CodeParam;
import com.dt.platform.wms.param.receipt.ReceiptBizParam;
import com.dt.platform.wms.param.shelf.ShelfDetailCompleteBizParam;
import com.dt.portal.wms.web.client.IReceiptPortalClient;
import com.dt.portal.wms.web.client.IShelfPortalClient;
import com.dt.portal.wms.web.vo.receipt.ReceiptPageVO;
import org.springframework.context.annotation.Profile;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/dt-wms-portal/")
@Profile("dev")
public class TestReceiptController {

    @Resource
    private IReceiptPortalClient receiptPortalClient;

    /**
     *
     * @param param
     * @return
     */
    @PostMapping("/rec/page")
    public Result<PageVO<ReceiptPageVO>> queryPage(@RequestBody ReceiptBizParam param){
        RpcContextUtil.setWarehouseCode("DT_JYWMS1230");
        return receiptPortalClient.queryPage(param);
    }
}
