package com.dt.portal.wms.web.controller;

import com.dt.component.common.filter.RpcContextUtil;
import com.dt.component.common.result.Result;
import com.dt.component.common.vo.PageVO;
import com.dt.platform.wms.param.IdParam;
import com.dt.platform.wms.param.pkg.PackageBizParam;
import com.dt.portal.wms.web.client.IPackagePortClient;
import com.dt.portal.wms.web.vo.pkg.PackageVO;
import org.springframework.context.annotation.Profile;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2020/9/16 16:11
 */
@RestController
@RequestMapping("/dt-wms-portal/")
@Profile("dev")
public class TestPackageWeb {
    @Resource
    IPackagePortClient ipackagePortClient;

    @RequestMapping(value = "/package/page", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<PageVO<PackageVO>> queryPage(@RequestBody PackageBizParam param) {
        RpcContextUtil.setWarehouseCode("DT_JYWMS1230");
        return ipackagePortClient.queryPage(param);
    }

    @RequestMapping(value = "/package/queryDetail", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<PackageVO>  queryDetail(@RequestBody IdParam param) {
        return ipackagePortClient.queryDetail(param);
    }
}
