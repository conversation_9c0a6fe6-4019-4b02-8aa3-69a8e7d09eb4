package com.dt.portal.wms.web.client;

import com.dt.component.common.result.Result;
import com.dt.component.common.vo.IdNameVO;
import com.dt.component.common.vo.PageVO;
import com.dt.platform.wms.param.IdParam;
import com.dt.platform.wms.param.pkg.FileUploadParam;
import com.dt.platform.wms.param.pkg.PackageBizParam;
import com.dt.platform.wms.param.pkg.PackageVideoReportBizParam;
import com.dt.platform.wms.param.pkg.PackageVideoReportBySelfBizParam;
import com.dt.portal.wms.web.vo.pkg.PackageDetailVO;
import com.dt.portal.wms.web.vo.pkg.PackageVO;

import java.util.List;

public interface IPackagePortClient {
    Result<PageVO<PackageVO>> queryPageFromES(PackageBizParam param);

    /**
     * 包裹查询
     *
     * @param param
     * @return
     */
    Result<PageVO<PackageVO>> queryPage(PackageBizParam param);


    Result videoReport(PackageVideoReportBizParam param);

    /**
     * @param param
     * @return com.dt.component.common.result.Result
     * <AUTHOR>
     * @describe: 非淘天视频上传
     * @date 2025/4/3 9:30
     */
    Result<String> videoReportBySelf(PackageVideoReportBySelfBizParam param);


    Result fileUploadApply(FileUploadParam param);

    /**
     * 预包包裹类型下拉列表
     *
     * @return
     */
    Result<List<IdNameVO>> preSelectItem();

    /**
     * 包裹查看明细
     *
     * @param idParam
     * @return
     */

    Result<PackageVO> queryDetail(IdParam idParam);

    Result<PageVO<PackageDetailVO>> getDetailPage(PackageBizParam packageBizParam);

    /**
     * 包裹状态列表
     *
     * @return
     */
    Result<List<IdNameVO>> findListStatus();

    /**
     * 包裹结构
     *
     * @return
     */
    Result<List<IdNameVO>> findPackageStruct();
}
