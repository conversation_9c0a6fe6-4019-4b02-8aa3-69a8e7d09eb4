package com.dt.portal.wms.web.client;

import com.dt.component.common.result.Result;
import com.dt.portal.wms.web.vo.base.AreaVO;

import java.util.List;

public interface IAreaPortClient {
    /**
     * @param
     * @return com.dt.component.common.result.Result<java.util.List < com.dt.portal.wms.web.vo.base.AreaVO>>
     * @author: WuXian
     * description: 省市区
     * create time: 2021/9/18 13:57
     */
    Result<List<AreaVO>> findAll();
}
