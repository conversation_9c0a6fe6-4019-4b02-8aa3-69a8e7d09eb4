package com.dt.portal.wms.web.config;


import com.dt.portal.wms.web.config.swagger.BaseSwaggerConfig;
import com.dt.portal.wms.web.config.swagger.SwaggerProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

@Configuration
@EnableSwagger2
public class SwaggerConfig extends BaseSwaggerConfig {
    @Bean
    @Override
    public SwaggerProperties swaggerProperties() {
        return SwaggerProperties.builder()
                .apiBasePackage("com.dt")
                .title("代塔网关接入系统")
                .description("代塔网关接入系统接口文档")
                .contactName("xxy")
                .version("1.0")
                .enableSecurity(true)
                .build();
    }
}