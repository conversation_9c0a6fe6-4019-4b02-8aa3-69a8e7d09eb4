package com.dt.portal.wms.web.client;

import com.dt.component.common.result.Result;
import com.dt.component.common.vo.IdNameVO;
import com.dt.component.common.vo.PageVO;
import com.dt.domain.bill.dto.sn.ScanSnReceiveDTO;
import com.dt.domain.bill.param.sn.ScanSnReceiveParam;
import com.dt.platform.wms.param.CodeListParam;
import com.dt.platform.wms.param.CodeParam;
import com.dt.platform.wms.param.receipt.ReceiptBizParam;
import com.dt.platform.wms.param.receipt.ReceiptDetailBizParam;
import com.dt.portal.wms.web.vo.receipt.ReceiptPageVO;
import com.dt.portal.wms.web.vo.receipt.ReceiptVO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/9/21 17:00
 */
public interface IReceiptPortalClient {
    /**
     * 收货作业批次分页查询
     *
     * @param param
     * @return
     */
    Result<PageVO<ReceiptPageVO>> queryPage(ReceiptBizParam param);

    /**
     * @param scanSnReceiveParam
     * @return com.dt.component.common.result.Result<com.dt.component.common.vo.PageVO<com.dt.domain.bill.dto.sn.ScanSnReceiveDTO>>
     * <AUTHOR>
     * @describe:
     * @date 2024/5/10 15:18
     */
    Result<PageVO<ScanSnReceiveDTO>> queryScanSnReceivePage(ScanSnReceiveParam scanSnReceiveParam);

    /**
     * 收货作业批次明细查询
     *
     * @param param
     * @return
     */
    Result<ReceiptVO> queryDetail(ReceiptDetailBizParam param);

    /**
     * 查询状态码
     *
     * @return
     */
    Result<List<IdNameVO>> queryStatus();

    /**
     * 查询状态码
     *
     * @return
     */
    Result<List<IdNameVO>> queryStatusAll();

    /**
     * 查询收货方式
     *
     * @return
     */
    Result<List<IdNameVO>> queryRecFlag();

    /**
     * 查询收货方式
     *
     * @return
     */
    Result<List<IdNameVO>> queryRecFlagAll();

    /**
     * 收货类型
     *
     * @return
     */
    Result<List<IdNameVO>> queryReceiptType();

    /**
     * 收货类型
     *
     * @return
     */
    Result<List<IdNameVO>> queryReceiptTypeAll();

    /**
     * 取消收货作业批次
     *
     * @param param
     * @return
     */
    Result<Boolean> cancel(ReceiptBizParam param);

    /**
     * @param param
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * @author: WuXian
     * description:  导入收货完成容器生成上架单
     * create time: 2022/3/4 11:06
     */
    Result<Boolean> completeContByImportRec(CodeListParam param) throws Exception;

    /**
     * @param param
     * @return com.dt.component.common.result.Result<java.lang.Boolean>
     * @author: WuXian
     * description:  按收货作业批次(容器)取消收货
     * create time: 2021/12/15 9:18
     */
    Result<Boolean> cancelReceiptAndStock(CodeParam param) throws Exception;

}
