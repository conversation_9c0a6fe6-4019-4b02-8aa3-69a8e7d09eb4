package com.dt.portal.wms.web.client;

import com.dt.component.common.result.Result;
import com.dt.component.common.vo.IdNameVO;
import com.dt.component.common.vo.PageVO;
import com.dt.platform.wms.ReplenishSkuLotAndStockDTO;
import com.dt.platform.wms.dto.replenish.ReplenishRecommendDTO;
import com.dt.platform.wms.param.receipt.ReplenishTaskBizParam;
import com.dt.platform.wms.param.replenish.ReplenishRecommendParam;
import com.dt.portal.wms.web.vo.ReplenishTaskDataVO;
import com.dt.portal.wms.web.vo.ReplenishTaskVO;
import com.dt.portal.wms.web.vo.replenish.ReplenishRecommendNewVO;
import com.dt.portal.wms.web.vo.replenish.ReplenishTaskNewVO;

import java.util.List;

public interface IReplenishTaskPortalClient {

    Result<List<IdNameVO>> queryReplenishType();

//    /**
//     * 分页查询
//     * @param param
//     * @return
//     */
//    @Deprecated
//    Result<PageVO<ReplenishTaskDataVO>> getPage(ReplenishTaskBizParam param);
//
//
//
//    /**
//     * 补货指引推荐
//     * @param replenishRecommendParam
//     * @return
//     */
//    @Deprecated
//    Result<List<ReplenishRecommendDTO>> recommendStockLocation(ReplenishRecommendParam replenishRecommendParam);

    /**
     * @param param
     * @return com.dt.component.common.result.Result<com.dt.component.common.vo.PageVO < com.dt.portal.wms.web.vo.ReplenishTaskDataVO>>
     * @author: WuXian
     * description: 补货指引新分页
     * create time: 2021/7/5 9:44
     */
    Result<PageVO<ReplenishTaskNewVO>> getPageNew(ReplenishTaskBizParam param);

    /**
     * @param replenishRecommendParam
     * @return com.dt.component.common.result.Result<java.util.List < com.dt.platform.wms.dto.replenish.ReplenishRecommendDTO>>
     * @author: WuXian
     * description:
     * create time: 2021/7/5 17:01
     */
    Result<List<ReplenishRecommendNewVO>> recommendStockLocationNew(ReplenishRecommendParam replenishRecommendParam);
}
