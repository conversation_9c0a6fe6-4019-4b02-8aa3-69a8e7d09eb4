package com.dt.portal.wms.web.controller;

import com.dt.component.common.result.Result;
import com.dt.platform.wms.param.template.CustomTemplateCenterBizParam;
import com.dt.portal.wms.web.client.template.ICustomTemplateCenterPortalClient;
import com.dt.portal.wms.web.form.template.CustomTemplateCenterForm;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @date 2021/10/9 15:14
 */
@RestController
@Slf4j
public class CustomTemplateController {

    @Resource
    private ICustomTemplateCenterPortalClient customTemplateCenterPortalClient;

    @PostMapping("/custom/save")
    public void save(@RequestBody @Valid CustomTemplateCenterForm customTemplateCenterForm){
        customTemplateCenterPortalClient.save(customTemplateCenterForm);
    }

    @PostMapping("/custom/modify")
    public void modify(@RequestBody @Valid CustomTemplateCenterForm customTemplateCenterForm) {
        customTemplateCenterPortalClient.modify(customTemplateCenterForm);
    }

    @PostMapping("/custom/type/selectItem")
    public Result typeS() {
        return customTemplateCenterPortalClient.typeSelectItem();
    }

    @PostMapping("/custom/status/selectItem")
    public Result statusS() {
        return customTemplateCenterPortalClient.statusSelectItem();
    }

    @PostMapping("/custom/getPage")
    public Result getPage(@RequestBody CustomTemplateCenterBizParam param) {
        return customTemplateCenterPortalClient.getPage(param);
    }

    @PostMapping("/custom/status/enable")
    public Result statusEnable(@RequestBody CustomTemplateCenterBizParam param) {
        return customTemplateCenterPortalClient.statusEnable(param);
    }
}
