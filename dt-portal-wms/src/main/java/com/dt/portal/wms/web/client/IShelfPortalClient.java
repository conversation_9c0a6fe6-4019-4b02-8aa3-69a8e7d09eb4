package com.dt.portal.wms.web.client;

import com.danding.soul.client.common.annotation.SoulClient;
import com.dt.component.common.result.Result;
import com.dt.component.common.vo.IdNameVO;
import com.dt.component.common.vo.PageVO;
import com.dt.platform.wms.dto.shelf.SaleReturnBatchShelfTipDTO;
import com.dt.platform.wms.param.CodeListParam;
import com.dt.platform.wms.param.CodeParam;
import com.dt.platform.wms.param.shelf.ShelfBizParam;
import com.dt.platform.wms.param.shelf.ShelfCompleteParam;
import com.dt.platform.wms.param.shelf.ShelfDetailCompleteBizParam;
import com.dt.platform.wms.param.shelf.ShelfModifyOpTypeParam;
import com.dt.portal.wms.web.vo.shelf.ShelfVO;
import io.swagger.annotations.ApiOperation;

import java.util.List;

public interface IShelfPortalClient {

    /**
     * 获取上架单状态下拉列表
     * @return
     */
    Result<List<IdNameVO>> getStatusList();
    Result<List<IdNameVO>> getAllStatusList();


    /**
     * 获取上架方式列表
     * @return
     */
    Result<List<IdNameVO>> getOpTypeList();
    Result<List<IdNameVO>> getMark();
    Result<List<IdNameVO>> getAllOpTypeList();

    Result<List<IdNameVO>> getShelfTypeList();
    Result<List<IdNameVO>> getAllShelfTypeList();

    /**
     * 上架单分页列表
     * @param param
     * @return
     */
    Result<PageVO<ShelfVO>> getPage(ShelfBizParam param);

    /**
     * 修改上架单上架方式
     * @param param
     * @return
     */
    Result<Boolean> modifyShelfOpType(ShelfModifyOpTypeParam param);

    /**
     * 获取详情
     * @param param
     * @return
     */
    Result<ShelfVO> getDetail(CodeParam param);

    /**
     * 完成上架单
     * @param param
     * @return
     */
//    Result<Boolean> complete(CodeParam param);

    /**
     * 整个完成上架单
     * @param param
     * @return
     */
    Result<Boolean> completeWholeShelf(ShelfCompleteParam param);

    /**
     * 完成上架单明细
     * @param param
     * @return
     */
    Result<Boolean> completeDetail(ShelfDetailCompleteBizParam param);

    Result<Boolean> commitTaoTianShelf(ShelfBizParam param);

    Result<Boolean> commitTaoTianShelfCheckSku(ShelfBizParam param);

    @ApiOperation("销退上架")
    Result<Boolean> saleReturnBatchShelf(ShelfBizParam shelfBizParam);

    @ApiOperation("销退上架提示")
    Result<List<SaleReturnBatchShelfTipDTO>> saleReturnBatchShelfTip(ShelfBizParam shelfBizParam);
}
