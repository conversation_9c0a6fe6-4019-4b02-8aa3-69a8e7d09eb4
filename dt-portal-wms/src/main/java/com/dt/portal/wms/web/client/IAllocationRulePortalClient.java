package com.dt.portal.wms.web.client;

import com.dt.component.common.result.Result;
import com.dt.component.common.vo.IdNameVO;
import com.dt.component.common.vo.PageVO;
import com.dt.platform.wms.param.base.AllocationRuleBizParam;
import com.dt.portal.wms.web.vo.base.AllocationRuleVO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/10/14 13:32
 */
public interface IAllocationRulePortalClient {

    /**
     * 分配规则分页查询
     * @param param
     * @return
     */
    Result<PageVO<AllocationRuleVO>> queryPage(AllocationRuleBizParam param);

    Result<List<IdNameVO>> getOptions();
    /**
     *状态码
     * @return
     */
    Result<List<IdNameVO>> queryStatus();

    /**
     *状态码
     * @return
     */
    Result<List<IdNameVO>> queryStatusAll();


}
